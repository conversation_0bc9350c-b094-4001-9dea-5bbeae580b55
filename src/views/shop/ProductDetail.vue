<template>
  <div class="ag-container product-detail-container">
    <div class="ag-card product-detail" v-loading="loading">
      <!-- 面包屑导航 -->
      <div class="breadcrumb-nav">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/shop' }">农品汇</el-breadcrumb-item>
          <el-breadcrumb-item>{{ product.name }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      
      <!-- 产品基本信息区域 -->
      <div class="product-basic-info">
        <!-- 产品图片区域 -->
        <div class="product-images">
          <el-carousel :interval="4000" height="400px" arrow="always" indicator-position="outside">
            <el-carousel-item v-for="(image, index) in productImages" :key="index">
              <div class="ag-image">
                <img :src="image" :alt="product.name + ' - 图片 ' + (index + 1)" @error="handleImageError" />
              </div>
            </el-carousel-item>
          </el-carousel>
          
          <!-- 缩略图预览 -->
          <div class="thumbnail-list" v-if="productImages.length > 1">
            <div 
              v-for="(image, index) in productImages" 
              :key="'thumb-' + index" 
              class="thumbnail-item"
              :class="{ active: currentImageIndex === index }"
              @click="currentImageIndex = index"
            >
              <img :src="image" :alt="product.name + ' - 缩略图 ' + (index + 1)" />
        </div>
          </div>
        </div>
        
        <!-- 产品信息区域 -->
        <div class="product-info">
          <div class="product-title-section">
            <h1 class="product-title">{{ product.name }}</h1>
            <!-- 产品标签 -->
            <div class="product-badges">
              <el-tag
                v-if="product.sourceType === 'admin_direct'"
                type="success"
                effect="dark"
                class="admin-direct-badge"
              >
                <i class="el-icon-s-check"></i>
                产品直购
              </el-tag>
              <el-tag
                v-if="product.hasTraceability === 1 || product.traceCode"
                type="primary"
                effect="plain"
                class="traceability-badge"
              >
                <i class="el-icon-medal"></i>
                可溯源
              </el-tag>
              <el-tag
                v-if="product.isOrganic"
                type="success"
                effect="plain"
                class="organic-badge"
              >
                <i class="el-icon-medal"></i>
                有机认证
              </el-tag>
              <el-tag
                v-if="product.isNew"
                type="warning"
                effect="plain"
                class="new-badge"
              >
                <i class="el-icon-star-on"></i>
                新品
              </el-tag>
            </div>
          </div>

          <div class="product-meta">
            <div class="rating-info">
              <el-rate :value="productRating" disabled show-score text-color="#ff9900" />
              <span class="review-count">({{ product.reviewCount || 0 }}条评价)</span>
            </div>
            <div class="sales-info">
              <i class="el-icon-sold-out"></i>
              <span>已售 {{ product.salesCount || 0 }}</span>
            </div>
            <div class="favorites-info">
              <i class="el-icon-star-off"></i>
              <span>{{ product.favorites || 0 }}人收藏</span>
            </div>
          </div>
          
          <div class="product-price">
            <span class="price-label">价格</span>
            <span class="price-symbol">¥</span>
            <span class="price-value">{{ product.price }}</span>
            <span class="price-unit">/{{ product.unit }}</span>
            <span class="original-price" v-if="product.originalPrice && product.originalPrice > product.price">
              ¥{{ product.originalPrice }}
            </span>
          </div>
          
          <div class="product-attributes">
            <div class="attribute-item">
              <span class="attribute-label">分类</span>
              <span class="attribute-value">{{ categoryName }}</span>
            </div>
            <div class="attribute-item">
              <span class="attribute-label">库存</span>
              <span class="attribute-value">{{ product.stock }} {{ product.unit }}</span>
            </div>
            <div class="attribute-item" v-if="product.origin">
              <span class="attribute-label">产地</span>
              <span class="attribute-value">{{ product.origin }}</span>
            </div>
            <div class="attribute-item" v-if="product.specification">
              <span class="attribute-label">规格</span>
              <span class="attribute-value">{{ product.specification }}</span>
          </div>
          </div>
          
          <div class="product-purchase">
            <div class="quantity-control">
              <span class="quantity-label">数量</span>
              <el-input-number 
                v-model="purchaseQuantity" 
                :min="1" 
                :max="product.stock" 
                :disabled="product.stock <= 0"
                size="medium"
              ></el-input-number>
              <span class="quantity-unit">{{ product.unit }}</span>
              <span class="stock-info" :class="{ 'low-stock': product.stock < 10 && product.stock > 0 }">
                {{ product.stock > 0 ? `库存 ${product.stock} ${product.unit}` : '暂无库存' }}
              </span>
            </div>
            
            <div class="purchase-actions">
            <el-button 
              type="primary" 
              size="large" 
                icon="el-icon-shopping-cart-2" 
                class="ag-button ag-button--primary"
                @click="addToCart" 
                :disabled="product.stock <= 0"
              >
              加入购物车
            </el-button>
            <el-button 
                type="danger" 
              size="large"
                class="ag-button"
                @click="buyNow" 
                :disabled="product.stock <= 0"
              >
                立即购买
            </el-button>
            </div>
          </div>

          <!-- 商品互动工具栏 -->
          <div class="product-interaction-section">
            <ProductInteractionBar
              :product-id="productId"
              :initial-liked="isLiked"
              :initial-favorited="isFavorited"
              :initial-like-count="product.likeCount"
              :initial-favorite-count="product.favoriteCount"
              :view-count="product.viewCount"
              :review-count="product.reviewCount"
              @like-changed="handleLikeChanged"
              @favorite-changed="handleFavoriteChanged"
              @need-login="handleNeedLogin"
            />
          </div>

          <div class="seller-info" v-if="product.seller">
            <div class="seller-avatar">
              <img :src="product.seller.avatar || defaultAvatar" alt="卖家头像" />
            </div>
            <div class="seller-detail">
              <div class="seller-name">{{ product.seller.name || '匿名卖家' }}</div>
              <div class="seller-rating">
                <el-rate :value="getSellerRating(product.seller)" disabled show-score text-color="#ff9900" />
              </div>
            </div>
            <el-button size="small" type="success" class="ag-button" plain>联系卖家</el-button>
          </div>
        </div>
      </div>

      <!-- 产品详细内容区域 -->
      <div class="product-content">
        <el-tabs v-model="activeTab" class="product-tabs">
          <el-tab-pane label="商品详情" name="detail">
            <div class="product-description" v-if="product.detailDescription">
              <div v-html="product.detailDescription"></div>
            </div>
            <div class="product-description" v-else>
              <h3>产品描述</h3>
              <p>{{ product.description || '暂无详细描述' }}</p>
              
              <div class="product-images-list">
                <div class="ag-image" v-for="(image, index) in productImages" :key="'detail-' + index">
                  <img :src="image" :alt="product.name + ' - 详情图片 ' + (index + 1)" />
                </div>
              </div>
              
              <table class="product-spec-table" v-if="product.specifications && product.specifications.length > 0">
                <thead>
                  <tr>
                    <th colspan="2">产品规格</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(spec, index) in product.specifications" :key="'spec-' + index">
                    <td class="spec-name">{{ spec.name }}</td>
                    <td class="spec-value">{{ spec.value }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="商品评价" name="reviews">
            <div class="review-summary" v-if="reviews.length > 0">
              <div class="review-stats">
                <div class="review-average-score">
                  {{ reviewStats.averageRating || 0 }}
                  <span>综合评分</span>
                </div>
                <div class="review-score-distribution">
                  <div class="score-item">
                    <span class="score-label">5星</span>
                    <el-progress :percentage="getScorePercentage(5)" :show-text="false"></el-progress>
                    <span class="score-percent">{{ getScorePercentage(5) }}%</span>
                  </div>
                  <div class="score-item">
                    <span class="score-label">4星</span>
                    <el-progress :percentage="getScorePercentage(4)" :show-text="false"></el-progress>
                    <span class="score-percent">{{ getScorePercentage(4) }}%</span>
                  </div>
                  <div class="score-item">
                    <span class="score-label">3星</span>
                    <el-progress :percentage="getScorePercentage(3)" :show-text="false"></el-progress>
                    <span class="score-percent">{{ getScorePercentage(3) }}%</span>
                  </div>
                  <div class="score-item">
                    <span class="score-label">2星</span>
                    <el-progress :percentage="getScorePercentage(2)" :show-text="false"></el-progress>
                    <span class="score-percent">{{ getScorePercentage(2) }}%</span>
                  </div>
                  <div class="score-item">
                    <span class="score-label">1星</span>
                    <el-progress :percentage="getScorePercentage(1)" :show-text="false"></el-progress>
                    <span class="score-percent">{{ getScorePercentage(1) }}%</span>
                  </div>
                </div>
              </div>
              <div class="review-tags">
                <el-tag 
                  v-for="tag in reviewTags" 
                  :key="tag.name"
                  :type="activeReviewTag === tag.name ? 'primary' : 'info'"
                  effect="plain"
                  @click="filterReviewsByTag(tag.name)"
                >
                  {{ tag.name }} ({{ tag.count }})
                </el-tag>
              </div>
            </div>
            
            <div class="review-list">
              <div class="empty-review" v-if="reviews.length === 0">
                <i class="el-icon-chat-dot-round"></i>
                <p>暂无评价</p>
              </div>
              
              <div v-else v-for="review in filteredReviews" :key="review.id" class="review-item">
                <div class="review-user">
                  <div class="user-avatar">
                    <img :src="review.userAvatar || defaultAvatar" :alt="review.userName" />
                  </div>
                  <div class="user-info">
                    <div class="user-name">{{ review.userName }}</div>
                    <div class="review-time">{{ formatDate(review.createTime) }}</div>
                  </div>
                </div>
                <div class="review-content">
                  <div class="review-rating">
                    <el-rate :value="review.rating" disabled></el-rate>
                    <div class="review-tags" v-if="review.tags && review.tags.length">
                      <el-tag v-for="tag in review.tags" :key="tag" size="mini" effect="plain">{{ tag }}</el-tag>
                    </div>
                  </div>
                  <div class="review-text">{{ review.content }}</div>
                  <div class="review-images" v-if="review.images && review.images.length">
                    <div class="review-image-item" v-for="(image, index) in review.images" :key="'review-img-' + index" @click="previewImage(image)">
                      <img :src="image" :alt="'评价图片' + (index + 1)" />
                    </div>
                  </div>
                  <div class="review-actions">
                    <span class="review-action" @click="likeReview(review)">
                      <i :class="['el-icon-thumb', {'active': review.isLiked}]"></i>
                      {{ review.likes || 0 }}
                    </span>
                    <span class="review-action" @click="replyReview(review)">
                      <i class="el-icon-chat-dot-square"></i> 回复
                    </span>
                  </div>
                  
                  <div class="review-replies" v-if="review.replies && review.replies.length">
                    <div v-for="reply in review.replies" :key="reply.id" class="reply-item">
                      <span class="reply-user">{{ reply.userName }}</span>
                      <span class="reply-content">{{ reply.content }}</span>
                      <span class="reply-time">{{ formatDate(reply.createTime) }}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="pagination-container" v-if="reviews.length > pageSize">
                <el-pagination
                  @current-change="handleReviewPageChange"
                  :current-page.sync="reviewPage"
                  :page-size="pageSize"
                  layout="prev, pager, next, jumper"
                  :total="reviews.length"
                  background
                ></el-pagination>
              </div>
            </div>
            
            <div class="write-review" v-if="isLoggedIn">
              <h3>发表评价</h3>
              <div class="review-form">
                <div class="form-item">
                  <span class="form-label">评分</span>
                  <el-rate v-model="newReview.rating" show-text></el-rate>
                </div>
                <div class="form-item">
                <el-input
                  type="textarea"
                    v-model="newReview.content" 
                  :rows="3"
                    placeholder="请分享您对这个商品的使用感受，这对其他买家有很大帮助"
                  ></el-input>
                </div>
                <div class="form-item">
                  <el-upload
                    action="/api/upload/image"
                    list-type="picture-card"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemove"
                    :before-upload="beforeUpload"
                    :on-success="handleUploadSuccess"
                  >
                    <i class="el-icon-plus"></i>
                  </el-upload>
              </div>
                <div class="form-actions">
                  <el-button type="primary" class="ag-button ag-button--primary" @click="submitReview">提交评价</el-button>
                    </div>
                  </div>
                  </div>
            
            <div class="login-to-review" v-else>
              <i class="el-icon-warning-outline"></i>
              <p>请<router-link to="/login">登录</router-link>后发表评价</p>
                </div>
          </el-tab-pane>
          
          <el-tab-pane label="购买记录" name="purchases">
            <div class="purchase-list">
              <div class="empty-purchases" v-if="purchases.length === 0">
                <i class="el-icon-shopping-bag-1"></i>
                <p>暂无购买记录</p>
              </div>
              
              <el-table v-else :data="paginatedPurchases" style="width: 100%">
                <el-table-column prop="userName" label="用户" width="180">
                  <template slot-scope="scope">
                    <div class="user-brief">
                      <img :src="scope.row.userAvatar || defaultAvatar" class="user-avatar" />
                      <span>{{ scope.row.userName }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" label="数量" width="80"></el-table-column>
                <el-table-column prop="price" label="价格" width="100">
                  <template slot-scope="scope">
                    ¥{{ scope.row.price }}
                  </template>
                </el-table-column>
                <el-table-column prop="createTime" label="购买时间" width="180">
                  <template slot-scope="scope">
                    {{ formatDate(scope.row.createTime) }}
                  </template>
                </el-table-column>
                <el-table-column prop="address" label="地区">
                  <template slot-scope="scope">
                    {{ scope.row.address || '未知地区' }}
                  </template>
                </el-table-column>
              </el-table>
              
              <div class="pagination-container" v-if="purchases.length > pageSize">
                <el-pagination
                  @current-change="handlePurchasePageChange"
                  :current-page.sync="purchasePage"
                  :page-size="pageSize"
                  layout="prev, pager, next, jumper"
                  :total="purchases.length"
                  background
                ></el-pagination>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="溯源信息" name="traceability">
            <div class="traceability-container">
              <!-- 溯源信息加载状态 -->  
              <div v-loading="traceabilityLoading" class="traceability-content">
                <!-- 溯源码展示 -->
                <div class="trace-code-section" v-if="hasTraceabilityData && traceabilityData.traceCode">
                  <div class="trace-code-card">
                    <div class="trace-code-info">
                      <h3><i class="el-icon-postcard"></i> 产品溯源码</h3>
                      <div class="trace-code-display">
                        <span class="trace-code">{{ traceabilityData.traceCode }}</span>
                        <el-button size="mini" type="primary" @click="copyTraceCode">复制</el-button>
                      </div>
                      <p class="trace-code-desc">扫描二维码或输入溯源码查看完整溯源信息</p>
                    </div>
                    <div class="trace-qr-code">
                      <div v-if="traceabilityData.qrcodeUrl" class="qr-code-image">
                        <img :src="traceabilityData.qrcodeUrl" :alt="'溯源码 ' + traceabilityData.traceCode" />
                      </div>
                      <div v-else class="qr-code-placeholder">
                        <i class="el-icon-picture-outline"></i>
                        <p>二维码加载中...</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 溯源步骤时间线 -->
                <div class="trace-steps-section" v-if="traceabilityData.steps && traceabilityData.steps.length > 0">
                  <h3><i class="el-icon-time"></i> 溯源轨迹</h3>
                  <el-timeline class="trace-timeline">
                    <el-timeline-item 
                      v-for="(step, index) in traceabilityData.steps" 
                      :key="index"
                      :timestamp="step.date"
                      :type="step.type || 'primary'"
                      :icon="step.icon"
                      placement="top"
                    >
                      <el-card class="trace-step-card">
                        <div class="step-header">
                          <h4>{{ step.title }}</h4>
                          <el-tag :type="step.type" size="mini">{{ step.status || '已完成' }}</el-tag>
                        </div>
                        <p class="step-description">{{ step.description }}</p>
                        <div class="step-images" v-if="step.images && step.images.length > 0">
                          <div 
                            v-for="(image, imgIndex) in step.images" 
                            :key="imgIndex"
                            class="step-image"
                            @click="previewTraceImage(image)"
                          >
                            <img :src="processImageUrl(image)" :alt="step.title + '图片'" />
                          </div>
                        </div>
                      </el-card>
                    </el-timeline-item>
                  </el-timeline>
                </div>
                
                <!-- 质检报告 -->
                <div class="quality-reports-section" v-if="traceabilityData.qualityReports && traceabilityData.qualityReports.length > 0">
                  <h3><i class="el-icon-document-checked"></i> 质检报告</h3>
                  <el-table :data="traceabilityData.qualityReports" style="width: 100%" stripe>
                    <el-table-column prop="item" label="检测项目" width="150"></el-table-column>
                    <el-table-column prop="standard" label="标准值" width="120"></el-table-column>
                    <el-table-column prop="result" label="检测结果" width="120"></el-table-column>
                    <el-table-column prop="status" label="检测状态" width="100">
                      <template slot-scope="scope">
                        <el-tag :type="scope.row.status === '合格' ? 'success' : 'danger'" size="mini">
                          {{ scope.row.status }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="date" label="检测日期"></el-table-column>
                  </el-table>
                </div>
                
                <!-- 认证信息 -->
                <div class="certifications-section" v-if="traceabilityData.certificationReports && traceabilityData.certificationReports.length > 0">
                  <h3><i class="el-icon-medal"></i> 认证信息</h3>
                  <el-row :gutter="20">
                    <el-col 
                      :xs="24" 
                      :sm="12" 
                      :md="8" 
                      v-for="(cert, index) in traceabilityData.certificationReports" 
                      :key="index"
                    >
                      <el-card class="certification-card">
                        <div class="cert-header">
                          <i class="el-icon-medal cert-icon"></i>
                          <h4>{{ cert.name }}</h4>
                        </div>
                        <div class="cert-info">
                          <p><strong>认证机构：</strong>{{ cert.issuer }}</p>
                          <p><strong>认证日期：</strong>{{ cert.date }}</p>
                          <p><strong>有效期至：</strong>{{ cert.validUntil }}</p>
                        </div>
                        <div class="cert-actions">
                          <el-button size="mini" type="primary" @click="viewCertificate(cert)">查看证书</el-button>
                        </div>
                      </el-card>
                    </el-col>
                  </el-row>
                </div>
                
                <!-- 溯源统计 -->
                <div class="trace-stats-section" v-if="traceabilityData.stats">
                  <h3><i class="el-icon-data-analysis"></i> 质量分析</h3>
                  <el-row :gutter="20">
                    <el-col :xs="24" :md="12">
                      <el-card class="stats-card">
                        <h4>质量雷达图</h4>
                        <div class="chart-container" id="qualityRadarChart" style="height: 300px;"></div>
                      </el-card>
                    </el-col>
                    <el-col :xs="24" :md="12">
                      <el-card class="stats-card">
                        <h4>溯源查询统计</h4>
                        <div class="chart-container" id="traceQueryChart" style="height: 300px;"></div>
                      </el-card>
                    </el-col>
                  </el-row>
                </div>
                
                <!-- 溯源操作 -->
                <div class="trace-actions-section" v-if="hasTraceabilityData">
                  <h3><i class="el-icon-share"></i> 溯源操作</h3>
                  <div class="trace-actions">
                    <el-button type="primary" icon="el-icon-share" @click="shareTraceability">分享溯源信息</el-button>
                    <el-button type="success" icon="el-icon-download" @click="downloadTraceReport">下载溯源报告</el-button>
                    <el-button type="info" icon="el-icon-view" @click="viewFullTrace">查看完整溯源</el-button>
                  </div>
                </div>
                
                <!-- 无溯源信息提示 -->
                <div class="no-trace-info" v-if="!hasTraceabilityData">
                  <i class="el-icon-warning-outline"></i>
                  <h3>暂无溯源信息</h3>
                  <p>该产品暂未录入溯源信息，如有疑问请联系商家</p>
                  <el-button type="primary" @click="contactSeller">联系商家</el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <!-- 相关推荐区域 -->
      <div class="related-products">
        <h2 class="section-title">相关推荐</h2>
        <ProductRecommendation 
          :product-id="product.id"
          recommendation-type="similar"
          :max-count="8"
          @product-click="handleRecommendationClick"
          @add-to-cart="handleRecommendationAddToCart"
        />
      </div>
    </div>
    
    <!-- 图片预览 -->
    <el-dialog :visible.sync="imagePreviewVisible" append-to-body>
      <img width="100%" :src="previewImageUrl" alt="预览图片" />
    </el-dialog>
  </div>
</template>

<script>
import { getProductDetail, getProductPriceHistory } from '@/api/products'
import { addToCart } from '@/api/cart'
import { formatDate } from '@/utils/dateFormatter'
import { isLoggedIn } from '@/utils/auth'
import { 
  generateShareLink, 
  getTraceabilityReport, 
  getTraceabilitySteps, 
  getQualityReports, 
  getCertifications, 
  getTraceabilityStats 
} from '@/api/traceability'
import { generateTraceabilityQRCode } from '@/services/traceabilityService'
import { getCategories } from '@/services/productService'
import { getSimilarProductRecommendations } from '@/api/products'
import request from '@/utils/request'
import ProductRecommendation from '@/components/shop/ProductRecommendation.vue'
import ProductInteractionBar from '@/components/ProductDetail/ProductInteractionBar.vue'
import { addFavorite, removeFavorite, checkFavoriteStatus } from '@/api/favorite'
import { isLiked as checkLikeStatus } from '@/api/productLikes'
import { getProductReviews, getReviewStats } from '@/api/reviews'

// 自定义函数，提交产品评论
function submitProductReview(productId, data) {
  return request({
    url: `/api/mall/products/${productId}/reviews`,
    method: 'post',
    data
  })
}

export default {
  name: 'ProductDetail',
  components: {
    ProductRecommendation,
    ProductInteractionBar
  },
  data() {
    return {
      // 产品数据
      product: {},
      productRating: 0,
      categoryName: '',
      loading: true,
      
      // 购买相关
      purchaseQuantity: 1,
      
      // 图片相关
      productImages: [],
      currentImageIndex: 0,
      
      // 收藏状态
      isFavorited: false,

      // 点赞状态
      isLiked: false,
      
      // 选项卡
      activeTab: 'detail',
      
      // 评价相关
      reviews: [],
      reviewPage: 1,
      pageSize: 10,
      activeReviewTag: '',
      reviewTags: [
        { name: '全部', count: 0 },
        { name: '好评', count: 0 },
        { name: '中评', count: 0 },
        { name: '差评', count: 0 },
        { name: '有图', count: 0 }
      ],
      reviewStats: {
        totalReviews: 0,
        averageRating: 0,
        ratingDistribution: {
          5: 0, 4: 0, 3: 0, 2: 0, 1: 0
        },
        withImages: 0
      },
      newReview: {
        rating: 5,
        content: '',
        images: []
      },
      
      // 图片预览
      imagePreviewVisible: false,
      previewImageUrl: '',
      
      // 购买记录
      purchases: [],
      purchasePage: 1,
      
      // 相关推荐
      relatedProducts: [],
      
      // 溯源相关数据
      traceabilityLoading: false,
      traceabilityData: {
        traceCode: '',
        steps: [],
        qualityReports: [],
        certificationReports: [],
        stats: null
      },
      
      // 价格历史
      priceHistoryLoading: false,
      priceHistory: [],
      
      // 相似产品
      similarProductsLoading: false,
      similarProducts: []
    }
  },
  
  computed: {
    productId() {
      return this.$route.params.id
    },

    isLoggedIn() {
      return isLoggedIn()
    },
    
    filteredReviews() {
      if (!this.activeReviewTag || this.activeReviewTag === '全部') {
        return this.getPaginatedArray(this.reviews, this.reviewPage, this.pageSize)
      } else if (this.activeReviewTag === '有图') {
        return this.getPaginatedArray(
          this.reviews.filter(review => review.images && review.images.length > 0),
          this.reviewPage, 
          this.pageSize
        )
      } else if (this.activeReviewTag === '好评') {
        return this.getPaginatedArray(
          this.reviews.filter(review => review.rating >= 4),
          this.reviewPage, 
          this.pageSize
        )
      } else if (this.activeReviewTag === '中评') {
        return this.getPaginatedArray(
          this.reviews.filter(review => review.rating >= 2 && review.rating < 4),
          this.reviewPage, 
          this.pageSize
        )
      } else if (this.activeReviewTag === '差评') {
        return this.getPaginatedArray(
          this.reviews.filter(review => review.rating < 2),
          this.reviewPage, 
          this.pageSize
        )
      }
      return this.getPaginatedArray(this.reviews, this.reviewPage, this.pageSize)
    },
    
    paginatedPurchases() {
      return this.getPaginatedArray(this.purchases, this.purchasePage, this.pageSize)
    },
    
    // 判断是否有溯源数据
    hasTraceabilityData() {
      return this.traceabilityData.traceCode || 
             (this.traceabilityData.steps && this.traceabilityData.steps.length > 0) ||
             (this.traceabilityData.qualityReports && this.traceabilityData.qualityReports.length > 0) ||
             (this.traceabilityData.certificationReports && this.traceabilityData.certificationReports.length > 0)
    }
  },
  
  watch: {
    // 监听product变化，更新rating
    product: {
      handler(newProduct) {
        this.productRating = newProduct.rating || 0;
      },
      deep: true,
      immediate: true
    },

    // 监听路由参数变化，重新加载商品详情
    '$route.params.id': {
      handler(newId, oldId) {
        console.log('商品ID变化:', oldId, '->', newId);
        if (newId && newId !== oldId) {
          this.loadProductDetail();
        }
      },
      immediate: false
    }
  },
  
  created() {
    this.loadProductDetail()
    
    // 如果URL中包含溯源码参数，自动切换到溯源信息标签页
    if (this.$route.query.traceCode) {
      this.activeTab = 'traceability'
    }
  },
  
  mounted() {
    console.log('ProductDetail mounted, productId:', this.productId);

    // 确保DOM挂载后再初始化图表
    this.$nextTick(() => {
      if (this.hasTraceabilityData && this.traceabilityData.stats) {
        this.initTraceCharts()
      }
    })
  },
  
  methods: {
    // 加载产品详情
    async loadProductDetail() {
      console.log('🔍 开始加载商品详情, productId:', this.productId);

      if (!this.productId) {
        console.error('❌ 商品ID为空，无法加载商品详情');
        this.$message.error('商品ID不存在');
        this.$router.push('/shop');
        return;
      }

      this.loading = true;

      try {
        console.log('📡 调用商品详情API...');
        const response = await getProductDetail(this.productId);

        console.log('📊 产品详情API响应:', response);
        console.log('📊 响应状态码:', response?.code);
        console.log('📊 响应数据:', response?.data);
        
        if (response && (response.code === 200 || response.code === 0) && response.data) {
          console.log('✅ 商品详情加载成功');
          this.product = response.data;
          console.log('📦 商品数据:', this.product);

          // 处理产品图片
          this.processProductImages();
          
          // 加载分类名称
          this.loadCategoryName();
          
          // 检查收藏状态
          this.checkFavoriteStatus();

          // 检查点赞状态
          this.checkLikeStatus();

          // 异步加载其他数据（不阻塞页面显示）
          this.loadAdditionalData();
            
          // 设置页面标题
          document.title = `${this.product.name} - 农品汇`;
        } else {
          console.error('❌ 获取产品详情失败 - 响应格式错误');
          console.error('❌ 响应详情:', response);
          this.$message.error('获取产品详情失败：' + (response?.message || '数据格式错误'));
          // 设置默认产品数据以防止页面崩溃
          this.setDefaultProductData();
        }
      } catch (error) {
        console.error('❌ 加载产品详情出错:', error);
        console.error('❌ 错误详情:', error.message);
        console.error('❌ 错误堆栈:', error.stack);

        if (error.response) {
          console.error('❌ HTTP响应错误:', error.response.status, error.response.data);
          this.$message.error(`网络错误：${error.response.status} - ${error.response.statusText}`);
        } else if (error.request) {
          console.error('❌ 网络请求失败:', error.request);
          this.$message.error('网络连接失败，请检查网络连接');
        } else {
          this.$message.error('加载产品详情时发生错误：' + error.message);
        }

        // 设置默认产品数据以防止页面崩溃
        this.setDefaultProductData();
      } finally {
        this.loading = false;
      }
    },
    
    // 加载价格历史
    async loadPriceHistory() {
      this.priceHistoryLoading = true;

      try {
        const response = await getProductPriceHistory(this.productId);

        console.log('价格历史API响应:', response);

        if (response && (response.code === 200 || response.code === 0) && response.data) {
          this.priceHistory = response.data;
          this.renderPriceChart();
        } else {
          console.warn('获取价格历史失败或无数据');
          this.priceHistory = [];
        }
      } catch (error) {
        console.warn('价格历史数据暂不可用:', error);
        this.priceHistory = [];
        // 不显示错误消息，因为这是可选功能
      } finally {
        this.priceHistoryLoading = false;
      }
    },
    
    // 加载相似产品
    async loadSimilarProducts() {
      this.similarProductsLoading = true;

      try {
        const response = await getSimilarProductRecommendations(this.productId, 6);
        
        console.log('相似产品API响应:', response);
        
        if (response && (response.code === 200 || response.code === 0) && response.data) {
          this.similarProducts = response.data;
          } else {
          console.warn('获取相似产品失败或无数据');
          this.similarProducts = [];
          }
      } catch (error) {
        console.error('加载相似产品出错:', error);
        this.similarProducts = [];
      } finally {
        this.similarProductsLoading = false;
      }
    },
    

    
    // 提交评价
    submitReview() {
      if (!this.newReview.content) {
        this.$message.warning('请填写评价内容');
        return;
      }
      
      this.submittingReview = true;
      
      submitProductReview(this.productId, this.newReview)
        .then(response => {
          if (response.code === 200) {
            this.$message.success('评价提交成功');
            this.newReview = {
              rating: 5,
              content: '',
              images: []
            };
            this.loadReviews();
          } else {
            this.$message.error(response.message || '评价提交失败');
          }
        })
        .catch(error => {
          console.error('评价提交失败:', error);
          this.$message.error('评价提交失败');
        })
        .finally(() => {
          this.submittingReview = false;
        });
    },
    
    // 设置默认产品数据以防止页面崩溃
    setDefaultProductData() {
      this.product = {
        id: this.productId,
        name: '商品信息加载失败',
        price: 0,
        originalPrice: 0,
        stock: 0,
        unit: '件',
        description: '暂无描述',
        rating: 0,
        reviewCount: 0,
        salesCount: 0,
        favorites: 0,
        images: [],
        image: '',
        categoryId: null,
        origin: '',
        specification: '',
        seller: {
          name: '未知卖家',
          avatar: '',
          rating: 0
        }
      };
      this.processProductImages();
      this.categoryName = '未分类';
    },
    
    // 获取卖家评分
    getSellerRating(seller) {
      return seller && seller.rating ? seller.rating : 0;
    },
    
    // 处理产品图片
    processProductImages() {
      try {
        // 如果有多张图片
        if (this.product.images && Array.isArray(this.product.images) && this.product.images.length > 0) {
          this.productImages = this.product.images.map(img => this.processImageUrl(img)).filter(url => url);
        } 
        // 如果只有一张图片
        else if (this.product.image) {
          const processedUrl = this.processImageUrl(this.product.image);
          this.productImages = processedUrl ? [processedUrl] : [this.defaultProductImage];
        } 
        // 没有图片，使用默认图片
        else {
          this.productImages = [this.defaultProductImage];
        }
        
        // 确保至少有一张图片
        if (this.productImages.length === 0) {
          this.productImages = [this.defaultProductImage];
        }
      } catch (error) {
        console.error('处理产品图片失败:', error);
        this.productImages = [this.defaultProductImage];
      }
    },
    
    // 处理图片URL
    processImageUrl(url) {
      if (!url || typeof url !== 'string') {
        return require('@/assets/images/products/default.jpg');
      }

      // 如果已经是完整的URL
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }

      // 如果是相对路径，添加基础URL
      if (url.startsWith('/')) {
        const baseUrl = process.env.VUE_APP_BASE_API || process.env.VUE_APP_API_URL || 'http://localhost:8081';
        return baseUrl + url;
      }

      // 如果是相对路径但不以/开头
      if (!url.startsWith('http')) {
        const baseUrl = process.env.VUE_APP_BASE_API || process.env.VUE_APP_API_URL || 'http://localhost:8081';
        return baseUrl + '/' + url;
      }

      // 验证URL格式
      try {
        new URL(url);
        return url;
      } catch (e) {
        console.warn('无效的图片URL:', url);
        return require('@/assets/images/products/default.jpg');
      }
    },
    
    // 处理图片加载错误
    handleImageError(e) {
      console.warn('图片加载失败:', e.target.src);
      e.target.src = this.defaultProductImage;
    },
    
    // 预览图片
    previewImage(imageUrl) {
      this.previewImageUrl = imageUrl;
      this.imagePreviewVisible = true;
    },
    
    // 预览溯源图片
    previewTraceImage(imageUrl) {
      this.previewImage(this.processImageUrl(imageUrl));
    },
    
    // 加载分类名称
    async loadCategoryName() {
      if (!this.product.categoryId) {
        this.categoryName = '未分类'
        return
      }
      
      try {
        const response = await getCategories()
        if (response && response.code === 200) {
          const category = response.data.find(cat => cat.id === this.product.categoryId)
          this.categoryName = category ? category.name : '未分类'
        } else {
          this.categoryName = '未分类'
        }
      } catch (error) {
        console.error('获取分类信息失败:', error)
        this.categoryName = '未分类'
      }
    },
    
    // 检查收藏状态
    async checkFavoriteStatus() {
      try {
        const userInfo = JSON.parse(localStorage.getItem('sfap_user') || '{}')
        if (!userInfo.id) {
          this.isFavorited = false
          return
        }

        const response = await checkFavoriteStatus(this.product.id)
        if (response.success) {
          this.isFavorited = response.data.favorited || false
        }
      } catch (error) {
        console.error('检查收藏状态失败:', error)
        this.isFavorited = false
      }
    },

    // 检查点赞状态
    async checkLikeStatus() {
      try {
        const userInfo = JSON.parse(localStorage.getItem('sfap_user') || '{}')
        if (!userInfo.id) {
          this.isLiked = false
          return
        }

        const response = await checkLikeStatus(this.product.id)
        if (response.success) {
          this.isLiked = response.data || false
        }
      } catch (error) {
        console.error('检查点赞状态失败:', error)
        this.isLiked = false
      }
    },

    // 添加/取消收藏
    async toggleFavorite() {
      const userInfo = JSON.parse(localStorage.getItem('sfap_user') || '{}')
      if (!userInfo.id) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }

      try {
        if (this.isFavorited) {
          // 取消收藏
          const response = await removeFavorite(userInfo.id, this.product.id)
          if (response.success) {
            this.isFavorited = false
            this.$message.success('已取消收藏')
          } else {
            throw new Error(response.message || '取消收藏失败')
          }
        } else {
          // 添加收藏
          const response = await addFavorite(userInfo.id, this.product.id, '默认收藏夹')
          if (response.success) {
            this.isFavorited = true
            this.$message.success('收藏成功')
          } else {
            throw new Error(response.message || '收藏失败')
          }
        }
      } catch (error) {
        console.error('收藏操作失败:', error)
        this.$message.error('操作失败：' + error.message)
      }
    },
    
    // 添加到购物车
    async addToCart() {
      if (!isLoggedIn()) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }
      
      if (this.purchaseQuantity <= 0) {
        this.$message.warning('请选择购买数量')
        return
      }
      
      try {
        const response = await addToCart({
          productId: this.product.id,
          quantity: this.purchaseQuantity,
          price: this.product.price
        })
        
        if (response && response.code === 200) {
          this.$notify({
            title: '成功',
            message: '商品已添加到购物车',
            type: 'success'
          })
          // 触发购物车更新事件
          this.$root.$emit('cart-updated')
        } else {
          this.$message.error('添加到购物车失败')
        }
      } catch (error) {
        console.error('添加到购物车失败:', error)
        this.$message.error('添加到购物车失败，请稍后重试')
      }
    },
    
    // 立即购买
    buyNow() {
      if (!isLoggedIn()) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }
      
      // 添加到购物车后立即跳转到结算页
      this.addToCart().then(() => {
        this.$router.push('/cart')
      })
    },

    // 异步加载附加数据
    async loadAdditionalData() {
      console.log('开始加载附加数据...')

      // 并行加载所有附加数据，但不让任何一个失败影响其他数据的加载
      const loadTasks = [
        this.loadReviews().catch(error => console.warn('评价数据加载失败:', error)),
        this.loadPurchases().catch(error => console.warn('购买记录加载失败:', error)),
        this.loadRelatedProducts().catch(error => console.warn('相关推荐加载失败:', error)),
        this.loadTraceabilityData().catch(error => console.warn('溯源数据加载失败:', error)),
        this.loadPriceHistory().catch(error => console.warn('价格历史加载失败:', error)),
        this.loadSimilarProducts().catch(error => console.warn('相似产品加载失败:', error))
      ]

      // 等待所有任务完成（无论成功还是失败）
      await Promise.allSettled(loadTasks)

      console.log('附加数据加载完成')
    },

    // 加载评价数据
    async loadReviews() {
      try {
        // 同时加载评价列表和统计数据
        const [reviewsResponse, statsResponse] = await Promise.all([
          getProductReviews(this.productId, {
            page: 1,
            size: 10
          }),
          getReviewStats(this.productId)
        ])

        // 处理评价列表数据
        if (reviewsResponse && reviewsResponse.code === 200 && reviewsResponse.data) {
          const reviews = reviewsResponse.data.records || reviewsResponse.data || []

          // 根据评价数计算标签计数
          this.reviewTags[0].count = reviews.length
          this.reviewTags[1].count = reviews.filter(r => r.rating >= 4).length
          this.reviewTags[2].count = reviews.filter(r => r.rating >= 2 && r.rating < 4).length
          this.reviewTags[3].count = reviews.filter(r => r.rating < 2).length
          this.reviewTags[4].count = reviews.filter(r => r.images && r.images.length > 0).length

          this.reviews = reviews
          console.log('评价数据加载成功:', reviews.length, '条评价')
        } else {
          console.warn('获取评价数据失败:', reviewsResponse)
          this.reviews = []
        }

        // 处理统计数据
        if (statsResponse && statsResponse.code === 200 && statsResponse.data) {
          this.updateReviewStats(statsResponse.data)
          console.log('评价统计数据加载成功:', statsResponse.data)
        } else {
          console.warn('获取评价统计失败:', statsResponse)
        }
      } catch (error) {
        console.error('加载评价失败:', error)
        this.reviews = []
      }
    },
    
    // 加载购买记录
    loadPurchases() {
      try {
        // 模拟获取购买记录
        // 在实际应用中，应该调用API获取购买记录
        this.purchases = []
      } catch (error) {
        console.error('加载购买记录失败:', error)
      }
    },
    
    // 加载相关推荐
    loadRelatedProducts() {
      try {
        // 模拟获取相关推荐
        // 在实际应用中，应该调用API获取相关推荐
        this.relatedProducts = []
      } catch (error) {
        console.error('加载相关推荐失败:', error)
      }
    },

    // 更新评价统计数据
    updateReviewStats(statsData) {
      this.reviewStats = {
        totalReviews: statsData.total_count || 0,
        averageRating: parseFloat((statsData.avg_rating || 0).toFixed(1)),
        ratingDistribution: {
          5: statsData.five_star || 0,
          4: statsData.four_star || 0,
          3: statsData.three_star || 0,
          2: statsData.two_star || 0,
          1: statsData.one_star || 0
        },
        withImages: statsData.with_images || 0
      }
    },

    // 获取评分百分比
    getScorePercentage(score) {
      if (this.reviewStats.totalReviews === 0) return 0

      const count = this.reviewStats.ratingDistribution[score] || 0
      return Math.round((count / this.reviewStats.totalReviews) * 100)
    },
    
    // 按标签筛选评价
    filterReviewsByTag(tag) {
      this.activeReviewTag = tag
      this.reviewPage = 1
    },
    
    // 处理评价分页
    handleReviewPageChange(page) {
      this.reviewPage = page
    },
    
    // 处理购买记录分页
    handlePurchasePageChange(page) {
      this.purchasePage = page
    },
    
    // 加载溯源数据
    async loadTraceabilityData() {
      if (!this.product.id) return
      
      this.traceabilityLoading = true
      try {
        // 生成溯源码（基于产品ID）
        const traceCode = `TR${this.product.id.toString().padStart(8, '0')}`
        
        // 初始化基础溯源数据
        this.traceabilityData = {
          traceCode: traceCode,
          steps: [],
          qualityReports: [],
          certificationReports: [],
          stats: null,
          qrcodeUrl: await this.generateQRCode(traceCode)
        }

        // 尝试获取溯源步骤数据（这个API通常存在）
        try {
          const stepsRes = await getTraceabilitySteps(this.product.id)
          if (stepsRes && stepsRes.data) {
            this.traceabilityData.steps = stepsRes.data
            console.log('溯源步骤数据加载成功')
          }
        } catch (error) {
          console.warn('溯源步骤数据加载失败:', error)
        }

        // 尝试获取质检报告（可选数据，失败不影响页面显示）
        try {
          const qualityRes = await getQualityReports(this.product.id)
          if (qualityRes && qualityRes.data) {
            this.traceabilityData.qualityReports = qualityRes.data
            console.log('质检报告数据加载成功')
          }
        } catch (error) {
          console.warn('质检报告数据暂不可用:', error)
          // 不显示错误消息，因为这是可选功能
        }

        // 尝试获取认证信息（可选数据，失败不影响页面显示）
        try {
          const certsRes = await getCertifications(this.product.id)
          if (certsRes && certsRes.data) {
            this.traceabilityData.certificationReports = certsRes.data
            console.log('认证信息数据加载成功')
          }
        } catch (error) {
          console.warn('认证信息数据暂不可用:', error)
          // 不显示错误消息，因为这是可选功能
        }

        // 尝试获取统计数据（可选数据，失败不影响页面显示）
        try {
          const statsRes = await getTraceabilityStats(this.product.id)
          if (statsRes && statsRes.data) {
            this.traceabilityData.stats = statsRes.data
            console.log('溯源统计数据加载成功')
          }
        } catch (error) {
          console.warn('溯源统计数据暂不可用:', error)
          // 不显示错误消息，因为这是可选功能
        }

        console.log('溯源数据加载完成:', this.traceabilityData)

        // 如果有溯源数据，初始化图表
        if (this.hasTraceabilityData && this.traceabilityData.stats) {
          this.$nextTick(() => {
            this.initTraceCharts()
          })
        }
      } catch (error) {
        console.error('加载溯源数据失败:', error)
        // 即使加载失败，也显示基本的溯源码
        this.traceabilityData = {
          traceCode: `TR${this.product.id.toString().padStart(8, '0')}`,
          qrcodeUrl: await this.generateQRCode(`TR${this.product.id.toString().padStart(8, '0')}`)
        }
      } finally {
        this.traceabilityLoading = false
      }
    },
    
    // 渲染价格图表
    renderPriceChart() {
      if (!this.priceHistory || this.priceHistory.length === 0) {
        return;
      }
      
      // 使用动态导入ECharts
      import('echarts').then(echarts => {
        const chartDom = document.getElementById('priceChart');
        if (!chartDom) return;
        
        const myChart = echarts.init(chartDom);
        const option = {
          title: {
            text: '价格走势',
            left: 'center'
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: this.priceHistory.map(item => item.date)
          },
          yAxis: {
            type: 'value',
            name: '价格(元)'
          },
          series: [{
            data: this.priceHistory.map(item => item.price),
            type: 'line',
            smooth: true,
            itemStyle: {
              color: '#409eff'
            }
          }]
        };
        
        myChart.setOption(option);
      }).catch(error => {
        console.error('加载ECharts失败:', error);
      });
    },
    
    // 处理推荐产品点击
    handleRecommendationClick(product) {
      this.$router.push(`/shop/product/${product.id}`);
    },
    
    // 处理推荐产品加入购物车
    handleRecommendationAddToCart(_product) {
      this.addToCart();
    },
    

    



    
    // 查看证书
    viewCertificate(_cert) {
      this.$message.info('证书查看功能开发中...');
    },
    
    // 生成溯源二维码
    async generateQRCode(traceCode) {
      try {
        // 使用服务生成二维码
        const qrcodeUrl = await generateTraceabilityQRCode(traceCode)
        return qrcodeUrl
      } catch (error) {
        console.error('生成二维码失败:', error)
        // 使用在线二维码生成服务作为备选
        return `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(window.location.origin + '/traceability?code=' + traceCode)}`
      }
    },
    
    // 初始化溯源图表
    initTraceCharts() {
      if (!this.traceabilityData.stats) return
      
      // 使用动态导入ECharts以减少打包体积
      import('echarts').then(echarts => {
        // 质量雷达图
        const radarChart = echarts.init(document.getElementById('qualityRadarChart'))
        const radarOption = {
          title: {
            text: '产品质量对比',
            left: 'center',
            textStyle: { fontSize: 14 }
          },
          tooltip: {},
          legend: {
            data: ['本产品', '行业平均'],
            bottom: 0
          },
          radar: {
            indicator: this.traceabilityData.stats.qualityRadar.indicators
          },
          series: [{
            name: '质量对比',
            type: 'radar',
            data: this.traceabilityData.stats.qualityRadar.data
          }]
        }
        radarChart.setOption(radarOption)
        
        // 溯源查询统计图
        const queryChart = echarts.init(document.getElementById('traceQueryChart'))
        const queryOption = {
          title: {
            text: '溯源查询趋势',
            left: 'center',
            textStyle: { fontSize: 14 }
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: this.traceabilityData.stats.traceQueryStats.xAxis
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            name: '查询次数',
            data: this.traceabilityData.stats.traceQueryStats.data,
            type: 'line',
            smooth: true,
            areaStyle: {}
          }]
        }
        queryChart.setOption(queryOption)
        
        // 响应式处理
        window.addEventListener('resize', () => {
          radarChart.resize()
          queryChart.resize()
        })
      }).catch(error => {
        console.error('加载ECharts失败:', error)
      })
    },
    
    // 复制溯源码
    copyTraceCode() {
      if (!this.traceabilityData.traceCode) {
        this.$message.warning('暂无溯源码')
        return
      }
      
      // 使用Clipboard API或fallback方法
      if (navigator.clipboard) {
        navigator.clipboard.writeText(this.traceabilityData.traceCode).then(() => {
          this.$message.success('溯源码已复制到剪贴板')
        }).catch(() => {
          this.fallbackCopyTextToClipboard(this.traceabilityData.traceCode)
        })
      } else {
        this.fallbackCopyTextToClipboard(this.traceabilityData.traceCode)
      }
    },
    
    // 备用复制方法
    fallbackCopyTextToClipboard(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      try {
        const successful = document.execCommand('copy')
        if (successful) {
          this.$message.success('溯源码已复制到剪贴板')
        } else {
          this.$message.error('复制失败，请手动复制')
        }
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }
      
      document.body.removeChild(textArea)
    },
    


    
    // 分享溯源信息
    async shareTraceability() {
      if (!this.traceabilityData.traceCode) {
        this.$message.warning('暂无溯源信息可分享')
        return
      }
      
      try {
        const response = await generateShareLink(this.product.id, this.traceabilityData.traceCode)
        if (response && response.code === 200) {
          const shareUrl = response.data.shareUrl
          
          // 使用Web Share API（如果支持）
          if (navigator.share) {
            await navigator.share({
              title: `${this.product.name} - 溯源信息`,
              text: `查看 ${this.product.name} 的完整溯源信息`,
              url: shareUrl
            })
          } else {
            // 备用方案：复制链接
            this.fallbackCopyTextToClipboard(shareUrl)
            this.$message.success('分享链接已复制到剪贴板')
          }
        } else {
          this.$message.error('生成分享链接失败')
        }
      } catch (error) {
        console.error('分享溯源信息失败:', error)
        this.$message.error('分享失败，请稍后重试')
      }
    },
    
    // 下载溯源报告
    async downloadTraceReport() {
      if (!this.product.id) {
        this.$message.warning('产品信息不完整')
        return
      }
      
      try {
        this.$message.info('正在生成溯源报告...')
        const response = await getTraceabilityReport(this.product.id)
        
        // 创建下载链接
        const blob = new Blob([response], { type: 'application/pdf' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${this.product.name}_溯源报告.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        this.$message.success('溯源报告下载成功')
      } catch (error) {
        console.error('下载溯源报告失败:', error)
        this.$message.error('下载失败，请稍后重试')
      }
    },
    
    // 查看完整溯源
    viewFullTrace() {
      if (!this.traceabilityData.traceCode) {
        this.$message.warning('暂无溯源码')
        return
      }
      
      // 跳转到溯源中心页面
      this.$router.push({
        path: '/traceability',
        query: {
          code: this.traceabilityData.traceCode,
          productId: this.product.id
        }
      })
    },
    
    // 联系商家
    contactSeller() {
      if (this.product.seller && this.product.seller.id) {
        // 跳转到商家联系页面或打开聊天窗口
        this.$message.info('联系商家功能开发中...')
      } else {
        this.$message.warning('商家信息不完整')
      }
    },
    
    // 点赞评论
    likeReview(review) {
      if (!isLoggedIn()) {
        this.$message.warning('请先登录')
        return
      }
      
      review.isLiked = !review.isLiked
      if (review.isLiked) {
        review.likes = (review.likes || 0) + 1
          } else {
        review.likes = Math.max(0, (review.likes || 0) - 1)
      }
    },
    
    // 回复评论
    replyReview(review) {
      if (!isLoggedIn()) {
        this.$message.warning('请先登录')
        return
      }
      
      this.$prompt('请输入回复内容', '回复评论', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: value => {
          return value.trim().length > 0 || '回复内容不能为空'
        }
      }).then(({ value }) => {
        if (!review.replies) review.replies = []
        
        const userInfo = JSON.parse(localStorage.getItem('sfap_user') || '{}')
        
        review.replies.push({
          id: Date.now(),
          userId: userInfo.id || 'anonymous',
          userName: userInfo.name || '匿名用户',
          content: value,
          createTime: new Date().toISOString()
        })
        
        this.$message({
          type: 'success',
          message: '回复成功'
        })
      }).catch(() => {})
    },
    

    
    handlePictureCardPreview(file) {
      this.previewImageUrl = file.url
      this.imagePreviewVisible = true
    },
    
    handleRemove(file, fileList) {
      this.newReview.images = fileList.map(f => f.url || f.response.data.url)
    },
    
    beforeUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        this.$message.error('只能上传图片文件!')
      }
      
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!')
      }
      
      return isImage && isLt2M
    },
    
    handleUploadSuccess(response, file, fileList) {
      if (response && response.code === 200) {
        this.newReview.images = fileList.map(f => f.url || (f.response && f.response.data && f.response.data.url))
        } else {
        this.$message.error('上传图片失败')
      }
    },
    
    // 跳转到产品详情
    goToProduct(productId) {
      if (productId === this.$route.params.id) return
      
      this.$router.push({
        name: 'ProductDetail',
        params: { id: productId }
      })
    },
    
    // 工具方法：格式化日期
    formatDate(dateStr) {
      return formatDate(dateStr)
    },
    
    // 工具方法：获取分页数据
    getPaginatedArray(array, page, pageSize) {
      const start = (page - 1) * pageSize
      const end = start + pageSize
      return array.slice(start, end)
    },

    // 处理点赞状态变化
    handleLikeChanged(data) {
      this.isLiked = data.liked
      if (this.product) {
        this.product.likeCount = data.likeCount
      }
      console.log('商品点赞状态变化:', data)
    },

    // 处理收藏状态变化
    handleFavoriteChanged(data) {
      this.isFavorited = data.favorited
      if (this.product) {
        this.product.favoriteCount = data.favoriteCount
      }
      console.log('商品收藏状态变化:', data)
    },

    // 处理需要登录
    handleNeedLogin() {
      this.$message.warning('请先登录')
      this.$router.push('/login')
    }

  }
}
</script>

<style lang="scss" scoped>
// 基础变量
$primary-color: var(--primary-color, #409EFF);
$success-color: var(--success-color, #67C23A);
$warning-color: var(--warning-color, #E6A23C);
$danger-color: var(--danger-color, #F56C6C);
$info-color: var(--info-color, #909399);

$text-primary: var(--text-primary, #303133);
$text-regular: var(--text-regular, #606266);
$text-secondary: var(--text-secondary, #909399);

$border-color-base: var(--border-color-base, #DCDFE6);
$border-color-light: var(--border-color-light, #E4E7ED);

$background-color-base: var(--background-color-base, #F5F7FA);
$card-background: var(--card-background, #FFFFFF);

$transition-duration-base: var(--transition-duration-base, 0.3s);

$spacing-xs: var(--spacing-xs, 4px);
$spacing-sm: var(--spacing-sm, 8px);
$spacing-md: var(--spacing-md, 16px);
$spacing-lg: var(--spacing-lg, 24px);
$spacing-xl: var(--spacing-xl, 32px);

$breakpoint-xs: var(--breakpoint-xs, 576px);
$breakpoint-sm: var(--breakpoint-sm, 768px);
$breakpoint-md: var(--breakpoint-md, 992px);
$breakpoint-lg: var(--breakpoint-lg, 1200px);

// 产品详情页容器
.product-detail-container {
  padding: $spacing-md;
}

// 产品详情卡片
.product-detail {
  margin-bottom: $spacing-lg;
  border-radius: 8px;
  padding: $spacing-md;
  
  // 面包屑导航
  .breadcrumb-nav {
    margin-bottom: $spacing-md;
  }
  
  // 产品基本信息区域
  .product-basic-info {
    display: flex;
    gap: $spacing-xl;
    margin-bottom: $spacing-lg;

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
    }
    
    // 产品图片区域
    .product-images {
      width: 40%;
      min-width: 300px;
      
      @media (max-width: $breakpoint-md) {
        width: 100%;
        min-width: auto;
      }
      
      .el-carousel {
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: $spacing-sm;
      }
      
      .ag-image {
        width: 100%;
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        
        img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

      // 缩略图列表
      .thumbnail-list {
        display: flex;
        gap: $spacing-xs;
        margin-top: $spacing-sm;
        overflow-x: auto;
        padding-bottom: $spacing-xs;
        
        .thumbnail-item {
          width: 60px;
          height: 60px;
          border-radius: 4px;
          overflow: hidden;
          cursor: pointer;
          border: 2px solid transparent;
          transition: all $transition-duration-base;
          flex-shrink: 0;
          
          &.active {
            border-color: $primary-color;
          }
          
          &:hover {
            transform: translateY(-2px);
          }
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }

    // 商品互动工具栏
    .product-interaction-section {
      margin: $spacing-lg 0;
      padding: $spacing-md;
      background: $background-color-base;
      border-radius: 8px;
      border: 1px solid $border-color-light;
    }

    // 产品信息区域
    .product-info {
      flex: 1;

      .product-title-section {
        margin-bottom: $spacing-md;

        .product-title {
          font-size: 24px;
          margin: 0 0 $spacing-sm 0;
          color: $text-primary;
          line-height: 1.4;
        }

        .product-badges {
          display: flex;
          flex-wrap: wrap;
          gap: $spacing-xs;
          margin-top: $spacing-sm;

          .el-tag {
            font-size: 12px;
            border-radius: 12px;
            padding: 4px 12px;

            i {
              margin-right: 4px;
              font-size: 12px;
            }

            &.admin-direct-badge {
              background: linear-gradient(135deg, #67C23A 0%, #85CE61 100%);
              border-color: #67C23A;
              color: white;
              box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
              }
            }

            &.traceability-badge {
              border-color: #409EFF;
              color: #409EFF;

              &:hover {
                background-color: #409EFF;
                color: white;
              }
            }

            &.organic-badge {
              border-color: #67C23A;
              color: #67C23A;

              &:hover {
                background-color: #67C23A;
                color: white;
              }
            }

            &.new-badge {
              border-color: #E6A23C;
              color: #E6A23C;

              &:hover {
                background-color: #E6A23C;
                color: white;
              }
            }
          }
        }
      }
      
      .product-meta {
        display: flex;
        flex-wrap: wrap;
        gap: $spacing-lg;
        margin-bottom: $spacing-md;
        
        > div {
          display: flex;
          align-items: center;
          color: $text-secondary;
          font-size: 14px;
          
          i {
            margin-right: $spacing-xs;
          }
          
          .review-count {
            margin-left: $spacing-xs;
          }
        }
      }

      .product-price {
        background-color: rgba($danger-color, 0.05);
        padding: $spacing-md;
        border-radius: 4px;
        margin-bottom: $spacing-md;

        .price-label {
          color: $text-secondary;
          margin-right: $spacing-sm;
        }

        .price-symbol {
          color: $danger-color;
          font-size: 16px;
          margin-right: 2px;
        }
        
        .price-value {
          color: $danger-color;
          font-size: 24px;
          font-weight: 500;
        }
        
        .price-unit {
          color: $text-secondary;
          margin-left: 4px;
          font-size: 14px;
        }
        
        .original-price {
          margin-left: $spacing-sm;
          color: $text-secondary;
          text-decoration: line-through;
          font-size: 14px;
        }
      }

      .product-attributes {
        margin-bottom: $spacing-md;
        
        .attribute-item {
        display: flex;
          margin-bottom: $spacing-xs;

          .attribute-label {
            width: 80px;
            color: $text-secondary;
          }
          
          .attribute-value {
            color: $text-primary;
          }
        }
      }
      
      .product-purchase {
        margin-bottom: $spacing-lg;
        
        .quantity-control {
          display: flex;
          align-items: center;
          margin-bottom: $spacing-md;
          
          .quantity-label {
            color: $text-secondary;
            margin-right: $spacing-md;
          }
          
          .quantity-unit {
            margin: 0 $spacing-md;
      }

          .stock-info {
            color: $text-secondary;
            font-size: 12px;
            
            &.low-stock {
              color: $warning-color;
            }
          }
        }
        
        .purchase-actions {
          display: flex;
          gap: $spacing-md;
          flex-wrap: wrap;
          
        .el-button {
            min-width: 120px;
          }
        }
      }
      
      .seller-info {
        display: flex;
        align-items: center;
        gap: $spacing-md;
        padding: $spacing-md;
        background-color: $background-color-base;
        border-radius: 4px;
        
        .seller-avatar {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          overflow: hidden;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        
        .seller-detail {
          flex: 1;
          
          .seller-name {
            font-weight: 500;
            margin-bottom: $spacing-xs;
        }
      }
    }
  }
  }
  
  // 产品详细内容区域
  .product-content {
    margin-bottom: $spacing-lg;

  .product-tabs {
      border: 1px solid $border-color-light;
      border-radius: 4px;

    .product-description {
        padding: $spacing-md;
        color: $text-regular;
        line-height: 1.6;
        
        h3 {
          margin-top: 0;
          margin-bottom: $spacing-md;
          font-size: 18px;
        }
        
        p {
          margin-bottom: $spacing-md;
        }
        
        .product-images-list {
          margin: $spacing-lg 0;
          
          .ag-image {
            margin-bottom: $spacing-md;
            
            img {
              width: 100%;
              border-radius: 4px;
            }
          }
        }
        
        .product-spec-table {
          width: 100%;
          border-collapse: collapse;
          margin: $spacing-lg 0;
          
          th, td {
            padding: $spacing-sm;
            border: 1px solid $border-color-light;
          }
          
          th {
            background-color: $background-color-base;
            text-align: center;
            font-weight: 500;
          }
          
          .spec-name {
            width: 30%;
            color: $text-secondary;
          }
          
          .spec-value {
            color: $text-primary;
          }
        }
      }
      
      // 评价区域
      .review-summary {
          display: flex;
        flex-direction: column;
        padding: $spacing-md;
        border-bottom: 1px solid $border-color-light;
        
        .review-stats {
          display: flex;
          gap: $spacing-lg;
          margin-bottom: $spacing-md;
          
          @media (max-width: $breakpoint-sm) {
            flex-direction: column;
          }
          
          .review-average-score {
            font-size: 48px;
            font-weight: 500;
            color: $primary-color;
            line-height: 1.2;
            width: 120px;
            text-align: center;
            
            span {
              display: block;
              font-size: 14px;
              color: $text-secondary;
              font-weight: normal;
            }
          }
          
          .review-score-distribution {
            flex: 1;

            .score-item {
            display: flex;
            align-items: center;
              margin-bottom: $spacing-xs;

              .score-label {
                width: 40px;
                margin-right: $spacing-sm;
              }
              
              .el-progress {
              flex: 1;
                margin-right: $spacing-sm;
              }
              
              .score-percent {
                width: 40px;
                text-align: right;
                color: $text-secondary;
              }
            }
          }
        }
        
        .review-tags {
          display: flex;
          flex-wrap: wrap;
          gap: $spacing-xs;
          
          .el-tag {
            cursor: pointer;
            margin-right: 0;
          }
        }
      }
      
      .review-list {
        padding: $spacing-md;
        
        .empty-review {
          text-align: center;
          padding: $spacing-xl 0;
          color: $text-secondary;
          
          i {
            font-size: 48px;
            margin-bottom: $spacing-sm;
              }
            }

        .review-item {
          margin-bottom: $spacing-lg;
          padding-bottom: $spacing-md;
          border-bottom: 1px solid $border-color-light;
          
          &:last-child {
            border-bottom: none;
          }
          
          .review-user {
            display: flex;
            margin-bottom: $spacing-sm;
            
            .user-avatar {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              overflow: hidden;
              margin-right: $spacing-sm;
              
              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
            
            .user-info {
              flex: 1;
              
              .user-name {
                font-weight: 500;
                margin-bottom: 4px;
              }
              
              .review-time {
              font-size: 12px;
                color: $text-secondary;
              }
            }
          }

          .review-content {
            padding-left: 52px;
            
            .review-rating {
              margin-bottom: $spacing-sm;
              display: flex;
              align-items: center;
              
              .review-tags {
                margin-left: auto;
              }
            }
            
            .review-text {
              margin-bottom: $spacing-sm;
            line-height: 1.6;
          }

            .review-images {
              display: flex;
              flex-wrap: wrap;
              gap: $spacing-sm;
              margin-bottom: $spacing-sm;
              
              .review-image-item {
                width: 80px;
                height: 80px;
                border-radius: 4px;
                overflow: hidden;
              cursor: pointer;
                
                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
              }
            }
            
            .review-actions {
              margin-bottom: $spacing-sm;
              
              .review-action {
                margin-right: $spacing-lg;
                cursor: pointer;
                color: $text-secondary;
                
                i {
                  margin-right: 4px;
                  
                  &.active {
                    color: $primary-color;
                  }
                }

              &:hover {
                  color: $primary-color;
                }
              }
            }
            
            .review-replies {
              background-color: $background-color-base;
              padding: $spacing-sm;
              border-radius: 4px;
              
              .reply-item {
                padding: $spacing-xs 0;
                font-size: 14px;
                
                &:not(:last-child) {
                  border-bottom: 1px solid $border-color-light;
                  padding-bottom: $spacing-xs;
                  margin-bottom: $spacing-xs;
              }

                .reply-user {
                  font-weight: 500;
                  color: $primary-color;
                }
                
                .reply-content {
                  margin: 0 $spacing-xs;
                }
                
                .reply-time {
                  font-size: 12px;
                  color: $text-secondary;
                }
              }
            }
          }
        }
      }
      
      .write-review {
        padding: $spacing-md;
        border-top: 1px solid $border-color-light;
        
        h3 {
          margin-top: 0;
          margin-bottom: $spacing-md;
          font-size: 18px;
        }
        
        .review-form {
          .form-item {
            margin-bottom: $spacing-md;
            
            .form-label {
              display: inline-block;
              margin-right: $spacing-sm;
              width: 60px;
              color: $text-secondary;
            }
          }
          
          .form-actions {
            text-align: right;
          }
        }
      }
      
      .login-to-review {
        padding: $spacing-xl 0;
        text-align: center;
        color: $text-secondary;
        
        i {
          font-size: 24px;
          margin-bottom: $spacing-sm;
        }
        
        a {
          color: $primary-color;
        }
      }
    }
  }
  
  // 相关推荐区域
  .related-products {
    margin-bottom: $spacing-lg;
    
    .section-title {
      font-size: 20px;
      margin-top: 0;
      margin-bottom: $spacing-md;
      color: $text-primary;
    }
    
    .product-col {
      margin-bottom: $spacing-md;
    }
  }
}

// 产品卡片样式
.product-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform $transition-duration-base, box-shadow $transition-duration-base;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
              }
  
  .product-image {
    position: relative;
    height: 160px;
    
    .ag-image {
      width: 100%;
      height: 100%;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
            }
          }
        }
  
  .product-info {
    flex: 1;
    padding: $spacing-sm;
    
    .product-name {
      font-size: 16px;
      margin: 0 0 $spacing-xs;
      color: $text-primary;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    
    .product-price {
      color: $danger-color;
      font-size: 18px;
      font-weight: 500;
      
      .price-symbol {
        font-size: 14px;
      }
      
      .unit {
        font-size: 12px;
        color: $text-secondary;
      }
    }
  }
}

// 表格样式
.user-brief {
  display: flex;
  align-items: center;
  
  .user-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: $spacing-sm;
  }
}

// 分页样式
.pagination-container {
  margin: $spacing-md 0;
  text-align: center;
}

// 溯源信息样式
.traceability-section {
  padding: $spacing-lg;
  background: $background-color-base;
  border-radius: 8px;
  margin-bottom: $spacing-lg;
}

.trace-code-display {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: $spacing-lg;
  border-radius: 12px;
  text-align: center;
  margin-bottom: $spacing-lg;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  
  h3 {
    margin: 0 0 $spacing-sm 0;
    font-size: 18px;
    font-weight: 600;
  }
  
  .trace-code {
    font-size: 24px;
    font-weight: bold;
    letter-spacing: 2px;
    margin: $spacing-sm 0;
    font-family: 'Courier New', monospace;
  }
  
  .copy-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: $spacing-sm $spacing-md;
    border-radius: 20px;
    transition: all $transition-duration-base ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }
  }
}

.trace-timeline {
  position: relative;
  padding-left: 30px;
  
  &::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, $success-color, $primary-color);
  }
}

.trace-step {
  position: relative;
  margin-bottom: $spacing-lg;
  background: $card-background;
  padding: $spacing-md;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  
  &:hover {
    transform: translateX(5px);
  }
  
  &::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 20px;
    width: 12px;
    height: 12px;
    background: $success-color;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 0 0 2px $success-color;
  }
  
  &.completed::before {
    background: $success-color;
  }
  
  &.in-progress::before {
    background: $warning-color;
    box-shadow: 0 0 0 2px $warning-color;
  }
  
  &.pending::before {
    background: $info-color;
    box-shadow: 0 0 0 2px $info-color;
  }
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-sm;
}

.step-title {
  font-weight: 600;
  color: $text-primary;
  font-size: 16px;
}

.step-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  
  &.completed {
    background: rgba($success-color, 0.1);
    color: $success-color;
  }
  
  &.in-progress {
    background: rgba($warning-color, 0.1);
    color: $warning-color;
  }
  
  &.pending {
    background: rgba($info-color, 0.1);
    color: $info-color;
  }
}

.step-content {
  color: $text-regular;
  line-height: 1.6;
  margin-bottom: $spacing-sm;
}

.step-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: $text-secondary;
}

.step-images {
  display: flex;
  gap: $spacing-sm;
  margin-top: $spacing-sm;
  
  .step-image {
    width: 60px;
    height: 60px;
    border-radius: 6px;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.2s ease;
    
    &:hover {
      transform: scale(1.1);
    }
  }
}

.quality-reports {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: $spacing-lg;
  margin-bottom: $spacing-lg;
}

.quality-report {
  background: $card-background;
  border-radius: 8px;
  padding: $spacing-lg;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid $success-color;
  
  .report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;
  }
  
  .report-title {
    font-weight: 600;
    color: $text-primary;
    font-size: 16px;
  }
  
  .report-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    background: rgba($success-color, 0.1);
    color: $success-color;
  }
  
  .report-content {
    color: $text-regular;
    line-height: 1.6;
    margin-bottom: $spacing-md;
  }
  
  .report-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: $text-secondary;
  }
}

.certifications {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: $spacing-md;
  margin-bottom: $spacing-lg;
}

.certification {
  background: $card-background;
  border-radius: 8px;
  padding: $spacing-md;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid $border-color-light;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  }
  
  .cert-header {
    display: flex;
    align-items: center;
    margin-bottom: $spacing-sm;
  }
  
  .cert-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 12px;
    font-size: 18px;
  }
  
  .cert-info {
    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: $text-primary;
    }
    
    p {
      margin: 2px 0 0 0;
      font-size: 12px;
      color: $text-secondary;
    }
  }
  
  .cert-validity {
    font-size: 12px;
    color: $success-color;
    font-weight: 500;
  }
}

.stats-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-lg;
  margin-bottom: $spacing-lg;
}

.chart-container {
  background: $card-background;
  border-radius: 8px;
  padding: $spacing-lg;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .chart-wrapper {
    width: 100%;
    height: 300px;
  }
}

.trace-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: $spacing-lg;
}

.trace-action-btn {
  padding: $spacing-sm $spacing-lg;
  border-radius: 6px;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all $transition-duration-base ease;
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  
  &.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }
  
  &.secondary {
    background: $background-color-base;
    color: $text-primary;
    border: 1px solid $border-color-base;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
}

.no-trace-data {
  text-align: center;
  padding: 60px $spacing-lg;
  color: $text-secondary;
  
  .el-icon-warning {
    font-size: 48px;
    color: $warning-color;
    margin-bottom: $spacing-lg;
  }
  
  h3 {
    margin: 0 0 $spacing-sm 0;
    color: $text-regular;
  }
  
  p {
    margin: 0;
    line-height: 1.6;
  }
}

.contact-seller-btn {
   background: $primary-color;
   color: white;
   border: none;
   padding: $spacing-sm $spacing-lg;
   border-radius: 6px;
   cursor: pointer;
   transition: background $transition-duration-base ease;
   
   &:hover {
     background: #337ecc;
   }
 }

// 加载动画
.trace-loading {
  text-align: center;
  padding: 40px;
  
  .el-icon-loading {
    font-size: 24px;
    color: $primary-color;
    margin-bottom: $spacing-sm;
  }
}

// 溯源信息容器样式
.traceability-container {
  padding: $spacing-lg;
  background: $background-color-base;
  border-radius: 8px;
  
  .traceability-content {
    min-height: 200px;
  }
  
  h3 {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    margin-bottom: $spacing-lg;
    color: $text-primary;
    font-size: 18px;
    font-weight: 600;
    
    i {
      color: $primary-color;
      font-size: 20px;
    }
  }
}

// 溯源码展示区域
.trace-code-section {
  margin-bottom: $spacing-xl;
  
  .trace-code-card {
    display: flex;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: $spacing-lg;
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    
    .trace-code-info {
      flex: 1;
      
      h3 {
        color: white;
        margin-bottom: $spacing-md;
        
        i {
          color: rgba(255, 255, 255, 0.8);
        }
      }
      
      .trace-code-display {
        display: flex;
        align-items: center;
        gap: $spacing-md;
        margin-bottom: $spacing-sm;
        
        .trace-code {
          font-size: 24px;
          font-weight: bold;
          letter-spacing: 2px;
          font-family: 'Courier New', monospace;
          background: rgba(255, 255, 255, 0.1);
          padding: $spacing-sm $spacing-md;
          border-radius: 6px;
          border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .el-button {
          background: rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.3);
          color: white;
          
          &:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
          }
        }
      }
      
      .trace-code-desc {
        margin: 0;
        opacity: 0.9;
        font-size: 14px;
      }
    }
    
    .trace-qr-code {
      width: 120px;
      height: 120px;
      background: white;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: $spacing-lg;
      
      .qr-code-image {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        img {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }
      }
      
      .qr-code-placeholder {
        text-align: center;
        color: $text-secondary;
        
        i {
          font-size: 32px;
          margin-bottom: $spacing-xs;
          display: block;
        }
        
        p {
          margin: 0;
          font-size: 12px;
        }
      }
    }
  }
}

// 溯源步骤时间线
.trace-steps-section {
  margin-bottom: $spacing-xl;
  
  .trace-timeline {
    .el-timeline-item {
      .trace-step-card {
        margin-bottom: $spacing-md;
        
        .step-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: $spacing-sm;
          
          h4 {
            margin: 0;
            color: $text-primary;
            font-size: 16px;
            font-weight: 600;
          }
        }
        
        .step-description {
          color: $text-regular;
          line-height: 1.6;
          margin-bottom: $spacing-sm;
        }
        
        .step-images {
          display: flex;
          gap: $spacing-sm;
          
          .step-image {
            width: 80px;
            height: 80px;
            border-radius: 6px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s ease;
            
            &:hover {
              transform: scale(1.05);
            }
            
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }
      }
    }
  }
}

// 质检报告区域
.quality-reports-section {
  margin-bottom: $spacing-xl;
  
  .el-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 认证信息区域
.certifications-section {
  margin-bottom: $spacing-xl;
  
  .certification-card {
    height: 100%;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    }
    
    .cert-header {
      display: flex;
      align-items: center;
      margin-bottom: $spacing-md;
      
      .cert-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-right: $spacing-sm;
        font-size: 18px;
      }
      
      h4 {
        margin: 0;
        color: $text-primary;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .cert-info {
      p {
        margin: $spacing-xs 0;
        color: $text-regular;
        font-size: 14px;
        
        strong {
          color: $text-primary;
        }
      }
    }
    
    .cert-actions {
      margin-top: $spacing-md;
      text-align: right;
    }
  }
}

// 溯源统计区域
.trace-stats-section {
  margin-bottom: $spacing-xl;
  
  .stats-card {
    height: 100%;
    
    h4 {
      margin: 0 0 $spacing-md 0;
      color: $text-primary;
      font-size: 16px;
      font-weight: 600;
      text-align: center;
    }
    
    .chart-container {
      width: 100%;
      height: 300px;
    }
  }
}

// 溯源操作区域
.trace-actions-section {
  margin-bottom: $spacing-xl;
  
  .trace-actions {
    display: flex;
    gap: $spacing-md;
    flex-wrap: wrap;
    
    .el-button {
      flex: 1;
      min-width: 140px;
    }
  }
}

// 无溯源信息提示
.no-trace-info {
  text-align: center;
  padding: 60px $spacing-lg;
  color: $text-secondary;
  
  i {
    font-size: 48px;
    color: $warning-color;
    margin-bottom: $spacing-lg;
    display: block;
  }
  
  h3 {
    margin: 0 0 $spacing-sm 0;
    color: $text-regular;
    font-size: 18px;
  }
  
  p {
    margin: 0 0 $spacing-lg 0;
    line-height: 1.6;
    color: $text-secondary;
  }
  
  .el-button {
    margin-top: $spacing-md;
  }
}

// 响应式设计
@media (max-width: $breakpoint-sm) {
  .traceability-container {
    padding: $spacing-md;
  }
  
  .trace-code-card {
    flex-direction: column;
    
    .trace-qr-code {
      width: 100px;
      height: 100px;
      margin: $spacing-md auto 0;
    }
  }
  
  .trace-code-display {
    flex-direction: column;
    align-items: flex-start !important;
    
    .trace-code {
      font-size: 18px !important;
      letter-spacing: 1px !important;
      word-break: break-all;
    }
  }
  
  .trace-actions {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
  
  .stats-section {
    grid-template-columns: 1fr;
  }
  
  .quality-reports {
    grid-template-columns: 1fr;
  }
  
  .certifications {
    grid-template-columns: 1fr;
  }
  
  .step-images {
    .step-image {
      width: 60px;
      height: 60px;
    }
  }
}

// 图片预览样式优化
.el-image-viewer__wrapper {
  z-index: 3000;
}
</style>