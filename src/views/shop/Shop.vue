<template>
  <div class="shop-container">
    <!-- 页面头部横幅 -->
    <div class="shop-banner">
      <div class="banner-content">
        <div class="banner-left">
          <h1>农品汇</h1>
          <p>优质农产品交易平台，连接生产者与消费者</p>
        </div>
        <div class="banner-stats">
          <div class="stat-item">
            <span class="stat-number">{{ totalProducts }}</span>
            <span class="stat-label">在售商品</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ totalSuppliers }}</span>
            <span class="stat-label">合作供应商</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ totalOrders }}</span>
            <span class="stat-label">成功交易</span>
          </div>
        </div>
        <!-- 右上角操作按钮区域 -->
        <div class="banner-actions">
          <!-- 顶栏操作区域，如需要可添加其他按钮 -->
        </div>
      </div>
    </div>

    <!-- 主要内容布局 -->
    <div class="main-layout">
      <!-- 左侧主内容区 -->
      <div class="main-content-area">
        <!-- 搜索和筛选区域 -->
        <div class="search-filter-section">
          <div class="search-container">
            <!-- 搜索栏和溯源查询按钮 -->
            <div class="search-top-row">
          <!-- 使用SearchBar组件 -->
          <div class="search-bar-wrapper">
            <SearchBar
              v-model="searchQuery"
              :external-suggestions="searchSuggestions"
              :hot-tags="hotSearchTags"
              :history="searchHistory"
              @search="handleSearch"
              @clear="clearSearch"
              @tag-click="handleTagSearch"
            />
          </div>

          <!-- 溯源查询按钮 -->
          <div class="trace-query-wrapper">
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="showTraceQueryDialog"
              class="trace-query-btn"
            >
              溯源查询
            </el-button>
          </div>
        </div>

        <!-- 搜索框下方内容区域 -->
        <div class="content-below-search">
          <!-- 四个功能模块已移除：热门推荐、农产品分类、限时优惠、农业资讯 -->
        </div>

        
        <!-- 分类导航 -->
        <div class="category-navigation">
          <el-menu
            mode="horizontal"
            :default-active="activeCategory"
            @select="handleCategorySelect"
            class="category-menu"
          >
            <el-menu-item index="all">
              <i class="el-icon-menu"></i>
              全部分类
            </el-menu-item>

            <!-- 主分类项（带下拉子分类） -->
            <div
              v-for="(category, index) in mainCategories.slice(0, 7)"
              :key="category.id"
              :class="['category-item-wrapper', { 'expanded': expandedCategories.includes(category.id) }]"
              :style="{ 'animation-delay': (0.6 + index * 0.1) + 's' }"
            >
              <el-menu-item
                :index="category.id.toString()"
                :class="['main-category-item', { 'has-subcategories': getSubCategories(category.id).length > 0 }]"
              >
                <i :class="getCategoryIcon(category)"></i>
                {{ category.name }}
                <span class="category-count" v-if="category.productCount > 0">({{ category.productCount }})</span>
                <!-- 二级分类展开按钮 -->
                <i
                  v-if="getSubCategories(category.id).length > 0"
                  :class="['sub-category-toggle', expandedCategories.includes(category.id) ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
                  @click.stop="toggleSubCategories(category.id)"
                  @keydown.enter.stop="toggleSubCategories(category.id)"
                  @keydown.space.prevent.stop="toggleSubCategories(category.id)"
                  tabindex="0"
                  :aria-label="`${expandedCategories.includes(category.id) ? '收起' : '展开'}${category.name}的子分类`"
                  role="button"
                ></i>
              </el-menu-item>

              <!-- 下拉子分类菜单 -->
              <transition name="dropdown-slide">
                <div
                  v-if="expandedCategories.includes(category.id)"
                  class="sub-category-dropdown"
                >
                  <div class="sub-category-list">
                    <div
                      v-for="(subCategory, subIndex) in getSubCategories(category.id)"
                      :key="subCategory.id"
                      :class="['sub-category-item', { 'active': activeSubCategory === subCategory.id }]"
                      @click="selectSubCategory(subCategory)"
                      @keydown.enter="selectSubCategory(subCategory)"
                      @keydown.space.prevent="selectSubCategory(subCategory)"
                      :style="{ 'animation-delay': (subIndex * 0.05) + 's' }"
                      tabindex="0"
                      :aria-label="`选择${subCategory.name}分类，共${subCategory.productCount || 0}个商品`"
                      role="button"
                    >
                      <i :class="getSubCategoryIcon(subCategory)"></i>
                      <span class="sub-category-name">{{ subCategory.name }}</span>
                      <span class="sub-category-product-count" v-if="subCategory.productCount && subCategory.productCount > 0">
                        ({{ subCategory.productCount }})
                      </span>
                    </div>
                  </div>
                </div>
              </transition>
            </div>


          </el-menu>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="20">
        <!-- 左侧边栏 -->
        <el-col :xs="24" :sm="24" :md="6" :lg="5">
          <div class="sidebar">
            <!-- 筛选器 -->
            <el-card class="filter-card">
              <div slot="header" class="card-header">
                <span><i class="el-icon-s-operation"></i> 筛选条件</span>
                <el-button type="text" size="mini" @click="resetFilters">重置</el-button>
              </div>
              
              <!-- 二级分类筛选 -->
            <div v-if="activeCategory && activeCategory !== 'all' && currentSubCategories.length > 0" class="sub-category-filter">
              <h4 class="filter-title">
                <i class="el-icon-menu"></i>
                {{ getMainCategoryName(activeCategory) }} - 子分类
              </h4>
              <div class="sub-category-list">
                <el-checkbox-group v-model="selectedSubCategories" @change="handleSubCategoryChange">
                  <div 
                    v-for="subCategory in currentSubCategories" 
                    :key="subCategory.id"
                    class="sub-category-item"
                  >
                    <el-checkbox :label="subCategory.id">
                      <span class="category-name">{{ subCategory.name }}</span>
                      <span class="product-count" v-if="subCategory.productCount && subCategory.productCount > 0">({{ subCategory.productCount }})</span>
                    </el-checkbox>
                  </div>
                </el-checkbox-group>
              </div>
            </div>
            
            <!-- 其他筛选条件 -->
            <div class="other-filters">
              <!-- 商品特性 -->
              <div class="filter-section">
                <h4 class="section-title">商品特性</h4>
                <div class="filter-tags">
                  <el-tag 
                    v-for="tag in productTags" 
                    :key="tag.key"
                    :type="selectedTags.includes(tag.key) ? 'success' : 'info'"
                    :effect="selectedTags.includes(tag.key) ? 'dark' : 'plain'"
                    size="small"
                    @click="toggleTag(tag.key)"
                    class="filter-tag"
                  >
                    {{ tag.label }}
                  </el-tag>
                </div>
              </div>
              
              <!-- 价格区间 -->
              <div class="filter-section">
                <h4 class="section-title">价格区间</h4>
                <div class="price-ranges">
                  <el-tag 
                    v-for="range in priceRanges" 
                    :key="range.key"
                    :type="selectedPriceRange === range.key ? 'warning' : 'info'"
                    :effect="selectedPriceRange === range.key ? 'dark' : 'plain'"
                    size="small"
                    @click="selectPriceRange(range.key)"
                    class="filter-tag"
                  >
                    {{ range.label }}
                  </el-tag>
                </div>
              </div>
            </div>
            </el-card>

            <!-- 快捷操作 -->
            <el-card class="quick-actions">
              <div slot="header" class="card-header">
                <span><i class="el-icon-s-tools"></i> 快捷操作</span>
              </div>
              <div class="action-buttons">
                <!-- 管理员功能 -->
                <el-button
                  v-if="isAdmin"
                  type="danger"
                  icon="el-icon-s-custom"
                  @click="goToAdminDashboard"
                  size="small"
                  block
                >
                  管理后台
                </el-button>

                <!-- 销售者功能 -->
                <el-button
                  v-if="isSeller"
                  type="primary"
                  icon="el-icon-plus"
                  @click="showPublishDialog"
                  size="small"
                  block
                >
                  发布商品
                </el-button>
                <el-button
                  v-if="isSeller"
                  type="success"
                  icon="el-icon-s-shop"
                  @click="showMyProducts"
                  size="small"
                  block
                >
                  我的店铺
                </el-button>

                <!-- 普通用户功能 -->
                <el-button
                  v-if="isLoggedIn && !isSeller && !isAdmin"
                  type="warning"
                  icon="el-icon-user"
                  @click="showSellerApplication"
                  size="small"
                  block
                >
                  申请成为销售者
                </el-button>
                <el-button 
                  type="info" 
                  icon="el-icon-data-line" 
                  @click="showPriceChart"
                  size="small"
                  block
                >
                  价格走势
                </el-button>
                <!-- 未登录用户 -->
                <el-button
                  v-if="!isLoggedIn"
                  type="primary"
                  @click="$router.push('/login')"
                  size="small"
                  block
                >
                  登录/注册
                </el-button>
              </div>
            </el-card>

            <!-- 热门分类 -->
            <el-card class="hot-categories">
              <div slot="header" class="card-header">
                <span><i class="el-icon-star-on"></i> 热门分类</span>
              </div>
              <div class="category-list">
                <el-tag 
                  v-for="category in hotCategories" 
                  :key="category.id"
                  @click="selectCategory(category.id)"
                  class="category-tag"
                  size="small"
                  :type="activeCategory === category.id.toString() ? 'primary' : ''"
                >
                  {{ category.name }}
                </el-tag>
              </div>
            </el-card>
          </div>
        </el-col>

        <!-- 右侧主内容 -->
        <el-col :xs="24" :sm="24" :md="18" :lg="19">
          <!-- 使用ProductRecommendation组件 -->
          <ProductRecommendation
            v-if="!searchActive && !categoryActive"
            :user-preferences="userPreferences"
            :categories="categories"
            @product-click="showProductDetail"
            @add-to-cart="addToCart"
            @add-to-favorites="addToFavorites"
          />

          <!-- 商品列表 -->
          <el-card class="product-list-section">
            <div slot="header" class="list-header">
              <span>商品列表</span>
              <div class="list-controls">
                <span class="result-count">
                  共 {{ totalProducts }} 件商品
                  <span v-if="traceableProductsCount > 0" class="traceable-count">
                    ({{ traceableProductsCount }} 件可溯源)
                  </span>
                </span>

                <!-- 排序选择器 -->
                <el-select
                  v-model="filters.sortBy"
                  @change="handleSortChange"
                  size="small"
                  placeholder="排序方式"
                  class="sort-selector"
                >
                  <el-option label="默认排序" value="default"></el-option>
                  <el-option label="新品优先" value="new_first"></el-option>
                  <el-option label="最新发布" value="time_desc"></el-option>
                  <el-option label="价格从低到高" value="price_asc"></el-option>
                  <el-option label="价格从高到低" value="price_desc"></el-option>
                  <el-option label="销量优先" value="sales_desc"></el-option>
                  <el-option label="评分优先" value="rating_desc"></el-option>
                  <el-option label="溯源优先" value="traceable_first"></el-option>
                </el-select>

                <el-radio-group v-model="viewMode" size="small">
                  <el-radio-button label="grid"><i class="el-icon-menu"></i></el-radio-button>
                  <el-radio-button label="list"><i class="el-icon-s-unfold"></i></el-radio-button>
                </el-radio-group>
              </div>
            </div>
            
            <div class="product-list">
              <!-- 加载骨架屏 -->
              <div v-if="loading" class="skeleton-grid">
                <div v-for="n in 12" :key="n" class="skeleton-card" :style="{ 'animation-delay': (n * 0.1) + 's' }">
                  <div class="skeleton-image"></div>
                  <div class="skeleton-content">
                    <div class="skeleton-title"></div>
                    <div class="skeleton-price"></div>
                    <div class="skeleton-info"></div>
                  </div>
                </div>
              </div>

              <!-- 网格视图 -->
              <div v-else-if="viewMode === 'grid'" class="grid-view">
                <el-row :gutter="20" type="flex" justify="start">
                  <el-col
                    v-for="(product, index) in products"
                    :key="product.id"
                    :xs="12"
                    :sm="8"
                    :md="8"
                    :lg="6"
                    :xl="4"
                    class="product-col"
                    :style="{ 'animation-delay': (index * 0.1) + 's' }"
                  >
                    <div class="product-card-wrapper">
                      <ProductCard
                        :product="product"
                        @click="showProductDetail(product)"
                        @quick-view="showProductDetail"
                        @add-to-cart="addToCart"
                        @add-to-favorites="addToFavorites"
                      />
                    </div>
                  </el-col>
                </el-row>
              </div>
              
              <!-- 列表视图 -->
              <div v-else class="list-view">
                <ProductListItem 
                  v-for="product in products" 
                  :key="product.id"
                  :product="product"
                  @click="showProductDetail"
                  @add-to-cart="addToCart"
                  @add-to-favorites="addToFavorites"
                />
              </div>
              
              <!-- 空状态 -->
              <div v-if="!loading && products.length === 0" class="empty-state">
                <i class="el-icon-goods empty-icon"></i>
                <p class="empty-text">
                  {{ searchActive ? '没有找到符合条件的商品' : '暂无商品数据' }}
                </p>
                <div class="empty-actions">
                  <el-button v-if="searchActive" @click="clearSearch">清除搜索条件</el-button>
                  <el-button v-if="userRole === 'ROLE_SELLER'" type="primary" @click="showPublishDialog">发布商品</el-button>
                </div>
              </div>
            </div>
            
            <!-- 分页 -->
            <div class="pagination-wrapper">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[6, 12, 24, 36]"
                :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalProducts"
                background
              >
              </el-pagination>
            </div>
          </el-card>
        </el-col>
      </el-row>
      </div>

      <!-- 左侧主内容区结束 -->
      </div>

      <!-- 右侧边栏 -->
      <div class="right-sidebar">
        <!-- 角色差异化功能区域 -->
        <div class="sidebar-role-functions">
          <div class="sidebar-section-title">
            <i class="el-icon-user"></i>
            用户中心
          </div>

          <!-- 调试信息按钮 (开发环境) -->
          <div v-if="isDevelopment" class="debug-info-section">
            <el-button
              type="info"
              size="mini"
              @click="showDebugInfo"
              style="margin-bottom: 4px; width: 100%;"
            >
              🔍 调试信息
            </el-button>
            <el-button
              type="warning"
              size="mini"
              @click="showValidationTool"
              style="margin-bottom: 8px; width: 100%;"
            >
              🛠️ 验证工具
            </el-button>
          </div>

          <!-- 基础角色组件 - 所有用户可见 -->
          <div class="sidebar-base-component">
            <SellerApplicationButton
              :is-logged-in="isLoggedIn"
              :external-is-seller="isSeller"
              :external-user-info="userInfo"
            />
          </div>

          <!-- 销售者专用功能区域 -->
          <RoleBasedComponent
            :required-roles="['seller']"
            :external-user-info="userInfo"
            :external-is-seller="isSeller"
            :external-is-admin="isAdmin"
            :external-is-logged-in="isLoggedIn"
          >
            <div class="sidebar-seller-area">
              <div class="sidebar-section-title">
                <i class="el-icon-s-shop"></i>
                销售中心
              </div>
              <!-- 销售者快捷操作面板 -->
              <div class="sidebar-seller-actions">
                <SellerQuickActionsCompact
                  :user-info="userInfo"
                  @create-record="handleCreateRecord"
                  @manage-products="showMyProducts"
                  @view-stats="goToSellerStats"
                  @go-to-trace-center="goToTraceCenter"
                />
              </div>

              <!-- 我的店铺快捷入口 -->
              <div class="sidebar-shop-access">
                <ShopQuickAccess
                  :user-info="userInfo"
                  @manage-products="showMyProducts"
                  @add-product="goToAddProduct"
                  @view-stats="goToProductStats"
                  @shop-settings="goToShopSettings"
                />
              </div>
            </div>
          </RoleBasedComponent>

          <!-- 管理员专用功能区域 -->
          <RoleBasedComponent
            :required-roles="['admin']"
            :external-user-info="userInfo"
            :external-is-seller="isSeller"
            :external-is-admin="isAdmin"
            :external-is-logged-in="isLoggedIn"
          >
            <div class="sidebar-admin-area">
              <div class="sidebar-section-title">
                <i class="el-icon-s-custom"></i>
                管理中心
              </div>
              <!-- 系统统计概览 -->
              <div class="sidebar-admin-stats">
                <AdminStatsCompact
                  :user-info="userInfo"
                  @view-dashboard="goToAdminDashboard"
                />
              </div>

              <!-- 管理员快捷操作 -->
              <div class="sidebar-admin-actions">
                <AdminQuickActions
                  :user-info="userInfo"
                  @manage-users="goToUserManagement"
                  @manage-sellers="goToSellerManagement"
                  @audit-traceability="goToTraceabilityAudit"
                  @system-settings="goToSystemSettings"
                />
              </div>
            </div>
          </RoleBasedComponent>

          <!-- 普通用户专用功能区域 -->
          <RoleBasedComponent
            :required-roles="['user']"
            :external-user-info="userInfo"
            :external-is-seller="isSeller"
            :external-is-admin="isAdmin"
            :external-is-logged-in="isLoggedIn"
          >
            <div class="sidebar-user-area">
              <div class="sidebar-section-title">
                <i class="el-icon-user"></i>
                用户中心
              </div>
              <!-- 用户快捷操作 -->
              <div class="sidebar-user-actions">
                <UserQuickActions
                  :user-info="userInfo"
                  @view-profile="goToUserProfile"
                  @view-orders="goToUserOrders"
                  @view-favorites="goToUserFavorites"
                  @apply-seller="showSellerApplication"
                />
              </div>

              <!-- 申请销售者状态 -->
              <div class="sidebar-seller-status">
                <SellerApplicationStatus
                  :user-info="userInfo"
                  @check-status="checkSellerApplicationStatus"
                  @start-application="showSellerApplication"
                  @reapply="showSellerApplication"
                />
              </div>
            </div>
          </RoleBasedComponent>
        </div>

        <!-- 优质品牌展示 -->
        <div class="sidebar-brands-section">
          <div class="sidebar-section-title">
            <i class="el-icon-medal"></i>
            优质品牌
          </div>
          <div class="brands-list">
            <div
              v-for="brand in featuredBrands"
              :key="brand.id"
              class="brand-item"
              @click="handleBrandClick(brand)"
            >
              <img :src="brand.logo" :alt="brand.name" class="brand-logo" />
              <div class="brand-info">
                <h5 class="brand-name">{{ brand.name }}</h5>
                <p class="brand-desc">{{ brand.description }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户评价展示 -->
        <div class="sidebar-reviews-section">
          <div class="sidebar-section-title">
            <i class="el-icon-chat-dot-round"></i>
            用户评价
          </div>
          <div class="reviews-list">
            <div
              v-for="review in featuredReviews"
              :key="review.id"
              class="review-item"
            >
              <div class="review-header">
                <img :src="review.userAvatar" :alt="review.userName" class="user-avatar" />
                <div class="user-info">
                  <h6 class="user-name">{{ review.userName }}</h6>
                  <el-rate :value="review.rating" disabled size="small"></el-rate>
                </div>
              </div>
              <p class="review-content">{{ review.content }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 商品详情对话框 -->
    <el-dialog
      title="商品详情"
      :visible.sync="productDetailVisible"
      width="900px"
      :modal="true"
      :append-to-body="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :z-index="3000"
      @close="productDetailVisible = false"
      class="product-detail-dialog"
      custom-class="optimized-product-detail-dialog"
    >
      <div v-if="selectedProduct" class="product-detail-content">
        <!-- 商品主要信息区域 -->
        <div class="product-main-section">
          <el-row :gutter="32">
            <el-col :xl="10" :lg="10" :md="12" :sm="24" :xs="24">
              <!-- 商品图片区域 -->
              <div class="product-image-section">
                <div class="main-image-container">
                  <el-image
                    :src="selectedProduct.image"
                    fit="cover"
                    class="main-product-image"
                    :preview-src-list="[selectedProduct.image]"
                  >
                    <div slot="error" class="image-error">
                      <i class="el-icon-picture-outline"></i>
                      <span>暂无图片</span>
                    </div>
                  </el-image>

                  <!-- 商品标签 -->
                  <div class="product-badges">
                    <el-tag v-if="selectedProduct.hasTraceability || selectedProduct.has_traceability"
                            type="success" size="small" class="badge-item">
                      <i class="el-icon-connection"></i>
                      可溯源
                    </el-tag>
                    <el-tag v-if="selectedProduct.sourceType === 'admin_direct'"
                            type="primary" size="small" class="badge-item">
                      <i class="el-icon-s-check"></i>
                      直购
                    </el-tag>
                    <el-tag v-if="selectedProduct.isNew || selectedProduct.is_new"
                            type="warning" size="small" class="badge-item">
                      <i class="el-icon-star-on"></i>
                      新品
                    </el-tag>
                    <el-tag v-if="selectedProduct.isHot || selectedProduct.is_hot"
                            type="danger" size="small" class="badge-item">
                      <i class="el-icon-hot-water"></i>
                      热销
                    </el-tag>
                  </div>
                </div>
              </div>
            </el-col>

            <el-col :xl="14" :lg="14" :md="12" :sm="24" :xs="24">
              <!-- 商品信息区域 -->
              <div class="product-info-section">
                <!-- 商品标题 -->
                <div class="product-header">
                  <h2 class="product-title">{{ selectedProduct.name }}</h2>
                  <div class="product-subtitle">
                    <span class="category-tag">{{ getCategoryLabel(selectedProduct.category) }}</span>
                  </div>
                </div>

                <!-- 商品描述 -->
                <div class="product-description-card">
                  <p class="product-description">{{ selectedProduct.description || '暂无商品描述' }}</p>
                </div>

                <!-- 商品详细信息 -->
                <div class="product-details-grid">
                  <div class="detail-card price-card">
                    <div class="card-icon">
                      <i class="el-icon-price-tag"></i>
                    </div>
                    <div class="card-content">
                      <div class="card-label">价格</div>
                      <div class="card-value price-value">
                        ¥{{ selectedProduct.price }}
                        <span class="unit">/ {{ selectedProduct.unit || '件' }}</span>
                      </div>
                      <div v-if="selectedProduct.originalPrice && selectedProduct.originalPrice > selectedProduct.price"
                           class="original-price">
                        原价：¥{{ selectedProduct.originalPrice }}
                      </div>
                    </div>
                  </div>

                  <div class="detail-card stock-card">
                    <div class="card-icon">
                      <i class="el-icon-goods"></i>
                    </div>
                    <div class="card-content">
                      <div class="card-label">库存</div>
                      <div class="card-value stock-value">
                        {{ selectedProduct.stock }}{{ selectedProduct.unit || '件' }}
                      </div>
                      <div class="stock-status" :class="getStockStatusClass(selectedProduct.stock)">
                        {{ getStockStatusText(selectedProduct.stock) }}
                      </div>
                    </div>
                  </div>

                  <div class="detail-card rating-card">
                    <div class="card-icon">
                      <i class="el-icon-star-on"></i>
                    </div>
                    <div class="card-content">
                      <div class="card-label">评分</div>
                      <div class="card-value rating-value">
                        <el-rate :value="selectedProduct.rating || 5" disabled show-score text-color="#ff9900" />
                      </div>
                    </div>
                  </div>

                  <div v-if="selectedProduct.brand" class="detail-card brand-card">
                    <div class="card-icon">
                      <i class="el-icon-medal"></i>
                    </div>
                    <div class="card-content">
                      <div class="card-label">品牌</div>
                      <div class="card-value">{{ selectedProduct.brand }}</div>
                    </div>
                  </div>

                  <div v-if="selectedProduct.origin" class="detail-card origin-card">
                    <div class="card-icon">
                      <i class="el-icon-location"></i>
                    </div>
                    <div class="card-content">
                      <div class="card-label">产地</div>
                      <div class="card-value">{{ selectedProduct.origin }}</div>
                    </div>
                  </div>

                  <div v-if="selectedProduct.traceCode || selectedProduct.trace_code" class="detail-card trace-card">
                    <div class="card-icon">
                      <i class="el-icon-connection"></i>
                    </div>
                    <div class="card-content">
                      <div class="card-label">溯源码</div>
                      <div class="card-value trace-code">{{ selectedProduct.traceCode || selectedProduct.trace_code }}</div>
                      <el-button size="mini" type="text" @click="viewTraceability(selectedProduct)">
                        查看溯源
                      </el-button>
                    </div>
                  </div>
                </div>

                <!-- 购买操作区域 -->
                <div class="purchase-section">
                  <div class="quantity-selector">
                    <span class="quantity-label">数量：</span>
                    <el-input-number
                      v-model="purchaseQuantity"
                      :min="1"
                      :max="selectedProduct.stock"
                      size="medium"
                      controls-position="right"
                    />
                  </div>

                  <div class="action-buttons">
                    <el-button
                      type="primary"
                      size="medium"
                      icon="el-icon-shopping-cart-2"
                      @click="addToCart(selectedProduct)"
                      :disabled="selectedProduct.stock <= 0"
                    >
                      加入购物车
                    </el-button>
                    <el-button
                      type="danger"
                      size="medium"
                      icon="el-icon-check"
                      @click="buyNow(selectedProduct)"
                      :disabled="selectedProduct.stock <= 0"
                    >
                      立即购买
                    </el-button>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 商品评价 -->
        <div style="margin-top: 30px;">
          <ProductEvaluation
            :product-id="selectedProduct.id"
            :can-write-review="!!userRole"
          />
        </div>
      </div>

      <!-- 对话框底部按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="productDetailVisible = false" size="medium">关闭</el-button>
      </div>
    </el-dialog>
    
    <!-- 发布商品对话框 -->
    <el-dialog 
      title="发布商品" 
      :visible.sync="publishDialogVisible" 
      width="800px"
      @close="publishDialogVisible = false"
    >
      <div class="publish-product-content">
        <p>请使用"我的店铺"功能来发布和管理您的商品。</p>
        <el-button type="primary" @click="myProductsVisible = true; publishDialogVisible = false">
          前往我的店铺
        </el-button>
      </div>
    </el-dialog>
    
    <!-- 我的店铺对话框 -->
    <el-dialog
      title="我的店铺"
      :visible.sync="myProductsVisible"
      width="90%"
      top="5vh"
      :modal="true"
      :append-to-body="true"
      :close-on-click-modal="true"
      :z-index="3000"
      @close="myProductsVisible = false"
    >
      <MyProducts />
    </el-dialog>
    
    <!-- 商家申请对话框 -->
    <el-dialog
      title="申请成为商家"
      :visible.sync="sellerApplicationVisible"
      width="90%"
      top="5vh"
      @close="sellerApplicationVisible = false"
    >
      <SellerApplication @application-submitted="handleSellerApplication" />
    </el-dialog>

    <!-- 验证工具对话框 (开发环境) -->
    <el-dialog
      v-if="isDevelopment"
      title="角色差异化界面验证工具"
      :visible.sync="validationToolVisible"
      width="90%"
      top="5vh"
      :close-on-click-modal="false">
      <RoleInterfaceValidator />
    </el-dialog>
    
    <!-- 价格走势图对话框 -->
    <el-dialog
      title="价格走势"
      :visible.sync="priceChartVisible"
      width="800px"
      @close="priceChartVisible = false"
    >
      <PriceTrendChart v-if="selectedProduct" :product="selectedProduct" />
    </el-dialog>

    <!-- 溯源查询对话框 -->
    <el-dialog
      title="农产品溯源查询"
      :visible.sync="traceQueryDialogVisible"
      width="600px"
      :modal="true"
      :append-to-body="true"
      :close-on-click-modal="true"
      :z-index="3000"
      @close="traceQueryDialogVisible = false"
    >
      <div class="trace-query-content">
        <el-form :model="traceQueryForm" label-width="100px">
          <el-form-item label="查询方式">
            <el-radio-group v-model="traceQueryForm.queryType">
              <el-radio label="code">输入溯源码</el-radio>
              <el-radio label="qr">扫描二维码</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="traceQueryForm.queryType === 'code'" label="溯源码">
            <el-input
              v-model="traceQueryForm.traceCode"
              placeholder="请输入商品溯源码"
              clearable
              @keyup.enter.native="executeTraceQuery"
            >
              <el-button slot="append" @click="executeTraceQuery" :loading="traceQueryLoading">
                查询
              </el-button>
            </el-input>
          </el-form-item>

          <el-form-item v-if="traceQueryForm.queryType === 'qr'" label="二维码">
            <div class="qr-scan-area">
              <el-button type="primary" @click="scanQRCode">
                <i class="el-icon-camera"></i>
                扫描二维码
              </el-button>
              <p class="scan-tip">点击按钮启动摄像头扫描商品二维码</p>
            </div>
          </el-form-item>
        </el-form>

        <!-- 查询结果 -->
        <div v-if="traceQueryResult" class="trace-result">
          <el-divider content-position="left">查询结果</el-divider>

          <div class="result-card">
            <div class="product-info">
              <div class="product-image">
                <el-image
                  :src="getImageUrl(traceQueryResult.productImage)"
                  fit="cover"
                  style="width: 80px; height: 80px; border-radius: 8px;"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
              <div class="product-details">
                <h4>{{ traceQueryResult.productName }}</h4>
                <p class="trace-code">溯源码：{{ traceQueryResult.traceCode }}</p>
                <p class="producer">生产者：{{ traceQueryResult.producerName || '-' }}</p>
                <p class="origin">产地：{{ traceQueryResult.origin || '-' }}</p>
              </div>
            </div>

            <div class="trace-actions">
              <el-button type="primary" @click="viewTraceabilityDetail(traceQueryResult.traceCode)">
                查看详细溯源信息
              </el-button>
            </div>
          </div>
        </div>

        <!-- 查询说明 -->
        <div class="query-tips">
          <el-alert
            title="溯源查询说明"
            type="info"
            :closable="false"
          >
            <div slot="default">
              <p>• 输入商品包装上的溯源码或扫描二维码即可查询</p>
              <p>• 可查询商品的生产信息、质量检测、物流轨迹等</p>
              <p>• 确保购买到安全、优质的农产品</p>
            </div>
          </el-alert>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import { productService } from '@/api/product'
import { getProducts, getHotProducts, getNewProducts, getFeaturedProducts } from '@/api/products'
import { getAllCategories, getCategoryTree, getHotCategories, getCategoriesWithProductCount } from '@/api/categories'
import { getSellerProducts } from '@/api/seller'
import { getUserInfo, getUserRole, isLoggedIn, isAdmin, isSeller, normalizeRole, refreshUserPermissions, onPermissionsChange, checkSellerApplicationStatus } from '@/utils/auth'
import request from '@/utils/request'
import * as shopAPI from '@/api/shop'

// 导入组件
import ProductCard from '@/components/shop/ProductCard.vue'
import ProductListItem from '@/components/shop/ProductListItem.vue'
import PriceTrendChart from '@/components/shop/PriceTrendChart.vue'
import SellerApplication from '@/components/shop/SellerApplication.vue'
import MyProducts from '@/components/shop/MyProducts.vue'

import SearchBar from '@/components/shop/SearchBar.vue'
import ProductEvaluation from '@/components/shop/ProductEvaluation.vue'
import ProductRecommendation from '@/components/shop/ProductRecommendation.vue'
import SellerApplicationButton from '@/components/shop/SellerApplicationButton.vue'
import RoleBasedComponent from '@/components/common/RoleBasedComponent.vue'

// 角色专用组件（将要创建）
import SellerQuickActionsCompact from '@/components/seller/SellerQuickActionsCompact.vue'
import ShopQuickAccess from '@/components/seller/ShopQuickAccess.vue'
import AdminStatsCompact from '@/components/admin/AdminStatsCompact.vue'
import AdminQuickActions from '@/components/admin/AdminQuickActions.vue'
import UserQuickActions from '@/components/user/UserQuickActions.vue'
import SellerApplicationStatus from '@/components/user/SellerApplicationStatus.vue'

// 调试工具组件
import RoleInterfaceValidator from '@/components/debug/RoleInterfaceValidator.vue'

export default {
  name: 'Shop',
  components: {
    ProductCard,
    ProductListItem,
    PriceTrendChart,
    SellerApplication,
    MyProducts,
    SearchBar,
    ProductEvaluation,
    ProductRecommendation,
    SellerApplicationButton,
    RoleBasedComponent,
    // 角色专用组件
    SellerQuickActionsCompact,
    ShopQuickAccess,
    AdminStatsCompact,
    AdminQuickActions,
    UserQuickActions,
    SellerApplicationStatus,
    // 调试工具
    RoleInterfaceValidator
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      // 加载用户的分类偏好
      vm.checkUserRole()
      vm.updateUserPreferences()
    })
  },
  // 路由更新时保留用户选择
  beforeRouteUpdate(to, from, next) {
    // 执行路由更新
    next()
  },
  data() {
    return {
      // 基础数据
      loading: false,
      products: [],
      categories: [],
      totalProducts: 0,
      totalSuppliers: 0,
      totalOrders: 0,
      currentPage: 1,
      pageSize: 12,
      
      // 搜索和筛选
      searchQuery: '',
      searchActive: false,
      showAdvancedSearch: false,
      advancedSearchActive: false, // 是否使用高级搜索
      searchKeyword: '',
      searchHistory: [],
      searchSuggestions: [],
      hotSearchTags: [],
      filters: {
        category: '',
        subCategories: [],
        priceRange: null,
        customPriceRange: null,
        location: '',
        sortBy: 'default',
        tags: []
      },
      
      // 高级搜索表单
      advancedSearchForm: {
        name: '',
        category: '',
        origin: '',
        priceRange: [],
        supplier: '',
        tags: [],
        rating: null,
        sortBy: 'default',
        hasStock: true
      },
      priceRange: [0, 1000],
      
      // 分类相关
      activeCategory: 'all',
      categoryActive: false,
      mainCategories: [],
      hotCategories: [],
      selectedSubCategories: [],
      selectedTags: [],
      selectedPriceRange: null,

      // 二级分类树相关
      expandedCategories: [], // 展开的主分类ID列表
      activeSubCategory: null, // 当前选中的子分类ID
      
      // 商品特性标签
      productTags: [
        { key: 'hot', label: '热门商品' },
        { key: 'new', label: '新品上市' },
        { key: 'organic', label: '有机认证' },
        { key: 'local', label: '本地特产' },
        { key: 'discount', label: '促销商品' },
        { key: 'highRating', label: '高评分' }
      ],
      
      // 价格区间
      priceRanges: [
        { key: 'under10', label: '10元以下', min: 0, max: 10 },
        { key: '10to30', label: '10-30元', min: 10, max: 30 },
        { key: '30to50', label: '30-50元', min: 30, max: 50 },
        { key: '50to100', label: '50-100元', min: 50, max: 100 },
        { key: 'over100', label: '100元以上', min: 100, max: null }
      ],
      
      // 视图模式
      viewMode: 'grid', // grid 或 list
      
      // 推荐商品
      hotProducts: [],
      latestProducts: [],
      hotProductsLoading: false,
      latestProductsLoading: false,

      // 新增内容模块数据（已移除：hotRecommendations, quickCategories, promotionBanners, agricultureNews）

      featuredBrands: [],

      featuredReviews: [],
      
      // 用户相关
      userRole: null,
      userInfo: null,
      userPreferences: {
        categories: [],
        priceRange: [0, 1000],
        location: '',
        tags: []
      },
      
      // 对话框状态
      productDetailVisible: false,
      publishDialogVisible: false,
      myProductsVisible: false,
      sellerApplicationVisible: false,
      validationToolVisible: false,
      priceChartVisible: false,
      traceQueryDialogVisible: false,
      
      // 选中的商品
      selectedProduct: {},
      purchaseQuantity: 1,
      
      // 我的店铺商品
      myProducts: [],
      myProductsLoading: false,
      
      // 图表相关
      chartProducts: [],
      minPrice: 0,
      maxPrice: 10000,

      // 溯源查询相关
      traceQueryForm: {
        traceCode: '',
        queryType: 'code' // code: 溯源码查询, qr: 二维码扫描
      },
      traceQueryResult: null,
      traceQueryLoading: false,

      // 路由守卫
      routeGuardUnwatch: null
    }
  },
  computed: {
    ...mapState('user', ['user']),
    ...mapState('cart', ['cartItems']),

    // 开发环境检查
    isDevelopment() {
      return process.env.NODE_ENV === 'development'
    },

    // 可溯源商品数量
    traceableProductsCount() {
      return this.products.filter(product =>
        (product.hasTraceability === 1 || product.has_traceability === 1) &&
        (product.traceCode || product.trace_code)
      ).length
    },

    isLoggedIn() {
      const userInfo = getUserInfo()
      const result = !!(userInfo && userInfo.id && !userInfo.isGuest)

      console.log('🔍 Shop.vue isLoggedIn计算属性 (仅用户信息):', {
        hasUserInfo: !!userInfo,
        hasUserId: !!(userInfo && userInfo.id),
        isGuest: userInfo?.isGuest,
        result: result,
        authFunctionResult: isLoggedIn(),
        userInfo: userInfo
      })

      return result
    },
    isSeller() {
      if (!this.isLoggedIn || !this.userInfo) return false

      const sellerRoles = ['ROLE_SELLER', 'seller', 'SELLER', 'ROLE_seller']
      const result = sellerRoles.includes(this.userRole) ||
                     sellerRoles.includes(this.userInfo.role) ||
                     this.userInfo.user_type === 'seller'

      console.log('🔍 Shop.vue isSeller计算属性:', {
        userRole: this.userRole,
        userInfoRole: this.userInfo?.role,
        userType: this.userInfo?.user_type,
        result: result,
        authFunctionResult: isSeller()
      })

      return result
    },
    isAdmin() {
      if (!this.isLoggedIn || !this.userInfo) return false

      const adminRoles = ['ROLE_ADMIN', 'admin', 'ADMIN', 'ROLE_admin']
      const result = adminRoles.includes(this.userRole) ||
                     adminRoles.includes(this.userInfo.role) ||
                     this.userInfo.user_type === 'admin'

      console.log('🔍 Shop.vue isAdmin计算属性:', {
        userRole: this.userRole,
        userInfoRole: this.userInfo?.role,
        userType: this.userInfo?.user_type,
        result: result,
        authFunctionResult: isAdmin()
      })

      return result
    },
    // 当前选中主分类的子分类
    currentSubCategories() {
      if (!this.activeCategory || this.activeCategory === 'all') {
        return []
      }
      return this.categories.filter(cat => cat.parent_id === parseInt(this.activeCategory))
    }
  },
  watch: {
    // 监听登录状态变化
    isLoggedIn: {
      handler(newVal, oldVal) {
        console.log('🔄 登录状态变化:', { from: oldVal, to: newVal })
        if (newVal !== oldVal) {
          this.checkUserRole()
        }
      },
      immediate: true
    },
    // 监听用户角色变化
    userRole: {
      handler(newVal, oldVal) {
        console.log('🔄 用户角色变化:', { from: oldVal, to: newVal })
      }
    }
  },
  created() {
    // 确保在下一个tick中检查用户角色，让响应式系统完全建立
    this.$nextTick(() => {
      this.checkUserRole()
    })
    this.updateUserPreferences()
    this.init()

    // 监听权限变化
    onPermissionsChange((userInfo) => {
      this.userInfo = userInfo
      this.userRole = normalizeRole(userInfo?.role)
      this.$message.success('权限已更新')

      // 如果用户变成销售者，加载商品数据
      if (this.isSeller) {
        this.loadMyProducts()
      }
    })

    // 定期检查销售者申请状态
    this.checkSellerApplicationStatusPeriodically()

    // 如果是销售者，加载商品数据
    if (this.isSeller) {
      this.loadMyProducts()
    }
  },
  mounted() {
    // 在组件挂载后，确保分类导航正确显示
    this.$nextTick(() => {
      // 尝试从用户偏好中恢复最近浏览的分类
      this.loadUserCategoryPreference()
    })

    // 加载页面数据
    this.loadPageData()

    // 页面挂载后再次检查登录状态，确保状态同步
    this.$nextTick(() => {
      console.log('📱 页面挂载完成，重新检查登录状态')
      this.checkUserRole()

      // 调试组件显示状态
      this.$nextTick(() => {
        console.log('🎯 角色差异化组件显示状态检查:', {
          roleBasedArea: document.querySelector('.role-based-function-area'),
          sellerArea: document.querySelector('.seller-exclusive-area'),
          adminArea: document.querySelector('.admin-exclusive-area'),
          userArea: document.querySelector('.user-exclusive-area'),
          baseComponent: document.querySelector('.base-role-component'),
          isLoggedIn: this.isLoggedIn,
          isSeller: this.isSeller,
          isAdmin: this.isAdmin,
          userRole: this.userRole,
          getUserRoleResult: getUserRole(this.userInfo)
        })
      })
    })

    // 监听localStorage变化，确保多标签页状态同步
    window.addEventListener('storage', this.handleStorageChange)

    // 添加路由守卫，防止意外跳转到登录页面
    this.setupRouteGuard()
  },

  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener('storage', this.handleStorageChange)

    // 清理路由守卫
    if (this.routeGuardUnwatch) {
      this.routeGuardUnwatch()
    }
  },
  methods: {
    ...mapActions('cart', ['addProductToCart']),
    ...mapActions('user', ['fetchUserInfo']),
    
    // 初始化
    async init() {
      await this.loadCategories()
      await this.loadProducts()
      await this.loadRecommendations()
      await this.loadStatistics()
      await this.loadHotSearchTags()
    },
    
    // 加载热门搜索标签
    async loadHotSearchTags() {
      try {
        console.log('🔥 开始加载热门搜索标签...');

        // 从API获取热门搜索关键词
        const response = await request({
          url: '/api/mall/search/hot-keywords',
          method: 'get',
          params: { limit: 8 }
        });

        console.log('🔥 热门搜索API响应:', response);

        if (response && response.code === 200 && response.data && response.data.length > 0) {
          this.hotSearchTags = response.data;
          console.log('✅ 成功从API获取热门搜索标签:', this.hotSearchTags);
        } else {
          console.warn('⚠️ API返回数据为空，使用默认热门搜索标签');
          this.hotSearchTags = [
            '有机蔬菜', '新鲜水果', '五常大米', '绿色食品',
            '农家菜', '特色农产品', '当季水果', '无公害蔬菜'
          ];
        }
      } catch (error) {
        console.error('❌ 获取热门搜索标签失败:', error);
        // 发生错误时使用默认标签
        this.hotSearchTags = [
          '有机蔬菜', '新鲜水果', '五常大米', '绿色食品',
          '农家菜', '特色农产品', '当季水果', '无公害蔬菜'
        ];
      }
    },
    
    // 检查用户角色
    checkUserRole() {
      const userInfo = getUserInfo()
      const loggedIn = isLoggedIn()

      console.log('🔍 登录状态检查 (无token验证):', {
        userInfo: userInfo,
        hasUserId: !!(userInfo && userInfo.id),
        isGuest: userInfo?.isGuest,
        isLoggedIn: loggedIn,
        authFunctionResult: isLoggedIn()
      })

      // 验证用户数据完整性
      if (userInfo && !userInfo.id) {
        console.warn('⚠️ 用户数据不完整，缺少ID字段:', userInfo)
        // 清除无效的用户数据
        localStorage.removeItem('sfap_user')
        this.userInfo = null
        this.userRole = null
        return
      }

      if (loggedIn && userInfo) {
        this.userInfo = userInfo
        this.userRole = normalizeRole(userInfo?.role)

        // 等待响应式更新完成后再输出调试信息
        this.$nextTick(() => {
          console.log('✅ 用户已登录 (响应式更新后):', {
            username: userInfo?.username,
            role: userInfo?.role,
            userType: userInfo?.user_type,
            normalizedRole: this.userRole,
            计算属性结果: {
              isSeller: this.isSeller,
              isAdmin: this.isAdmin,
              isLoggedIn: this.isLoggedIn
            },
            getUserRoleResult: getUserRole(userInfo),
            传递给RoleBasedComponent的值: {
              externalIsLoggedIn: this.isLoggedIn,
              externalIsSeller: this.isSeller,
              externalIsAdmin: this.isAdmin,
              externalUserInfo: this.userInfo
            }
          })

          // 检查角色差异化组件的显示状态
          this.$nextTick(() => {
            console.log('🎯 角色差异化组件检查:', {
              sellerArea: document.querySelector('.seller-exclusive-area'),
              adminArea: document.querySelector('.admin-exclusive-area'),
              userArea: document.querySelector('.user-exclusive-area'),
              baseComponent: document.querySelector('.base-role-component')
            })
          })
        })
      } else {
        this.userRole = null
        this.userInfo = null
        console.log('❌ 用户未登录或用户信息缺失')
      }
    },

    // 设置路由守卫，防止意外跳转到登录页面
    setupRouteGuard() {
      // 监听路由变化
      this.routeGuardUnwatch = this.$watch('$route', (to, from) => {
        console.log('🛡️ Shop页面路由守卫检测到路由变化:', {
          from: from.fullPath,
          to: to.fullPath,
          isGoingToLogin: to.path === '/login'
        })

        // 如果要跳转到登录页面，但用户实际上已经登录了，阻止跳转
        if (to.path === '/login' && this.isLoggedIn) {
          console.log('🛡️ 阻止已登录用户跳转到登录页面')
          this.$message.warning('您已经登录，无需重复登录')

          // 阻止跳转，保持在当前页面
          this.$nextTick(() => {
            if (this.$route.path === '/login') {
              this.$router.replace('/shop')
            }
          })
          return false
        }
      })
    },

    // 处理localStorage变化事件
    handleStorageChange(event) {
      console.log('🔄 localStorage变化事件:', event)

      // 检查是否是登录相关的变化
      if (event.key === 'token' || event.key === 'sfap_user') {
        console.log('🔄 检测到登录状态相关的localStorage变化，重新检查用户角色')
        this.$nextTick(() => {
          this.checkUserRole()
        })
      }
    },

    // 定期检查销售者申请状态
    async checkSellerApplicationStatusPeriodically() {
      if (!isLoggedIn() || this.userRole === 'ROLE_SELLER') {
        return // 已经是销售者或未登录，无需检查
      }

      try {
        const application = await checkSellerApplicationStatus()
        if (application && application.status === 1) {
          // 申请已通过，刷新页面权限
          this.$message.success('恭喜！您的销售者申请已通过，现在可以发布商品了！')
          this.checkUserRole() // 重新检查角色
        }
      } catch (error) {
        console.error('检查销售者申请状态失败:', error)
      }
    },

    // 手动刷新用户权限
    async refreshPermissions() {
      try {
        const success = await refreshUserPermissions()
        if (success) {
          this.checkUserRole()
          this.$message.success('权限状态已刷新')
        } else {
          this.$message.warning('权限刷新失败')
        }
      } catch (error) {
        console.error('刷新权限失败:', error)
        this.$message.error('权限刷新失败')
      }
    },
    
    // 加载分类
    async loadCategories() {
      try {
        console.log('开始加载分类数据...')

        // 优先尝试获取带产品数量的分类列表
        try {
          const response = await getCategoriesWithProductCount()
          console.log('获取带产品数量的分类数据:', response)
          console.log('分类API响应详情 - code:', response?.code, 'data:', response?.data)

          // 详细分析响应数据结构
          if (response?.data) {
            console.log('分类数据类型:', typeof response.data)
            console.log('分类数据是否为数组:', Array.isArray(response.data))
            if (Array.isArray(response.data)) {
              console.log('分类数据长度:', response.data.length)
              console.log('前3个分类数据:', response.data.slice(0, 3))
            } else if (response.data.mainCategories) {
              console.log('主分类数据:', response.data.mainCategories)
              console.log('子分类数据:', response.data.subCategories)
            }
          }

          if (response && (response.code === 200 || response.code === 0) && response.data) {
            console.log('✅ 成功获取分类API数据，开始处理...')

            // 处理不同的数据结构
            if (response.data.mainCategories) {
              // 如果返回的是包含mainCategories的对象
              this.categories = [...response.data.mainCategories, ...(response.data.subCategories || [])]
              this.mainCategories = this.optimizeCategoryDisplay(response.data.mainCategories)
              console.log('使用mainCategories结构，主分类数量:', this.mainCategories.length)
            } else if (Array.isArray(response.data)) {
              // 如果返回的是数组
              this.categories = response.data
              const rawMainCategories = this.categories.filter(cat => cat.level === 1 || (!cat.parentId || cat.parentId === 0))
              this.mainCategories = this.optimizeCategoryDisplay(rawMainCategories)
              console.log('使用数组结构，总分类数量:', this.categories.length, '主分类数量:', this.mainCategories.length)
            } else {
              // 其他情况，尝试直接使用data
              this.categories = response.data
              const rawMainCategories = this.categories.filter(cat => cat.level === 1 || (!cat.parentId || cat.parentId === 0))
              this.mainCategories = this.optimizeCategoryDisplay(rawMainCategories)
              console.log('使用直接data结构，总分类数量:', this.categories.length)
            }

            // 输出分类ID映射信息用于调试
            console.log('🔍 分类ID映射检查:')
            this.mainCategories.forEach(cat => {
              console.log(`分类ID: ${cat.id}, 名称: ${cat.name}, 产品数量: ${cat.productCount || '未知'}`)
            })

            // 缓存分类数据
            const cacheData = {
              categories: this.categories,
              mainCategories: this.mainCategories,
              timestamp: Date.now()
            }
            localStorage.setItem('shop_categories', JSON.stringify(cacheData))

            console.log(`✅ 成功加载 ${this.categories.length} 个分类，其中主分类 ${this.mainCategories.length} 个`)

            // 验证分类数据完整性
            this.validateCategoryData()
            return
          }
        } catch (error) {
          console.warn('获取带产品数量的分类失败，尝试获取分类树:', error)
        }

        // 回退到获取分类树
        try {
          const response = await getCategoryTree()
          console.log('获取分类树数据:', response)

          if (response && response.code === 200 && response.data) {
            this.categories = this.flattenCategoryTree(response.data)
            this.mainCategories = this.categories.filter(cat => cat.level === 1)

            // 缓存分类数据
            const cacheData = {
              categories: this.categories,
              mainCategories: this.mainCategories,
              timestamp: Date.now()
            }
            localStorage.setItem('shop_categories', JSON.stringify(cacheData))

            console.log(`从分类树加载 ${this.categories.length} 个分类`)
            this.validateCategoryData()
            return
          }
        } catch (error) {
          console.warn('获取分类树失败，尝试获取普通分类列表:', error)
        }

        // 最后回退到普通分类列表
        const response = await getAllCategories()
        console.log('获取普通分类列表:', response)

        if (response && response.code === 200 && response.data) {
          this.categories = response.data
          this.mainCategories = this.categories.filter(cat => cat.level === 1)

          // 缓存分类数据
          const cacheData = {
            categories: this.categories,
            mainCategories: this.mainCategories,
            timestamp: Date.now()
          }
          localStorage.setItem('shop_categories', JSON.stringify(cacheData))

          console.log(`从普通分类列表加载 ${this.categories.length} 个分类`)
          this.validateCategoryData()
        } else {
          throw new Error('所有分类API都失败了')
        }

      } catch (error) {
        console.error('加载分类失败:', error)

        // 显示详细的错误信息用于调试
        console.log('分类API调用失败，错误详情:', error)

        // 尝试从缓存加载
        try {
          const cached = localStorage.getItem('shop_categories')
          if (cached) {
            const cacheData = JSON.parse(cached)
            this.categories = cacheData.categories || []
            this.mainCategories = cacheData.mainCategories || []
            console.log('从缓存加载分类数据')
            this.$message.info('已从缓存加载分类数据')
            this.validateCategoryData()
          } else {
            // 如果缓存也没有数据，显示错误信息
            console.error('无法加载分类数据：API和缓存都失败')
            this.$message.error('无法加载分类数据，请检查网络连接或联系管理员')
            this.categories = []
            this.mainCategories = []
          }
        } catch (cacheError) {
          console.error('缓存加载也失败了:', cacheError)
          this.$message.error('数据加载失败，请刷新页面重试')
          this.categories = []
          this.mainCategories = []
        }
      }

      // 加载热门分类
      await this.loadHotCategories()
    },

    // 优化分类显示逻辑
    optimizeCategoryDisplay(categories) {
      if (!Array.isArray(categories)) {
        return []
      }

      // 1. 智能排序：优先显示有商品的热门分类
      const sortedCategories = categories.sort((a, b) => {
        // 首先按是否有商品排序
        const aHasProducts = (a.productCount || a.product_count || 0) > 0
        const bHasProducts = (b.productCount || b.product_count || 0) > 0

        if (aHasProducts !== bHasProducts) {
          return bHasProducts ? 1 : -1 // 有商品的排在前面
        }

        // 然后按热门程度排序
        const aIsHot = a.isHot || a.is_hot || 0
        const bIsHot = b.isHot || b.is_hot || 0

        if (aIsHot !== bIsHot) {
          return bIsHot - aIsHot // 热门的排在前面
        }

        // 最后按排序字段排序
        const aSortOrder = a.sortOrder || a.sort_order || 999
        const bSortOrder = b.sortOrder || b.sort_order || 999

        return aSortOrder - bSortOrder
      })

      // 2. 标准化数据格式
      return sortedCategories.map(category => ({
        ...category,
        // 统一字段名称
        productCount: category.productCount || category.product_count || 0,
        isHot: category.isHot || category.is_hot || 0,
        sortOrder: category.sortOrder || category.sort_order || 999,
        // 确保图标存在
        icon: category.icon || this.getDefaultCategoryIcon(category.name)
      }))
    },

    // 获取默认分类图标
    getDefaultCategoryIcon(categoryName) {
      const iconMap = {
        '蔬菜类': 'icon-vegetables',
        '水果类': 'icon-fruits',
        '粮食作物': 'icon-grains',
        '畜牧产品': 'icon-livestock',
        '水产品': 'icon-seafood',
        '农副产品': 'icon-agricultural',
        '种子种苗': 'icon-seeds',
        '苗木花草': 'icon-plants',
        '中药材': 'icon-herbs',
        '农资农机': 'icon-machinery'
      }
      return iconMap[categoryName] || 'el-icon-goods'
    },

    // 验证分类数据完整性
    validateCategoryData() {
      console.log('🔍 验证分类数据完整性:')
      console.log('- 总分类数量:', this.categories.length)
      console.log('- 主分类数量:', this.mainCategories.length)

      // 检查数据完整性
      if (this.categories.length === 0) {
        console.warn('⚠️ 分类数据为空，可能导致商品查询失败')
        console.log('💡 建议：检查分类API是否正常返回数据')
      }

      // 输出分类ID列表
      if (this.mainCategories.length > 0) {
        console.log('- 可用分类ID:', this.mainCategories.map(cat => `${cat.id}(${cat.name})`).join(', '))
      }

      // 输出优化后的分类排序
      console.log('🎯 优化后的分类显示顺序:')
      this.mainCategories.forEach((cat, index) => {
        console.log(`${index + 1}. ${cat.name} (商品数: ${cat.productCount}, 热门: ${cat.isHot ? '是' : '否'})`)
      })
    },
    
    // 加载热门分类
    async loadHotCategories() {
      try {
        const response = await getHotCategories(8)
        if (response && response.data) {
          this.hotCategories = response.data
        } else {
          // 如果API不可用，使用主分类中的热门标记或前8个
          this.hotCategories = this.mainCategories
            .filter(cat => cat.isHot)
            .slice(0, 8)
          
          // 如果没有热门标记的分类，则取前8个
          if (this.hotCategories.length === 0) {
            this.hotCategories = this.mainCategories.slice(0, 8)
          }
        }
      } catch (error) {
        console.error('加载热门分类失败:', error)
        this.hotCategories = this.mainCategories.slice(0, 8)
      }
    },
    
    // 将分类树扁平化为列表
    flattenCategoryTree(categoryTree) {
      const result = []
      
      const flatten = (categories, parentId = 0) => {
        categories.forEach(category => {
          // 创建一个新对象，避免引用问题
          const flatCategory = { ...category }
          
          // 设置父分类ID
          if (parentId !== 0) {
            flatCategory.parent_id = parentId
          } else {
            flatCategory.parent_id = 0
          }
          
          // 设置层级
          if (!flatCategory.level) {
            flatCategory.level = parentId === 0 ? 1 : 2
          }
          
          // 保存子分类
          const children = flatCategory.children || []
          delete flatCategory.children
          
          // 添加到结果列表
          result.push(flatCategory)
          
          // 递归处理子分类
          if (children.length > 0) {
            flatten(children, flatCategory.id)
          }
        })
      }
      
      flatten(categoryTree)
      return result
    },
    
    // 优化排序参数
    optimizeSortBy(sortBy) {
      const sortMap = {
        'default': 'comprehensive', // 综合排序
        'new_first': 'new_first', // 新品优先
        'time_desc': 'created_at_desc', // 最新发布
        'price_asc': 'price_asc', // 价格从低到高
        'price_desc': 'price_desc', // 价格从高到低
        'sales_desc': 'sales_desc', // 销量优先
        'rating_desc': 'rating_desc', // 评分优先
        'traceable_first': 'traceable_first' // 溯源优先
      }

      return sortMap[sortBy] || 'comprehensive'
    },

    // 客户端排序优化（作为后端排序的补充）
    optimizeProductsSort(products) {
      if (!Array.isArray(products) || products.length === 0) {
        return products
      }

      const sortBy = this.filters.sortBy

      // 根据排序类型进行客户端优化
      switch (sortBy) {
        case 'new_first':
          // 新品优先：is_new=1的商品排在前面，然后按创建时间排序
          return products.sort((a, b) => {
            const aIsNew = a.isNew || a.is_new || 0
            const bIsNew = b.isNew || b.is_new || 0

            if (aIsNew !== bIsNew) {
              return bIsNew - aIsNew // 新品排在前面
            }

            // 新品状态相同时，按创建时间排序
            return new Date(b.createdAt || b.created_at) - new Date(a.createdAt || a.created_at)
          })

        case 'traceable_first':
          // 溯源优先：有溯源的商品排在前面
          return products.sort((a, b) => {
            const aHasTrace = (a.hasTraceability || a.has_traceability) && (a.traceCode || a.trace_code)
            const bHasTrace = (b.hasTraceability || b.has_traceability) && (b.traceCode || b.trace_code)

            if (aHasTrace !== bHasTrace) {
              return bHasTrace ? 1 : -1 // 有溯源的排在前面
            }

            // 溯源状态相同时，按创建时间排序
            return new Date(b.createdAt || b.created_at) - new Date(a.createdAt || a.created_at)
          })

        default:
          return products
      }
    },

    // 加载商品列表
    async loadProducts() {
      this.loading = true;
      console.log('开始加载商品列表');
      
      // 构建查询参数
      const params = {
        page: this.currentPage,
        size: this.pageSize,
        categoryId: this.filters.category || null,
        subCategoryIds: this.filters.subCategories && this.filters.subCategories.length > 0 ? this.filters.subCategories.join(',') : null,
        keyword: this.searchQuery,
        minPrice: this.filters.priceRange ? this.filters.priceRange.min : (this.priceRange[0] > 0 ? this.priceRange[0] : null),
        maxPrice: this.filters.priceRange ? this.filters.priceRange.max : (this.priceRange[1] < this.maxPrice ? this.priceRange[1] : null),
        sortBy: this.optimizeSortBy(this.filters.sortBy),
        sortOrder: this.filters.sortOrder
      };
      
      // 添加高级搜索参数
      if (this.advancedSearchActive) {
        // 添加产地
        if (this.filters.location) {
          params.origin = this.filters.location;
        }
        
        // 添加供应商
        if (this.filters.supplier) {
          params.supplier = this.filters.supplier;
        }
        
        // 添加标签
        if (this.filters.tags && this.filters.tags.length > 0) {
          params.tags = this.filters.tags.join(',');
        }
        
        // 添加评分
        if (this.filters.minRating) {
          params.minRating = this.filters.minRating;
        }
        
        // 添加库存状态
        if (this.filters.stockStatus && this.filters.stockStatus !== 'all') {
          params.stockStatus = this.filters.stockStatus;
        }
      }
      
      console.log('商品查询参数:', params);

      // 🔍 分类ID调试信息
      if (params.categoryId) {
        console.log('🔍 分类查询调试信息:')
        console.log('- 查询的分类ID:', params.categoryId)

        // 查找对应的分类信息
        const selectedCategory = this.categories.find(cat => cat.id == params.categoryId)
        if (selectedCategory) {
          console.log('- 分类名称:', selectedCategory.name)
          console.log('- 分类级别:', selectedCategory.level)
          console.log('- 父分类ID:', selectedCategory.parentId || selectedCategory.parent_id)
          console.log('- 产品数量:', selectedCategory.productCount || '未知')
        } else {
          console.warn('⚠️ 警告：在分类列表中找不到ID为', params.categoryId, '的分类')
          console.log('当前可用的分类ID:', this.categories.map(cat => ({ id: cat.id, name: cat.name })))
        }
      }

      // 🔍 子分类ID调试信息
      if (params.subCategoryIds) {
        console.log('🔍 子分类查询调试信息:')
        console.log('- 查询的子分类ID字符串:', params.subCategoryIds)
        console.log('- 子分类ID数组:', params.subCategoryIds.split(','))
        
        // 验证每个子分类ID
        const subCategoryIdArray = params.subCategoryIds.split(',')
        subCategoryIdArray.forEach(subId => {
          const subCategory = this.categories.find(cat => cat.id == subId)
          if (subCategory) {
            console.log(`- 子分类 ${subId}: ${subCategory.name} (父分类: ${subCategory.parent_id})`)
          } else {
            console.warn(`⚠️ 找不到子分类ID ${subId}`)
          }
        })
      }

      try {
        // 直接使用API请求，不回退到模拟数据
        const response = await request({
          url: '/api/mall/products',
          method: 'get',
          params: params,
          timeout: 10000 // 增加超时时间
        });
        
        console.log('商品列表API响应:', response);

        // 🔍 详细分析API响应
        console.log('📊 API响应分析:')
        console.log('- 响应状态码:', response?.code)
        console.log('- 响应消息:', response?.message)
        console.log('- 数据类型:', typeof response?.data)
        console.log('- 数据结构:', response?.data)

        // 统一处理API响应格式（基于后端优化后的响应格式）
        if (response && (response.code === 200 || response.code === 0) && response.data) {
          const data = response.data;

          // 处理分页数据
          if (data.records) {
            this.products = data.records || [];
            this.totalProducts = data.total || 0;
          } else if (Array.isArray(data)) {
            this.products = data;
            this.totalProducts = data.length;
          } else {
            this.products = [];
            this.totalProducts = 0;
          }

          // 应用客户端排序优化
          this.products = this.optimizeProductsSort(this.products);

          console.log(`成功加载 ${this.products.length} 个商品，总计 ${this.totalProducts} 个`);
          console.log(`可溯源商品数量: ${this.traceableProductsCount}`);
        } else {
          console.warn('API响应格式异常:', response);
          this.products = [];
          this.totalProducts = 0;

          if (response && response.message) {
            this.$message.warning(response.message);
          } else {
            this.$message.error('获取商品列表失败');
          }
        }
      } catch (error) {
        console.error('加载商品失败:', error);
        this.products = [];
        this.totalProducts = 0;

        // 显示具体错误信息
        const errorMessage = error.response?.data?.message || error.message || '网络连接失败';
        this.$message.error(`加载商品失败: ${errorMessage}`);
      } finally {
        this.loading = false;
      }
    },
    
    // 加载推荐商品
    async loadRecommendations() {
      await Promise.all([
        this.loadHotProducts(),
        this.loadLatestProducts()
      ])
    },
    
    // 加载热门商品
    async loadHotProducts() {
      this.hotProductsLoading = true
      try {
        console.log('🔥 开始加载热门商品推荐...')

        // 优先尝试新的推荐系统API
        try {
          const response = await request({
            url: '/api/mall/recommendations/hot',
            method: 'get',
            params: { limit: 6 }
          })

          console.log('🔥 新推荐系统API响应:', response)

          if (response && response.code === 200 && response.data && response.data.length > 0) {
            this.hotProducts = response.data
            console.log('✅ 新推荐系统加载成功:', this.hotProducts.length, '个商品')
            return
          }
        } catch (newApiError) {
          console.warn('⚠️ 新推荐系统API失败，尝试备用方案:', newApiError.message)
        }

        // 备用方案：使用原有API
        console.log('🔄 使用原有热门商品API...')
        const fallbackResponse = await getHotProducts(6)

        if (fallbackResponse && fallbackResponse.data) {
          this.hotProducts = Array.isArray(fallbackResponse.data) ? fallbackResponse.data : fallbackResponse.data.records || []
          console.log('✅ 备用API加载成功:', this.hotProducts.length, '个商品')
        } else {
          console.warn('⚠️ 备用API也返回空数据')
          this.hotProducts = []
        }
      } catch (error) {
        console.error('❌ 加载热门商品完全失败:', error)
        this.hotProducts = []
      } finally {
        this.hotProductsLoading = false
      }
    },
    
    // 加载最新商品
    async loadLatestProducts() {
      this.latestProductsLoading = true
      try {
        console.log('🆕 开始加载新品推荐...')

        // 优先尝试新的推荐系统API
        try {
          const response = await request({
            url: '/api/mall/recommendations/new',
            method: 'get',
            params: { limit: 8 }
          })

          console.log('🆕 新推荐系统API响应:', response)

          if (response && response.code === 200 && response.data && response.data.length > 0) {
            this.latestProducts = response.data
            console.log('✅ 新推荐系统加载成功:', this.latestProducts.length, '个商品')
            return
          }
        } catch (newApiError) {
          console.warn('⚠️ 新推荐系统API失败，尝试备用方案:', newApiError.message)
        }

        // 备用方案1：使用getNewProducts API
        try {
          console.log('🔄 使用getNewProducts API...')
          const newProductsResponse = await getNewProducts(8)

          if (newProductsResponse && newProductsResponse.data) {
            this.latestProducts = Array.isArray(newProductsResponse.data) ? newProductsResponse.data : newProductsResponse.data.records || []
            console.log('✅ getNewProducts API加载成功:', this.latestProducts.length, '个商品')
            return
          }
        } catch (newProductsError) {
          console.warn('⚠️ getNewProducts API失败:', newProductsError.message)
        }

        // 备用方案2：使用通用产品API按时间排序
        console.log('🔄 使用通用产品API按时间排序...')
        const fallbackResponse = await getProducts({
          page: 1,
          size: 8,
          sortBy: 'time_desc'
        })

        if (fallbackResponse && fallbackResponse.data) {
          this.latestProducts = fallbackResponse.data.records || fallbackResponse.data || []
          console.log('✅ 备用API加载成功:', this.latestProducts.length, '个商品')
        } else {
          console.warn('⚠️ 所有API都返回空数据')
          this.latestProducts = []
        }
      } catch (error) {
        console.error('❌ 加载新品推荐完全失败:', error)
        this.latestProducts = []
      } finally {
        this.latestProductsLoading = false
      }
    },
    
    // 加载统计数据
    async loadStatistics() {
      try {
        const response = await request({
          url: '/api/mall/products/stats/home',
          method: 'get'
        })
        
        if (response.code === 200 && response.data) {
          this.totalSuppliers = response.data.supplierCount || 0
          this.totalOrders = response.data.orderCount || 0
          this.totalProducts = response.data.productCount || this.totalProducts
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    },
    
    // 搜索处理
    handleSearch(searchParams) {
      if (searchParams) {
        if (searchParams.type === 'simple') {
          // 简单搜索
          this.searchQuery = searchParams.query;
          this.searchActive = !!this.searchQuery;
          this.advancedSearchActive = false;
        } else if (searchParams.type === 'advanced') {
          // 高级搜索
          this.advancedSearchActive = true;
          this.searchActive = true;
          
          // 处理高级搜索参数
          this.filters.category = searchParams.category;
          this.filters.location = searchParams.location;
          this.filters.supplier = searchParams.supplier;
          this.filters.tags = searchParams.tags || [];
          this.filters.sortBy = searchParams.sortBy || 'comprehensive';
          this.filters.stockStatus = searchParams.stockStatus || 'all';
          
          // 处理价格区间
          if (searchParams.priceMin !== null || searchParams.priceMax !== null) {
            this.priceRange = [
              searchParams.priceMin !== null ? searchParams.priceMin : 0,
              searchParams.priceMax !== null ? searchParams.priceMax : this.maxPrice
            ];
          }
          
          // 处理评分
          if (searchParams.minRating) {
            this.filters.minRating = searchParams.minRating;
          }
          
          // 处理名称搜索
          this.searchQuery = searchParams.name || '';
        }
      } else {
        // 无参数时，使用当前搜索框的值
        this.searchActive = !!this.searchQuery;
        this.advancedSearchActive = false;
      }
      
      this.currentPage = 1;
      this.loadProducts();

      // 记录搜索行为
      this.recordSearchBehavior();
    },

    // 记录搜索行为
    async recordSearchBehavior() {
      try {
        if (!this.searchQuery || this.searchQuery.trim() === '') {
          return;
        }

        const keyword = this.searchQuery.trim();
        console.log('📝 记录搜索行为:', keyword);

        // 记录到后端API
        await request({
          url: '/api/mall/search/record',
          method: 'post',
          params: {
            keyword: keyword,
            userId: this.userInfo?.id || null,
            resultCount: this.products.length
          }
        });

        console.log('✅ 搜索行为记录成功');
      } catch (error) {
        console.error('❌ 记录搜索行为失败:', error);
        // 记录失败不影响用户体验，只记录日志
      }
    },

    // 切换高级搜索
    toggleAdvancedSearch() {
      this.showAdvancedSearch = !this.showAdvancedSearch
    },
    
    // 应用筛选
    applyFilters() {
      this.currentPage = 1
      this.loadProducts()
    },
    
    // 重置筛选
    resetFilters() {
      this.filters = {
        category: '',
        subCategories: [],
        priceRange: null,
        customPriceRange: null,
        location: '',
        sortBy: 'default',
        supplier: '',
        tags: [],
        minRating: 0,
        stockStatus: 'all'
      }
      this.priceRange = [0, 1000]
      this.advancedSearchActive = false
      // 使用我们的统一方法重置分类
      this.resetCategoryToAll()
      this.currentPage = 1
      this.loadProducts()
    },
    
    // 清除搜索
    clearSearch() {
      this.searchQuery = ''
      this.searchActive = false
      this.advancedSearchActive = false
      this.showAdvancedSearch = false
      this.resetFilters()
    },

    // 处理排序变化
    handleSortChange(sortBy) {
      console.log('排序方式变更:', sortBy)
      this.filters.sortBy = sortBy
      this.currentPage = 1
      this.loadProducts()
    },
    
    // 分类选择
    handleCategorySelect(categoryId) {
      console.log('选择分类:', categoryId)

      // 如果是"更多分类"菜单，不做处理
      if (categoryId === 'more') return

      this.activeCategory = categoryId
      this.categoryActive = categoryId !== 'all'

      // 清空二级分类选择
      this.selectedSubCategories = []
      this.activeSubCategory = null

      // 重置筛选条件
      this.filters = {
        ...this.filters,
        category: categoryId === 'all' ? '' : parseInt(categoryId),
        subCategories: []
      }

      // 如果选择了主分类，自动展开其子分类
      if (categoryId !== 'all') {
        const categoryIdNum = parseInt(categoryId)
        const category = this.categories.find(cat => cat.id === categoryIdNum)
        if (category) {
          console.log(`选择了分类: ${category.name}，产品数量: ${category.productCount || '未知'}`)

          // 检查是否有子分类
          const subCategories = this.getSubCategories(categoryIdNum)
          if (subCategories.length > 0) {
            // 自动展开子分类
            if (!this.expandedCategories.includes(categoryIdNum)) {
              this.expandedCategories.push(categoryIdNum)
              console.log('自动展开子分类:', categoryIdNum)
            }
          }
        }
      } else {
        console.log('选择了全部分类')
        // 收起所有展开的分类
        this.expandedCategories = []
      }

      // 重置页码并加载商品
      this.currentPage = 1
      this.loadProducts()

      // 记录用户分类选择
      this.saveUserCategoryPreference(categoryId)
    },
    
    // 保存用户分类偏好
    saveUserCategoryPreference(categoryId) {
      if (categoryId === 'all') return
      
      try {
        // 获取现有偏好
        const preferences = JSON.parse(localStorage.getItem('user_preferences') || '{}')
        
        // 更新最近浏览的分类
        if (!preferences.recentCategories) {
          preferences.recentCategories = []
        }
        
        // 添加到最近浏览的分类中
        const categoryIdNum = parseInt(categoryId)
        const existingIndex = preferences.recentCategories.indexOf(categoryIdNum)
        
        if (existingIndex > -1) {
          // 如果已存在，移到最前面
          preferences.recentCategories.splice(existingIndex, 1)
        }
        
        // 添加到最前面
        preferences.recentCategories.unshift(categoryIdNum)
        
        // 限制最多保存5个
        if (preferences.recentCategories.length > 5) {
          preferences.recentCategories = preferences.recentCategories.slice(0, 5)
        }
        
        // 保存回本地存储
        localStorage.setItem('user_preferences', JSON.stringify(preferences))
      } catch (error) {
        console.error('保存用户分类偏好失败:', error)
      }
    },
    
    // 加载用户分类偏好
    loadUserCategoryPreference() {
      try {
        // 获取用户偏好
        const preferences = JSON.parse(localStorage.getItem('user_preferences') || '{}')
        
        // 如果有最近浏览的分类，并且当前没有选中分类
        if (preferences.recentCategories && 
            preferences.recentCategories.length > 0 && 
            this.activeCategory === 'all') {
          
          // 获取最近浏览的第一个分类
          const recentCategoryId = preferences.recentCategories[0]
          
          // 检查该分类是否存在于当前分类列表中
          const categoryExists = this.categories.some(cat => cat.id === recentCategoryId)
          
          if (categoryExists) {
            // 如果存在，自动选中该分类（但不触发加载）
            console.log(`从用户偏好中恢复最近浏览的分类: ${recentCategoryId}`)
            
            // 注意：这里只是更新UI状态，不触发加载，避免重复加载
            this.activeCategory = recentCategoryId.toString()
            this.categoryActive = true
            
            // 更新筛选条件，但不触发加载
            this.filters = {
              ...this.filters,
              category: recentCategoryId
            }
          }
        }
      } catch (error) {
        console.error('加载用户分类偏好失败:', error)
      }
    },
    
    selectCategory(categoryId) {
      this.handleCategorySelect(categoryId.toString())
    },
    
    // 价格区间变化
    handlePriceRangeChange(range) {
      if (Array.isArray(range) && range.length === 2) {
        this.priceRange = range
        this.currentPage = 1
        this.loadProducts()
      }
    },
    
    // 分页处理
    handleCurrentChange(page) {
      this.currentPage = page
      this.loadProducts()
    },
    
    handleSizeChange(size) {
      this.pageSize = size
      this.currentPage = 1
      this.loadProducts()
    },
    
    // 显示商品详情
    showProductDetail(product) {
      this.selectedProduct = product
      this.productDetailVisible = true
    },
    
    // 添加到购物车
    async addToCart(product, quantity = 1) {
      if (!isLoggedIn()) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }
      
      try {
        await this.addProductToCart({ product, quantity })
        this.$message.success('已添加到购物车')
        // 触发购物车更新事件
        this.$root.$emit('cart-updated')
      } catch (error) {
        console.error('添加到购物车失败:', error)
        this.$message.error(error.message || '添加到购物车失败')
      }
    },
    
    // 立即购买
    buyNow(product, quantity = 1) {
      if (!isLoggedIn()) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }
      
      // 跳转到订单页面
      this.$router.push({
        path: '/order/create',
        query: {
          productId: product.id,
          quantity: quantity
        }
      })
    },
    
    // 收藏相关
    async addToFavorites(product) {
      if (!isLoggedIn()) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }
      
      try {
        const response = await request({
          url: '/api/mall/favorites',
          method: 'post',
          data: {
            userId: this.userInfo.id,
            productId: product.id
          }
        })
        
        if (response.code === 200) {
          this.$message.success('已添加到收藏')
        } else {
          this.$message.error(response.message || '收藏失败')
        }
      } catch (error) {
        console.error('收藏失败:', error)
        this.$message.error('收藏失败')
      }
    },
    
    async removeFromFavorites(product) {
      try {
        const response = await request({
          url: `/api/mall/favorites/${product.id}`,
          method: 'delete'
        })
        
        if (response.code === 200) {
          this.$message.success('已取消收藏')
        } else {
          this.$message.error(response.message || '取消收藏失败')
        }
      } catch (error) {
        console.error('取消收藏失败:', error)
        this.$message.error('取消收藏失败')
      }
    },
    
    // 显示发布对话框
    showPublishDialog() {
      if (!isLoggedIn()) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }
      
      if (this.userRole !== 'ROLE_SELLER') {
        this.$message.warning('只有销售者才能发布商品')
        return
      }
      
      this.publishDialogVisible = true
    },
    
    // 处理商品提交
    handleProductSubmit(_productData) {
      // 调用发布商品API
      this.$message.success('商品发布成功')
      this.publishDialogVisible = false
      this.loadProducts()
    },
    
    // 显示我的店铺
    showMyProducts() {
      if (!isLoggedIn()) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }

      this.myProductsVisible = true
      this.loadMyProducts()
    },

    // 加载我的店铺商品
    async loadMyProducts() {
      this.myProductsLoading = true
      try {
        console.log('🔍 loadMyProducts - 用户信息检查:', {
          userInfo: this.userInfo,
          userId: this.userInfo?.id,
          isSeller: this.isSeller,
          userRole: this.userRole,
          userType: this.userInfo?.user_type,
          role: this.userInfo?.role
        })

        const userId = this.userInfo?.id
        if (!userId) {
          this.$message.error('用户信息不完整')
          return
        }

        if (!this.isSeller) {
          this.$message.warning('您不是销售者，无法查看店铺商品')
          return
        }

        // 使用seller API获取商品列表
        const response = await getSellerProducts({
          page: 1,
          size: 20
        })

        console.log('🔍 API响应数据:', response)
        console.log('🔍 response.success:', response?.success)
        console.log('🔍 response.data:', response?.data)

        if (response && response.success) {
          // 修复：正确的数据结构是 response.data.records，不是 response.data.data.records
          this.myProducts = response.data?.records || []
          console.log('✅ 成功加载我的店铺商品:', this.myProducts.length, '个商品')
        } else {
          console.error('❌ API返回失败:', response)
          this.$message.error(response?.message || '获取店铺商品失败')
        }
      } catch (error) {
        console.error('加载店铺商品失败:', error)
        // 提供更详细的错误信息
        if (error.response) {
          const status = error.response.status
          const message = error.response.data?.message || error.message

          if (status === 401) {
            this.$message.error('请先登录')
            this.$router.push('/login')
          } else if (status === 403) {
            this.$message.error('您没有销售者权限，无法查看商品')
          } else {
            this.$message.error(`加载商品失败: ${message}`)
          }
        } else {
          this.$message.error('网络连接失败，请检查网络设置')
        }
      } finally {
        this.myProductsLoading = false
      }
    },
    
    // 编辑商品
    editProduct(_product) {
      // 实现编辑逻辑
    },
    
    // 删除商品
    deleteProduct(_product) {
      this.$confirm('确定要删除这个商品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用删除API
        this.$message.success('删除成功')
        this.loadMyProducts()
      })
    },
    
    // 查看商品统计
    viewProductStats(_product) {
      // 实现统计查看逻辑
    },

    // 跳转到发布商品页面
    goToAddProduct() {
      if (!isLoggedIn()) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }

      if (!this.isSeller) {
        this.$message.warning('只有销售者才能发布商品')
        return
      }

      this.$router.push('/seller/products/add')
    },

    // 跳转到商品统计页面
    goToProductStats() {
      if (!isLoggedIn()) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }

      if (!this.isSeller) {
        this.$message.warning('只有销售者才能查看商品统计')
        return
      }

      this.$router.push('/seller/stats')
    },
    
    // 显示销售者申请
    showSellerApplication() {
      if (!isLoggedIn()) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }

      // 跳转到独立的销售者申请页面
      this.$router.push('/seller/apply')
    },

    // 跳转到管理后台
    goToAdminDashboard() {
      if (!isLoggedIn()) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }

      if (!this.isAdmin) {
        this.$message.warning('您没有管理员权限')
        return
      }

      this.$router.push('/admin')
    },

    // 销售者相关方法
    handleCreateRecord() {
      this.$router.push('/seller/traceability-center/create')
    },

    goToSellerStats() {
      this.$router.push('/seller/stats')
    },

    goToTraceCenter() {
      this.$router.push('/seller/traceability-center')
    },

    goToShopSettings() {
      this.$router.push('/seller/shop')
    },

    // 管理员相关方法
    goToUserManagement() {
      this.$router.push('/admin/users')
    },

    goToSellerManagement() {
      this.$router.push('/admin/seller/applications')
    },

    goToSystemSettings() {
      this.$router.push('/admin/settings')
    },

    goToTraceabilityAudit() {
      this.$router.push('/admin/traceability/audit')
    },

    // 用户相关方法
    goToUserProfile() {
      this.$router.push('/user/profile')
    },

    goToUserOrders() {
      this.$router.push('/user/orders')
    },

    goToUserFavorites() {
      this.$router.push('/user/favorites')
    },

    // 检查销售者申请状态
    async checkSellerApplicationStatus() {
      try {
        const application = await checkSellerApplicationStatus()
        if (application) {
          this.$message.info(`申请状态：${application.statusText}`)
        } else {
          this.$message.info('您还没有提交销售者申请')
        }
      } catch (error) {
        console.error('检查申请状态失败:', error)
        this.$message.error('检查申请状态失败')
      }
    },

    // 显示调试信息
    showDebugInfo() {
      const debugInfo = {
        '登录状态': {
          isLoggedIn: this.isLoggedIn,
          userInfo: this.userInfo,
          hasUserId: !!(this.userInfo && this.userInfo.id)
        },
        '角色状态': {
          isSeller: this.isSeller,
          isAdmin: this.isAdmin,
          userRole: this.userRole,
          getUserRoleResult: getUserRole(this.userInfo)
        },
        '权限函数结果': {
          'auth.isAdmin()': isAdmin(),
          'auth.isSeller()': isSeller(),
          'auth.getUserRole()': getUserRole()
        },
        'DOM元素检查': {
          roleBasedArea: !!document.querySelector('.role-based-function-area'),
          sellerArea: !!document.querySelector('.seller-exclusive-area'),
          adminArea: !!document.querySelector('.admin-exclusive-area'),
          userArea: !!document.querySelector('.user-exclusive-area')
        }
      }

      console.log('🔍 完整调试信息:', debugInfo)

      this.$alert(
        `<pre>${JSON.stringify(debugInfo, null, 2)}</pre>`,
        '调试信息',
        {
          dangerouslyUseHTMLString: true,
          customClass: 'debug-alert'
        }
      )
    },

    // 显示验证工具
    showValidationTool() {
      this.validationToolVisible = true
    },

    // 新增内容模块处理方法
    async handleBannerClick(banner) {
      console.log('点击促销横幅:', banner)

      // 统计点击
      try {
        await shopAPI.trackBannerClick(banner.id)
      } catch (error) {
        console.error('统计横幅点击失败:', error)
      }

      // 根据横幅类型处理不同的跳转逻辑
      if (banner.linkUrl) {
        this.$router.push(banner.linkUrl)
      } else if (banner.id === 1) {
        // 新用户专享
        this.$router.push('/promotions/new-user')
      } else if (banner.id === 2) {
        // 限时秒杀
        this.$router.push('/promotions/flash-sale')
      }
    },

    async handleBrandClick(brand) {
      console.log('点击品牌:', brand)

      // 统计点击
      try {
        await shopAPI.trackBrandClick(brand.id)
      } catch (error) {
        console.error('统计品牌点击失败:', error)
      }

      // 跳转到品牌专区
      this.$router.push(`/brands/${brand.id}`)
    },

    viewProductDetail(product) {
      console.log('查看商品详情:', product)
      this.selectedProduct = product
      this.productDetailVisible = true
    },

    // 获取库存状态样式类
    getStockStatusClass(stock) {
      if (stock <= 0) return 'out-of-stock'
      if (stock <= 10) return 'low-stock'
      return 'in-stock'
    },

    // 获取库存状态文本
    getStockStatusText(stock) {
      if (stock <= 0) return '缺货'
      if (stock <= 10) return '库存紧张'
      return '库存充足'
    },

    // 查看溯源信息
    viewTraceability(product) {
      const traceCode = product.traceCode || product.trace_code
      if (traceCode) {
        this.$router.push(`/trace/${traceCode}`)
        this.productDetailVisible = false
      } else {
        this.$message.warning('该商品暂无溯源信息')
      }
    },

    async viewArticle(article) {
      console.log('查看文章:', article)

      // 增加阅读量
      try {
        await shopAPI.incrementArticleViews(article.id)
      } catch (error) {
        console.error('增加文章阅读量失败:', error)
      }

      // 跳转到文章详情页
      this.$router.push(`/articles/${article.id}`)
    },

    formatDate(dateString) {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    },

    // 加载页面数据
    async loadPageData() {
      try {
        // 并行加载所有数据（已移除四个功能模块的加载）
        await Promise.all([
          this.loadFeaturedBrands(),
          this.loadFeaturedReviews()
        ])
      } catch (error) {
        console.error('加载页面数据失败:', error)
        this.$message.error('加载页面数据失败')
      }
    },

    // 已移除四个功能模块的加载方法：
    // - loadHotRecommendations() - 热门推荐商品
    // - loadQuickCategories() - 农产品分类快速导航
    // - loadPromotionBanners() - 促销活动横幅
    // - loadAgricultureNews() - 农业资讯

    // 加载优质品牌展示
    async loadFeaturedBrands() {
      try {
        const response = await this.$api.getFeaturedBrands({ limit: 3 })
        if (response && response.code === 200) {
          this.featuredBrands = response.data || []
        } else {
          // 使用模拟数据
          this.featuredBrands = response.data || []
        }
      } catch (error) {
        // 生产环境静默处理错误
        if (process.env.NODE_ENV !== 'production') {
          console.error('加载优质品牌失败:', error)
        }
        // 使用默认数据
        this.featuredBrands = []
      }
    },

    // 加载用户评价展示
    async loadFeaturedReviews() {
      try {
        const response = await this.$api.getFeaturedReviews({ limit: 3 })
        if (response && response.code === 200) {
          this.featuredReviews = response.data || []
        } else {
          // 使用模拟数据
          this.featuredReviews = response.data || []
        }
      } catch (error) {
        // 生产环境静默处理错误
        if (process.env.NODE_ENV !== 'production') {
          console.error('加载用户评价失败:', error)
        }
        // 使用默认数据
        this.featuredReviews = []
      }
    },
    
    // 处理销售者申请
    async handleSellerApplication(applicationData) {
      try {
        const response = await request({
          url: '/api/mall/sellers/apply',
          method: 'post',
          data: {
            ...applicationData,
            userId: this.userInfo.id
          }
        })
        
        if (response.code === 200) {
          this.$message.success('申请已提交，请等待审核')
          this.sellerApplicationVisible = false
        } else {
          this.$message.error(response.message || '申请提交失败')
        }
      } catch (error) {
        console.error('申请提交失败:', error)
        this.$message.error('申请提交失败')
      }
    },
    
    // 显示价格走势
    showPriceChart() {
      this.priceChartVisible = true
      this.loadChartProducts()
    },
    
    // 加载图表商品
    async loadChartProducts() {
      try {
        const response = await getProducts({ page: 1, size: 50 })
        if (response.code === 200 && response.data) {
          this.chartProducts = response.data.records || []
        }
      } catch (error) {
        console.error('加载图表商品失败:', error)
      }
    },
    
    // 加载图表数据
    async loadChartData(params) {
      try {
        const response = await request({
          url: `/api/mall/products/${params.productId}/price-history`,
          method: 'get'
        })
        
        if (response.code === 200 && response.data) {
          return response.data
        }
        return []
      } catch (error) {
        console.error('加载图表数据失败:', error)
        return []
      }
    },
    
    // 获取分类标签
    getCategoryLabel(value) {
      const category = this.categories.find(cat => cat.value === value);
      return category ? category.label : value;
    },
    
    // 处理筛选变化
    handleFilterChange(filterData) {
      console.log('筛选条件变化:', filterData)
      
      // 更新筛选条件
      this.filters = { ...this.filters, ...filterData }
      
      // 处理分类筛选
      if (filterData.category !== undefined) {
        this.activeCategory = filterData.category || 'all'
        this.categoryActive = !!filterData.category
      }
      
      // 处理价格区间
      if (filterData.priceRange) {
        this.priceRange = [
          filterData.priceRange.min !== null ? filterData.priceRange.min : 0,
          filterData.priceRange.max !== null ? filterData.priceRange.max : this.maxPrice
        ]
      } else if (filterData.customPriceRange) {
        this.priceRange = [
          filterData.customPriceRange.min !== null ? filterData.customPriceRange.min : 0,
          filterData.customPriceRange.max !== null ? filterData.customPriceRange.max : this.maxPrice
        ]
      }
      
      // 重置页码并加载数据
      this.currentPage = 1
      this.loadProducts()
      
      // 记录筛选条件到历史记录
      this.saveFilterHistory(filterData)
    },
    
    // 保存筛选历史记录
    saveFilterHistory(filterData) {
      if (!filterData) return
      
      try {
        // 获取现有历史记录
        const historyStr = localStorage.getItem('filter_history')
        let history = historyStr ? JSON.parse(historyStr) : []
        
        // 创建新记录
        const newRecord = {
          id: Date.now(),
          timestamp: Date.now(),
          filter: filterData,
          mainCategory: this.getMainCategoryName(filterData.category),
          subCategories: (filterData.subCategories || []).map(id => {
            const cat = this.categories.find(c => c.id === id)
            return cat ? cat.name : ''
          }).filter(Boolean),
          tags: filterData.tags || []
        }
        
        // 添加到历史记录
        history.unshift(newRecord)
        
        // 限制历史记录数量
        if (history.length > 10) {
          history = history.slice(0, 10)
        }
        
        // 保存到本地存储
        localStorage.setItem('filter_history', JSON.stringify(history))
      } catch (error) {
        console.error('保存筛选历史记录失败:', error)
      }
    },
    
    // 获取分类名称
    getMainCategoryName(categoryId) {
      if (!categoryId) return ''
      const category = this.categories.find(cat => cat.id === categoryId)
      return category ? category.name : ''
    },
    
    // 获取分类图标
    getCategoryIcon(category) {
      // 优先使用数据库中的图标
      if (category.icon && category.icon !== 'null') {
        return category.icon
      }

      // 根据分类名称返回合适的图标
      const iconMap = {
        // 主分类图标
        '蔬菜类': 'el-icon-cherry',
        '水果类': 'el-icon-apple',
        '粮食作物': 'el-icon-food',
        '畜牧产品': 'el-icon-chicken',
        '水产品': 'el-icon-dish',
        '农副产品': 'el-icon-goods',
        '种子种苗': 'el-icon-grape',
        '苗木花草': 'el-icon-orange',
        '中药材': 'el-icon-coffee-cup',
        '农资农机': 'el-icon-tools',

        // 子分类图标
        '叶菜类': 'el-icon-cherry',
        '根茎类': 'el-icon-potato-strips',
        '瓜果类': 'el-icon-watermelon',
        '菜用花类': 'el-icon-orange',
        '稻米类': 'el-icon-food',
        '调料香料': 'el-icon-coffee-cup',
        '猪肉类': 'el-icon-chicken',
        '海水鱼类': 'el-icon-dish'
      }

      // 返回匹配的图标或默认图标
      return iconMap[category.name] || 'el-icon-goods'
    },

    // 获取子分类图标
    getSubCategoryIcon(subCategory) {
      const subIconMap = {
        // 蔬菜类子分类
        '叶菜类': 'el-icon-cherry',
        '根茎类': 'el-icon-potato-strips',
        '瓜果类': 'el-icon-watermelon',
        '茄果类': 'el-icon-tomato',
        '豆类菜': 'el-icon-pea',
        '菌菇类': 'el-icon-mushroom',
        '葱蒜类': 'el-icon-onion',
        '芽苗菜': 'el-icon-sprout',

        // 水果类子分类
        '柑橘类': 'el-icon-orange',
        '浆果类': 'el-icon-grape',
        '核果类': 'el-icon-apple',
        '仁果类': 'el-icon-pear',
        '热带水果': 'el-icon-pineapple',
        '瓜类水果': 'el-icon-watermelon',
        '坚果类': 'el-icon-nut',
        '干果类': 'el-icon-date',

        // 粮食作物子分类
        '稻米类': 'el-icon-rice',
        '小麦类': 'el-icon-wheat',
        '玉米类': 'el-icon-corn',
        '豆类粮食': 'el-icon-bean',
        '杂粮类': 'el-icon-grain',
        '薯类作物': 'el-icon-potato'
      }

      return subCategory.icon || subIconMap[subCategory.name] || 'el-icon-goods'
    },

    // 获取指定主分类的子分类列表
    getSubCategories(mainCategoryId) {
      if (!mainCategoryId) return []
      return this.categories.filter(cat =>
        cat.level === 2 &&
        (cat.parent_id === mainCategoryId || cat.parentId === mainCategoryId)
      )
    },

    // 获取主分类对象
    getMainCategory(categoryId) {
      return this.mainCategories.find(cat => cat.id === categoryId)
    },

    // 切换子分类展开/收起
    toggleSubCategories(categoryId) {
      console.log('切换子分类展开状态:', categoryId)

      const index = this.expandedCategories.indexOf(categoryId)
      if (index > -1) {
        // 收起
        this.expandedCategories.splice(index, 1)
        console.log('收起分类:', categoryId)
      } else {
        // 展开
        this.expandedCategories.push(categoryId)
        console.log('展开分类:', categoryId)

        // 记录用户交互行为
        this.recordCategoryInteraction(categoryId, 'expand')
      }
    },

    // 选择子分类
    selectSubCategory(subCategory) {
      console.log('选择子分类:', subCategory)

      this.activeSubCategory = subCategory.id
      this.activeCategory = subCategory.id.toString()
      this.categoryActive = true

      // 更新筛选条件
      this.filters = {
        ...this.filters,
        category: subCategory.id,
        subCategories: [subCategory.id]
      }

      // 重置页码并加载商品
      this.currentPage = 1
      this.loadProducts()

      // 记录用户行为
      this.recordCategoryInteraction(subCategory.id, 'select')
      this.saveUserCategoryPreference(subCategory.id.toString())

      // 显示选择反馈
      this.$message.success(`已选择分类：${subCategory.name}`)
    },

    // 记录分类交互行为
    recordCategoryInteraction(categoryId, action) {
      try {
        const interaction = {
          categoryId,
          action, // 'expand', 'collapse', 'select'
          timestamp: Date.now(),
          userAgent: navigator.userAgent
        }

        // 保存到本地存储用于分析
        const interactions = JSON.parse(localStorage.getItem('category_interactions') || '[]')
        interactions.push(interaction)

        // 只保留最近100条记录
        if (interactions.length > 100) {
          interactions.splice(0, interactions.length - 100)
        }

        localStorage.setItem('category_interactions', JSON.stringify(interactions))
      } catch (error) {
        console.error('记录分类交互失败:', error)
      }
    },
    
    // 处理二级分类变化
    handleSubCategoryChange() {
      console.log('🔍 子分类选择变化调试:')
      console.log('- 选中的子分类ID数组:', this.selectedSubCategories)
      console.log('- 当前主分类:', this.activeCategory)
      console.log('- 可用的子分类列表:', this.currentSubCategories.map(cat => ({ id: cat.id, name: cat.name })))
      
      // 验证选中的子分类是否有效
      const validSubCategories = this.selectedSubCategories.filter(subId => {
        const exists = this.currentSubCategories.some(cat => cat.id == subId)
        if (!exists) {
          console.warn('⚠️ 无效的子分类ID:', subId)
        }
        return exists
      })
      
      console.log('- 有效的子分类ID:', validSubCategories)
      
      // 更新筛选条件
      this.filters.subCategories = validSubCategories
      console.log('- 更新后的filters.subCategories:', this.filters.subCategories)
      
      // 重置页码并加载数据
      this.currentPage = 1
      this.loadProducts()
    },
    
    // 切换标签
    toggleTag(tagKey) {
      const index = this.selectedTags.indexOf(tagKey)
      if (index > -1) {
        this.selectedTags.splice(index, 1)
      } else {
        this.selectedTags.push(tagKey)
      }
      
      // 更新筛选条件
      this.filters.tags = this.selectedTags
      
      // 重置页码并加载数据
      this.currentPage = 1
      this.loadProducts()
    },
    
    // 选择价格区间
    selectPriceRange(rangeKey) {
      if (this.selectedPriceRange === rangeKey) {
        this.selectedPriceRange = null
        this.filters.priceRange = null
      } else {
        this.selectedPriceRange = rangeKey
        const range = this.priceRanges.find(r => r.key === rangeKey)
        if (range) {
          this.filters.priceRange = {
            min: range.min,
            max: range.max
          }
        }
      }
      
      // 重置页码并加载数据
      this.currentPage = 1
      this.loadProducts()
    },
    
    // 处理标签搜索
    handleTagSearch(tag) {
      this.searchQuery = tag
      this.handleSearch()
      
      // 添加到搜索历史
      if (!this.searchHistory.includes(tag)) {
        this.searchHistory.unshift(tag)
        if (this.searchHistory.length > 10) {
          this.searchHistory = this.searchHistory.slice(0, 10)
        }
      }
    },
    
    // 更新用户偏好
    async updateUserPreferences() {
      if (this.userInfo) {
        // 基本偏好设置
        this.userPreferences = {
          categories: this.userInfo.preferredCategories || [],
          priceRange: this.userInfo.preferredPriceRange || [0, 1000],
          location: this.userInfo.preferredLocation || '',
          tags: this.userInfo.preferredTags || []
        }
        
        // 尝试从API获取用户行为数据
        try {
          // 如果有API可以获取用户行为数据，可以在这里调用
          // 例如: const response = await getUserBehaviors(this.userInfo.id)
          
          // 从本地存储获取用户分类偏好
          const preferences = JSON.parse(localStorage.getItem('user_preferences') || '{}')
          if (preferences.recentCategories && preferences.recentCategories.length > 0) {
            // 将用户最近浏览的分类添加到偏好中
            this.userPreferences.categories = [
              ...new Set([...preferences.recentCategories, ...this.userPreferences.categories])
            ]
          }
        } catch (error) {
          console.error('获取用户行为数据失败:', error)
        }
      }
    },
    
    // 加载价格区间
    loadPriceRanges() {
      const categoryId = this.filters.category ? this.filters.category.id : null;
      productService.getProductPriceRanges(categoryId)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.minPrice = response.data.min || 0;
            this.maxPrice = response.data.max || 10000;
            this.filters.priceRange = [this.minPrice, this.maxPrice];
          }
        })
        .catch(error => {
          console.error('获取价格区间失败:', error);
        });
    },
    
    // 加载新品
    loadNewProducts() {
      getNewProducts(6)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.newProducts = response.data;
          }
        })
        .catch(error => {
          console.error('获取新品失败:', error);
        });
    },
    
    // 加载精选商品
    loadFeaturedProducts() {
      getFeaturedProducts(6)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.featuredProducts = response.data;
          }
        })
        .catch(error => {
          console.error('获取精选商品失败:', error);
        });
    },
    
    // 重置分类选择为全部
    resetCategoryToAll() {
      this.activeCategory = 'all'
      this.categoryActive = false
      this.filters.category = ''
      this.selectedSubCategories = []
      
      // 不再清除用户的历史分类偏好，保留它们以便个性化推荐
      // 只是暂时将UI重置为全部分类视图
    },

    // =====================================================
    // 溯源查询功能
    // =====================================================

    // 显示溯源查询对话框
    showTraceQueryDialog() {
      this.traceQueryDialogVisible = true
      this.traceQueryForm = {
        traceCode: '',
        queryType: 'code'
      }
      this.traceQueryResult = null
    },

    // 执行溯源查询
    async executeTraceQuery() {
      if (!this.traceQueryForm.traceCode.trim()) {
        this.$message.warning('请输入溯源码')
        return
      }

      this.traceQueryLoading = true
      try {
        const response = await this.$http.get('/api/traceability-query/query', {
          params: {
            traceCode: this.traceQueryForm.traceCode.trim()
          }
        })

        if (response.data.success) {
          this.traceQueryResult = response.data.data
          this.$message.success('查询成功')
        } else {
          this.$message.error(response.data.message || '查询失败')
          this.traceQueryResult = null
        }
      } catch (error) {
        console.error('溯源查询失败:', error)
        if (error.response && error.response.status === 404) {
          this.$message.error('未找到该溯源码对应的商品信息')
        } else {
          this.$message.error('查询失败，请稍后重试')
        }
        this.traceQueryResult = null
      } finally {
        this.traceQueryLoading = false
      }
    },

    // 查看溯源详情
    viewTraceabilityDetail(traceCode) {
      this.$router.push(`/traceability/detail/${traceCode}`)
    },

    // 扫描二维码查询
    scanQRCode() {
      // TODO: 实现二维码扫描功能
      this.$message.info('二维码扫描功能开发中...')
    }
  }
}
</script>

<style lang="scss" scoped>
// =====================================================
// 主要布局样式
// =====================================================

.main-layout {
  display: flex;
  gap: 20px;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

.main-content-area {
  flex: 1;
  min-width: 0; // 防止flex子项溢出
}

.right-sidebar {
  width: 320px;
  flex-shrink: 0;
}

// 搜索框下方内容区域样式
.content-below-search {
  margin-top: 20px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #67C23A;
        font-size: 20px;
      }
    }

    .more-btn {
      color: #409EFF;
      font-size: 14px;

      &:hover {
        color: #66B1FF;
      }
    }
  }
}

// 已移除热门推荐区域样式

// 已移除分类导航快速入口样式

// 已移除促销活动横幅样式

// 已移除农业资讯区域样式

// 右侧边栏样式
.right-sidebar {
  .sidebar-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 8px;

    i {
      color: #67C23A;
    }
  }

  .sidebar-role-functions,
  .sidebar-brands-section,
  .sidebar-reviews-section {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  }

  .debug-info-section {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .sidebar-base-component,
  .sidebar-seller-area,
  .sidebar-admin-area,
  .sidebar-user-area {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .sidebar-seller-actions,
  .sidebar-shop-access,
  .sidebar-admin-stats,
  .sidebar-admin-actions,
  .sidebar-user-actions,
  .sidebar-seller-status {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 品牌展示区域
.sidebar-brands-section {
  .brands-list {
    .brand-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: #f8f9fa;
        border-radius: 8px;
        margin: 0 -8px;
        padding: 12px 8px;
      }

      .brand-logo {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        object-fit: cover;
        margin-right: 12px;
        background-color: #f5f5f5; // 默认背景色
      }

      .brand-info {
        flex: 1;

        .brand-name {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          margin: 0 0 4px 0;
        }

        .brand-desc {
          font-size: 12px;
          color: #909399;
          margin: 0;
        }
      }
    }
  }
}

// 用户评价区域
.sidebar-reviews-section {
  .reviews-list {
    .review-item {
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .review-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          object-fit: cover;
          margin-right: 8px;
          background-color: #f5f5f5; // 默认背景色
        }

        .user-info {
          flex: 1;

          .user-name {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
            margin: 0 0 4px 0;
          }
        }
      }

      .review-content {
        font-size: 13px;
        color: #606266;
        line-height: 1.5;
        margin: 0;
      }
    }
  }
}

// =====================================================
// 动画关键帧定义 - 参考管理员后台设计风格
// =====================================================

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

// =====================================================
// 性能优化和响应式动画设置
// =====================================================

// 减少动画对于偏好减少动画的用户
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// 硬件加速优化
.shop-container,
.product-card-wrapper,
.sidebar,
.search-filter-section,
.shop-banner {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

// =====================================================
// 通用交互动画
// =====================================================

// 按钮点击反馈动画
.el-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0) scale(0.98);
    transition-duration: 0.1s;
  }
}

// 卡片悬停效果
.el-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

// =====================================================
// 主容器样式
// =====================================================

.shop-container {
  min-height: 100vh;
  background-color: #f8fafc;
  padding-top: 60px;

  // 页面加载动画
  animation: fadeIn 0.6s ease-out;
}

// =====================================================
// 页面横幅样式 - 带动画效果
// =====================================================

.shop-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px 20px;
  position: relative;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  // 横幅加载动画
  animation: slideInFromTop 0.8s ease-out;

  .banner-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;

    .banner-left {
      flex: 1;
      padding-right: 20px;

      // 标题动画
      animation: slideInFromLeft 0.8s ease-out 0.2s both;

      h1 {
        font-size: 2.2rem;
        margin-bottom: 8px;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      p {
        font-size: 1rem;
        opacity: 0.9;
        margin: 0;
      }
    }

    .banner-stats {
      display: flex;
      gap: 40px;

      // 统计数据动画
      animation: slideInFromBottom 0.8s ease-out 0.4s both;

      .stat-item {
        text-align: center;
        transition: transform 0.3s ease;

        &:hover {
          transform: translateY(-2px);
        }

        .stat-number {
          display: block;
          font-size: 1.6rem;
          font-weight: 700;
          margin-bottom: 4px;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .stat-label {
          font-size: 0.85rem;
          opacity: 0.8;
        }
      }
    }
  }
}

.banner-right {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 12px;
  align-items: center;
  
  .my-products-btn {
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

// =====================================================
// 搜索筛选区域样式 - 带动画效果
// =====================================================

.search-filter-section {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  border-radius: 0 0 16px 16px;
  border-top: 3px solid transparent;
  background-image: linear-gradient(white, white), linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
  position: relative;
  z-index: 100; // 设置较低的z-index，确保不遮挡分类下拉菜单

  // 搜索区域加载动画
  animation: slideInFromTop 0.8s ease-out 0.3s both;

  .search-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;

    .search-top-row {
      display: flex;
      align-items: flex-start;
      gap: 24px;
      margin-bottom: 20px;
      min-height: 60px; // 确保有足够的高度

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 16px;
        min-height: auto;
      }
    }

    .search-bar-wrapper {
      flex: 1;
      min-width: 0;

      // 搜索栏加载动画
      animation: slideInFromLeft 0.8s ease-out 0.4s both;

      // 搜索栏聚焦效果
      ::v-deep .el-input {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        .el-input__inner {
          border-radius: 12px;
          border: 2px solid #e2e8f0;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          &:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
          }
        }
      }
    }

    .seller-application-wrapper {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      margin-top: 8px; /* 与搜索框对齐 */

      @media (max-width: 768px) {
        margin-top: 0;
        justify-content: center;
      }
    }

    .category-navigation {
      margin-top: 10px;
      position: relative;
      z-index: 1001; // 确保整个导航容器在顶层

      // 分类导航加载动画
      animation: slideInFromLeft 0.8s ease-out 0.5s both;

      .category-menu {
        border-bottom: none;
        background: transparent;
        display: flex;
        flex-wrap: wrap;
        position: relative;
        z-index: 1002; // 菜单容器层级

        // 分类项包装器
        .category-item-wrapper {
          position: relative;
          margin-right: 8px;
          margin-bottom: 8px;

          // 分类项stagger动画
          opacity: 0;
          transform: translateX(-20px);
          animation: slideInFromLeft 0.4s ease-out forwards;

          &.expanded {
            .main-category-item {
              background: linear-gradient(135deg, #ebf8ff 0%, #bee3f8 100%);
              border-color: #409eff;
              color: #2b6cb0;

              .sub-category-toggle {
                color: #409eff;
                transform: translateY(-50%) rotate(180deg);
              }
            }
          }
        }

        .el-menu-item {
          height: 42px;
          line-height: 42px;
          border-bottom: 2px solid transparent;
          border-radius: 8px;
          padding: 0 16px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          overflow: visible;
          font-weight: 500;
          margin: 0;
          background: rgba(255, 255, 255, 0.9);
          backdrop-filter: blur(10px);
          z-index: 1003; // 确保菜单项在正确层级

          &:hover {
            z-index: 1004; // 悬停时提升层级
          }

          &.main-category-item {
            &.has-subcategories {
              padding-right: 35px;
            }
          }

          // 二级分类展开按钮
          .sub-category-toggle {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: #909399;
            transition: all 0.3s ease;
            cursor: pointer;
            border-radius: 4px;
            padding: 2px;

            &:hover,
            &:focus {
              color: #409eff;
              outline: none;
            }

            &:focus {
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
          }
        }

        // 下拉子分类菜单样式
        .sub-category-dropdown {
          position: absolute;
          top: 100%;
          left: 0;
          right: 0;
          background: rgba(255, 255, 255, 0.98);
          backdrop-filter: blur(15px);
          border-radius: 0 0 12px 12px;
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          border: 1px solid #e2e8f0;
          border-top: none;
          z-index: 1999; // 确保低于对话框层级
          overflow: hidden;

          // 响应式调整
          @media (max-width: 768px) {
            position: fixed; // 移动端使用固定定位
            left: 10px;
            right: 10px;
            top: auto;
            bottom: auto;
            z-index: 1999; // 移动端保持一致的层级
            border-radius: 12px;
            max-height: 60vh;
            overflow-y: auto;
          }

          .sub-category-list {
            padding: 8px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            gap: 6px;
            max-height: 300px;
            overflow-y: auto;

            .sub-category-item {
              display: flex;
              align-items: center;
              padding: 8px 12px;
              border-radius: 8px;
              background: #ffffff;
              border: 1px solid #e2e8f0;
              cursor: pointer;
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

              // 子分类项动画
              opacity: 0;
              transform: translateY(10px);
              animation: slideInFromBottom 0.3s ease-out forwards;

              i {
                font-size: 14px;
                color: #4a5568;
                margin-right: 8px;
                transition: color 0.3s ease;
              }

              .sub-category-name {
                flex: 1;
                font-size: 13px;
                color: #2d3748;
                font-weight: 500;
                transition: color 0.3s ease;
              }

              .sub-category-product-count {
                font-size: 11px;
                color: #718096;
                background: #f7fafc;
                padding: 2px 6px;
                border-radius: 10px;
                transition: all 0.3s ease;
              }

              &:hover,
              &:focus {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                border-color: #409eff;
                outline: none;

                i {
                  color: #409eff;
                }

                .sub-category-name {
                  color: #409eff;
                }

                .sub-category-product-count {
                  background: #ebf8ff;
                  color: #2b6cb0;
                }
              }

              &:focus {
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2), 0 4px 12px rgba(0, 0, 0, 0.1);
              }

              &.active {
                background: linear-gradient(135deg, #ebf8ff 0%, #bee3f8 100%);
                border-color: #409eff;

                i {
                  color: #409eff;
                }

                .sub-category-name {
                  color: #2b6cb0;
                  font-weight: 600;
                }

                .sub-category-product-count {
                  background: #409eff;
                  color: white;
                }
              }
            }
          }
        }

        .el-submenu {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(103, 194, 58, 0.1) 100%);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
          }

          i {
            margin-right: 6px;
            font-size: 16px;
            color: #606266;
            transition: all 0.3s ease;
          }

          .category-count {
            margin-left: 6px;
            font-size: 11px;
            color: #909399;
            background: rgba(144, 147, 153, 0.1);
            padding: 2px 6px;
            border-radius: 10px;
            transition: all 0.3s ease;
          }

          &:hover {
            background-color: rgba(64, 158, 255, 0.05);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

            &::before {
              transform: translateX(0);
            }

            i {
              color: #409eff;
            }

            .category-count {
              background: rgba(64, 158, 255, 0.2);
              color: #409eff;
            }
          }

          &.is-active {
            border-bottom-color: #409eff;
            color: #409eff;
            background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(103, 194, 58, 0.1) 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);

            i {
              color: #409eff;
            }

            .category-count {
              color: #409eff;
              background: rgba(64, 158, 255, 0.2);
            }
          }
        }
        
        .el-submenu {
          &__title {
            height: 40px;
            line-height: 40px;
            border-radius: 4px;

            i {
              color: #606266;
              margin-right: 5px;
            }

            &:hover {
              background-color: #f5f7fa;
            }
          }
        }
      }

      // 二级分类树样式
      .sub-category-tree {
        margin-top: 16px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        overflow: hidden;

        .sub-category-group {
          border-bottom: 1px solid #f0f2f5;

          &:last-child {
            border-bottom: none;
          }

          .sub-category-header {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid #e2e8f0;

            i {
              font-size: 16px;
              color: #409eff;
              margin-right: 8px;
            }

            .main-category-name {
              font-weight: 600;
              color: #2d3748;
              flex: 1;
            }

            .sub-category-count {
              font-size: 12px;
              color: #718096;
              margin-right: 12px;
            }

            .collapse-btn {
              padding: 4px 8px;
              font-size: 12px;

              i {
                margin-right: 4px;
                font-size: 12px;
              }
            }
          }

          .sub-category-items {
            padding: 8px;

            .sub-items-container {
              display: grid;
              grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
              gap: 8px;
            }

            .sub-category-item {
              display: flex;
              align-items: center;
              padding: 8px 12px;
              border-radius: 8px;
              background: #ffffff;
              border: 1px solid #e2e8f0;
              cursor: pointer;
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

              // 子分类项动画
              opacity: 0;
              transform: translateY(10px);
              animation: slideInFromBottom 0.3s ease-out forwards;

              i {
                font-size: 14px;
                color: #4a5568;
                margin-right: 8px;
                transition: color 0.3s ease;
              }

              .sub-category-name {
                flex: 1;
                font-size: 13px;
                color: #2d3748;
                font-weight: 500;
                transition: color 0.3s ease;
              }

              .sub-category-product-count {
                font-size: 11px;
                color: #718096;
                background: #f7fafc;
                padding: 2px 6px;
                border-radius: 10px;
                transition: all 0.3s ease;
              }

              &:hover,
              &:focus {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                border-color: #409eff;
                outline: none;

                i {
                  color: #409eff;
                }

                .sub-category-name {
                  color: #409eff;
                }

                .sub-category-product-count {
                  background: #ebf8ff;
                  color: #2b6cb0;
                }
              }

              &:focus {
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2), 0 4px 12px rgba(0, 0, 0, 0.1);
              }

              &.active {
                background: linear-gradient(135deg, #ebf8ff 0%, #bee3f8 100%);
                border-color: #409eff;

                i {
                  color: #409eff;
                }

                .sub-category-name {
                  color: #2b6cb0;
                  font-weight: 600;
                }

                .sub-category-product-count {
                  background: #409eff;
                  color: white;
                }
              }
            }
          }
        }
      }

      // 响应式设计
      @media (max-width: 768px) {
        .sub-category-tree {
          margin-top: 12px;
          border-radius: 8px;

          .sub-category-group {
            .sub-category-header {
              padding: 10px 12px;

              .main-category-name {
                font-size: 14px;
              }

              .sub-category-count {
                font-size: 11px;
              }
            }

            .sub-category-items {
              padding: 6px;

              .sub-items-container {
                grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
                gap: 6px;
              }

              .sub-category-item {
                padding: 6px 10px;

                i {
                  font-size: 12px;
                  margin-right: 6px;
                }

                .sub-category-name {
                  font-size: 12px;
                }

                .sub-category-product-count {
                  font-size: 10px;
                  padding: 1px 4px;
                }
              }
            }
          }
        }
      }

      @media (max-width: 480px) {
        .sub-category-tree {
          .sub-category-group {
            .sub-category-items {
              .sub-items-container {
                grid-template-columns: 1fr;
              }
            }
          }
        }
      }
    }
  }

// 主要内容
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px 40px;
}

// =====================================================
// 侧边栏样式 - 带动画效果
// =====================================================

.sidebar {
  // 侧边栏整体加载动画
  animation: slideInFromLeft 0.8s ease-out 0.4s both;

  .filter-card,
  .quick-actions,
  .hot-categories {
    margin-bottom: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    // 侧边栏卡片stagger动画
    opacity: 0;
    transform: translateX(-30px);
    animation: slideInFromLeft 0.6s ease-out forwards;

    &:nth-child(1) { animation-delay: 0.5s; }
    &:nth-child(2) { animation-delay: 0.6s; }
    &:nth-child(3) { animation-delay: 0.7s; }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.03);
    
    .card-header {
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      background-color: #fafafa;
      border-bottom: 1px solid #f0f0f0;
      
      i {
        margin-right: 8px;
        color: #409eff;
      }
    }
    
    .card-body {
      padding: 15px;
    }
  }
  
  .action-buttons {
    .el-button {
      margin-bottom: 10px;
      border-radius: 20px;
      width: 100%;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .category-list {
    .category-tag {
      margin: 4px 6px 4px 0;
      cursor: pointer;
      transition: all 0.3s;
      border-radius: 20px;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      }
      
      &.active {
        background-color: #409eff;
        color: white;
      }
    }
  }
}

// 推荐区域
.recommendations {
  margin-bottom: 20px;
  
  .recommendation-section {
    margin-bottom: 20px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      
      i {
        margin-right: 8px;
        color: #f56c6c;
      }
    }
  }
}

// =====================================================
// 商品列表区域样式 - 带动画效果
// =====================================================

.product-list-section {
  margin-top: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  // 商品列表区域加载动画
  animation: slideInFromBottom 0.8s ease-out 0.6s both;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    padding: 16px 20px;
    border-bottom: 1px solid #e2e8f0;

    // 头部内容动画
    animation: fadeIn 0.6s ease-out 0.8s both;
    border-radius: 12px;
    margin-bottom: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    
    .result-count {
      color: #606266;
      font-size: 14px;
      font-weight: 500;

      .traceable-count {
        color: #E6A23C;
        font-weight: 600;
        margin-left: 8px;
      }
    }

    .list-controls {
      display: flex;
      align-items: center;
      gap: 16px;

      .sort-selector {
        min-width: 140px;

        .el-input__inner {
          border-radius: 20px;
          border: 1px solid #DCDFE6;
          transition: all 0.3s ease;

          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
        }
      }

      .el-radio-group {
        .el-radio-button__inner {
          border-radius: 20px;
          padding: 8px 16px;
          transition: all 0.3s ease;

          &:hover {
            background-color: #f5f7fa;
          }
        }

        .el-radio-button:first-child .el-radio-button__inner {
          border-top-left-radius: 20px;
          border-bottom-left-radius: 20px;
        }

        .el-radio-button:last-child .el-radio-button__inner {
          border-top-right-radius: 20px;
          border-bottom-right-radius: 20px;
        }
      }
    }
  }
  
  .product-list {
    min-height: 300px;

    // =====================================================
    // 骨架屏样式
    // =====================================================

    .skeleton-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
      padding: 20px;
    }

    .skeleton-card {
      background: #ffffff;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      opacity: 0;
      animation: fadeIn 0.6s ease-out forwards;

      .skeleton-image {
        width: 100%;
        height: 180px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200px 100%;
        animation: shimmer 1.5s infinite;
        border-radius: 8px;
        margin-bottom: 12px;
      }

      .skeleton-content {
        .skeleton-title {
          height: 20px;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200px 100%;
          animation: shimmer 1.5s infinite;
          border-radius: 4px;
          margin-bottom: 8px;
        }

        .skeleton-price {
          height: 16px;
          width: 60%;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200px 100%;
          animation: shimmer 1.5s infinite;
          border-radius: 4px;
          margin-bottom: 8px;
        }

        .skeleton-info {
          height: 14px;
          width: 80%;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200px 100%;
          animation: shimmer 1.5s infinite;
          border-radius: 4px;
        }
      }
    }

    .grid-view {
      .el-row {
        display: flex;
        flex-wrap: wrap;

        &:after,
        &:before {
          display: none;
        }
      }

      .product-col {
        margin-bottom: 24px;
        height: 100%;
        display: flex;

        // 商品卡片stagger动画
        opacity: 0;
        transform: translateY(20px);
        animation: slideInFromBottom 0.6s ease-out forwards;

        // 动画延迟通过内联样式动态设置，这里提供备用方案
        &:nth-child(n) {
          animation-delay: calc(var(--index, 0) * 0.1s);
        }

        .product-card-wrapper {
          height: 100%;
          width: 100%;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          &:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          }

          /* 确保ProductCard组件填满整个容器 */
          ::v-deep .product-card {
            height: 100%;
            display: flex;
            flex-direction: column;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            &:hover {
              box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }
          }
        }
      }
    }
    
    .list-view {
      margin-top: 16px;
    }
    
    .empty-state {
      padding: 60px 0;
      text-align: center;
      background-color: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
      
      .empty-icon {
        font-size: 60px;
        color: #c0c4cc;
        margin-bottom: 20px;
      }
      
      .empty-text {
        color: #909399;
        font-size: 16px;
        margin-bottom: 20px;
      }
    }
  }
  
  .pagination-wrapper {
    margin-top: 30px;
    margin-bottom: 20px;
    text-align: center;

    // 分页组件加载动画
    animation: slideInFromBottom 0.8s ease-out 0.8s both;

    ::v-deep .el-pagination {
      padding: 16px;
      background-color: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
      display: inline-flex;
      justify-content: center;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }
      
      .el-pagination__total,
      .el-pagination__sizes,
      .el-pagination__jump {
        margin-right: 16px;
      }
      
      .btn-prev,
      .btn-next,
      .number {
        margin: 0 5px;
        border-radius: 4px;
        min-width: 32px;
        
        &.active {
          background-color: #409eff;
          color: #fff;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .shop-banner {
    padding: 20px 15px;
    
    .banner-content {
      flex-direction: column;
      text-align: center;
      gap: 20px;
      
      .banner-left {
        text-align: center;
        
        h1 {
          font-size: 1.8rem;
        }
        
        p {
          font-size: 0.9rem;
        }
      }
      
      .banner-stats {
        gap: 20px;
        
        .stat-item .stat-number {
          font-size: 1.3rem;
        }
      }
    }
  }
  
  .search-filter-section {
    .search-container {
      padding: 15px;
    }
  }
  
  .main-content {
    padding: 0 15px 40px;
    
    .sidebar {
      margin-bottom: 20px;
      
      .filter-card,
      .quick-actions,
      .hot-categories {
        margin-bottom: 12px;
      }
    }
  }
  
  .product-list {
    .grid-view {
      .product-col {
        padding: 0 8px;
      }
    }
  }
}

// =====================================================
// 商品详情对话框优化样式
// =====================================================

.product-detail-content {
  padding: 0;

  // 主要信息区域
  .product-main-section {
    margin-bottom: 32px;
  }

  // 商品图片区域
  .product-image-section {
    position: relative;

    .main-image-container {
      position: relative;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
      background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);

      .main-product-image {
        width: 100%;
        height: 350px;
        border-radius: 16px;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.02);
        }
      }

      .image-error {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 350px;
        color: #c0c4cc;
        background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);

        i {
          font-size: 48px;
          margin-bottom: 12px;
        }

        span {
          font-size: 14px;
        }
      }
    }

    // 商品标签
    .product-badges {
      position: absolute;
      top: 16px;
      left: 16px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      z-index: 10;

      .badge-item {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.9) !important;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        font-weight: 600;

        i {
          margin-right: 4px;
        }
      }
    }
  }

  // 商品信息区域
  .product-info-section {
    height: 100%;
    display: flex;
    flex-direction: column;

    // 商品标题区域
    .product-header {
      margin-bottom: 20px;

      .product-title {
        font-size: 24px;
        font-weight: 700;
        color: #2d3748;
        margin: 0 0 8px 0;
        line-height: 1.3;
        background: linear-gradient(135deg, #2d3748, #4a5568);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .product-subtitle {
        .category-tag {
          display: inline-block;
          padding: 4px 12px;
          background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(100, 181, 246, 0.1));
          color: #409EFF;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 500;
          border: 1px solid rgba(64, 158, 255, 0.2);
        }
      }
    }

    // 商品描述卡片
    .product-description-card {
      margin-bottom: 24px;
      padding: 16px 20px;
      background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(255, 255, 255, 0.8) 100%);
      border-radius: 12px;
      border: 1px solid rgba(226, 232, 240, 0.6);

      .product-description {
        color: #4a5568;
        margin: 0;
        line-height: 1.6;
        font-size: 14px;
      }
    }

    // 商品详细信息网格
    .product-details-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-bottom: 32px;

      .detail-card {
        padding: 20px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        border-radius: 12px;
        border: 1px solid rgba(226, 232, 240, 0.6);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        display: flex;
        align-items: flex-start;
        gap: 12px;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        .card-icon {
          width: 40px;
          height: 40px;
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          flex-shrink: 0;
        }

        .card-content {
          flex: 1;

          .card-label {
            font-size: 12px;
            color: #718096;
            margin-bottom: 4px;
            font-weight: 500;
          }

          .card-value {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
          }
        }

        // 价格卡片特殊样式
        &.price-card {
          .card-icon {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
            color: #ef4444;
          }

          .price-value {
            font-size: 20px;
            color: #ef4444;

            .unit {
              font-size: 14px;
              color: #718096;
              font-weight: 400;
            }
          }

          .original-price {
            font-size: 12px;
            color: #9ca3af;
            text-decoration: line-through;
          }
        }

        // 库存卡片特殊样式
        &.stock-card {
          .card-icon {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(22, 163, 74, 0.1));
            color: #22c55e;
          }

          .stock-status {
            font-size: 12px;
            font-weight: 500;

            &.in-stock {
              color: #22c55e;
            }

            &.low-stock {
              color: #f59e0b;
            }

            &.out-of-stock {
              color: #ef4444;
            }
          }
        }

        // 评分卡片特殊样式
        &.rating-card {
          .card-icon {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.1));
            color: #f59e0b;
          }
        }

        // 品牌卡片特殊样式
        &.brand-card {
          .card-icon {
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(124, 58, 237, 0.1));
            color: #8b5cf6;
          }
        }

        // 产地卡片特殊样式
        &.origin-card {
          .card-icon {
            background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(8, 145, 178, 0.1));
            color: #06b6d4;
          }
        }

        // 溯源卡片特殊样式
        &.trace-card {
          .card-icon {
            background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(100, 181, 246, 0.1));
            color: #409EFF;
          }

          .trace-code {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            color: #409EFF;
          }
        }
      }
    }

    // 购买操作区域
    .purchase-section {
      margin-top: auto;
      padding: 24px 0 0 0;
      border-top: 1px solid rgba(226, 232, 240, 0.6);

      .quantity-selector {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .quantity-label {
          font-weight: 600;
          color: #2d3748;
          margin-right: 12px;
          min-width: 50px;
        }
      }

      .action-buttons {
        display: flex;
        gap: 12px;

        .el-button {
          flex: 1;
          height: 44px;
          border-radius: 12px;
          font-weight: 600;
          font-size: 14px;
          transition: all 0.3s ease;

          &.el-button--primary {
            background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
            border: none;

            &:hover:not(:disabled) {
              background: linear-gradient(135deg, #66b1ff 0%, #85ce61 100%);
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
            }
          }

          &.el-button--danger {
            background: linear-gradient(135deg, #f56c6c 0%, #ff7875 100%);
            border: none;

            &:hover:not(:disabled) {
              background: linear-gradient(135deg, #f78989 0%, #ff9c9c 100%);
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(245, 108, 108, 0.3);
            }
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
          }
        }
      }
    }
  }
}

// 发布商品对话框
.publish-product-content {
  text-align: center;
  padding: 40px 20px;
  
  p {
    font-size: 1.1rem;
    color: #606266;
    margin-bottom: 20px;
  }
}



// 角色差异化功能区域样式
.role-based-function-area {
  display: flex;
  flex-direction: column;
  gap: 18px;
  min-width: 260px;
  max-width: 340px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;

  // 添加微妙的边框光效
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 16px;
    padding: 1px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    pointer-events: none;
  }

  .base-role-component {
    // 基础角色组件样式
    flex-shrink: 0;
    margin-bottom: 8px;
  }

  .seller-exclusive-area {
    display: flex;
    flex-direction: column;
    gap: 14px;
    padding: 12px;
    background: linear-gradient(135deg, rgba(103, 194, 58, 0.05) 0%, rgba(67, 160, 71, 0.05) 100%);
    border-radius: 12px;
    border-left: 4px solid #67C23A;

    .seller-quick-actions,
    .shop-quick-access {
      // 销售者专用组件样式
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border-radius: 8px;
      overflow: hidden;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(103, 194, 58, 0.15);
      }
    }
  }

  .admin-exclusive-area {
    display: flex;
    flex-direction: column;
    gap: 14px;
    padding: 12px;
    background: linear-gradient(135deg, rgba(245, 108, 108, 0.05) 0%, rgba(229, 57, 53, 0.05) 100%);
    border-radius: 12px;
    border-left: 4px solid #F56C6C;

    .admin-stats-overview,
    .admin-quick-actions {
      // 管理员专用组件样式
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border-radius: 8px;
      overflow: hidden;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(245, 108, 108, 0.15);
      }
    }
  }

  .user-exclusive-area {
    display: flex;
    flex-direction: column;
    gap: 14px;
    padding: 12px;
    background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(33, 150, 243, 0.05) 100%);
    border-radius: 12px;
    border-left: 4px solid #409EFF;

    .user-quick-actions,
    .seller-application-status {
      // 普通用户专用组件样式
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border-radius: 8px;
      overflow: hidden;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(64, 158, 255, 0.15);
      }
    }
  }

  // 添加整体容器的视觉效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 1px;
    background: linear-gradient(to bottom, transparent, #e5e7eb, transparent);
    opacity: 0.5;
  }
}

// 我的店铺容器样式 - 完全复制SellerApplicationButton的样式
.products-container {
    position: relative;

    .user-center-trigger.products-trigger {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      min-width: 160px;

      // 我的店铺特有的渐变背景
      background: linear-gradient(135deg, rgba(103, 194, 58, 0.1) 0%, rgba(133, 206, 97, 0.1) 100%);
      border: 1px solid rgba(103, 194, 58, 0.2);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px rgba(103, 194, 58, 0.2);
        background: linear-gradient(135deg, rgba(103, 194, 58, 0.15) 0%, rgba(133, 206, 97, 0.15) 100%);
      }

      .trigger-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-size: 18px;
        color: white;
        position: relative;
        overflow: hidden;
        background: linear-gradient(135deg, #67C23A 0%, #85CE61 100%);

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
          transform: translateX(-100%);
          transition: transform 0.6s ease;
        }
      }

      .trigger-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .trigger-title {
          font-size: 14px;
          font-weight: 600;
          color: #67C23A;
          line-height: 1.2;
        }

        .trigger-subtitle {
          font-size: 12px;
          color: #7f8c8d;
          margin-top: 2px;
        }
      }

      .trigger-arrow {
        margin-left: 8px;
        color: #bdc3c7;
        transition: transform 0.3s ease;
      }

      &:hover {
        .trigger-icon::before {
          transform: translateX(100%);
        }

        .trigger-arrow {
          transform: rotate(180deg);
        }
      }
    }
  }

// 我的店铺下拉菜单样式
.products-dropdown {
  .el-dropdown-menu__item {
    padding: 12px 16px;

    i {
      margin-right: 8px;
      color: #67C23A;
    }

    &:hover {
      background-color: #f5f7fa;
      color: #67C23A;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .main-layout {
    flex-direction: column;
    gap: 16px;
    padding: 0 15px;
  }

  .right-sidebar {
    width: 100%;
    order: -1; // 移动端将侧边栏移到顶部
  }

  .content-below-search {
    .recommendations-grid {
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 12px;
    }

    .categories-grid {
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 12px;
    }

    .banners-grid {
      grid-template-columns: 1fr;
      gap: 12px;

      .promotion-banner {
        height: 100px;

        .banner-content {
          padding: 16px;

          .banner-title {
            font-size: 16px;
          }

          .banner-subtitle {
            font-size: 13px;
          }
        }
      }
    }

    .news-grid {
      grid-template-columns: 1fr;
      gap: 12px;

      .news-card {
        .news-image {
          height: 100px;
        }

        .news-content {
          padding: 12px;
        }
      }
    }
  }

  .right-sidebar {
    .sidebar-role-functions,
    .sidebar-brands-section,
    .sidebar-reviews-section {
      padding: 16px;
    }

    .sidebar-section-title {
      font-size: 15px;
    }
  }

  .role-based-function-area {
    flex-direction: column;
    gap: 14px;
    min-width: auto;
    max-width: none;
    padding: 12px;
    margin: 0 8px;

    &::before {
      display: none;
    }

    .seller-exclusive-area,
    .admin-exclusive-area,
    .user-exclusive-area {
      padding: 10px;
      gap: 12px;
    }

    .products-container {
      .user-center-trigger.products-trigger {
        padding: 8px 12px;
        min-width: 140px;

        .trigger-icon {
          width: 32px;
          height: 32px;
          margin-right: 8px;
          font-size: 16px;
        }

        .trigger-content {
          .trigger-title {
            font-size: 13px;
          }

          .trigger-subtitle {
            font-size: 11px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .main-layout {
    padding: 0 10px;
  }

  .content-below-search {
    .section-header {
      .section-title {
        font-size: 16px;
      }
    }

    .recommendations-grid {
      grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
      gap: 10px;

      .recommendation-card {
        .product-image {
          height: 120px;
        }

        .product-info {
          padding: 10px;

          .product-name {
            font-size: 13px;
          }

          .product-price {
            font-size: 15px;
          }
        }
      }
    }

    .categories-grid {
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 10px;

      .category-card {
        padding: 16px;

        .category-icon i {
          font-size: 28px;
        }

        .category-name {
          font-size: 13px;
        }
      }
    }

    .news-grid {
      .news-card {
        .news-content {
          padding: 12px;

          .news-title {
            font-size: 14px;
          }

          .news-summary {
            font-size: 12px;
          }
        }
      }
    }
  }

  .right-sidebar {
    .sidebar-role-functions,
    .sidebar-brands-section,
    .sidebar-reviews-section {
      padding: 12px;
    }

    .brands-list .brand-item {
      .brand-logo {
        width: 36px;
        height: 36px;
      }

      .brand-info {
        .brand-name {
          font-size: 13px;
        }

        .brand-desc {
          font-size: 11px;
        }
      }
    }

    .reviews-list .review-item {
      .review-header {
        .user-avatar {
          width: 28px;
          height: 28px;
        }

        .user-info .user-name {
          font-size: 13px;
        }
      }

      .review-content {
        font-size: 12px;
      }
    }
  }

  .role-based-function-area {
    .products-container {
      .user-center-trigger.products-trigger {
        padding: 6px 10px;
        min-width: 120px;

        .trigger-icon {
          width: 28px;
          height: 28px;
          margin-right: 6px;
          font-size: 14px;
        }

        .trigger-content {
          .trigger-title {
            font-size: 12px;
          }

          .trigger-subtitle {
            display: none;
          }
        }
      }
    }
  }
}

.sidebar {
  .quick-actions .action-buttons .el-button {
    font-size: 0.9rem;
  }
}

// =====================================================
// 二级分类下拉动画样式
// =====================================================

// 下拉菜单展开/收起动画
.dropdown-slide-enter-active,
.dropdown-slide-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top;
}

.dropdown-slide-enter,
.dropdown-slide-leave-to {
  opacity: 0;
  transform: scaleY(0) translateY(-10px);
}

.dropdown-slide-enter-to,
.dropdown-slide-leave {
  opacity: 1;
  transform: scaleY(1) translateY(0);
}

// 子分类项动画
.sub-item-enter-active,
.sub-item-leave-active {
  transition: all 0.3s ease;
}

.sub-item-enter,
.sub-item-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.sub-item-move {
  transition: transform 0.3s ease;
}

// 从底部滑入动画
@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// =====================================================
// 子分类筛选样式
// =====================================================

.sub-category-filter {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  
  .filter-title {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 6px;
      color: #4299e1;
    }
  }
  
  .sub-category-list {
    .el-checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    
    .sub-category-item {
      margin-bottom: 8px;
      
      .el-checkbox {
        width: 100%;
        margin-right: 0;
        
        .el-checkbox__label {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          padding: 8px 12px;
          border-radius: 6px;
          transition: all 0.2s ease;
          
          &:hover {
            background-color: #e2e8f0;
          }
          
          .category-name {
            flex: 1;
            font-size: 13px;
            color: #4a5568;
          }
          
          .product-count {
            font-size: 12px;
            color: #718096;
            margin-left: 8px;
          }
        }
        
        &.is-checked {
          .el-checkbox__label {
            background-color: #ebf8ff;
            color: #2b6cb0;
            
            .category-name {
              color: #2b6cb0;
              font-weight: 500;
            }
            
            .product-count {
              color: #4299e1;
            }
          }
        }
      }
    }
  }
}

// 溯源查询按钮样式
.trace-query-wrapper {
  margin-bottom: 20px;

  .trace-query-btn {
    background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
    border: none;
    border-radius: 20px;
    padding: 10px 20px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

// 溯源查询对话框样式
.trace-query-content {
  .qr-scan-area {
    text-align: center;
    padding: 20px;
    border: 2px dashed #dcdfe6;
    border-radius: 8px;
    background: #fafafa;

    .scan-tip {
      margin: 10px 0 0 0;
      color: #909399;
      font-size: 14px;
    }
  }

  .trace-result {
    margin-top: 20px;

    .result-card {
      border: 1px solid #ebeef5;
      border-radius: 8px;
      padding: 20px;
      background: #fafafa;

      .product-info {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        .product-image {
          margin-right: 15px;
          flex-shrink: 0;

          .image-slot {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 80px;
            height: 80px;
            background: #f5f7fa;
            color: #909399;
            border-radius: 8px;
            font-size: 24px;
          }
        }

        .product-details {
          flex: 1;

          h4 {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }

          p {
            margin: 4px 0;
            font-size: 14px;
            color: #606266;

            &.trace-code {
              color: #409eff;
              font-weight: 500;
            }
          }
        }
      }

      .trace-actions {
        text-align: center;
      }
    }
  }

  .query-tips {
    margin-top: 20px;

    .el-alert__content p {
      margin: 2px 0;
      font-size: 13px;
    }
  }
}

// 调试样式
::v-deep .debug-alert {
  .el-message-box__content {
    max-height: 400px;
    overflow-y: auto;
  }

  pre {
    font-size: 12px;
    line-height: 1.4;
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    white-space: pre-wrap;
    word-wrap: break-word;
  }
}

// =====================================================
// 全局模态框背景模糊效果（基础样式，优先级较低）
// =====================================================

// Element UI 对话框背景模糊（基础样式）
::v-deep .el-dialog__wrapper {
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  background: rgba(0, 0, 0, 0.25) !important;
  transition: all 0.3s ease;
}

// Element UI 对话框优化（基础样式）
::v-deep .el-dialog {
  border-radius: 12px;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  background: rgba(255, 255, 255, 0.92);

  .el-dialog__header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    padding: 20px 24px 16px;

    .el-dialog__title {
      font-weight: 600;
      color: #2d3748;
    }

    .el-dialog__close {
      background: rgba(255, 255, 255, 0.8);
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
        transform: scale(1.1);
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
    background: rgba(255, 255, 255, 0.8);
  }

  .el-dialog__footer {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: 0 0 12px 12px;
    border-top: 1px solid rgba(226, 232, 240, 0.5);
    padding: 16px 24px 20px;
  }
}

// 商品表单对话框特殊样式
::v-deep .product-form-dialog {
  .el-dialog {
    max-width: 95vw;

    @media (min-width: 768px) {
      max-width: 900px;
    }
  }
}

// 二维码查看对话框样式
::v-deep .qr-code-dialog {
  .el-dialog {
    .el-dialog__body {
      text-align: center;
      padding: 40px 24px;

      .qr-code-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 300px;

        .qr-code-image {
          max-width: 100%;
          max-height: 400px;
          border-radius: 12px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
          background: white;
          padding: 20px;
          margin-bottom: 20px;
        }

        .qr-code-info {
          text-align: center;

          .trace-code {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
          }

          .qr-tips {
            font-size: 14px;
            color: #718096;
            line-height: 1.5;
          }
        }
      }
    }
  }
}

// 响应式优化
@media (max-width: 768px) {
  ::v-deep .el-dialog {
    margin: 20px !important;
    width: calc(100% - 40px) !important;
    max-width: none !important;

    .el-dialog__header {
      padding: 16px 20px 12px;
    }

    .el-dialog__body {
      padding: 20px;
    }

    .el-dialog__footer {
      padding: 12px 20px 16px;
    }
  }

  // 二维码对话框移动端优化
  ::v-deep .qr-code-dialog {
    .el-dialog__body {
      padding: 20px;

      .qr-code-container {
        min-height: 250px;

        .qr-code-image {
          max-width: 280px;
          padding: 15px;
        }
      }
    }
  }
}

// 浏览器兼容性降级方案
@supports not (backdrop-filter: blur(8px)) {
  ::v-deep .el-dialog__wrapper {
    background: rgba(0, 0, 0, 0.5) !important;
  }

  ::v-deep .el-dialog {
    background: rgba(255, 255, 255, 1) !important;
  }
}

// =====================================================
// 商品详情对话框背景模糊效果和现代化样式（高优先级）
// =====================================================

// 商品详情对话框特定样式 - 使用更高的优先级
.product-detail-dialog.product-detail-dialog {
  // 遮罩层背景模糊效果
  ::v-deep .el-dialog__wrapper {
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    background: rgba(0, 0, 0, 0.45) !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: 2001 !important;
  }

  // 对话框本体样式
  ::v-deep .el-dialog {
    border-radius: 24px !important;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    backdrop-filter: blur(25px) !important;
    -webkit-backdrop-filter: blur(25px) !important;
    background: rgba(255, 255, 255, 0.96) !important;
    overflow: hidden !important;
    margin: 5vh auto !important;
    position: relative !important;

    // 对话框头部样式
    .el-dialog__header {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.97) 0%, rgba(248, 250, 252, 0.97) 100%) !important;
      border-radius: 24px 24px 0 0 !important;
      border-bottom: 1px solid rgba(226, 232, 240, 0.6) !important;
      padding: 24px 32px 20px !important;
      position: relative !important;
      backdrop-filter: blur(10px) !important;
      -webkit-backdrop-filter: blur(10px) !important;

      .el-dialog__title {
        font-weight: 700 !important;
        font-size: 22px !important;
        color: #2d3748 !important;
        background: linear-gradient(135deg, #409EFF, #67C23A) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
      }

      .el-dialog__close {
        position: absolute !important;
        top: 16px !important;
        right: 16px !important;
        background: rgba(255, 255, 255, 0.95) !important;
        border-radius: 50% !important;
        width: 44px !important;
        height: 44px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: all 0.3s ease !important;
        border: 1px solid rgba(226, 232, 240, 0.6) !important;
        color: #718096 !important;
        font-size: 20px !important;
        font-weight: bold !important;
        cursor: pointer !important;
        backdrop-filter: blur(10px) !important;
        -webkit-backdrop-filter: blur(10px) !important;
        z-index: 10 !important;
        margin: 0 !important;
        padding: 0 !important;
        line-height: 1 !important;

        // 确保关闭按钮在最右上角
        &::before {
          content: '×' !important;
          font-size: 24px !important;
          line-height: 1 !important;
          display: block !important;
        }

        &:hover {
          background: rgba(239, 68, 68, 0.15) !important;
          color: #e53e3e !important;
          transform: scale(1.1) rotate(90deg) !important;
          border-color: rgba(239, 68, 68, 0.4) !important;
          box-shadow: 0 4px 16px rgba(239, 68, 68, 0.25) !important;
        }

        &:active {
          transform: scale(0.95) rotate(90deg) !important;
        }

        &:focus {
          outline: 2px solid rgba(64, 158, 255, 0.6) !important;
          outline-offset: 2px !important;
        }
      }
    }

    // 对话框主体样式
    .el-dialog__body {
      padding: 32px !important;
      background: rgba(255, 255, 255, 0.85) !important;
      max-height: 75vh !important;
      overflow-y: auto !important;
      backdrop-filter: blur(8px) !important;
      -webkit-backdrop-filter: blur(8px) !important;

      // 自定义滚动条
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(226, 232, 240, 0.3);
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(160, 174, 192, 0.6);
        border-radius: 4px;

        &:hover {
          background: rgba(160, 174, 192, 0.8);
        }
      }
    }

    // 对话框底部样式
    .el-dialog__footer {
      background: linear-gradient(135deg, rgba(248, 250, 252, 0.97) 0%, rgba(255, 255, 255, 0.97) 100%) !important;
      border-radius: 0 0 24px 24px !important;
      border-top: 1px solid rgba(226, 232, 240, 0.6) !important;
      padding: 20px 32px 24px !important;
      backdrop-filter: blur(10px) !important;
      -webkit-backdrop-filter: blur(10px) !important;
    }
  }
}

// 强制修复关闭按钮位置和可见性 - 最高优先级
.product-detail-dialog ::v-deep .el-dialog__close {
  position: absolute !important;
  top: 16px !important;
  right: 16px !important;
  width: 44px !important;
  height: 44px !important;
  border-radius: 50% !important;
  background: rgba(255, 255, 255, 0.98) !important;
  border: 2px solid rgba(107, 114, 128, 0.8) !important;
  color: #374151 !important;
  font-size: 28px !important;
  font-weight: 900 !important;
  cursor: pointer !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  z-index: 9999 !important;
  margin: 0 !important;
  padding: 0 !important;
  line-height: 40px !important;
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;

  &:hover {
    background: rgba(239, 68, 68, 0.95) !important;
    color: #ffffff !important;
    transform: scale(1.1) rotate(90deg) !important;
    border-color: rgba(239, 68, 68, 0.8) !important;
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.4) !important;
  }

  &:active {
    transform: scale(0.95) rotate(90deg) !important;
  }

  &:focus {
    outline: 3px solid rgba(64, 158, 255, 0.8) !important;
    outline-offset: 2px !important;
  }

  // 确保×符号清晰可见
  &::before {
    content: '×' !important;
    font-family: Arial, sans-serif !important;
    font-size: 28px !important;
    font-weight: 900 !important;
    line-height: 1 !important;
  }
}

// 响应式优化
@media (max-width: 1200px) {
  .product-detail-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 2.5vh auto !important;
    }
  }
}

@media (max-width: 768px) {
  .product-detail-dialog {
    .el-dialog {
      margin: 20px !important;
      width: calc(100% - 40px) !important;
      max-width: none !important;
      border-radius: 20px;

      .el-dialog__header {
        padding: 20px 24px 16px;
        border-radius: 20px 20px 0 0;

        .el-dialog__title {
          font-size: 20px;
        }

        .el-dialog__close {
          top: 12px !important;
          right: 12px !important;
          width: 40px !important;
          height: 40px !important;
          font-size: 20px !important;
          line-height: 40px !important;
        }
      }

      .el-dialog__body {
        padding: 24px 20px;
        max-height: 70vh;
      }

      .el-dialog__footer {
        padding: 16px 20px 20px;
        border-radius: 0 0 20px 20px;
      }
    }
  }

  // 移动端商品详情内容优化
  .product-detail-content {
    .product-details-grid {
      grid-template-columns: 1fr;
      gap: 12px;

      .detail-card {
        padding: 16px;

        .card-content {
          .card-value {
            font-size: 14px;
          }
        }

        &.price-card .price-value {
          font-size: 18px;
        }
      }
    }

    .purchase-section {
      .action-buttons {
        flex-direction: column;

        .el-button {
          height: 40px;
        }
      }
    }
  }
}

// 移动端关闭按钮强制定位和可见性
@media (max-width: 768px) {
  .product-detail-dialog ::v-deep .el-dialog__close {
    top: 12px !important;
    right: 12px !important;
    width: 40px !important;
    height: 40px !important;
    font-size: 24px !important;
    line-height: 36px !important;
    border-width: 2px !important;
    background: rgba(255, 255, 255, 0.98) !important;
    color: #374151 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;

    &::before {
      font-size: 24px !important;
    }
  }
}

// 浏览器兼容性降级方案
@supports not (backdrop-filter: blur(12px)) {
  .product-detail-dialog {
    .el-dialog__wrapper {
      background: rgba(0, 0, 0, 0.6) !important;
    }

    .el-dialog {
      background: rgba(255, 255, 255, 1) !important;
    }
  }
}

// 无障碍访问性优化
.product-detail-dialog {
  .el-dialog {
    &:focus {
      outline: 3px solid rgba(64, 158, 255, 0.5);
      outline-offset: 2px;
    }
  }

  .el-button {
    &:focus {
      outline: 2px solid rgba(64, 158, 255, 0.8);
      outline-offset: 2px;
    }
  }
}

// 动画效果
.product-detail-dialog {
  .el-dialog {
    animation: dialogSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-30px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// Element UI 组件样式优化
.product-detail-content {
  ::v-deep .el-input-number {
    .el-input__inner {
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      transition: all 0.3s ease;

      &:focus {
        border-color: #409EFF;
        box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
      }
    }
  }

  ::v-deep .el-rate {
    .el-rate__item {
      margin-right: 4px;
    }
  }

  ::v-deep .el-image {
    .el-image__error {
      background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    }
  }
}
</style>