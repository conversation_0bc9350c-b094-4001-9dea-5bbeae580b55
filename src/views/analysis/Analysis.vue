<template>
  <div class="analysis">
    <!-- 筛选条件 -->
    <el-card class="filter-section">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="产品类别">
          <el-select v-model="filterForm.category" placeholder="请选择类别" clearable>
            <el-option
              v-for="item in categories"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="地区">
          <el-cascader
            v-model="filterForm.region"
            :options="regions"
            placeholder="请选择地区"
            clearable/>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">分析</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 市场概览 -->
    <el-row :gutter="20" class="overview-section">
      <el-col :span="6" v-for="(item, index) in marketOverview" :key="index">
        <el-card class="overview-card" :body-style="{ padding: '20px' }">
          <div class="overview-icon" :style="{ backgroundColor: item.color }">
            <i :class="item.icon"/>
          </div>
          <div class="overview-content">
            <div class="overview-title">{{ item.title }}</div>
            <div class="overview-value">{{ item.value }}</div>
            <div class="overview-change" :class="item.trend">
              <i :class="item.trend === 'up' ? 'el-icon-top' : 'el-icon-bottom'"/>
              {{ item.change }}
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 市场趋势分析 -->
    <el-card class="trend-section">
      <div slot="header">
        <span>市场趋势分析</span>
        <el-radio-group v-model="trendType" size="small" style="float: right">
          <el-radio-button label="price">价格趋势</el-radio-button>
          <el-radio-button label="volume">成交量趋势</el-radio-button>
          <el-radio-button label="supply">供应趋势</el-radio-button>
        </el-radio-group>
      </div>
      <div class="trend-chart" ref="trendChart"/>
    </el-card>

    <!-- 市场洞察 -->
    <el-row :gutter="20" class="insight-section">
      <el-col :span="16">
        <el-card class="insight-card">
          <div slot="header">
            <span>市场洞察</span>
          </div>
          <div class="insight-list">
            <div v-for="(insight, index) in marketInsights" :key="index" class="insight-item">
              <div class="insight-icon" :class="insight.type">
                <i :class="getInsightIcon(insight.type)"/>
              </div>
              <div class="insight-content">
                <h3>{{ insight.title }}</h3>
                <p>{{ insight.description }}</p>
                <div class="insight-meta">
                  <span class="insight-time">{{ insight.time }}</span>
                  <span class="insight-source">{{ insight.source }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="hot-topics">
          <div slot="header">
            <span>热门话题</span>
          </div>
          <div class="topic-list">
            <div v-for="(topic, index) in hotTopics" :key="index" class="topic-item">
              <span class="topic-rank" :class="{ 'top-three': index < 3 }">{{ index + 1 }}</span>
              <span class="topic-title">{{ topic.title }}</span>
              <span class="topic-count">{{ topic.count }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 供需分析 -->
    <el-card class="supply-demand">
      <div slot="header">
        <span>供需分析</span>
      </div>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="supply-chart" ref="supplyChart"/>
        </el-col>
        <el-col :span="12">
          <div class="demand-chart" ref="demandChart"/>
        </el-col>
      </el-row>
    </el-card>

    <!-- 智能价格预测 -->
    <el-card class="price-prediction-redirect">
      <div slot="header">
        <span>智能价格预测</span>
      </div>
      <div class="prediction-redirect-content">
        <div class="redirect-info">
          <div class="redirect-icon">
            <i class="el-icon-data-analysis"></i>
          </div>
          <div class="redirect-text">
            <h3>专业AI预测系统</h3>
            <p>基于RNN神经网络和ARIMA时间序列的农产品价格预测平台，提供更精准的预测分析和丰富的可视化图表。</p>
            <div class="features">
              <el-tag type="primary" size="small">
                <i class="el-icon-cpu"></i>
                RNN神经网络
              </el-tag>
              <el-tag type="warning" size="small">
                <i class="el-icon-data-line"></i>
                ARIMA时间序列
              </el-tag>
              <el-tag type="success" size="small">
                <i class="el-icon-view"></i>
                可视化分析
              </el-tag>
              <el-tag type="info" size="small">
                <i class="el-icon-setting"></i>
                多维度配置
              </el-tag>
            </div>
          </div>
        </div>
        <div class="redirect-action">
          <el-button
            type="success"
            size="large"
            @click="goToPricePrediction"
            icon="el-icon-data-analysis"
            class="prediction-button">
            进入预测系统
          </el-button>
          <p class="redirect-hint">点击进入专业的价格预测分析平台</p>
        </div>
      </div>
    </el-card>

    <!-- 市场热点分析 -->
    <el-card class="market-hotspots">
      <div slot="header">
        <span>市场热点分析</span>
      </div>
      <div class="hotspots-content">
        <div v-for="(hotspot, index) in marketHotspots" :key="index" class="hotspot-item">
          <div class="hotspot-header">
            <h3>{{ hotspot.title }}</h3>
            <el-tag :type="getHotspotType(hotspot.level)">{{ hotspot.level }}</el-tag>
          </div>
          <div class="hotspot-body">
            <p>{{ hotspot.description }}</p>
            <div class="hotspot-chart" :ref="'hotspotChart' + index"/>
          </div>
          <div class="hotspot-footer">
            <span class="hotspot-time">{{ hotspot.time }}</span>
            <span class="hotspot-impact">影响程度: {{ hotspot.impact }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import axios from 'axios'

export default {
  name: 'Analysis',
  data() {
    return {
      filterForm: {
        category: '',
        region: [],
        dateRange: []
      },
      categories: [
        { value: 'grain', label: '粮食' },
        { value: 'vegetable', label: '蔬菜' },
        { value: 'fruit', label: '水果' },
        { value: 'meat', label: '肉类' }
      ],
      regions: [
        {
          value: 'beijing',
          label: '北京',
          children: [
            { value: 'chaoyang', label: '朝阳区' },
            { value: 'haidian', label: '海淀区' },
            { value: 'dongcheng', label: '东城区' },
            { value: 'xicheng', label: '西城区' }
          ]
        },
        {
          value: 'shanghai',
          label: '上海',
          children: [
            { value: 'pudong', label: '浦东新区' },
            { value: 'huangpu', label: '黄浦区' },
            { value: 'jingan', label: '静安区' },
            { value: 'xuhui', label: '徐汇区' }
          ]
        },
        {
          value: 'guangdong',
          label: '广东省',
          children: [
            { value: 'guangzhou', label: '广州市' },
            { value: 'shenzhen', label: '深圳市' },
            { value: 'dongguan', label: '东莞市' },
            { value: 'foshan', label: '佛山市' }
          ]
        },
        {
          value: 'jiangsu',
          label: '江苏省',
          children: [
            { value: 'nanjing', label: '南京市' },
            { value: 'suzhou', label: '苏州市' },
            { value: 'wuxi', label: '无锡市' },
            { value: 'changzhou', label: '常州市' }
          ]
        },
        {
          value: 'zhejiang',
          label: '浙江省',
          children: [
            { value: 'hangzhou', label: '杭州市' },
            { value: 'ningbo', label: '宁波市' },
            { value: 'wenzhou', label: '温州市' },
            { value: 'shaoxing', label: '绍兴市' }
          ]
        },
        {
          value: 'sichuan',
          label: '四川省',
          children: [
            { value: 'chengdu', label: '成都市' },
            { value: 'mianyang', label: '绵阳市' },
            { value: 'deyang', label: '德阳市' },
            { value: 'leshan', label: '乐山市' }
          ]
        },
        {
          value: 'hubei',
          label: '湖北省',
          children: [
            { value: 'wuhan', label: '武汉市' },
            { value: 'xiangyang', label: '襄阳市' },
            { value: 'yichang', label: '宜昌市' },
            { value: 'jingzhou', label: '荆州市' }
          ]
        },
        {
          value: 'henan',
          label: '河南省',
          children: [
            { value: 'zhengzhou', label: '郑州市' },
            { value: 'luoyang', label: '洛阳市' },
            { value: 'kaifeng', label: '开封市' },
            { value: 'nanyang', label: '南阳市' }
          ]
        },
        {
          value: 'shandong',
          label: '山东省',
          children: [
            { value: 'jinan', label: '济南市' },
            { value: 'qingdao', label: '青岛市' },
            { value: 'yantai', label: '烟台市' },
            { value: 'weifang', label: '潍坊市' }
          ]
        },
        {
          value: 'hebei',
          label: '河北省',
          children: [
            { value: 'shijiazhuang', label: '石家庄市' },
            { value: 'tangshan', label: '唐山市' },
            { value: 'baoding', label: '保定市' },
            { value: 'langfang', label: '廊坊市' }
          ]
        },
        {
          value: 'anhui',
          label: '安徽省',
          children: [
            { value: 'hefei', label: '合肥市' },
            { value: 'wuhu', label: '芜湖市' },
            { value: 'bengbu', label: '蚌埠市' },
            { value: 'anqing', label: '安庆市' },
            { value: 'fuyang', label: '阜阳市' },
            { value: 'huangshan', label: '黄山市' }
          ]
        }
      ],
      marketOverview: [
        {
          title: '市场总交易量',
          value: '1,234,567吨',
          change: '+12.5%',
          trend: 'up',
          icon: 'el-icon-data-line',
          color: '#409EFF'
        },
        {
          title: '平均价格',
          value: '￥2.5/kg',
          change: '-2.3%',
          trend: 'down',
          icon: 'el-icon-price-tag',
          color: '#67C23A'
        },
        {
          title: '活跃商家',
          value: '1,234',
          change: '+5.8%',
          trend: 'up',
          icon: 'el-icon-s-shop',
          color: '#E6A23C'
        },
        {
          title: '市场景气度',
          value: '85.6',
          change: '+3.2%',
          trend: 'up',
          icon: 'el-icon-data-board',
          color: '#F56C6C'
        }
      ],
      trendType: 'price',
      marketInsights: [
        {
          type: 'warning',
          title: '市场供应偏紧',
          description: '受天气影响，部分农产品供应量下降，价格出现上涨趋势',
          time: '2024-03-28 10:00',
          source: '市场分析报告'
        },
        {
          type: 'success',
          title: '需求持续增长',
          description: '随着消费升级，优质农产品需求持续增长，市场前景看好',
          time: '2024-03-28 09:30',
          source: '行业研究'
        }
      ],
      hotTopics: [
        { title: '农业补贴政策', count: 128 },
        { title: '种植技术', count: 96 },
        { title: '市场动态', count: 85 },
        { title: '天气预警', count: 76 },
        { title: '科技创新', count: 65 }
      ],
      marketHotspots: [
        {
          title: '新政策影响',
          level: '高',
          description: '最新农业补贴政策对市场产生重大影响',
          time: '2024-03-28',
          impact: '显著'
        },
        {
          title: '天气变化',
          level: '中',
          description: '近期天气变化影响农产品供应',
          time: '2024-03-27',
          impact: '中等'
        }
      ],
      trendChart: null,
      supplyChart: null,
      demandChart: null,
      forecastChart: null,
      forecastBarChart: null,
      hotspotCharts: [],
      http: axios.create({
        baseURL: 'http://localhost:8081', // 使用Java后端的端口
        timeout: 5000
      })
    }
  },
  mounted() {
    this.initCharts()
  },
  methods: {
    // 跳转到价格预测页面
    goToPricePrediction() {
      this.$router.push('/price-prediction')
    },

    handleFilter() {
      // 实现筛选逻辑
      this.fetchHistoryPrices();
    },
    resetFilter() {
      this.filterForm = {
        category: '',
        region: [],
        dateRange: []
      }
    },
    initCharts() {
      // 初始化趋势图表
      this.trendChart = echarts.init(this.$refs.trendChart)
      const trendOption = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['价格', '成交量', '供应量']
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '价格',
            type: 'line',
            data: [2.1, 2.3, 2.4, 2.5, 2.6, 2.5]
          },
          {
            name: '成交量',
            type: 'bar',
            data: [1000, 1200, 1100, 1300, 1250, 1400]
          },
          {
            name: '供应量',
            type: 'line',
            data: [900, 1100, 1000, 1200, 1150, 1300]
          }
        ]
      }
      this.trendChart.setOption(trendOption)

      // 初始化供需图表
      this.supplyChart = echarts.init(this.$refs.supplyChart)
      const supplyOption = {
        title: {
          text: '供应分析'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            type: 'pie',
            radius: '50%',
            data: [
              { value: 1048, name: '本地供应' },
              { value: 735, name: '外地供应' },
              { value: 580, name: '进口供应' }
            ]
          }
        ]
      }
      this.supplyChart.setOption(supplyOption)

      this.demandChart = echarts.init(this.$refs.demandChart)
      const demandOption = {
        title: {
          text: '需求分析'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            type: 'pie',
            radius: '50%',
            data: [
              { value: 1048, name: '零售需求' },
              { value: 735, name: '批发需求' },
              { value: 580, name: '加工需求' }
            ]
          }
        ]
      }
      this.demandChart.setOption(demandOption)

      // 初始化预测图表
      this.forecastChart = echarts.init(this.$refs.forecastChart)
      const forecastOption = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['实际价格', '预测价格']
        },
        xAxis: {
          type: 'category',
          data: ['3-22', '3-23', '3-24', '3-25', '3-26', '3-27', '3-28']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '实际价格',
            type: 'line',
            data: [2.1, 2.3, 2.4, 2.5, 2.6, 2.5, 2.5]
          },
          {
            name: '预测价格',
            type: 'line',
            data: [2.5, 2.6, 2.7, 2.8, 2.7, 2.6, 2.6]
          }
        ]
      }
      this.forecastChart.setOption(forecastOption)
    },
    getInsightIcon(type) {
      const icons = {
        'warning': 'el-icon-warning',
        'success': 'el-icon-success',
        'info': 'el-icon-info',
        'danger': 'el-icon-error'
      }
      return icons[type] || 'el-icon-info'
    },
    getConfidenceColor(confidence) {
      if (confidence >= 80) return '#67C23A'
      if (confidence >= 60) return '#E6A23C'
      return '#F56C6C'
    },
    getFactorType(type) {
      const types = {
        'warning': 'warning',
        'success': 'success',
        'danger': 'danger',
        'info': 'info'
      }
      return types[type] || 'info'
    },
    getHotspotType(level) {
      const types = {
        '高': 'danger',
        '中': 'warning',
        '低': 'info'
      }
      return types[level] || 'info'
    },
    handleExport() {
      this.$message.success('报告导出成功')
    },
    handleSubscribe() {
      this.$message.success('订阅成功')
    },




  },
  beforeDestroy() {
    if (this.trendChart) {
      this.trendChart.dispose()
    }
    if (this.supplyChart) {
      this.supplyChart.dispose()
    }
    if (this.demandChart) {
      this.demandChart.dispose()
    }

    this.hotspotCharts.forEach(chart => {
      if (chart) {
        chart.dispose()
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.analysis {
  padding: 20px;
  margin-top: 0;
}

.filter-section {
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
  margin-top: 20px; /* 增加顶部间距 */
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  gap: 10px; /* 使用gap替代margin */
  
  .el-form-item {
    margin-right: 0; /* 移除右边距，使用gap控制间距 */
    margin-bottom: 0;
    flex: 0 0 auto; /* 防止表单项被拉伸 */
  }
}

.overview-section {
  margin-bottom: 20px;
}

.overview-card {
  height: 100%;
}

.overview-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}

.overview-icon i {
  font-size: 24px;
  color: #fff;
}

.overview-content {
  text-align: center;
}

.overview-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.overview-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.overview-change {
  font-size: 14px;
}

.overview-change.up {
  color: #F56C6C;
}

.overview-change.down {
  color: #67C23A;
}

.trend-section {
  margin-bottom: 20px;
}

.trend-chart {
  height: 400px;
}

.insight-section {
  margin-bottom: 20px;
}

.insight-item {
  display: flex;
  padding: 15px 0;
  border-bottom: 1px solid #EBEEF5;
}

.insight-item:last-child {
  border-bottom: none;
}

.insight-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.insight-icon.warning {
  background-color: #E6A23C;
}

.insight-icon.success {
  background-color: #67C23A;
}

.insight-icon.info {
  background-color: #909399;
}

.insight-icon i {
  color: #fff;
  font-size: 20px;
}

.insight-content {
  flex: 1;
}

.insight-content h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #303133;
}

.insight-content p {
  margin: 0 0 10px 0;
  color: #606266;
  line-height: 1.5;
}

.insight-meta {
  font-size: 12px;
  color: #909399;
}

.insight-time {
  margin-right: 15px;
}

.topic-list {
  padding: 0;
  margin: 0;
  list-style: none;
}

.topic-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #EBEEF5;
}

.topic-item:last-child {
  border-bottom: none;
}

.topic-rank {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #F2F6FC;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 12px;
}

.topic-rank.top-three {
  background-color: #409EFF;
  color: #fff;
}

.topic-title {
  flex: 1;
  color: #303133;
}

.topic-count {
  color: #909399;
  font-size: 12px;
}

.supply-demand {
  margin-bottom: 20px;
}

.supply-chart,
.demand-chart {
  height: 300px;
}

.price-prediction-redirect {
  margin-top: 40px;
  margin-bottom: 20px;

  .prediction-redirect-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 8px;

    .redirect-info {
      display: flex;
      align-items: center;
      flex: 1;

      .redirect-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24px;
        box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);

        i {
          font-size: 36px;
          color: white;
        }
      }

      .redirect-text {
        flex: 1;

        h3 {
          margin: 0 0 12px 0;
          font-size: 20px;
          color: #303133;
          font-weight: 600;
        }

        p {
          margin: 0 0 16px 0;
          color: #606266;
          line-height: 1.6;
          font-size: 14px;
        }

        .features {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;

          .el-tag {
            i {
              margin-right: 4px;
            }
          }
        }
      }
    }

    .redirect-action {
      text-align: center;
      margin-left: 24px;

      .prediction-button {
        padding: 12px 32px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(103, 194, 58, 0.4);
        }
      }

      .redirect-hint {
        margin: 12px 0 0 0;
        color: #909399;
        font-size: 12px;
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .price-prediction-redirect {
    .prediction-redirect-content {
      flex-direction: column;
      text-align: center;

      .redirect-info {
        flex-direction: column;
        margin-bottom: 24px;

        .redirect-icon {
          margin-right: 0;
          margin-bottom: 16px;
        }

        .redirect-text {
          text-align: center;
        }
      }

      .redirect-action {
        margin-left: 0;
      }
    }
  }
}

.market-hotspots {
  margin-bottom: 20px;
}

.hotspot-item {
  padding: 20px;
  border-bottom: 1px solid #EBEEF5;
}

.hotspot-item:last-child {
  border-bottom: none;
}

.hotspot-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.hotspot-header h3 {
  margin: 0;
  flex: 1;
}

.hotspot-body {
  margin-bottom: 15px;
}

.hotspot-body p {
  margin: 0 0 15px 0;
  color: #606266;
  line-height: 1.5;
}

.hotspot-chart {
  height: 200px;
}

.hotspot-footer {
  display: flex;
  justify-content: space-between;
  color: #909399;
  font-size: 12px;
}


</style>