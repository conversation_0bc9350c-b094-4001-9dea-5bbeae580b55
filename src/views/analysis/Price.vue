<template>
  <div class="price">
    <!-- 筛选条件 -->
    <el-card class="filter-section">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="产品类别">
          <el-select v-model="filterForm.category" placeholder="请选择类别" clearable>
            <el-option
              v-for="item in categories"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="地区">
          <el-cascader
            v-model="filterForm.region"
            :options="regions"
            placeholder="请选择地区"
            clearable/>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-row :gutter="20">
      <!-- 左侧内容 -->
      <el-col :span="16">
        <!-- 价格概览 -->
        <el-row :gutter="20" class="overview-section">
          <el-col :span="6" v-for="(item, index) in priceOverview" :key="index">
            <el-card class="overview-card" :body-style="{ padding: '20px' }" v-loading="loading.overview">
              <div class="overview-icon" :style="{ backgroundColor: getTrendColor(item.trend) }">
                <i :class="item.icon"/>
              </div>
              <div class="overview-content">
                <div class="overview-title">{{ item.title }}</div>
                <div class="overview-value">{{ item.value }}</div>
                <div class="overview-change" :class="item.trend">
                  <i :class="item.trend === 'up' ? 'el-icon-top' : item.trend === 'down' ? 'el-icon-bottom' : 'el-icon-minus'"/>
                  {{ item.change }}
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 价格趋势 -->
        <el-card class="trend-section">
          <div slot="header">
            <span>价格趋势</span>
            <div class="trend-controls">
              <el-select 
                v-model="trendCategory" 
                size="small" 
                placeholder="选择品类" 
                style="width: 120px; margin-right: 10px">
                <el-option
                  v-for="item in trendCategories"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value" />
              </el-select>
              <el-radio-group
                v-model="trendType"
                size="small"
                @change="handleTrendTypeChange">
              <el-radio-button label="day">日</el-radio-button>
              <el-radio-button label="week">周</el-radio-button>
              <el-radio-button label="month">月</el-radio-button>
                <el-radio-button label="year">年</el-radio-button>
            </el-radio-group>
              <div class="trend-view-switch">
                <el-tooltip content="切换图表视图" placement="top">
                  <el-button 
                    size="small" 
                    icon="el-icon-s-data" 
                    circle
                    @click="switchChartView" />
                </el-tooltip>
                <el-tooltip content="查看对比数据" placement="top">
                  <el-button 
                    size="small" 
                    icon="el-icon-data-analysis" 
                    circle
                    @click="showCompareDialog" />
                </el-tooltip>
          </div>
            </div>
          </div>
          <div class="trend-content">
            <div class="trend-summary">
              <div class="trend-stat-item" :class="getStatTrendClass(currentTrendStats.trend)">
                <div class="stat-value">{{ currentTrendStats.value }}</div>
                <div class="stat-label">当前值</div>
                <div class="stat-change">
                  <i :class="getTrendIcon(currentTrendStats)" />
                  {{ currentTrendStats.change }}%
                </div>
              </div>
              
              <div class="trend-stat-item">
                <div class="stat-value">{{ currentTrendStats.high }}</div>
                <div class="stat-label">最高值</div>
              </div>
              
              <div class="trend-stat-item">
                <div class="stat-value">{{ currentTrendStats.low }}</div>
                <div class="stat-label">最低值</div>
              </div>
              
              <div class="trend-stat-item">
                <div class="stat-value">{{ currentTrendStats.avg }}</div>
                <div class="stat-label">平均值</div>
              </div>
            </div>
            
            <div class="trend-chart-wrapper">
              <div class="trend-chart" ref="trendChart" v-loading="loading.trend"/>
              
              <div class="trend-legend" v-if="chartView === 'multi'">
                <div 
                  v-for="(item, index) in trendLegendItems" 
                  :key="index"
                  class="legend-item"
                  @click="toggleSeries(item.name)"
                  :class="{ disabled: !item.active }"
                >
                  <span class="legend-color" :style="{ backgroundColor: item.color }" />
                  <span class="legend-name">{{ item.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 价格对比 -->
        <el-card class="compare-section">
          <div slot="header">
            <span>价格对比</span>
            <el-button-group style="float: right">
              <el-button size="small" @click="handleExport">导出数据</el-button>
              <el-button size="small" type="primary" @click="handleSubscribe">订阅价格</el-button>
            </el-button-group>
          </div>
          <el-table :data="priceCompare" style="width: 100%">
            <el-table-column prop="name" label="产品名称" min-width="120">
              <template slot-scope="scope">
                <div class="product-info">
                  <img :src="scope.row.image" class="product-image" />
                  <span>{{ scope.row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="currentPrice" label="当前价格" width="120">
              <template slot-scope="scope">
                <span :class="scope.row.priceChange >= 0 ? 'price-up' : 'price-down'">
                  {{ scope.row.currentPrice }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="priceChange" label="价格变化" width="120">
              <template slot-scope="scope">
                <span :class="scope.row.priceChange >= 0 ? 'price-up' : 'price-down'">
                  {{ scope.row.priceChange >= 0 ? '+' : '' }}{{ scope.row.priceChange }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="market" label="市场" width="120"/>
            <el-table-column prop="updateTime" label="更新时间" width="180"/>
            <el-table-column label="操作" width="150">
              <template slot-scope="scope">
                <el-button type="text" @click="showPriceDetail(scope.row)">详情</el-button>
                <el-button type="text" @click="addToWatchlist(scope.row)">关注</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 历史价格 -->
        <el-card class="history-section">
          <div slot="header">
            <span>历史价格</span>
            <el-select v-model="historyType" size="small" style="float: right">
              <el-option label="全部" value="all"/>
              <el-option label="最高价" value="high"/>
              <el-option label="最低价" value="low"/>
            </el-select>
          </div>
          <el-table :data="priceHistory" style="width: 100%">
            <el-table-column prop="date" label="日期" width="180"/>
            <el-table-column prop="price" label="价格" width="120"/>
            <el-table-column prop="change" label="变化">
              <template slot-scope="scope">
                <span :class="scope.row.change >= 0 ? 'price-up' : 'price-down'">
                  {{ scope.row.change >= 0 ? '+' : '' }}{{ scope.row.change }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="volume" label="成交量" width="120"/>
            <el-table-column prop="market" label="市场" width="120"/>
            <el-table-column prop="remark" label="备注"/>
          </el-table>
          <div class="pagination-container">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"/>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧侧边栏 -->
      <el-col :span="8">
        <!-- 市场分析 -->
        <el-card class="market-analysis-section">
          <div slot="header">
            <span>市场分析</span>
            <el-button 
              size="small" 
              type="text" 
              style="float: right" 
              @click="refreshAnalysis"
            >
              刷新
            </el-button>
          </div>
          <div v-loading="loading.analysis">
            <div class="analysis-item">
              <div class="analysis-title">
                <i class="el-icon-data-analysis" />
                市场走势预测
              </div>
              <div class="analysis-content">
                <div class="gauge-chart" ref="gaugeChart" />
                <div class="analysis-text">
                  <p>{{ marketAnalysis.trend }}</p>
                  <div class="analysis-tags">
                    <el-tag 
                      size="mini" 
                      v-for="(tag, index) in marketAnalysis.tags" 
                      :key="index" 
                      :type="tag.type"
                    >
                      {{ tag.name }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
            <div class="analysis-item">
              <div class="analysis-title">
                <i class="el-icon-bell"/>
                重点关注
              </div>
              <div class="analysis-content">
                <div class="focus-product" v-for="(item, index) in marketAnalysis.focusProducts" :key="index">
                  <div class="focus-product-name">{{ item.name }}</div>
                  <div class="focus-product-change" :class="item.trend">
                    <i :class="item.trend === 'up' ? 'el-icon-top' : 'el-icon-bottom'" />
                    {{ item.change }}%
                  </div>
                  <div class="focus-product-reason">{{ item.reason }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 推荐农产品 -->
        <el-card class="recommended-products-section">
          <div slot="header">
            <span>推荐农产品</span>
            <el-button 
              size="small" 
              type="text" 
              style="float: right" 
              @click="$router.push('/shop')"
            >
              查看更多
            </el-button>
          </div>
          <div class="recommended-list" v-loading="loading.recommended">
            <div 
              class="recommended-item" 
              v-for="(product, index) in recommendedProducts" 
              :key="index" 
              @click="viewProductDetail(product)"
            >
              <div class="product-image">
                <img :src="product.image" :alt="product.name" />
                <div class="product-tag" v-if="product.tag">{{ product.tag }}</div>
              </div>
              <div class="product-info">
                <div class="product-name">{{ product.name }}</div>
                <div class="product-price">{{ product.price }}</div>
                <div class="product-meta">
                  <span>{{ product.origin }}</span>
                  <span>{{ product.sales }}销量</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 农业新闻 -->
        <el-card class="ag-news-section">
          <div slot="header">
            <span>农业新闻</span>
            <el-button 
              size="small" 
              type="text" 
              style="float: right" 
              @click="$router.push('/news')"
            >
              更多
            </el-button>
          </div>
          <div v-loading="newsLoading">
            <div 
              v-for="(news, index) in agNews" 
              :key="index" 
              class="news-item" 
              @click="viewNewsDetail(news)">
              <div class="news-image">
                <img :src="news.picUrl || defaultNewsImage" alt="新闻图片" />
              </div>
              <div class="news-content">
                <div class="news-title">{{ news.title }}</div>
                <div class="news-meta">
                  <span class="news-source">{{ news.source }}</span>
                  <span class="news-time">{{ news.ctime }}</span>
                </div>
              </div>
            </div>
            <div v-if="agNews.length === 0 && !newsLoading" class="no-data">
              暂无农业新闻数据
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 价格详情对话框 -->
    <el-dialog
      title="价格详情"
      :visible.sync="dialogVisible"
      width="60%">
      <div class="price-detail">
        <div class="detail-header">
          <div class="product-info">
            <img :src="selectedProduct.image" class="product-image" />
            <div class="product-name">{{ selectedProduct.name }}</div>
          </div>
          <div class="price-info">
            <div class="current-price">{{ selectedProduct.currentPrice }}</div>
            <div class="price-change" :class="selectedProduct.priceChange >= 0 ? 'price-up' : 'price-down'">
              {{ selectedProduct.priceChange >= 0 ? '+' : '' }}{{ selectedProduct.priceChange }}%
            </div>
          </div>
        </div>
        <div class="detail-chart" ref="detailChart"/>
        <div class="detail-table">
          <el-table :data="selectedProduct.history" style="width: 100%">
            <el-table-column prop="date" label="日期" width="180"/>
            <el-table-column prop="price" label="价格" width="120"/>
            <el-table-column prop="change" label="变化">
              <template slot-scope="scope">
                <span :class="scope.row.change >= 0 ? 'price-up' : 'price-down'">
                  {{ scope.row.change >= 0 ? '+' : '' }}{{ scope.row.change }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="volume" label="成交量" width="120"/>
            <el-table-column prop="market" label="市场" width="120"/>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import newsService from '@/services/newsService'
import priceIndicesService from '@/api/price-indices'
import { getProducts } from '@/api/products'

export default {
  name: 'Price',
  data() {
    return {
      filterForm: {
        category: '',
        region: [],
        dateRange: []
      },
      categories: [
        { value: 'grain', label: '粮食' },
        { value: 'vegetable', label: '蔬菜' },
        { value: 'fruit', label: '水果' },
        { value: 'livestock', label: '畜禽' }
      ],
      regions: [
        {
          value: 'north',
          label: '华北地区',
          children: [
            { value: 'beijing', label: '北京' },
            { value: 'tianjin', label: '天津' }
          ]
        },
        {
          value: 'south',
          label: '华南地区',
          children: [
            { value: 'guangzhou', label: '广州' },
            { value: 'shenzhen', label: '深圳' }
          ]
        }
      ],
      priceOverview: [],
      trendType: 'day',
      priceCompare: [],
      historyType: 'all',
      priceHistory: [],
      currentPage: 1,
      pageSize: 10,
      total: 100,
      dialogVisible: false,
      selectedProduct: {
        name: '',
        currentPrice: '',
        priceChange: 0,
        image: '',
        history: []
      },
      trendChart: null,
      detailChart: null,
      gaugeChart: null,
      agNews: [],
      newsLoading: false,
      defaultNewsImage: require('@/assets/default-news-image.png'),
      loading: {
        overview: false,
        trend: false,
        compare: false,
        history: false,
        news: false,
        analysis: false,
        recommended: false
      },
      priceIndicesData: [],
      trendData: [],
      trendCategory: 'all',
      trendCategories: [
        { label: '全部类别', value: 'all' },
        { label: '谷物', value: 'grain' },
        { label: '蔬菜', value: 'vegetable' },
        { label: '水果', value: 'fruit' },
        { label: '肉类', value: 'meat' },
      ],
      chartView: 'single',
      currentTrendStats: {
        value: '100.0',
        trend: 'stable',
        change: '0.0',
        high: '100.0',
        low: '100.0',
        avg: '100.0'
      },
      trendLegendItems: [],
      activeSeries: [],
      compareDialogVisible: false,
      selectedCompareSeries: [],
      marketAnalysis: {
        trend: '',
        tags: [],
        focusProducts: []
      },
      recommendedProducts: []
    }
  },
  mounted() {
    this.initCharts()
  },
  created() {
    this.initData()
  },
  methods: {
    async initData() {
      // 加载价格概览数据
      await this.loadPriceOverview()
      
      // 加载价格趋势数据
      await this.loadTrendData()
      
      // 加载价格对比数据
      await this.loadPriceCompare()
      
      // 加载历史价格数据
      await this.loadPriceHistory()
      
      // 加载市场分析数据
      this.loadMarketAnalysis()
      
      // 加载推荐农产品
      this.loadRecommendedProducts()
      
      // 加载农业新闻
      this.fetchAgNews()
    },
    
    async loadPriceOverview() {
      this.loading.overview = true
      try {
        const response = await priceIndicesService.getMainProductsForHome()
        if (response && response.code === 200 && response.data) {
          // 处理主要产品数据为概览格式
          const mainProducts = response.data
          
          // 显示数据来源提示（仅在开发环境）
          if (process.env.NODE_ENV === 'development' && response.message && response.message.includes('模拟数据')) {
            console.info('使用模拟数据：价格概览')
            this.$message.info('当前使用模拟数据展示价格指数')
          }
          
          // 计算平均价格指数
          const avgIndex = mainProducts.reduce((sum, item) => sum + item.value, 0) / mainProducts.length
          
          // 找出最高和最低价格指数
          const maxIndex = Math.max(...mainProducts.map(item => item.value))
          const minIndex = Math.min(...mainProducts.map(item => item.value))
          
          // 计算波动值
          const volatility = Math.max(...mainProducts.map(item => Math.abs(item.value - 100)))
          
          // 更新价格概览数据
          this.priceOverview = [
            {
              title: '平均价格指数',
              value: avgIndex.toFixed(1),
              change: avgIndex > 100 ? `+${(avgIndex - 100).toFixed(1)}%` : `${(avgIndex - 100).toFixed(1)}%`,
              trend: avgIndex > 100 ? 'up' : avgIndex < 100 ? 'down' : 'stable',
          icon: 'el-icon-price-tag',
          color: '#409EFF'
        },
        {
              title: '最高价格指数',
              value: maxIndex.toFixed(1),
              change: `+${(maxIndex - 100).toFixed(1)}%`,
          trend: 'up',
          icon: 'el-icon-top',
          color: '#F56C6C'
        },
        {
              title: '最低价格指数',
              value: minIndex.toFixed(1),
              change: `${(minIndex - 100).toFixed(1)}%`,
          trend: 'down',
          icon: 'el-icon-bottom',
          color: '#67C23A'
        },
        {
              title: '指数波动率',
              value: volatility.toFixed(1),
              change: '±0',
              trend: 'stable',
              icon: 'el-icon-data-line',
              color: '#E6A23C'
            }
          ]
          
          // 保存完整数据供后续使用
          this.priceIndicesData = mainProducts
        } else {
          console.warn('价格指数数据格式不正确，使用默认数据')
          this.setDefaultPriceOverview()
        }
      } catch (error) {
        console.warn('加载价格概览数据异常，使用默认数据')
        this.setDefaultPriceOverview()
      } finally {
        this.loading.overview = false
      }
    },
    
    setDefaultPriceOverview() {
      this.priceOverview = [
        {
          title: '平均价格指数',
          value: '104.2',
          change: '+4.2%',
          trend: 'up',
          icon: 'el-icon-price-tag',
          color: '#409EFF'
        },
        {
          title: '最高价格指数',
          value: '155.7',
          change: '+55.7%',
          trend: 'up',
          icon: 'el-icon-top',
          color: '#F56C6C'
        },
        {
          title: '最低价格指数',
          value: '85.9',
          change: '-14.1%',
          trend: 'down',
          icon: 'el-icon-bottom',
          color: '#67C23A'
        },
        {
          title: '指数波动率',
          value: '±69.8',
          change: '±0',
          trend: 'stable',
          icon: 'el-icon-data-line',
          color: '#E6A23C'
        }
      ]
    },
    
    async loadTrendData() {
      this.loading.trend = true
      try {
        const response = await priceIndicesService.getPriceIndicesTrends()
        if (response && response.code === 200 && response.data) {
          // 显示数据来源提示（仅在开发环境）
          if (process.env.NODE_ENV === 'development' && response.message && response.message.includes('模拟数据')) {
            console.info('使用模拟数据：价格趋势')
          }
          
          this.trendData = response.data
          this.$nextTick(() => {
            this.initTrendChart()
          })
        } else {
          console.warn('价格趋势数据格式不正确，使用默认图表')
          this.initTrendChart() // 使用默认数据
        }
      } catch (error) {
        console.warn('加载价格趋势数据异常，使用默认图表')
        this.initTrendChart() // 使用默认数据
      } finally {
        this.loading.trend = false
      }
    },
    
    async loadPriceCompare() {
      this.loading.compare = true
      try {
        if (this.priceIndicesData.length > 0) {
          // 使用已加载的价格指数数据构建对比表
          const currentDate = new Date().toISOString().slice(0, 10)
          
          this.priceCompare = this.priceIndicesData.map(item => {
            // 从类别名中提取产品名
            const name = item.category.replace(/生产价格指数$/, '')
            
            // 随机生成市场名
            const markets = ['北京市场', '广州市场', '上海市场', '成都市场', '西安市场']
            const market = markets[Math.floor(Math.random() * markets.length)]
            
            // 取价格指数作为变化基准
            const priceChange = item.value - 100
            
            // 根据价格指数生成当前价格
            const basePrice = (2 + Math.random() * 8).toFixed(1) // 2-10元基础价
            const currentPrice = `￥${basePrice}/kg`
            
            return {
              id: item.id,
              name,
              currentPrice,
              priceChange,
              market,
              updateTime: `${currentDate} 10:00`,
              image: this.getCategoryImage(name),
              originalData: item
            }
          })
        } else {
          // 使用默认数据
          this.priceCompare = [
        {
          name: '大米',
          currentPrice: '￥2.5/kg',
          priceChange: 2.3,
          market: '北京市场',
          updateTime: '2024-03-28 10:00',
          image: require('@/assets/products/rice.jpg')
        },
        {
          name: '苹果',
          currentPrice: '￥5.8/kg',
          priceChange: -1.5,
          market: '广州市场',
          updateTime: '2024-03-28 10:00',
          image: require('@/assets/products/apple.jpg')
        }
          ]
        }
      } catch (error) {
        console.error('加载价格对比数据失败:', error)
        this.$message.warning('加载价格对比数据失败，使用默认数据')
      } finally {
        this.loading.compare = false
      }
    },
    
    async loadPriceHistory() {
      this.loading.history = true
      try {
        if (this.priceIndicesData.length > 0) {
          // 选择一种产品，为其生成历史数据
          const selectedProduct = this.priceIndicesData[0]
          const productName = selectedProduct.category.replace(/生产价格指数$/, '')
          
          // 获取最近30天的日期
          const dates = []
          const today = new Date()
          for (let i = 30; i >= 1; i--) {
            const date = new Date(today)
            date.setDate(date.getDate() - i)
            dates.push(date.toISOString().slice(0, 10))
          }
          
          // 生成历史数据
          this.priceHistory = dates.map((date, index) => {
            // 基于基准值生成波动价格
            const baseValue = 100
            const volatility = 5 // 波动范围
            const randomFactor = (Math.random() * volatility * 2) - volatility
            const value = baseValue + randomFactor
            
            // 计算与前一天的变化
            const prevValue = index > 0 
              ? parseFloat(this.priceHistory[index - 1]?.price.replace('￥', '').replace('/kg', '')) 
              : baseValue / 100 * 2.5
            const currentValue = value / 100 * 2.5
            const change = ((currentValue - prevValue) / prevValue * 100).toFixed(1)
            
            // 随机生成成交量
            const volume = `${900 + Math.floor(Math.random() * 200)}吨`
            
            // 随机生成市场
            const markets = ['北京市场', '广州市场', '上海市场', '成都市场', '西安市场']
            const market = markets[Math.floor(Math.random() * markets.length)]
            
            // 生成备注
            const remarks = [
              '市场供应充足',
              '需求稳定',
              '需求略有上升',
              '供应量增加',
              '库存略有减少',
              '市场交易活跃',
              '受天气影响供应减少',
              '节日需求增加'
            ]
            const remark = remarks[Math.floor(Math.random() * remarks.length)]
            
            return {
              date,
              price: `￥${currentValue.toFixed(1)}/kg`,
              change,
              volume,
              market,
              remark,
              product: productName
            }
          })
          
          // 更新总数
          this.total = this.priceHistory.length
        } else {
          // 使用默认数据
          this.priceHistory = [
        {
          date: '2024-03-28',
          price: '￥2.5/kg',
          change: 2.3,
          volume: '1000吨',
          market: '北京市场',
          remark: '市场供应充足'
        },
        {
          date: '2024-03-27',
          price: '￥2.4/kg',
          change: -1.2,
          volume: '950吨',
          market: '北京市场',
          remark: '需求略有下降'
        }
          ]
        }
      } catch (error) {
        console.error('加载历史价格数据失败:', error)
        this.$message.warning('加载历史价格数据失败，使用默认数据')
      } finally {
        this.loading.history = false
      }
    },
    
    // 获取产品图片
    getCategoryImage(category) {
      // 使用已有的图片资源
      const images = {
        '谷物': require('@/assets/products/rice.jpg'),
        '蔬菜': require('@/assets/products/fertilizer.jpg'), // 用肥料图片替代蔬菜
        '水果': require('@/assets/products/apple.jpg'),
        '猪': require('@/assets/products/seeds.jpg'), // 用种子图片替代猪肉
        '牛': require('@/assets/products/pesticide.jpg'), // 用农药图片替代牛肉
        '羊': require('@/assets/products/tools.jpg'), // 用工具图片替代羊肉
        '蛋类': require('@/assets/products/sprayer.jpg'), // 用喷雾器图片替代蛋类
        '渔业产品': require('@/assets/products/irrigation.jpg') // 用灌溉设备图片替代渔业产品
      }
      
      // 查找匹配的图片
      for (const key in images) {
        if (category.includes(key)) {
          return images[key]
        }
      }
      
      // 默认图片
      return require('@/assets/products/rice-seeds.jpg') // 用稻种图片作为默认图片
    },
    
    // 根据趋势返回颜色
    getTrendColor(trend) {
      const colors = {
        up: '#67C23A',
        down: '#F56C6C',
        stable: '#E6A23C'
      }
      return colors[trend] || '#909399'
    },
    
    handleFilter() {
      // 实现筛选逻辑
      this.loadPriceCompare()
      this.loadPriceHistory()
    },
    
    resetFilter() {
      this.filterForm = {
        category: '',
        region: [],
        dateRange: []
      }
      this.loadPriceCompare()
      this.loadPriceHistory()
    },
    
    handleTrendTypeChange() {
      // 切换趋势数据类型
      this.initTrendChart()
    },
    
    initCharts() {
      // 初始化趋势图表 - 使用空方法，实际在loadTrendData后调用initTrendChart
    },
    
    initTrendChart() {
      if (!this.$refs.trendChart) return
      
      // 如果图表已存在，先销毁
      if (this.trendChart) {
        this.trendChart.dispose()
      }
      
      // 创建新图表
      this.trendChart = echarts.init(this.$refs.trendChart)
      
      // 计算统计数据
      this.calculateTrendStats()
      
      // 根据当前视图类型设置图表
      if (this.chartView === 'single') {
        this.initSingleCategoryChart()
      } else {
        this.initMultiCategoryChart()
      }
      
      // 添加图表交互事件
      this.setupChartEvents()
      
      // 监听窗口大小变化，自动调整图表大小
      window.addEventListener('resize', () => {
        this.trendChart && this.trendChart.resize()
      })
    },
    
    // 初始化单品类图表
    initSingleCategoryChart() {
      // 构建X轴数据
      const xAxisData = this.buildXAxisData()
      
      // 获取价格数据
      let priceData = []
      let volumeData = []
      
      if (this.trendData.length > 0) {
        // 如果有实际数据，使用它
        const filteredData = this.trendCategory === 'all' 
          ? this.trendData 
          : this.trendData.filter(item => {
              const category = item.category?.toLowerCase() || ''
              return category.includes(this.trendCategory)
            })
        
        if (filteredData.length > 0) {
          // 使用第一条数据作为单一品类数据
          const mainData = filteredData[0]
          
          // 根据趋势类型生成数据
          priceData = xAxisData.map(() => 95 + Math.random() * 15)
          volumeData = xAxisData.map(() => 800 + Math.random() * 600)
          
          // 确保价格趋势与品类趋势一致
          if (mainData.trend === 'up') {
            priceData.sort((a, b) => a - b)
          } else if (mainData.trend === 'down') {
            priceData.sort((a, b) => b - a)
          }
        } else {
          // 没有匹配的品类，使用随机数据
          priceData = xAxisData.map(() => 95 + Math.random() * 15)
          volumeData = xAxisData.map(() => 800 + Math.random() * 600)
        }
      } else {
        // 没有数据，使用随机数据
        priceData = xAxisData.map(() => 95 + Math.random() * 15)
        volumeData = xAxisData.map(() => 800 + Math.random() * 600)
      }
      
      // 计算MA5和MA10
      const ma5 = this.calculateMA(5, priceData)
      const ma10 = this.calculateMA(10, priceData)
      
      // 图表配置
      const option = {
        animation: true,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: (params) => {
            let result = params[0].axisValue + '<br/>'
            params.forEach(param => {
              const color = param.color
              const marker = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${color};"></span>`
              result += marker + param.seriesName + ': ' + param.value + '<br/>'
            })
            return result
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        legend: {
          data: ['价格指数', '5日均线', '10日均线', '成交量'],
          bottom: 10
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData,
          axisLabel: {
            color: '#606266',
            rotate: 30
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '价格指数',
            min: Math.floor(Math.min(...priceData) * 0.95),
            max: Math.ceil(Math.max(...priceData) * 1.05),
            axisLabel: {
              color: '#606266',
              formatter: '{value}'
            },
            splitLine: {
              lineStyle: {
                type: 'dashed'
              }
            }
          },
          {
            type: 'value',
            name: '成交量(吨)',
            axisLabel: {
              color: '#606266'
            },
            splitLine: {
              show: false
            }
          }
        ],
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100,
            xAxisIndex: [0]
          },
          {
            show: true,
            type: 'slider',
            bottom: 50,
            start: 0,
            end: 100,
            xAxisIndex: [0]
          }
        ],
        series: [
          {
            name: '价格指数',
            type: 'line',
            smooth: true,
            symbol: 'emptyCircle',
            symbolSize: 6,
            data: priceData,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0.1)'
                }
              ])
            },
            markPoint: {
              data: [
                { type: 'max', name: '最高值' },
                { type: 'min', name: '最低值' }
              ]
            }
          },
          {
            name: '5日均线',
            type: 'line',
            smooth: true,
            symbol: 'none',
            data: ma5,
            lineStyle: {
              width: 1,
              type: 'dashed',
              color: '#F56C6C'
            }
          },
          {
            name: '10日均线',
            type: 'line',
            smooth: true,
            symbol: 'none',
            data: ma10,
            lineStyle: {
              width: 1,
              type: 'dashed',
              color: '#67C23A'
            }
          },
          {
            name: '成交量',
            type: 'bar',
            yAxisIndex: 1,
            data: volumeData,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#67C23A'
                },
                {
                  offset: 1,
                  color: '#95D475'
                }
              ])
            },
            barMaxWidth: 10,
            barGap: '30%'
          }
        ]
      }
      
      this.trendChart.setOption(option)
    },
    
    // 初始化多品类对比图表
    initMultiCategoryChart() {
      // 构建X轴数据
      const xAxisData = this.buildXAxisData(true) // 使用简化的X轴标签
      
      // 为不同类别准备不同的颜色
      const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#7B66FF', '#1ABC9C', '#3498DB']
      
      // 准备多个品类的数据
      let seriesData = []
      this.trendLegendItems = []
      this.activeSeries = []
      
      if (this.priceIndicesData.length > 0) {
        // 最多显示8个品类
        const categories = this.priceIndicesData.slice(0, 8)
        
        seriesData = categories.map((category, index) => {
          const categoryName = category.category.replace(/生产价格指数$/, '')
          const baseValue = category.value || 100
          
          // 生成基于品类指数特性的数据
          const data = xAxisData.map((_, idx) => {
            const trend = category.trend === 'up' ? 1 : category.trend === 'down' ? -1 : 0
            const factor = idx / xAxisData.length // 随时间变化的因子
            const volatility = 0.1 * Math.random() // 随机波动
            // 基于趋势生成有方向性的数据
            return baseValue * (1 + trend * factor * 0.1 + volatility)
          })
          
          // 添加图例项
          this.trendLegendItems.push({
            name: categoryName,
            color: colors[index % colors.length],
            active: true
          })
          
          // 添加到活跃系列
          this.activeSeries.push(categoryName)
          
          return {
            name: categoryName,
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            data: data,
            itemStyle: {
              color: colors[index % colors.length]
            },
            lineStyle: {
              width: 2
            }
          }
        })
      } else {
        // 没有实际数据，使用模拟数据
        const demoCategories = ['谷物', '蔬菜', '水果', '猪肉', '牛肉']
        const baseValues = [104.1, 105.2, 95.3, 155.7, 110.5]
        const trends = ['up', 'up', 'down', 'up', 'up']
        
        seriesData = demoCategories.map((name, index) => {
          const baseValue = baseValues[index]
          const trend = trends[index] === 'up' ? 1 : -1
          
          const data = xAxisData.map((_, idx) => {
            const factor = idx / xAxisData.length
            const volatility = 0.1 * Math.random()
            return baseValue * (1 + trend * factor * 0.1 + volatility)
          })
          
          // 添加图例项
          this.trendLegendItems.push({
            name: name,
            color: colors[index % colors.length],
            active: true
          })
          
          // 添加到活跃系列
          this.activeSeries.push(name)
          
          return {
            name: name,
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            data: data,
            itemStyle: {
              color: colors[index % colors.length]
            },
            lineStyle: {
              width: 2
            }
          }
        })
      }
      
      // 多品类图表配置
      const option = {
        animation: true,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData,
          axisLabel: {
            color: '#606266'
          }
        },
        yAxis: {
          type: 'value',
          name: '价格指数',
          min: 80,
          max: 160,
          axisLabel: {
            color: '#606266',
            formatter: '{value}'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            show: true,
            type: 'slider',
            bottom: 50,
            start: 0,
            end: 100
          }
        ],
        series: seriesData
      }
      
      this.trendChart.setOption(option)
    },
    
    // 设置图表交互事件
    setupChartEvents() {
      if (!this.trendChart) return
      
      // 点击事件
      this.trendChart.on('click', (params) => {
        if (params.componentType === 'markPoint') {
          // 点击标记点的逻辑
          this.$message.info(`${params.name}: ${params.value}`)
        } else if (params.componentType === 'series') {
          // 点击数据点的逻辑
          if (this.chartView === 'single' && params.seriesName === '价格指数') {
            // 在单品类视图下点击价格指数点时显示详细信息
            this.$notify({
              title: '价格详情',
              message: `日期: ${params.name}, 价格指数: ${params.value}`,
              type: 'info',
              duration: 2000
            })
          }
        }
      })
    },
    
    // 根据趋势类型构建X轴数据
    buildXAxisData(simplified = false) {
      let xAxisData = []
      
      if (this.trendType === 'day') {
        // 最近7天
        const dates = []
        const today = new Date()
        for (let i = 6; i >= 0; i--) {
          const date = new Date(today)
          date.setDate(date.getDate() - i)
          
          // 格式化日期为MM-DD
          const month = date.getMonth() + 1
          const day = date.getDate()
          const formattedDate = simplified 
            ? `${day}日` 
            : `${month}-${day}`
          
          dates.push(formattedDate)
        }
        xAxisData = dates
      } else if (this.trendType === 'week') {
        // 最近4周
        for (let i = 4; i >= 1; i--) {
          xAxisData.push(simplified ? `第${i}周` : `第${i}周`)
        }
      } else if (this.trendType === 'month') {
        // 最近6个月
        const months = []
        const today = new Date()
        for (let i = 5; i >= 0; i--) {
          const date = new Date(today)
          date.setMonth(date.getMonth() - i)
          months.push(simplified ? `${date.getMonth() + 1}月` : `${date.getFullYear()}-${date.getMonth() + 1}月`)
        }
        xAxisData = months
      } else {
        // 年视图（最近5年）
        const currentYear = new Date().getFullYear()
        for (let i = 4; i >= 0; i--) {
          xAxisData.push(`${currentYear - i}`)
        }
      }
      
      return xAxisData
    },
    
    // 计算移动平均线
    calculateMA(dayCount, data) {
      const result = []
      for (let i = 0; i < data.length; i++) {
        if (i < dayCount - 1) {
          // 数据不足以计算均值，使用连接符表示
          result.push('-')
          continue
        }
        let sum = 0
        for (let j = 0; j < dayCount; j++) {
          sum += data[i - j]
        }
        result.push((sum / dayCount).toFixed(2))
      }
      return result
    },
    
    // 计算趋势统计数据
    calculateTrendStats() {
      // 使用当前趋势图表数据计算统计信息
      let dataPoints = []
      
      if (this.trendChart) {
        try {
          const option = this.trendChart.getOption()
          const mainSeries = option.series.find(s => s.name === '价格指数')
          if (mainSeries && mainSeries.data) {
            dataPoints = mainSeries.data.filter(d => d !== '-').map(Number)
          }
        } catch (e) {
          console.warn('无法获取图表数据:', e)
        }
      }
      
      if (dataPoints.length === 0 && this.trendData.length > 0) {
        // 使用基本趋势数据
        const firstItem = this.trendData[0]
        const baseValue = firstItem.value || 100
        
        dataPoints = Array(7).fill(0).map(() => baseValue * (0.95 + Math.random() * 0.1))
      } else if (dataPoints.length === 0) {
        // 生成随机数据
        dataPoints = Array(7).fill(0).map(() => 95 + Math.random() * 10)
      }
      
      if (dataPoints.length > 0) {
        const lastValue = dataPoints[dataPoints.length - 1]
        const prevValue = dataPoints[dataPoints.length - 2] || dataPoints[0]
        const change = ((lastValue - prevValue) / prevValue * 100).toFixed(1)
        const trend = change > 0 ? 'up' : change < 0 ? 'down' : 'stable'
        
        this.currentTrendStats = {
          value: lastValue.toFixed(1),
          high: Math.max(...dataPoints).toFixed(1),
          low: Math.min(...dataPoints).toFixed(1),
          avg: (dataPoints.reduce((sum, val) => sum + val, 0) / dataPoints.length).toFixed(1),
          change: change,
          trend: trend
        }
      }
    },
    
    // 获取统计趋势的样式类
    getStatTrendClass(trend) {
      return {
        'up': trend === 'up',
        'down': trend === 'down',
        'stable': trend === 'stable' || !trend
      }
    },
    
    // 获取趋势图标
    getTrendIcon(item) {
      if (!item || !item.trend) return 'el-icon-minus';
      
      return {
        'up': 'el-icon-top',
        'down': 'el-icon-bottom',
        'stable': 'el-icon-minus'
      }[item.trend] || 'el-icon-minus';
    },
    
    // 切换图表视图类型
    switchChartView() {
      this.chartView = this.chartView === 'single' ? 'multi' : 'single'
      this.$nextTick(() => {
        this.initTrendChart()
      })
    },
    
    // 显示对比对话框
    showCompareDialog() {
      this.compareDialogVisible = true
      // 在实际对话框中可以选择要对比的数据系列
    },
    
    // 切换数据系列的显示与隐藏
    toggleSeries(seriesName) {
      const index = this.activeSeries.indexOf(seriesName)
      if (index > -1) {
        this.activeSeries.splice(index, 1)
      } else {
        this.activeSeries.push(seriesName)
      }
      
      // 更新图例状态
      this.trendLegendItems.forEach(item => {
        if (item.name === seriesName) {
          item.active = !item.active
        }
      })
      
      // 更新图表
      if (this.trendChart) {
        const option = this.trendChart.getOption()
        option.series.forEach((series, idx) => {
          if (series.name === seriesName) {
            this.trendChart.dispatchAction({
              type: this.activeSeries.includes(seriesName) ? 'highlight' : 'downplay',
              seriesIndex: idx
            })
          }
        })
      }
    },
    
    showPriceDetail(product) {
      this.selectedProduct = {
        ...product,
        history: [] // 清空历史数据
      }
      
      // 生成模拟历史数据
      const dates = []
      const today = new Date()
      for (let i = 30; i >= 1; i--) {
        const date = new Date(today)
        date.setDate(date.getDate() - i)
        dates.push(date.toISOString().slice(0, 10))
      }
      
      // 基于当前价格和变化趋势生成历史数据
      let prevPrice = parseFloat(product.currentPrice.replace('￥', '').replace('/kg', ''))
      const volatility = 0.1 // 每日波动范围
      
      this.selectedProduct.history = dates.map((date, index) => {
        // 基于趋势生成每日价格变化
        const dailyChange = (Math.random() * volatility * 2 - volatility) * (product.priceChange >= 0 ? 1.5 : 0.5)
        const newPrice = index === 0 ? prevPrice * 0.9 : prevPrice - dailyChange
        prevPrice = newPrice
        
        // 计算变化率
        const change = index === 0 ? 0 : dailyChange / (prevPrice + dailyChange) * 100
        
        return {
          date,
          price: `￥${newPrice.toFixed(2)}/kg`,
          change: change.toFixed(1),
          volume: `${800 + Math.floor(Math.random() * 400)}吨`,
          market: product.market
        }
      }).reverse() // 从最早到最近排序
      
      this.dialogVisible = true
      this.$nextTick(() => {
        this.initDetailChart()
      })
    },
    
    initDetailChart() {
      if (!this.$refs.detailChart) return
      
      if (this.detailChart) {
        this.detailChart.dispose()
      }
      
      this.detailChart = echarts.init(this.$refs.detailChart)
      
      // 提取日期和价格数据
      const history = this.selectedProduct.history
      const dates = history.map(item => item.date.slice(5))
      const prices = history.map(item => parseFloat(item.price.replace('￥', '').replace('/kg', '')))
      
      // 计算移动平均线
      const ma5 = this.calculateMA(5, prices)
      const ma10 = this.calculateMA(10, prices)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params) {
            let result = params[0].axisValue + '<br/>'
            params.forEach(param => {
              result += `${param.seriesName}: ${param.value || '-'}<br/>`
            })
            return result
          }
        },
        legend: {
          data: ['价格', '5日均价', '10日均价'],
          bottom: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dates,
          axisLabel: {
            rotate: 30
          },
          boundaryGap: false
        },
        yAxis: {
          type: 'value',
          axisLine: { show: true },
          axisLabel: {
            formatter: '￥{value}/kg'
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        dataZoom: [
          {
            type: 'inside',
            start: 50,
            end: 100
          },
          {
            show: true,
            type: 'slider',
            bottom: 50,
            start: 50,
            end: 100
          }
        ],
        series: [
          {
            name: '价格',
            type: 'line',
            data: prices,
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#409EFF'
            },
            lineStyle: {
              width: 2
            },
            markPoint: {
              data: [
                { type: 'max', name: '最高价' },
                { type: 'min', name: '最低价' }
              ]
            }
          },
          {
            name: '5日均价',
            type: 'line',
            data: ma5,
            symbol: 'none',
            lineStyle: {
              width: 1,
              type: 'dashed',
              color: '#F56C6C'
            }
          },
          {
            name: '10日均价',
            type: 'line',
            data: ma10,
            symbol: 'none',
            lineStyle: {
              width: 1,
              type: 'dashed',
              color: '#67C23A'
            }
          }
        ]
      }
      
      this.detailChart.setOption(option)
      
      // 监听窗口大小变化，自动调整图表大小
      window.addEventListener('resize', () => {
        this.detailChart && this.detailChart.resize()
      })
    },
    
    addToWatchlist(product) {
      this.$message({
        message: `已将 ${product.name} 添加到关注列表`,
        type: 'success'
      })
    },
    
    handleExport() {
      this.$message({
        message: '数据导出成功',
        type: 'success'
      })
    },
    
    handleSubscribe() {
      this.$message({
        message: '订阅成功，价格变动将通过消息通知您',
        type: 'success'
      })
    },
    
    async fetchAgNews() {
      this.loading.news = true
      try {
        console.log('开始获取农业新闻...');
        const newsList = await newsService.getNewsList({ num: 5 });
        console.log('获取到的农业新闻列表:', newsList);
        
        if (newsList && newsList.length > 0) {
          this.agNews = newsList;
        } else {
          console.warn('API未返回新闻数据');
          this.agNews = [];
        }
      } catch (error) {
        console.error('获取农业新闻失败:', error);
        this.agNews = [];
      } finally {
        this.loading.news = false
      }
    },
    
    viewNewsDetail(news) {
      this.$router.push(`/news/detail/${news.id}?from=/price`);
    },
    
    handleSizeChange(val) {
      this.pageSize = val
      // 重新加载数据
    },
    
    handleCurrentChange(val) {
      this.currentPage = val
      // 重新加载数据
    },
    
    async loadMarketAnalysis() {
      this.loading.analysis = true
      try {
        // 未来可以从API获取市场分析数据
        // 目前使用模拟数据
        setTimeout(() => {
          // 模拟市场走势预测数据
          this.marketAnalysis = {
            trend: '近期农产品市场整体呈现季节性波动，受天气和季节变化影响，预计未来一周内谷物类价格将保持稳定，蔬菜和水果类价格可能小幅上涨，肉类价格或将出现回调。',
            tags: [
              { name: '谷物稳定', type: '' },
              { name: '蔬果上涨', type: 'success' },
              { name: '肉类回调', type: 'warning' }
            ],
            focusProducts: [
              {
                name: '有机蔬菜',
                trend: 'up',
                change: 12.5,
                reason: '受近期天气影响，供应减少，需求增加'
              },
              {
                name: '水果',
                trend: 'up',
                change: 8.3,
                reason: '季节性供应不足，价格上涨'
              },
              {
                name: '生猪',
                trend: 'down',
                change: 5.2,
                reason: '供应充足，需求稳定，价格回落'
              }
            ]
          }
          
          // 初始化仪表盘图表
          this.$nextTick(() => {
            this.initGaugeChart()
          })
          
          this.loading.analysis = false
        }, 1000)
      } catch (error) {
        console.error('加载市场分析数据失败:', error)
        this.loading.analysis = false
      }
    },
    
    async loadRecommendedProducts() {
      this.loading.recommended = true
      try {
        const response = await getProducts({ sortBy: 'sales', sortOrder: 'desc', pageSize: 5 });
        this.recommendedProducts = response.items;
        this.loading.recommended = false;
      } catch (error) {
        console.error('Failed to load recommended products:', error);
        this.recommendedProducts = [];
        this.loading.recommended = false;
      }
    },
    
    initGaugeChart() {
      if (!this.$refs.gaugeChart) return
      
      // 如果图表已存在，先销毁
      if (this.gaugeChart) {
        this.gaugeChart.dispose()
      }
      
      // 创建新图表
      this.gaugeChart = echarts.init(this.$refs.gaugeChart)
      
      // 计算市场信心指数（模拟数据）
      const confidenceIndex = 65 + Math.random() * 20
      
      const option = {
        series: [
          {
            type: 'gauge',
            data: [{ value: confidenceIndex.toFixed(1), name: '市场信心指数' }],
            title: {
              fontSize: 14,
              color: '#303133'
            },
            axisLine: {
              lineStyle: {
                width: 20,
                color: [
                  [0.3, '#F56C6C'],
                  [0.7, '#E6A23C'],
                  [1, '#67C23A']
                ]
              }
            },
            detail: {
              formatter: '{value}%',
              fontSize: 16,
              offsetCenter: [0, '70%']
            }
          }
        ]
      }
      
      this.gaugeChart.setOption(option)
      
      // 监听窗口大小变化，自动调整图表大小
      window.addEventListener('resize', () => {
        this.gaugeChart && this.gaugeChart.resize()
      })
    },
    
    refreshAnalysis() {
      this.loadMarketAnalysis()
      this.$message({
        message: '市场分析数据已更新',
        type: 'success'
      })
    },
    
    viewProductDetail(product) {
      this.$router.push(`/shop/product/${product.id}`)
    }
  },
  beforeDestroy() {
    if (this.trendChart) {
      this.trendChart.dispose()
    }
    if (this.detailChart) {
      this.detailChart.dispose()
    }
    
    // 销毁仪表盘图表
    if (this.gaugeChart) {
      this.gaugeChart.dispose()
    }
    
    // 清除窗口大小监听器
    window.removeEventListener('resize', () => {})
  }
}
</script>

<style lang="scss" scoped>
.price {
  padding: 20px;
  
  .filter-section {
    margin-bottom: 20px;
    border-radius: 12px;
    
    .filter-form {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
    }
  }
  
  .overview-section {
    margin-bottom: 20px;
    
    .overview-card {
      height: 100%;
      border-radius: 12px;
      transition: all 0.3s ease;
      overflow: hidden;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
      }
      
      .overview-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        float: left;
        
        i {
          color: #fff;
          font-size: 24px;
        }
      }
      
      .overview-content {
        overflow: hidden;
        
        .overview-title {
          color: #606266;
          font-size: 14px;
          margin-bottom: 10px;
        }
        
        .overview-value {
          color: #303133;
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 5px;
        }
        
        .overview-change {
          display: inline-block;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 12px;
          
          &.up {
            background-color: rgba(103, 194, 58, 0.1);
            color: #67C23A;
          }
          
          &.down {
            background-color: rgba(245, 108, 108, 0.1);
            color: #F56C6C;
          }
          
          &.stable {
            background-color: rgba(230, 162, 60, 0.1);
            color: #E6A23C;
          }
        }
      }
    }
  }
  
  .trend-section {
    margin-bottom: 20px;
    border-radius: 12px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
    }
    
    .trend-controls {
      float: right;
      display: flex;
      align-items: center;
      
      .trend-view-switch {
        margin-left: 10px;
        display: flex;
        gap: 5px;
      }
    }
    
    .trend-content {
      display: flex;
      flex-direction: column;
      
      .trend-summary {
        display: flex;
        margin-bottom: 20px;
        
        .trend-stat-item {
          flex: 1;
          text-align: center;
          padding: 15px 10px;
          border-radius: 8px;
          margin-right: 15px;
          background-color: #f5f7fa;
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
          transition: all 0.3s ease;
          
          &:last-child {
            margin-right: 0;
          }
          
          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.1);
          }
          
          &.up {
            background-color: rgba(103, 194, 58, 0.1);
            border-left: 3px solid #67C23A;
          }
          
          &.down {
            background-color: rgba(245, 108, 108, 0.1);
            border-left: 3px solid #F56C6C;
          }
          
          &.stable {
            background-color: rgba(230, 162, 60, 0.1);
            border-left: 3px solid #E6A23C;
          }
          
          .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #303133;
            margin-bottom: 5px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            margin-bottom: 8px;
          }
          
          .stat-change {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 500;
            
            .up & {
              background-color: rgba(103, 194, 58, 0.2);
              color: #67C23A;
            }
            
            .down & {
              background-color: rgba(245, 108, 108, 0.2);
              color: #F56C6C;
            }
            
            .stable & {
              background-color: rgba(230, 162, 60, 0.2);
              color: #E6A23C;
            }
          }
        }
      }
      
      .trend-chart-wrapper {
        position: relative;
        width: 100%;
    
    .trend-chart {
      height: 400px;
          width: 100%;
        }
        
        .trend-legend {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          margin-top: 10px;
          
          .legend-item {
            display: flex;
            align-items: center;
            padding: 5px 10px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            
            &:hover {
              background-color: #f5f7fa;
            }
            
            &.disabled {
              opacity: 0.5;
            }
            
            .legend-color {
              width: 12px;
              height: 12px;
              border-radius: 2px;
              margin-right: 5px;
            }
            
            .legend-name {
              font-size: 12px;
              color: #606266;
            }
          }
        }
      }
    }
  }
  
  .compare-section {
    margin-bottom: 20px;
    border-radius: 12px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
    }
    
    .product-info {
      display: flex;
      align-items: center;
      
      .product-image {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
        margin-right: 10px;
      }
    }
    
    .price-up {
      color: #67C23A;
    }
    
    .price-down {
      color: #F56C6C;
    }
  }
  
  .history-section {
    margin-bottom: 20px;
    border-radius: 12px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
    }
    
    .pagination-container {
      margin-top: 20px;
      text-align: center;
    }
  }
  
  .market-analysis-section {
    margin-bottom: 20px;
    border-radius: 12px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
    }
    
    .analysis-item {
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px dashed #ebeef5;
      
      &:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
      }
      
      .analysis-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 15px;
        
        i {
          margin-right: 8px;
          color: #409EFF;
        }
      }
      
      .analysis-content {
        .gauge-chart {
          height: 180px;
          width: 100%;
          margin-bottom: 10px;
        }
        
        .analysis-text {
          p {
            font-size: 14px;
            color: #606266;
            line-height: 1.6;
            margin-bottom: 10px;
          }
          
          .analysis-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
          }
        }
        
        .focus-product {
          display: flex;
          flex-wrap: wrap;
          padding: 10px;
          background-color: #f5f7fa;
          border-radius: 6px;
          margin-bottom: 10px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .focus-product-name {
            width: 40%;
            font-weight: 600;
            color: #303133;
          }
          
          .focus-product-change {
            width: 20%;
            text-align: right;
            font-weight: 600;
            
            &.up {
    color: #67C23A;
  }
  
            &.down {
    color: #F56C6C;
            }
          }
          
          .focus-product-reason {
            width: 100%;
            margin-top: 8px;
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
  
  .recommended-products-section {
      margin-bottom: 20px;
    border-radius: 12px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
    }
    
    .recommended-list {
      .recommended-item {
        display: flex;
        padding: 12px 0;
        border-bottom: 1px solid #f0f2f5;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:last-child {
          border-bottom: none;
        }
        
        &:hover {
          background-color: #f5f7fa;
          transform: translateX(5px);
        
        .product-name {
            color: #409EFF;
          }
        }
        
        .product-image {
          width: 70px;
          height: 70px;
          border-radius: 8px;
          overflow: hidden;
          margin-right: 12px;
          position: relative;
          flex-shrink: 0;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          .product-tag {
            position: absolute;
            top: 0;
            left: 0;
            background-color: #F56C6C;
            color: white;
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 0 0 8px 0;
          }
        }
        
        .product-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          
          .product-name {
            font-size: 14px;
          color: #303133;
          margin-bottom: 5px;
            transition: all 0.3s ease;
          }
          
          .product-price {
            font-size: 16px;
            font-weight: 600;
            color: #F56C6C;
            margin-bottom: 5px;
          }
          
          .product-meta {
            font-size: 12px;
            color: #909399;
            
            span {
              margin-right: 10px;
            }
          }
        }
      }
    }
  }

  .ag-news-section {
    margin-bottom: 20px;
    border-radius: 12px;
    transition: all 0.3s ease;
    height: calc(100% - 20px);
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
    }
    
    .news-item {
      display: flex;
      padding: 15px 0;
      border-bottom: 1px solid #f0f2f5;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:hover {
        background-color: #f5f7fa;
        transform: translateX(5px);
        
        .news-title {
          color: #409EFF;
        }
      }
      
      .news-image {
        width: 80px;
        height: 60px;
        border-radius: 4px;
        overflow: hidden;
        margin-right: 12px;
        flex-shrink: 0;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      .news-content {
        flex: 1;
        overflow: hidden;
        
        .news-title {
          font-size: 14px;
          color: #303133;
          margin-bottom: 8px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          transition: color 0.3s ease;
        }
        
        .news-meta {
          font-size: 12px;
          color: #909399;
          
          .news-source {
            margin-right: 10px;
          }
          
          .news-time {
            float: right;
          }
        }
      }
    }
    
    .no-data {
      padding: 30px 0;
      text-align: center;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .price-detail {
    padding: 20px 0;
    
    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      
      .product-info {
        display: flex;
        align-items: center;
        
        .product-image {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          object-fit: cover;
          margin-right: 15px;
        }
        
        .product-name {
          font-size: 20px;
          font-weight: 600;
          color: #303133;
        }
      }
      
      .price-info {
        text-align: right;
        
        .current-price {
          font-size: 24px;
          font-weight: 700;
          color: #303133;
          margin-bottom: 5px;
        }
        
        .price-change {
          display: inline-block;
          padding: 4px 10px;
          border-radius: 4px;
          font-weight: 600;
          
          &.price-up {
            background-color: rgba(103, 194, 58, 0.1);
            color: #67C23A;
          }
          
          &.price-down {
            background-color: rgba(245, 108, 108, 0.1);
            color: #F56C6C;
          }
        }
      }
    }
    
    .detail-chart {
      height: 400px;
      margin-bottom: 30px;
    }
    
    .detail-table {
      padding: 20px 0;
    }
  }
  
  // 加载状态样式
  .loading-container {
    padding: 50px 0;
    text-align: center;
    color: #909399;
    
    i {
      font-size: 30px;
      margin-bottom: 10px;
      color: #409EFF;
    }
    
    p {
      font-size: 14px;
    }
  }
  
  // 数据动画效果
  .animate-number-enter-active, .animate-number-leave-active {
    transition: all 0.3s ease;
  }
  .animate-number-enter, .animate-number-leave-to {
    opacity: 0;
    transform: translateY(10px);
  }
  
  // 响应式样式调整
  @media (max-width: 768px) {
    .overview-section {
      .el-col {
        width: 50%;
        margin-bottom: 20px;
      }
    }
    
    .ag-news-section {
      margin-top: 20px;
    }
  }
}
</style> 