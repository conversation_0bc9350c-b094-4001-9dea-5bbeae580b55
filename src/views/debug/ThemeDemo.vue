<template>
  <div class="theme-demo">
    <div class="page-container">
      <h1 class="page-title">主题切换演示</h1>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card class="theme-card">
            <div slot="header" class="card-header">
              <span>当前主题</span>
              <theme-toggle />
            </div>
            <div class="theme-info">
              <p>当前主题模式: <strong>{{ isDarkTheme ? '暗黑模式' : '亮色模式' }}</strong></p>
              <p>点击右上角的图标可以切换主题模式</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" class="mt-20">
        <el-col :span="12">
          <el-card>
            <div slot="header">
              <span>UI 组件展示</span>
            </div>
            
            <div class="demo-section">
              <h3>按钮</h3>
              <div class="button-group">
                <el-button>默认按钮</el-button>
                <el-button type="primary">主要按钮</el-button>
                <el-button type="success">成功按钮</el-button>
                <el-button type="warning">警告按钮</el-button>
                <el-button type="danger">危险按钮</el-button>
                <el-button type="info">信息按钮</el-button>
              </div>
            </div>
            
            <div class="demo-section">
              <h3>表单元素</h3>
              <el-form :model="form" label-width="80px">
                <el-form-item label="名称">
                  <el-input v-model="form.name" placeholder="请输入名称"></el-input>
                </el-form-item>
                <el-form-item label="选择器">
                  <el-select v-model="form.region" placeholder="请选择">
                    <el-option label="选项1" value="option1"></el-option>
                    <el-option label="选项2" value="option2"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="开关">
                  <el-switch v-model="form.delivery"></el-switch>
                </el-form-item>
              </el-form>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card>
            <div slot="header">
              <span>数据展示</span>
            </div>
            
            <div class="demo-section">
              <h3>表格</h3>
              <el-table :data="tableData" style="width: 100%">
                <el-table-column prop="date" label="日期" width="180"></el-table-column>
                <el-table-column prop="name" label="姓名" width="180"></el-table-column>
                <el-table-column prop="address" label="地址"></el-table-column>
              </el-table>
            </div>
            
            <div class="demo-section">
              <h3>分页</h3>
              <el-pagination
                background
                layout="prev, pager, next"
                :total="50">
              </el-pagination>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" class="mt-20">
        <el-col :span="24">
          <el-card>
            <div slot="header">
              <span>主题切换说明</span>
            </div>
            <div class="theme-description">
              <p>本网站支持亮色和暗色两种主题模式，您可以根据自己的喜好或当前环境光线选择合适的主题。</p>
              <p>暗色模式特别适合在夜间或低光环境下使用，可以减轻眼睛疲劳。</p>
              <p>系统会记住您的主题选择，下次访问时自动应用。您也可以随时通过导航栏上的主题切换按钮进行切换。</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import ThemeToggle from '@/components/ThemeToggle.vue'

export default {
  name: 'ThemeDemo',
  components: {
    ThemeToggle
  },
  data() {
    return {
      form: {
        name: '',
        region: '',
        delivery: false
      },
      tableData: [
        {
          date: '2023-05-03',
          name: '张三',
          address: '北京市朝阳区'
        },
        {
          date: '2023-05-04',
          name: '李四',
          address: '上海市浦东新区'
        },
        {
          date: '2023-05-05',
          name: '王五',
          address: '广州市天河区'
        }
      ]
    }
  },
  computed: {
    ...mapState(['theme']),
    isDarkTheme() {
      return this.theme === 'dark'
    }
  }
}
</script>

<style lang="scss" scoped>
.theme-demo {
  padding: 20px 0;
}

.page-title {
  margin-bottom: 20px;
  color: var(--text-primary);
}

.theme-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .theme-info {
    line-height: 1.8;
  }
}

.demo-section {
  margin-bottom: 20px;
  
  h3 {
    margin-bottom: 15px;
    color: var(--text-primary);
  }
  
  .button-group {
    margin-bottom: 15px;
    
    .el-button {
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }
}

.theme-description {
  line-height: 1.8;
  
  p {
    margin-bottom: 10px;
  }
}
</style> 