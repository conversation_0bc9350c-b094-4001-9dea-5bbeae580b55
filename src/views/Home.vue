<template>
  <div class="home">
    <!-- 天气农事活动提示悬浮窗 -->
    <weather-notification 
      v-if="showWeatherNotificationFloat" 
      :notification="weatherAdvisory" 
      :duration="5000"
      @close="showWeatherNotificationFloat = false" />
    <!-- 轮播图部分 -->
    <el-carousel
      :interval="4000"
      type="card"
      height="400px"
      class="main-carousel"
      :autoplay="true"
      indicator-position="outside">
      <el-carousel-item 
        v-for="(item, index) in carouselItems" 
        :key="index">
        <div 
          class="carousel-content" 
          :style="{ 
            backgroundImage: item.image ? `url(${item.image})` : 'none',
            backgroundColor: !item.image ? item.backgroundColor : 'transparent'
          }">
          <div class="carousel-info">
            <h2 class="carousel-title animate__animated animate__fadeInDown">{{ item.title }}</h2>
            <p class="carousel-desc animate__animated animate__fadeInUp">{{ item.description }}</p>
            <el-button 
              type="primary" 
              class="carousel-btn animate__animated animate__fadeInUp"
              @click="handleCarouselClick(item)">
              了解更多
            </el-button>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>

    <!-- 功能导航 -->
    <div class="feature-nav">
      <el-row :gutter="20">
        <el-col :span="6" v-for="(feature, index) in features" :key="index">
          <div class="feature-item" @click="handleFeatureClick(feature)">
            <div class="feature-icon" :style="{ backgroundColor: feature.color }">
              <i :class="feature.icon" />
            </div>
            <div class="feature-info">
              <h3>{{ feature.title }}</h3>
              <p>{{ feature.description }}</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 市场动态 -->
    <el-card class="market-trends" shadow="hover">
      <div slot="header" class="card-header">
        <span>市场动态</span>
        <el-button style="float: right" type="text" @click="$router.push('/price')">
          查看更多 <i class="el-icon-d-arrow-right" />
        </el-button>
      </div>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="trend-chart" ref="priceChart"/>
        </el-col>
        <el-col :span="12">
          <div class="trend-list">
            <div 
              v-for="(item, index) in marketTrends" 
              :key="index"
              class="trend-item animate__animated animate__fadeInRight"
              :style="{ animationDelay: index * 0.2 + 's' }"
              @click="showIndexDetail(item)">
              <div class="trend-icon" :style="{ backgroundColor: getTrendColor(item.trend) }">
                <i :class="item.icon" />
              </div>
              <div class="trend-info">
                <h4>{{ item.title }}</h4>
                <div class="trend-value">
                  <span class="value">{{ item.value }}</span>
                  <span class="trend-change" :class="item.trend">
                    <i :class="item.trend === 'up' ? 'el-icon-top' : item.trend === 'down' ? 'el-icon-bottom' : 'el-icon-minus'" />
                    {{ item.change }}
                  </span>
                </div>
                <div class="trend-meta">
                  <span class="year">{{ item.volume }}</span>
                  <span class="unit">{{ item.unit || '上年=100' }}</span>
                </div>
              </div>
              <div class="trend-action">
                <i class="el-icon-arrow-right" />
              </div>
            </div>
          </div>
          <div class="view-more">
            <el-button type="text" @click="$router.push('/price-indices')">
              查看更多价格指数 <i class="el-icon-d-arrow-right" />
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 价格指数详情弹窗 -->
    <el-dialog
      title="价格指数详情"
      :visible.sync="indexDetailVisible"
      width="50%"
      :before-close="handleDialogClose">
      <div v-if="selectedIndex" class="index-detail">
        <div class="detail-header">
          <h3>{{ selectedIndex.title }}</h3>
          <div class="detail-value">
            <span class="main-value">{{ selectedIndex.value }}</span>
            <span class="change-value" :class="selectedIndex.trend">
              <i :class="selectedIndex.trend === 'up' ? 'el-icon-top' : selectedIndex.trend === 'down' ? 'el-icon-bottom' : 'el-icon-minus'" />
              {{ selectedIndex.change }}
            </span>
          </div>
        </div>
        
        <el-divider />
        
        <div class="detail-info">
          <div class="info-item">
            <span class="label">统计周期:</span>
            <span class="value">{{ selectedIndex.volume }}</span>
          </div>
          <div class="info-item">
            <span class="label">计量单位:</span>
            <span class="value">{{ selectedIndex.unit || '上年=100' }}</span>
          </div>
          <div class="info-item">
            <span class="label">描述:</span>
            <span class="value">{{ selectedIndex.description || '反映该品类农产品价格变化情况' }}</span>
          </div>
        </div>
        
        <div class="yearly-trend" v-if="indexYearlyData.length > 0">
          <h4>历年走势</h4>
          <div class="yearly-chart" ref="yearlyChart" />
        </div>
        
        <div class="related-indices" v-if="relatedIndices.length > 0">
          <h4>相关指数</h4>
          <div class="related-list">
            <el-tag 
              v-for="(item, index) in relatedIndices" 
              :key="index" 
              :type="getTagTypeByValue(item.value)"
              effect="plain"
              class="related-tag"
              @click="switchToRelatedIndex(item)">
              {{ item.category.replace(/生产价格指数$/, '') }} ({{ item.value }})
            </el-tag>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="indexDetailVisible = false">关闭</el-button>
        <el-button size="small" type="primary" @click="viewAllIndices">查看全部指数</el-button>
      </span>
    </el-dialog>

    <!-- 天气预警 -->
    <el-card class="weather-alert" shadow="hover">
      <div slot="header" class="card-header">
        <span>天气预警与农事建议</span>
        <div class="header-controls">
          <span class="location-info" v-if="currentWeather">
            <i class="el-icon-location"></i> {{ currentCity || '北京' }}
          </span>
          <el-button 
            type="primary" 
            size="small" 
            @click="refreshWeather" 
            :loading="loading.weather">
            <i class="el-icon-refresh"></i>
            <span class="button-text">刷新天气</span>
          </el-button>
        </div>
      </div>

      <!-- 顶部天气状态与预报 -->
      <div class="weather-status-container">
        <!-- 当前天气状态 -->
        <div class="weather-current" v-if="currentWeather">
          <div class="weather-info">
            <div class="weather-icon">
              <i :class="getWeatherIconByCondition(currentWeather.weather)"></i>
            </div>
            <div class="weather-temp">
              <span class="temp-value">{{ currentWeather.temperature }}°</span>
              <span class="weather-condition">{{ currentWeather.weather }}</span>
            </div>
          </div>
          <div class="weather-details">
            <div class="weather-detail-item">
              <i class="el-icon-heavy-rain"></i>
              <span>湿度: {{ currentWeather.humidity }}%</span>
            </div>
            <div class="weather-detail-item">
              <i class="el-icon-wind-power"></i>
              <span>{{ currentWeather.windDir }} {{ currentWeather.windSpeed }}km/h</span>
            </div>
          </div>
        </div>
        
        <!-- 天气预报简讯 -->
        <div class="weather-forecast-brief" v-if="weatherForecast && weatherForecast.length > 0">
          <div class="forecast-title">未来天气预报</div>
          <div class="forecast-items">
            <div 
              v-for="(item, index) in weatherForecast.slice(0, 3)" 
              :key="index" 
              class="forecast-item">
              <div class="forecast-day">{{ formatForecastDay(item.fxDate) }}</div>
              <div class="forecast-icon">
                <i :class="getWeatherIconByCondition(item.textDay)"></i>
              </div>
              <div class="forecast-temp">{{ item.tempMin }}°~{{ item.tempMax }}°</div>
              <div class="forecast-text">{{ item.textDay }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部内容区 -->
      <div class="bottom-container">
        <!-- 左侧：天气预警展示 -->
        <div class="weather-alerts-section">
          <div class="section-title">
            <i class="el-icon-warning"></i> 天气预警信息
          </div>
          
          <div v-if="!weatherAlerts.length && !loading.weather" class="no-alerts">
            <i class="el-icon-sunny"></i>
            <p>当前天气平稳，暂无预警信息</p>
          </div>
          
          <div v-else class="alerts-list">
            <div 
              v-for="(alert, index) in weatherAlerts" 
              :key="index"
              class="alert-item animate__animated animate__fadeInUp"
              :class="getAlertClass(alert.level)"
              :style="{ animationDelay: `${index * 0.1}s` }"
              @click="handleAlertClick(alert)">
              <div class="alert-icon">
                <i :class="getWeatherIconByType(alert.type || alert.typeName)"></i>
              </div>
              <div class="alert-content">
                <div class="alert-header">
                  <span class="alert-level" :class="getAlertClass(alert.level)">{{ alert.level || '预警' }}</span>
                  <h4>{{ alert.title }}</h4>
                </div>
                <p>{{ alert.text || alert.description }}</p>
                <span class="alert-time">{{ formatAlertTime(alert.pubTime || alert.time) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧：农业建议与指数信息 -->
        <div class="weather-info-section">
          <!-- 生活指数信息 -->
          <div class="weather-indices" v-if="lifeIndices && lifeIndices.length > 0">
            <div class="section-title">
              <i class="el-icon-data-analysis"></i> 天气生活指数
            </div>
            <div class="indices-grid">
              <div 
                v-for="(index, i) in lifeIndices.slice(0, 4)" 
                :key="i"
                class="index-item"
                :style="{ animationDelay: `${i * 0.1}s` }">
                <div class="index-icon" :class="getIndexClass(index.category)">
                  <i :class="getIndexIcon(index.type)"></i>
                </div>
                <div class="index-content">
                  <div class="index-name">{{ index.name }}</div>
                  <div class="index-category">{{ index.category }}</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 空气质量信息 -->
          <div class="air-quality" v-if="airQuality">
            <div class="section-title">
              <i class="el-icon-cloudy"></i> 空气质量
            </div>
            <div class="air-data">
              <div class="air-aqi">
                <div class="air-number" :class="getAqiClass(airQuality.aqi)">{{ airQuality.aqi }}</div>
                <div class="air-category">{{ airQuality.category }}</div>
              </div>
              <div class="air-details">
                <div class="air-detail-item">
                  <span class="air-label">PM2.5:</span>
                  <span class="air-value">{{ airQuality.pm2p5 }}</span>
                </div>
                <div class="air-detail-item">
                  <span class="air-label">PM10:</span>
                  <span class="air-value">{{ airQuality.pm10 }}</span>
                </div>
                <div class="air-detail-item">
                  <span class="air-label">主要污染物:</span>
                  <span class="air-value">{{ airQuality.primary === 'NA' ? '无' : airQuality.primary }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 农业建议部分 -->
      <div class="farming-advice" v-if="smartFarmingAdvice">
        <h3 class="advice-title">
          <i class="el-icon-s-opportunity"></i> {{ smartFarmingAdvice.title || '今日农事建议' }}
        </h3>
        <div class="advice-content" :class="smartFarmingAdvice.level || 'info'">
          <p class="advice-description">{{ smartFarmingAdvice.general || smartFarmingAdvice.description }}</p>
          <div class="advice-suggestions">
            <ul>
              <li 
                v-for="(suggestion, index) in smartFarmingAdvice.actions || smartFarmingAdvice.suggestions" 
                :key="index"
                class="animate__animated animate__fadeInRight"
                :style="{ animationDelay: `${index * 0.1}s` }">
                {{ suggestion }}
              </li>
            </ul>
          </div>
          <div class="view-more-weather">
            <el-button type="text" @click="$router.push('/weather')">
              查看详细天气信息 <i class="el-icon-d-arrow-right"></i>
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 农业资讯 -->
    <el-card class="agriculture-news" shadow="hover">
      <div slot="header" class="card-header">
        <span>农业资讯</span>
        <el-button style="float: right" type="text" @click="$router.push('/news')">
          查看更多 <i class="el-icon-d-arrow-right" />
        </el-button>
      </div>
      <el-row :gutter="20">
        <el-col :span="16">
          <div class="news-list">
            <div
              v-for="(news, index) in latestNews"
              :key="index"
              class="news-item animate__animated animate__fadeInLeft"
              :style="{ animationDelay: `${index * 0.1}s` }"
              @click="goToNewsDetail(news.id)">
              <div class="news-image">
                <img :src="news.image" :alt="news.title"/>
                <div class="news-overlay">
                  <i class="el-icon-view" />
                </div>
              </div>
              <div class="news-content">
                <h3>{{ news.title }}</h3>
                <p>{{ news.summary }}</p>
                <div class="news-meta">
                  <el-tag size="mini" :type="getTagType(news.category)">{{ news.category }}</el-tag>
                  <span class="news-date">{{ news.date }}</span>
                  <span class="news-views">
                    <i class="el-icon-view" /> {{ news.views }}
                  </span>
                </div>
              </div>
            </div>
          </div> <!-- 新增：农业生产指导 -->
          <div class="production-guidance animate__animated animate__fadeInLeft" style="margin-top: 20px; padding: 15px; border-radius: 4px;">
            <h4 style="margin-bottom: 10px; font-size: 16px; color: #303133;"><i class="el-icon-guide" style="margin-right: 5px;"/> 农业生产指导</h4>
            <ul style="list-style: none; padding-left: 0; font-size: 15px; color: #606266; line-height: 1.8;"> <!-- Increased font-size here -->
              <li style="margin-bottom: 8px;"><strong>春季：</strong> 做好春耕备耕，注意早期病虫害防治，合理施用底肥。</li>
              <li style="margin-bottom: 8px;"><strong>夏季：</strong> 加强田间水肥管理，注意防汛抗旱及高温热害，及时中耕除草。</li>
              <li style="margin-bottom: 8px;"><strong>秋季：</strong> 适时收获，科学晾晒与储藏，做好秋种准备，防治晚期病虫。</li>
              <li style="margin-bottom: 8px;"><strong>冬季：</strong> 深耕土壤，增施有机肥，做好设施农业保温防冻，休整农田水利。</li>
            </ul>
            <el-alert
              title="小贴士"
              type="success"
              description="请结合当地具体气候条件和作物生长阶段，灵活调整农事安排，参考最新农业技术推广信息。"
              show-icon
              :closable="false"
              style="margin-top: 15px;"/>
          </div>
          <!-- 新增结束 -->
        </el-col>
        <el-col :span="8">
          <div class="hot-news">
            <h3>热门农业新闻</h3>
            <div 
              v-for="(news, index) in hotNews" 
              :key="index" 
              class="hot-news-item animate__animated animate__fadeInRight"
              :style="{ animationDelay: `${index * 0.1}s` }"
              @click="goToNewsDetail(news.id)">
              <span class="news-rank" :class="{ 'top-three': index < 3 }">{{ index + 1 }}</span>
              <div class="hot-news-content">
                <span class="hot-news-title">{{ news.title }}</span>
                <div class="hot-news-meta">
                  <el-tag size="mini" :type="getTagType(news.category)">{{ news.category }}</el-tag>
                  <span class="hot-news-views">
                    <i class="el-icon-view" /> {{ news.views }}
                  </span>
            </div>
          </div>
        </div>
            <div class="load-more" v-if="loading.hotNews">
              <el-skeleton :rows="3" animated />
      </div>
            <div v-if="!hotNews.length && !loading.hotNews" class="no-news-data">
              <i class="el-icon-document" />
              <p>暂无热门新闻</p>
      </div>
          </div>
          
          <!-- 农业政策动态 -->
          <div class="trending-news">
            <h3>农业政策动态</h3>
            <div 
              v-for="(news, index) in trendingNews" 
              :key="index" 
              class="trending-news-item animate__animated animate__fadeInRight"
            :style="{ animationDelay: `${index * 0.1}s` }"
              @click="goToNewsDetail(news.id)">
              <div class="trending-news-content">
                <span class="trending-news-title">{{ news.title }}</span>
                <div class="trending-news-meta">
                  <el-tag size="mini" type="primary">{{ news.category }}</el-tag>
                  <span class="trending-news-date">{{ news.date }}</span>
            </div>
              </div>
            </div>
            <div class="load-more" v-if="loading.trendingNews">
              <el-skeleton :rows="3" animated />
            </div>
            <div v-if="!trendingNews.length && !loading.trendingNews" class="no-news-data">
              <i class="el-icon-document" />
              <p>暂无政策动态</p>
            </div>
            <div class="view-more">
              <el-button type="text" @click="$router.push('/news?category=policy')">
                查看更多政策动态 <i class="el-icon-d-arrow-right" />
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import weatherService from '@/api/weather'
import priceIndicesService from '@/api/price-indices'
import newsService from '@/api/news'
import weatherAdvisoryService from '@/services/weatherAdvisoryService'
import WeatherNotification from '@/components/WeatherNotification'

export default {
  name: 'Home',
  components: {
    WeatherNotification
  },
  data() {
    return {
      carouselItems: [
        {
          image: require('@/assets/images/carousel/encyclopedia.jpg'),
          backgroundColor: '#409EFF',
          title: '农用百科全书',
          description: '为农民提供全面的农业知识库',
          link: '/encyclopedia'
        },
        {
          image: require('@/assets/images/carousel/market.jpg'),
          backgroundColor: '#67C23A',
          title: '市场行情分析',
          description: '实时掌握农产品市场价格动态',
          link: '/price'
        },
        {
          image: require('@/assets/images/carousel/news.jpg'),
          backgroundColor: '#E6A23C',
          title: '农业资讯服务',
          description: '及时了解农业政策和技术信息',
          link: '/news'
        },
        {
          image: require('@/assets/images/carousel/weather.jpg'),
          backgroundColor: '#F56C6C',
          title: '天气预警服务',
          description: '智能预警，科学种植',
          link: '/weather'
        }
      ],
      features: [
        {
          title: '农用百科',
          description: '全面的农业知识库',
          icon: 'el-icon-notebook-2',
          color: '#409EFF',
          link: '/encyclopedia'
        },
        {
          title: '农品汇',
          description: '优质农产品交易平台',
          icon: 'el-icon-shopping-cart-full',
          color: '#67C23A',
          link: '/shop'
        },
        {
          title: '价格行情',
          description: '实时市场价格动态',
          icon: 'el-icon-data-line',
          color: '#E6A23C',
          link: '/price'
        },
        {
          title: '数据分析',
          description: '智能数据分析服务',
          icon: 'el-icon-pie-chart',
          color: '#F56C6C',
          link: '/analysis'
        }
      ],
      marketTrends: [],
      hotTopics: [
        { title: '2024年种植补贴', category: '政策资讯', count: 128 },
        { title: '水稻高产技术', category: '技术指导', count: 96 },
        { title: '小麦价格上涨', category: '市场动态', count: 85 },
        { title: '南方强降雨预警', category: '天气预警', count: 76 },
        { title: '农业机械创新', category: '科技创新', count: 65 }
      ],
      weatherAlerts: [],
      weatherAdvice: null,
      weatherDialogVisible: false,
      weatherAdvisory: null,
      showWeatherNotificationFloat: false,
      loading: {
        weather: false,
        news: false,
        hotNews: false,
        trendingNews: false,
        weatherAdvice: false
      },
      apiKey: process.env.VUE_APP_JUHE_WEATHER_KEY || '',
      priceChart: null,
      priceData: null,
      news: [],
      latestNews: [],
      featuredProducts: [],
      indexDetailVisible: false,
      selectedIndex: null,
      indexYearlyData: [],
      relatedIndices: [],
      yearlyChart: null,
      hotNews: [],
      trendingNews: [],
      currentWeather: null,
      smartFarmingAdvice: null,
      weatherForecast: [],
      currentCity: '',
      lifeIndices: [],
      airQuality: null,
    }
  },
  created() {
    try {
      this.fetchNewsList()
      this.fetchHotNews()
      this.fetchMarketPriceData().catch(error => {
        console.error('获取市场价格数据失败:', error)
        // 确保一定有市场数据显示
        this.setDefaultMarketTrends()
        
        // 图表初始化
        if (this.priceChart) {
          this.priceChart.dispose() // 先清除旧图表
        }
        this.initPriceChart() // 使用默认图表
      })
      
      this.fetchFarmingTips()
      
      // 获取天气数据和预警
      this.fetchWeatherData()
      
      if (this.$route.query.refresh) {
        this.fetchData()
      }
      
      // 设置轮播自动切换
      this.setInterval()
      
      // 检查是否为当日首次访问网站，仅在首次访问时显示天气悬浮窗
      this.checkAndShowWeatherAdvisoryFloat()
    } catch (error) {
      console.error('创建组件时出错:', error)
      // 确保基本功能可用
      this.setDefaultMarketTrends()
    }
  },
  mounted() {
    this.initPriceChart();
    // 检查是否为当前会话首次访问网站，仅在首次访问时显示天气悬浮窗
    this.checkAndShowWeatherAdvisoryFloat();
  },
  methods: {
    initCharts() {
      console.log('initCharts方法被调用，但实际图表初始化在mounted中进行');
    },
    handleCarouselClick(item) {
      this.$router.push(item.link)
    },
    handleFeatureClick(feature) {
      this.$router.push(feature.link)
    },
    initPriceChart() {
      this.priceChart = echarts.init(this.$refs.priceChart)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['价格', '成交量'],
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['3-22', '3-23', '3-24', '3-25', '3-26', '3-27', '3-28'],
          axisLabel: {
            color: '#606266'
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '价格(元/kg)',
            axisLabel: {
              color: '#606266'
            }
          },
          {
            type: 'value',
            name: '成交量(kg)',
            axisLabel: {
              color: '#606266'
            }
          }
        ],
        series: [
          {
            name: '价格',
            type: 'line',
            smooth: true,
            data: [2.1, 2.3, 2.4, 2.5, 2.6, 2.5, 2.5],
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(64,158,255,0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(64,158,255,0.1)'
                }
              ])
            }
          },
          {
            name: '成交量',
            type: 'bar',
            yAxisIndex: 1,
            data: [1000, 1200, 1100, 1300, 1250, 1400, 1350],
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#67C23A'
                },
                {
                  offset: 1,
                  color: '#95D475'
                }
              ])
            }
          }
        ]
      }
      this.priceChart.setOption(option)
    },
    getTagType(category) {
      const types = {
        '政策资讯': 'primary',
        '技术指导': 'success',
        '市场动态': 'warning',
        '天气预警': 'danger'
      }
      return types[category] || 'info'
    },
    async fetchWeatherData() {
      this.loading.weather = true;
      
      try {
        // 获取当前位置
        const location = await weatherService.getLocationByIp();
        let cityName = location.name || '阜阳市'; // 默认使用阜阳市
        let cityId = location.id || '101220801';
        
        this.currentCity = cityName;
        
        console.log(`正在获取 ${cityName} (ID: ${cityId}) 的天气数据和预警信息...`);
        
        // 并行获取所有天气相关数据
        const [weatherData, alertsData, forecastData, indicesData, airQualityData] = await Promise.all([
          weatherService.getWeatherNow(cityId),
          weatherService.getWeatherAlertsNow(cityId),
          weatherService.getWeather15d(cityId),
          weatherService.getLifeIndices(cityId),
          weatherService.getAirQualityNow(cityId)
        ]);
        
        // 更新当前天气信息
        if (weatherData && weatherData.now) {
          this.currentWeather = {
            temperature: weatherData.now.temp,
            weather: weatherData.now.text,
            humidity: weatherData.now.humidity,
            windDir: weatherData.now.windDir,
            windSpeed: weatherData.now.windSpeed
          };
        }
        
        // 更新天气预报信息
        if (forecastData && forecastData.daily) {
          this.weatherForecast = forecastData.daily;
        }
        
        // 更新生活指数信息
        if (indicesData && indicesData.daily) {
          this.lifeIndices = indicesData.daily;
        }
        
        // 更新空气质量信息
        if (airQualityData && airQualityData.now) {
          this.airQuality = airQualityData.now;
        }
        
        // 处理预警数据
        if (alertsData && alertsData.warning) {
          if (alertsData.warning.length > 0) {
          // 最多显示3个预警
            this.weatherAlerts = alertsData.warning.slice(0, 3).map(alert => ({
              type: alert.type,
              typeName: alert.typeName,
              level: alert.level,
              title: alert.title || alert.typeName,
              text: alert.text,
              description: alert.text,
              pubTime: alert.pubTime,
              time: alert.pubTime
            }));
            
            console.log(`成功获取到 ${this.weatherAlerts.length} 条天气预警`);
          } else {
            // 如果没有预警，清空数组
            console.log('当前无天气预警');
            this.weatherAlerts = [];
            
            // 如果有天气数据，生成基于天气的模拟预警
            if (this.currentWeather) {
              this.generateWeatherAlerts(this.currentWeather);
            }
          }
        } else {
          console.error('预警数据格式不正确');
          this.weatherAlerts = [];
          
          // 如果有天气数据，生成基于天气的模拟预警
          if (this.currentWeather) {
            this.generateWeatherAlerts(this.currentWeather);
          }
        }
        
        // 获取智能农业建议
        if (!this.smartFarmingAdvice) {
          try {
            const farmingAdvice = await weatherService.getSmartFarmingAdvice(cityId);
            this.smartFarmingAdvice = farmingAdvice;
          } catch (adviceError) {
            console.error('获取农业建议失败:', adviceError);
          }
        }
        
      } catch (err) {
        console.error('天气数据获取错误:', err);
        this.weatherAlerts = []; // 清空天气预警数据
        
        // 显示错误提示
        this.$message.error('天气数据获取失败，请稍后重试');
      } finally {
        this.loading.weather = false;
      }
    },
    
    setDefaultWeatherAlerts() {
      // 不再使用静态默认数据，仅清空数组
      this.weatherAlerts = [];
    },
    
    refreshWeather() {
      this.loading.weather = true;
      this.fetchWeatherData()
        .then(() => {
      this.$message({
        message: '天气信息已更新',
        type: 'success'
          });
        })
        .catch(() => {
          this.$message.error('更新天气信息失败，请稍后重试');
        })
        .finally(() => {
          this.loading.weather = false;
        });
    },
    
    handleAlertClick(_alert) {
      this.fetchWeatherData() // 点击天气预警时调用API
      this.$router.push('/weather')
    },
    
    getWeatherIcon(type) {
      const icons = {
        'rain': 'el-icon-umbrella',
        'temperature': 'el-icon-sunrise',
        'aqi': 'el-icon-cloudy',
        'forecast': 'el-icon-time',
        'wind': 'el-icon-wind-power',
        'sunny': 'el-icon-sunny',
        'normal': 'el-icon-cloudy'
      }
      return icons[type] || 'el-icon-cloudy'
    },
    
    goToNewsDetail(id) {
      this.$router.push(`/news/detail/${id}?from=/`);
    },
    
    loadFarmingAdvice() {
      // 这是一个占位方法，用于模拟加载农业建议
      console.log('加载农业建议 - 暂未实现');
    },
    
    // 获取新闻列表
    async fetchNewsList() {
      console.log('获取新闻列表');
      this.loading.news = true;
      
      try {
        // 从API获取新闻数据
        const newsResponse = await newsService.getNewsList({ limit: 3 });
        
        if (newsResponse && newsResponse.data && newsResponse.data.list) {
          this.latestNews = newsResponse.data.list;
        } else {
          console.warn('未获取到新闻数据');
          this.latestNews = [];
        }
      } catch (error) {
        console.error('获取新闻列表失败:', error);
        this.latestNews = [];
        this.$message.error('获取新闻数据失败，请稍后重试');
      } finally {
        this.loading.news = false;
      }

      // 获取政策动态新闻
      await this.fetchTrendingNews();
    },
    
    // 获取政策动态新闻
    async fetchTrendingNews() {
      this.loading.trendingNews = true;
      try {
        // 从API获取政策类新闻数据
        const response = await newsService.getNewsList({ 
          category: 'policy', 
          limit: 4 
        });
        
        console.log('获取到的政策动态:', response);
        
        if (response && response.data && response.data.list) {
          this.trendingNews = response.data.list;
        } else {
          console.warn('获取政策动态失败，API未返回数据');
          this.trendingNews = [];
        }
      } catch (error) {
        console.error('获取政策动态失败:', error);
        this.trendingNews = [];
      } finally {
        this.loading.trendingNews = false;
      }
    },
    
    // 获取热门新闻
    async fetchHotNews() {
      this.loading.hotNews = true
      try {
        console.log('开始获取热门农业新闻...')
        // 使用news.js中的getHotNews方法获取热门新闻
        const response = await newsService.getHotNews(5) // 获取5条热门新闻
        console.log('获取到的热门新闻:', response)
        
        if (response && response.data) {
          this.hotNews = response.data
        } else {
          console.warn('获取热门新闻失败，API未返回数据')
          this.hotNews = []
        }
      } catch (error) {
        console.error('获取热门新闻失败:', error)
        this.hotNews = []
      } finally {
        this.loading.hotNews = false
      }
    },
    
    // 修改获取市场价格数据的方法，使用main_product表中数据
    async fetchMarketPriceData() {
      console.log('获取最新农产品价格数据和价格指数数据');
      try {
        // 获取价格指数数据用于图表展示
        const mainProductsResponse = await priceIndicesService.getMainProductsForHome();
        if (mainProductsResponse && mainProductsResponse.code === 200 && mainProductsResponse.data) {
          // 将主要产品数据存储到priceData
          this.priceData = mainProductsResponse.data;
          
          // 根据获取的数据更新市场动态显示
          if (this.priceData && this.priceData.length >= 3) {
            // 从主要产品中提取市场动态数据
            this.marketTrends = this.priceData.slice(0, 3).map(item => {
              // 计算趋势方向
              const trend = (item.value > 100) ? 'up' : (item.value < 100) ? 'down' : 'stable';
              
              // 计算变化值（与基准年100的差值）
              const change = Math.abs(item.value - 100).toFixed(1);
              
              return {
                id: item.id,
                title: item.category.replace(/生产价格指数$/, ''),
                value: `${item.value}`,
                icon: this.getCategoryIcon(item.category),
                trend: trend,
                change: trend === 'up' ? `+${change}%` : trend === 'down' ? `-${change}%` : '0%',
                volume: item.year ? `${item.year}年` : '本年度',
                unit: item.unit || '上年=100',
                description: item.description || '反映该品类农产品价格变化情况',
                originalData: item // 保存原始数据
              };
            });
          } else {
            console.warn('从API获取的主要产品数据少于3项，使用默认数据');
            this.setDefaultMarketTrends();
          }
          
          // 使用同一组数据初始化图表
          this.$nextTick(() => {
            if (this.priceChart) {
              this.priceChart.dispose(); // 先清除旧图表
            }
            this.initPriceChartWithIndicesData(mainProductsResponse.data);
          });
        } else {
          // API未返回有效数据，显示默认值
          console.warn('API未返回有效的价格指数数据');
          this.setDefaultMarketTrends();
          
          if (this.priceChart) {
            this.priceChart.dispose();
          }
          this.initPriceChart(); // 使用默认图表
        }
      } catch (error) {
        console.error('获取市场价格数据失败:', error);
        // 显示默认值
        this.setDefaultMarketTrends();
        
        if (this.priceChart) {
          this.priceChart.dispose();
        }
        this.initPriceChart(); // 使用默认图表
      }
    },
    
    // 添加初始化价格指数图表的方法
    initPriceChartWithIndicesData(indicesData) {
      if (!this.$refs.priceChart) return;
      
      // 初始化echarts图表
      this.priceChart = echarts.init(this.$refs.priceChart);
      
      // 提取数据
      const categories = indicesData.map(item => item.category.replace(/生产价格指数$/, ''));
      const values = indicesData.map(item => item.value);
      
      // 设置图表选项
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params) => {
            const data = params[0];
            const index = data.dataIndex;
            const item = indicesData[index];
            const trend = item.value > 100 ? '上涨' : item.value < 100 ? '下跌' : '持平';
            const change = Math.abs(item.value - 100).toFixed(1);
            
            return `<div style="padding: 8px">
              <div style="font-weight: bold; margin-bottom: 5px">${data.name}</div>
              <div>价格指数: <span style="float: right; font-weight: bold">${data.value}</span></div>
              <div>同比: <span style="float: right; color: ${item.value > 100 ? '#F56C6C' : item.value < 100 ? '#67C23A' : '#E6A23C'}">${trend} ${change}%</span></div>
              <div>年份: <span style="float: right">${item.year || '2023'}</span></div>
            </div>`;
          }
        },
        legend: {
          data: ['价格指数'],
          top: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: categories,
            axisLabel: {
              interval: 0,
              rotate: 30,
              fontSize: 12,
              color: '#606266'
            },
            axisTick: {
              alignWithLabel: true
            },
            axisLine: {
              lineStyle: {
                color: '#DCDFE6'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '价格指数',
            min: 80,
            max: 160,
            interval: 20,
            axisLabel: {
              formatter: '{value}',
              color: '#606266'
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#EBEEF5'
              }
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '价格指数',
            type: 'bar',
            barWidth: '60%',
            data: values,
            itemStyle: {
              color: params => {
                // 根据值确定颜色：>100为涨，<100为跌
                return params.data > 100 ? '#F56C6C' : params.data < 100 ? '#67C23A' : '#E6A23C';
              },
              borderRadius: [4, 4, 0, 0]
            },
            label: {
              show: true,
              position: 'top',
              formatter: '{c}',
              fontSize: 12,
              fontWeight: 'bold',
              color: params => {
                return params.value > 100 ? '#F56C6C' : params.value < 100 ? '#67C23A' : '#E6A23C';
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.1)'
              }
            },
            markLine: {
              symbol: 'none',
              lineStyle: {
                type: 'dashed',
                color: '#909399'
              },
              data: [
                {
                  name: '基准线',
                  yAxis: 100,
                  label: {
                    formatter: '基准值(100)',
                    position: 'end'
                  }
                }
              ]
            },
            animationDelay: (idx) => idx * 150,
            animationEasing: 'elasticOut'
          }
        ],
        animationEasing: 'elasticOut',
        animationDelayUpdate: (idx) => idx * 150
      };
      
      // 使用未修改的数据渲染图表
      this.priceChart.setOption(option);
      
      // 添加点击事件
      this.priceChart.on('click', (params) => {
        if (indicesData[params.dataIndex]) {
          // 构建与marketTrends兼容的数据结构
          const item = indicesData[params.dataIndex];
          const trend = (item.value > 100) ? 'up' : (item.value < 100) ? 'down' : 'stable';
          const change = Math.abs(item.value - 100).toFixed(1);
          
          const indexItem = {
            id: item.id,
            title: item.category.replace(/生产价格指数$/, ''),
            value: `${item.value}`,
            icon: this.getCategoryIcon(item.category),
            trend: trend,
            change: trend === 'up' ? `+${change}%` : trend === 'down' ? `-${change}%` : '0%',
            volume: item.year ? `${item.year}年` : '本年度',
            unit: item.unit || '上年=100',
            description: item.description || '反映该品类农产品价格变化情况',
            originalData: item
          };
          
          this.showIndexDetail(indexItem);
        }
      });
      
      // 设置自适应大小
      window.addEventListener('resize', () => {
        this.priceChart.resize();
      });
    },
    
    // 根据分类获取图标
    getCategoryIcon(category) {
      const icons = {
        '粮食': 'el-icon-rice',
        '水果': 'el-icon-apple',
        '蔬菜': 'el-icon-vegetable',
        '肉类': 'el-icon-meat',
        '禽蛋': 'el-icon-egg',
        '油料': 'el-icon-oil',
        '水产': 'el-icon-fish'
      };
      return icons[category] || 'el-icon-food';
    },
    
    // 获取农业提示的模拟实现
    fetchFarmingTips() {
      console.log('获取农业提示');
      // 使用本地数据代替服务调用
      this.loadFarmingAdvice();
    },
    
    // 数据刷新方法
    fetchData() {
      console.log('刷新所有数据');
      this.fetchNewsList();
      this.fetchMarketPriceData();
      this.fetchFarmingTips();
      this.setDefaultWeatherAlerts();
    },
    
    // 设置轮播自动切换
    setInterval() {
      // 可以在这里添加轮播相关的定时逻辑
      console.log('设置轮播自动切换');
    },
    
    // 添加设置默认市场趋势数据的方法，仅在API请求失败时使用
    setDefaultMarketTrends() {
      console.log('无法获取市场趋势数据');
      this.marketTrends = [];
      this.$message.error('无法获取市场趋势数据，请稍后重试');
    },
    // 显示指数详情
    async showIndexDetail(item) {
      this.selectedIndex = item;
      this.indexDetailVisible = true;
      
      // 清除之前的数据
      this.indexYearlyData = [];
      this.relatedIndices = [];
      
      try {
        // 加载历年数据
        this.loadYearlyData(item.id);
        
        // 加载相关指数
        this.loadRelatedIndices(item);
        
        // 在下一个DOM更新周期后初始化图表
        this.$nextTick(() => {
          this.initYearlyChart();
        });
      } catch (error) {
        console.error('加载指数详情数据失败:', error);
        this.$message.warning('加载详细数据失败，显示基本信息');
      }
    },
    
    // 加载历年数据
    loadYearlyData(_productId) {
      try {
        // 模拟从API获取历年数据
        const years = [2021, 2022, 2023];
        const mockYearlyData = years.map(year => {
          // 模拟不同年份的数据，基于当前值随机波动
          const baseValue = parseFloat(this.selectedIndex.value);
          const randomFactor = 0.95 + Math.random() * 0.15; // 在95%到110%之间波动
          return {
            year,
            value: (baseValue * randomFactor).toFixed(1)
          };
        });
        
        // 排序确保年份递增
        this.indexYearlyData = mockYearlyData.sort((a, b) => a.year - b.year);
      } catch (error) {
        console.error('加载历年数据失败:', error);
        this.indexYearlyData = [];
      }
    },
    
    // 加载相关指数
    loadRelatedIndices(item) {
      try {
        if (!this.priceData) return;
        
        // 从完整数据集中查找相关指数
        // 相关性规则：
        // 1. 排除当前选中的指数
        // 2. 按照与当前指数的类别相似度或相关性排序
        // 注释掉未使用的变量
        // const _currentCategory = item.title.toLowerCase();
        
        this.relatedIndices = this.priceData
          .filter(index => index.id !== item.id) // 排除当前指数
          .slice(0, 5); // 最多取5个相关指数
      } catch (error) {
        console.error('加载相关指数失败:', error);
        this.relatedIndices = [];
      }
    },
    
    // 初始化年度走势图表
    initYearlyChart() {
      if (!this.$refs.yearlyChart) return;
      
      const chartDom = this.$refs.yearlyChart;
      const yearlyChart = echarts.init(chartDom);
      
      // 准备数据
      const years = this.indexYearlyData.map(item => item.year);
      const values = this.indexYearlyData.map(item => item.value);
      
      // 生成均值和移动平均线数据
      const avg = values.reduce((sum, val) => sum + parseFloat(val), 0) / values.length;
      
      // 计算拟合趋势线数据
      const trendData = this.calculateTrendLine(years, values);
      
      // 设置图表选项
      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let result = `<div style="font-weight: bold; margin-bottom: 5px">${params[0].name}年</div>`;
            
            params.forEach(param => {
              if (param.seriesName === '价格指数') {
                const value = param.value;
                const baseline = 100;
                const diff = (value - baseline).toFixed(1);
                const trend = diff > 0 ? '上涨' : diff < 0 ? '下跌' : '持平';
                const color = diff > 0 ? '#F56C6C' : diff < 0 ? '#67C23A' : '#E6A23C';
                
                result += `<div>
                  ${param.marker} ${param.seriesName}: <span style="float: right; font-weight: bold">${value}</span>
                </div>`;
                result += `<div>
                  同比: <span style="float: right; color: ${color}">${trend} ${Math.abs(diff)}%</span>
                </div>`;
              } else {
                result += `<div>
                  ${param.marker} ${param.seriesName}: <span style="float: right">${param.value}</span>
                </div>`;
              }
            });
            
            return result;
          }
        },
        legend: {
          data: ['价格指数', '平均值', '趋势线'],
          bottom: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: years,
          axisLabel: {
            formatter: '{value}年',
            color: '#606266'
          },
          axisLine: {
            lineStyle: {
              color: '#DCDFE6'
            }
          },
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          type: 'value',
          name: '价格指数',
          min: Math.min(...values) * 0.95,
          max: Math.max(...values) * 1.05,
          axisLabel: {
            formatter: '{value}',
            color: '#606266'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#EBEEF5'
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '价格指数',
            data: values,
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: '#409EFF'
            },
            lineStyle: {
              width: 3
            },
            label: {
              show: true,
              position: 'top',
              formatter: '{c}',
              fontWeight: 'bold',
              color: params => {
                const baseline = 100;
                const diff = params.value - baseline;
                return diff > 0 ? '#F56C6C' : diff < 0 ? '#67C23A' : '#E6A23C';
              }
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.5)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0.1)'
                }
              ])
            },
            markPoint: {
              data: [
                { type: 'max', name: '最大值' },
                { type: 'min', name: '最小值' }
              ],
              symbolSize: 45,
              itemStyle: {
                color: '#409EFF'
              }
            }
          },
          {
            name: '平均值',
            type: 'line',
            symbol: 'none',
            lineStyle: {
              type: 'dashed',
              width: 1,
              color: '#F56C6C'
            },
            data: Array(years.length).fill(avg),
            markLine: {
              data: [
                { 
                  yAxis: 100, 
                  name: '基准线',
                  lineStyle: {
                    color: '#909399',
                    type: 'dashed'
                  },
                  label: {
                    formatter: '基准值(100)',
                    position: 'end'
                  }
                }
              ]
            }
          },
          {
            name: '趋势线',
            type: 'line',
            showSymbol: false,
            data: trendData,
            lineStyle: {
              color: '#67C23A',
              width: 2,
              type: 'dashed'
            }
          }
        ],
        animationDuration: 1000,
        animationEasing: 'elasticOut'
      };
      
      yearlyChart.setOption(option);
      
      // 保存图表引用以便销毁
      this.yearlyChart = yearlyChart;
    },
    
    // 计算趋势线的方法
    calculateTrendLine(years, values) {
      if (years.length < 2) return years.map(() => parseFloat(values[0]));
      
      // 将年份转换为数字序列 [0, 1, 2...]
      const xData = years.map((_, i) => i);
      const yData = values.map(v => parseFloat(v));
      
      // 计算线性回归
      let sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;
      const n = xData.length;
      
      for (let i = 0; i < n; i++) {
        sumX += xData[i];
        sumY += yData[i];
        sumXY += xData[i] * yData[i];
        sumX2 += xData[i] * xData[i];
      }
      
      const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
      const intercept = (sumY - slope * sumX) / n;
      
      // 生成趋势线数据
      return xData.map(x => {
        const y = slope * x + intercept;
        return parseFloat(y.toFixed(1));
      });
    },
    
    // 对话框关闭处理
    handleDialogClose() {
      if (this.yearlyChart) {
        this.yearlyChart.dispose();
        this.yearlyChart = null;
      }
      this.selectedIndex = null;
      this.indexYearlyData = [];
      this.relatedIndices = [];
      this.indexDetailVisible = false;
    },
    
    // 查看全部指数
    viewAllIndices() {
      this.indexDetailVisible = false;
      this.$router.push('/price');
    },
    
    // 切换到相关指数
    switchToRelatedIndex(item) {
      // 构建一个与marketTrends项结构相同的对象
      const trend = (item.value > 100) ? 'up' : (item.value < 100) ? 'down' : 'stable';
      const change = Math.abs(item.value - 100).toFixed(1);
      
      const indexItem = {
        id: item.id,
        title: item.category.replace(/生产价格指数$/, ''),
        value: `${item.value}`,
        icon: this.getCategoryIcon(item.category),
        trend: trend,
        change: trend === 'up' ? `+${change}%` : trend === 'down' ? `-${change}%` : '0%',
        volume: item.year ? `${item.year}年` : '本年度',
        unit: item.unit || '上年=100',
        description: item.description || '反映该品类农产品价格变化情况',
        originalData: item
      };
      
      // 关闭当前弹窗，然后显示新选择的指数详情
      this.handleDialogClose();
      this.$nextTick(() => {
        this.showIndexDetail(indexItem);
      });
    },
    
    getTrendColor(trend) {
      const colors = {
        up: '#67C23A',
        down: '#F56C6C',
        stable: '#E6A23C'
      };
      return colors[trend] || '#909399';
    },
    
    getTagTypeByValue(value) {
      if (value > 100) return 'success';
      if (value < 100) return 'danger';
      return 'info';
    },
    // 移除不再需要的方法
    /*
    checkAndShowWeatherNotification() {
      // 检查是否今日首次访问
      const lastVisitDate = localStorage.getItem('last_visit_date');
      const today = new Date().toDateString();
      
      // 如果是今日首次访问，显示天气提醒
      if (lastVisitDate !== today) {
        // 记录访问日期
        localStorage.setItem('last_visit_date', today);
        
        // 延迟1秒显示天气提醒，确保数据已加载
        setTimeout(() => {
          // this.showWeatherNotification(); // 注释掉：不再自动显示初始弹窗
          // this.showWeatherAdvisoryFloat(); // 注释掉：初始访问时不自动显示悬浮窗，应由 F2 等事件触发
        }, 1000);
      }
    },
    */

    // 显示天气提醒弹窗
    showWeatherNotification() {
      if (this.weatherAlerts && this.weatherAlerts.length > 0) {
        // 有天气预警，显示预警弹窗
        this.weatherDialogVisible = true;
      } else if (this.weatherAdvice) {
        // 没有预警但有农业建议，显示建议弹窗
        this.weatherDialogVisible = true;
      }
    },
    
    // 检查并显示天气农事活动提示悬浮窗（仅当前会话首次访问网站时）
    async checkAndShowWeatherAdvisoryFloat() {
      // 检查 sessionStorage 是否已标记当前会话显示过
      const sessionShown = sessionStorage.getItem('weatherNotificationShown');
      
      if (sessionShown === 'true') {
        console.log('Weather notification already shown in this session.');
        return; // 如果已显示过，则不执行后续操作
      }
  
      try {
        // 获取当前位置
        const location = await weatherService.getLocationByIp();
        let city = '北京'; // 默认城市
        
        if (location && location.city) {
          city = location.city;
        }
        
        // 获取天气农事建议
        this.weatherAdvisory = await weatherAdvisoryService.generateWeatherAdvisory(city);
        
        // 显示悬浮窗
        this.showWeatherNotificationFloat = true;
  
        // 在 sessionStorage 中设置标记，表示当前会话已显示过
        sessionStorage.setItem('weatherNotificationShown', 'true');
        console.log('Weather notification shown and session flag set.');
  
        // 移除或注释掉此处的 setTimeout，让 WeatherNotification 组件内部处理关闭逻辑
        // setTimeout(() => {
        //   this.showWeatherNotificationFloat = false;
        // }, 5000);
      } catch (err) {
        console.error('获取天气农事建议失败:', err);
      }
    },

    // 获取智能农业建议
    async fetchSmartFarmingAdvice() {
      this.loading.weatherAdvice = true;
      try {
        // 获取当前位置
        const location = await weatherService.getLocationByIp();
        const city = location.name || '北京';
        
        // 获取当前天气数据
        const weatherData = await weatherService.getWeather(city);
        this.currentWeather = weatherData;
        
        // 获取智能农业建议
        const farmingAdvice = await weatherService.getSmartFarmingAdvice(city);
        this.smartFarmingAdvice = farmingAdvice;
        
        // 如果没有预警数据，生成基于天气的建议
        if (!this.weatherAlerts || this.weatherAlerts.length === 0) {
          this.generateWeatherAlerts(this.currentWeather);
        }
        
      } catch (err) {
        console.error('获取智能农业建议失败:', err);
        // 设置默认建议
        this.smartFarmingAdvice = {
          title: '今日农事建议',
          general: '提示：无法获取实时天气数据，建议查看详细天气预报后安排农事活动。',
          actions: [
            '关注天气变化，合理安排农事活动',
            '定期检查农田灌溉系统',
            '根据作物生长阶段调整管理措施'
          ],
          level: 'info'
        };
      } finally {
        this.loading.weatherAdvice = false;
      }
    },
    
    // 根据天气状况生成预警
    generateWeatherAlerts(weather) {
      if (!weather) return;
      
      const alerts = [];
      
      // 温度预警
      if (parseInt(weather.temperature) >= 35) {
        alerts.push({
          type: 'temperature',
          level: '黄色',
          title: '高温预警',
          text: `当前温度${weather.temperature}°C，注意防暑降温。`,
          pubTime: new Date().toISOString()
        });
      } else if (parseInt(weather.temperature) <= 5) {
        alerts.push({
          type: 'temperature',
          level: '蓝色',
          title: '低温预警',
          text: `当前温度${weather.temperature}°C，注意农作物防寒保暖。`,
          pubTime: new Date().toISOString()
        });
      }
      
      // 风力预警
      if (parseInt(weather.windSpeed) >= 20) {
        alerts.push({
          type: 'wind',
          level: '黄色',
          title: '大风预警',
          text: `当前风力较大，风速${weather.windSpeed}km/h，注意农田设施防护。`,
          pubTime: new Date().toISOString()
        });
      }
      
      // 湿度预警
      if (parseInt(weather.humidity) >= 85) {
        alerts.push({
          type: 'rain',
          level: '蓝色',
          title: '湿度预警',
          text: `当前湿度${weather.humidity}%，注意农作物防霉，加强通风。`,
          pubTime: new Date().toISOString()
        });
      } else if (parseInt(weather.humidity) <= 30) {
        alerts.push({
          type: 'drought',
          level: '蓝色',
          title: '干燥预警',
          text: `当前湿度${weather.humidity}%，注意农田灌溉，防止土壤过度干燥。`,
          pubTime: new Date().toISOString()
        });
      }
      
      // 天气状况预警
      if (weather.weather.includes('雨')) {
        const level = weather.weather.includes('大雨') || weather.weather.includes('暴雨') ? '橙色' : '蓝色';
        alerts.push({
          type: 'rain',
          level: level,
          title: '降水预警',
          text: `当前天气${weather.weather}，注意农田排水，防止作物积水。`,
          pubTime: new Date().toISOString()
        });
      } else if (weather.weather.includes('雪')) {
        alerts.push({
          type: 'snow',
          level: '黄色',
          title: '降雪预警',
          text: `当前天气${weather.weather}，注意设施农业防护，防止积雪损坏设施。`,
          pubTime: new Date().toISOString()
        });
      } else if (weather.weather.includes('雾') || weather.weather.includes('霾')) {
        alerts.push({
          type: 'fog',
          level: '黄色',
          title: '能见度预警',
          text: `当前天气${weather.weather}，注意农作物防护，减少户外作业。`,
          pubTime: new Date().toISOString()
        });
      }
      
      // 更新预警信息
      if (alerts.length > 0) {
        this.weatherAlerts = alerts.slice(0, 3); // 最多显示3个预警
      }
    },
    
    // 根据天气状况获取图标
    getWeatherIconByCondition(condition) {
      if (!condition) return 'el-icon-cloudy';
      
      if (condition.includes('雨')) {
        return condition.includes('大雨') || condition.includes('暴雨') ? 'el-icon-heavy-rain' : 'el-icon-umbrella';
      } else if (condition.includes('雪')) {
        return 'el-icon-cold-drink';
      } else if (condition.includes('雾') || condition.includes('霾')) {
        return 'el-icon-cloudy-and-sunny';
      } else if (condition.includes('晴')) {
        return 'el-icon-sunny';
      } else if (condition.includes('阴')) {
        return 'el-icon-cloudy';
      } else if (condition.includes('风')) {
        return 'el-icon-wind-power';
      }
      
      return 'el-icon-cloudy';
    },
    
    // 根据预警类型获取图标
    getWeatherIconByType(type) {
      if (!type) return 'el-icon-warning';
      
      const typeStr = type.toString().toLowerCase();
      
      const iconMap = {
        'rain': 'el-icon-umbrella',
        'snow': 'el-icon-cold-drink',
        'fog': 'el-icon-cloudy-and-sunny',
        'wind': 'el-icon-wind-power',
        'temperature': 'el-icon-sunrise',
        'high': 'el-icon-sunrise',
        'low': 'el-icon-sunrise',
        'drought': 'el-icon-sunset',
        'thunder': 'el-icon-lightning',
        'hail': 'el-icon-heavy-rain',
        'frost': 'el-icon-cold'
      };
      
      for (const [key, icon] of Object.entries(iconMap)) {
        if (typeStr.includes(key)) {
          return icon;
        }
      }
      
      return 'el-icon-warning';
    },
    
    // 获取预警颜色类
    getAlertClass(level) {
      if (!level) return 'info';
      
      const levelMap = {
        '红色': 'danger',
        '橙色': 'warning',
        '黄色': 'warning',
        '蓝色': 'info'
      };
      
      return levelMap[level] || 'info';
    },
    
    // 格式化预警时间
    formatAlertTime(timeStr) {
      if (!timeStr) return '刚刚';
      
      try {
        const time = new Date(timeStr);
        return time.toLocaleString('zh-CN', { 
          month: 'numeric', 
          day: 'numeric', 
          hour: 'numeric', 
          minute: 'numeric' 
        });
      } catch (e) {
        return timeStr;
      }
    },
    // 格式化预报日期
    formatForecastDay(dateStr) {
      if (!dateStr) return '今天';
      
      try {
        const date = new Date(dateStr);
        const today = new Date();
        const tomorrow = new Date();
        tomorrow.setDate(today.getDate() + 1);
        
        if (date.toDateString() === today.toDateString()) {
          return '今天';
        } else if (date.toDateString() === tomorrow.toDateString()) {
          return '明天';
        } else {
          return date.getMonth() + 1 + '/' + date.getDate();
        }
      } catch (e) {
        return dateStr;
      }
    },
    // 获取生活指数图标
    getIndexIcon(type) {
      const iconMap = {
        '1': 'el-icon-baseball', // 运动指数
        '2': 'el-icon-watermelon', // 洗车指数
        '3': 'el-icon-s-cooperation', // 穿衣指数
        '5': 'el-icon-sunny', // 紫外线指数
        '6': 'el-icon-bangzhu', // 旅游指数
        '8': 'el-icon-heavy-rain', // 感冒指数
        '9': 'el-icon-cold-drink', // 空调指数
        '16': 'el-icon-moon' // 太阳镜指数
      };
      
      return iconMap[type] || 'el-icon-s-help';
    },
    
    // 获取指数类别样式
    getIndexClass(category) {
      const classMap = {
        '适宜': 'suitable',
        '较适宜': 'quite-suitable',
        '不太适宜': 'less-suitable',
        '不适宜': 'unsuitable',
        '炎热': 'hot',
        '舒适': 'comfortable',
        '较舒适': 'quite-comfortable',
        '较不舒适': 'less-comfortable',
        '很不舒适': 'uncomfortable',
        '极不舒适': 'very-uncomfortable',
        '寒冷': 'cold',
        '极强': 'very-strong',
        '强': 'strong',
        '中等': 'medium',
        '弱': 'weak',
        '较弱': 'quite-weak'
      };
      
      return classMap[category] || 'default';
    },
    
    // 获取空气质量级别样式
    getAqiClass(aqi) {
      const aqiNum = parseInt(aqi);
      
      if (isNaN(aqiNum)) return 'unknown';
      if (aqiNum <= 50) return 'excellent';
      if (aqiNum <= 100) return 'good';
      if (aqiNum <= 150) return 'moderate';
      if (aqiNum <= 200) return 'unhealthy';
      if (aqiNum <= 300) return 'very-unhealthy';
      return 'hazardous';
    },
  },
  // mounted方法已在上面定义，这里删除重复的mounted
  beforeDestroy() {
    if (this.priceChart) {
      this.priceChart.dispose()
    }
    // 移除事件监听器
    document.removeEventListener('keydown', this.handleKeyDown);
  }
}
</script>

<style lang="scss" scoped>
.home {
  padding-top: 0;
  margin-top: 60px;
  
  .main-carousel {
    margin-bottom: 30px;
    margin-top: 20px;
    position: relative;
    z-index: 1;
    
    :deep(.el-carousel__container) {
      height: 400px;
    }
    
    :deep(.el-carousel__item) {
      border-radius: 12px;
      overflow: hidden;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 16px rgba(0,0,0,0.2);
      }
    }
    
    .carousel-content {
      height: 100%;
      background-size: cover;
      background-position: center;
      display: flex;
      align-items: center;
      padding: 0 60px;
      border-radius: 12px;
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to right, rgba(0,0,0,0.5), rgba(0,0,0,0.2));
        border-radius: 12px;
      }
      
      .carousel-info {
        position: relative;
        z-index: 1;
        color: #fff;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        max-width: 600px;
        
        .carousel-title {
          font-size: 36px;
          margin-bottom: 20px;
          font-weight: 600;
          line-height: 1.2;
        }
        
        .carousel-desc {
          font-size: 18px;
          margin-bottom: 30px;
          line-height: 1.6;
          opacity: 0.9;
        }
        
        .carousel-btn {
          font-size: 18px;
          padding: 10px 20px;
          border-radius: 5px;
          background-color: #409EFF;
          color: #fff;
          transition: all 0.3s ease;
          
          &:hover {
            background-color: #66b1ff;
          }
        }
      }
    }
  }
  
  .feature-nav {
    margin-bottom: 30px;
    
    .feature-item {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(64,158,255,0.1), rgba(103,194,58,0.1));
        opacity: 0;
        transition: opacity 0.3s ease;
      }
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 16px 0 rgba(0,0,0,.2);
        
        &::before {
          opacity: 1;
        }
        
        .feature-icon {
          transform: scale(1.1);
        }
        
        .feature-info h3 {
          color: #409EFF;
        }
      }
      
      .feature-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        transition: all 0.3s ease;
        
        i {
          font-size: 30px;
          color: #fff;
        }
      }
      
      .feature-info {
        flex: 1;
        
        h3 {
          font-size: 18px;
          color: #303133;
          margin-bottom: 8px;
          transition: color 0.3s ease;
        }
        
        p {
          color: #909399;
          font-size: 14px;
          line-height: 1.4;
        }
      }
    }
  }
  
  .market-trends,
  .agriculture-news,
  .weather-alert {
    margin-bottom: 30px;
    border-radius: 12px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    }
    
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 15px 20px;
      border-bottom: 1px solid #EBEEF5;
      
      span {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
      
      .el-button {
        font-size: 14px;
        color: #409EFF;
        transition: all 0.3s ease;
        
        &:hover {
          color: #66b1ff;
          transform: translateX(5px);
        }
      }
    }
  }
  
  .market-trends {
    .trend-chart {
      height: 300px;
      padding: 20px;
    }
    
    .trend-list {
      .trend-item {
        display: flex;
        align-items: center;
        padding: 15px;
        margin-bottom: 15px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        transition: all 0.3s ease;
        cursor: pointer;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px 0 rgba(0,0,0,.2);
          
          .trend-icon {
            transform: scale(1.1);
          }
          
          .trend-action {
            background-color: #409EFF;
            i {
              color: #fff;
            }
          }
        }
        
        .trend-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          background: #f5f7fa;
          transition: all 0.3s ease;
          
          i {
            font-size: 24px;
            color: #fff;
          }
        }
        
        .trend-info {
          flex: 1;
          
          h4 {
            margin: 0 0 8px;
            font-size: 16px;
            font-weight: 500;
            color: #303133;
          }
          
          .trend-value {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            
            .value {
              font-weight: 500;
              font-size: 16px;
            }
              
            .trend-change {
              margin-left: 10px;
              font-weight: 500;
              
              &.up {
                color: #67C23A;
              }
              
              &.down {
                color: #F56C6C;
              }
              
              &.stable {
                color: #E6A23C;
              }
            }
          }
          
          .trend-meta {
            color: #909399;
            font-size: 14px;
            
            .unit {
              margin-left: 8px;
              padding: 2px 6px;
              background-color: #f5f7fa;
              border-radius: 4px;
              font-size: 12px;
            }
          }
        }
        
        .trend-action {
          width: 24px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          background: #f5f7fa;
          border-radius: 50%;
          margin-left: 15px;
          transition: all 0.3s ease;
          
          i {
            font-size: 16px;
            color: #409EFF;
          }
        }
      }
      
      .view-more {
        text-align: center;
        margin-top: 20px;
        
        .el-button {
          font-size: 14px;
          
          &:hover {
            i {
              animation: bounce-right 1s infinite;
            }
          }
        }
        
        @keyframes bounce-right {
          0%, 100% {
            transform: translateX(0);
          }
          50% {
            transform: translateX(5px);
          }
        }
      }
    }
  }
  
  .agriculture-news {
    .news-list {
      .news-item {
        display: flex;
        margin-bottom: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        &:hover {
          transform: translateX(10px);
          
          .news-image {
            transform: scale(1.05);
            
            .news-overlay {
              opacity: 1;
            }
          }
          
          .news-content h3 {
            color: #409EFF;
          }
        }
        
        .news-image {
          width: 200px;
          height: 150px;
          margin-right: 20px;
          border-radius: 8px;
          overflow: hidden;
          position: relative;
          transition: all 0.3s ease;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.3s ease;
          }
          
          .news-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.3s ease;
            
            i {
              color: #fff;
              font-size: 24px;
            }
          }
        }
        
        .news-content {
          flex: 1;
          
          h3 {
            font-size: 18px;
            color: #303133;
            margin-bottom: 10px;
            transition: color 0.3s ease;
          }
          
          p {
            color: #606266;
            margin-bottom: 10px;
            line-height: 1.6;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
          
          .news-meta {
            color: #909399;
            display: flex;
            align-items: center;
            
            .el-tag {
              margin-right: 15px;
            }
            
            span {
              margin-right: 15px;
              display: flex;
              align-items: center;
              
              i {
                margin-right: 5px;
              }
            }
          }
        }
      }
    }
    
    .hot-news {
      h3 {
        margin-bottom: 20px;
        color: #303133;
        font-size: 18px;
        font-weight: 600;
      }
      
      .hot-news-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding: 10px;
        border-radius: 8px;
        transition: all 0.3s ease;
        cursor: pointer;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        &:hover {
          background-color: #f5f7fa;
          transform: translateX(5px);
          
          .hot-news-title {
            color: #409EFF;
          }
        }
        
        .news-rank {
          width: 24px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          background: #F5F7FA;
          border-radius: 50%;
          margin-right: 10px;
          color: #909399;
          font-weight: 500;
          
          &.top-three {
            background: #F56C6C;
            color: #fff;
          }
        }
        
        .hot-news-content {
          flex: 1;
          
          .hot-news-title {
          font-size: 14px;
            color: #303133;
            margin-bottom: 5px;
            transition: color 0.3s ease;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
          
          .hot-news-meta {
            display: flex;
            align-items: center;
          color: #909399;
          font-size: 12px;
            
            .el-tag {
              margin-right: 10px;
            }
            
            .hot-news-views {
              display: flex;
              align-items: center;
              
              i {
                margin-right: 5px;
              }
            }
          }
        }
      }
      
      .load-more {
        text-align: center;
        margin-top: 20px;
      }
      
      .no-news-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px;
        background-color: #f8f9fa;
        border-radius: 8px;
        color: #909399;
        
        i {
          font-size: 50px;
          margin-bottom: 15px;
        }
        
        p {
          font-size: 16px;
        }
      }
    }

    .no-weather-data {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;
      background-color: #f8f9fa;
      border-radius: 8px;
      color: #909399;
      
      i {
        font-size: 50px;
        margin-bottom: 15px;
      }
      
      p {
        font-size: 16px;
      }
    }
  }
  
  // 价格指数详情弹窗样式
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    
    .el-dialog__header {
      padding: 20px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
    }
    
    .el-dialog__body {
      padding: 20px;
    }
    
    .el-dialog__footer {
      border-top: 1px solid #e4e7ed;
      padding: 15px 20px;
    }
  }
  
  .index-detail {
    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h3 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
      }
      
      .detail-value {
        display: flex;
        align-items: center;
        
        .main-value {
          font-size: 24px;
          font-weight: 700;
          margin-right: 10px;
        }
        
        .change-value {
          padding: 5px 10px;
          border-radius: 4px;
          font-weight: 600;
          
          &.up {
            background-color: rgba(103, 194, 58, 0.1);
            color: #67C23A;
          }
          
          &.down {
            background-color: rgba(245, 108, 108, 0.1);
            color: #F56C6C;
          }
          
          &.stable {
            background-color: rgba(230, 162, 60, 0.1);
            color: #E6A23C;
          }
          
          i {
            margin-right: 5px;
          }
        }
      }
    }
    
    .detail-info {
      background-color: #f5f7fa;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
      
      .info-item {
        margin-bottom: 10px;
        display: flex;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .label {
          width: 80px;
          color: #909399;
          font-weight: 500;
        }
        
        .value {
          flex: 1;
          color: #303133;
        }
      }
    }
    
    .yearly-trend {
      margin-bottom: 20px;
      
      h4 {
        font-size: 18px;
        margin-bottom: 15px;
        color: #303133;
      }
      
      .yearly-chart {
        height: 200px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
        overflow: hidden;
      }
    }
    
    .related-indices {
      h4 {
        font-size: 18px;
        margin-bottom: 15px;
        color: #303133;
      }
      
      .related-list {
        display: flex;
        flex-wrap: wrap;
        
        .related-tag {
          margin-right: 10px;
          margin-bottom: 10px;
          cursor: pointer;
          padding: 8px 12px;
          font-size: 14px;
          transition: all 0.3s ease;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          }
        }
      }
    }
  }
}

/* 农业政策动态样式 */
.trending-news {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
  
  h3 {
    margin-bottom: 20px;
    color: #303133;
    font-size: 18px;
    font-weight: 600;
  }
  
  .trending-news-item {
    padding: 12px 15px;
    margin-bottom: 12px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.05);
    transition: all 0.3s ease;
    cursor: pointer;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    &:hover {
      background-color: #f5f7fa;
      transform: translateX(5px);
      box-shadow: 0 4px 12px 0 rgba(0,0,0,.1);
      
      .trending-news-title {
        color: #409EFF;
      }
    }
    
    .trending-news-content {
      .trending-news-title {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 8px;
        line-height: 1.5;
        transition: color 0.3s ease;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      
      .trending-news-meta {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .el-tag {
          font-size: 12px;
        }
        
        .trending-news-date {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
  
  .view-more {
    text-align: center;
    margin-top: 15px;
    
    .el-button {
      font-size: 14px;
      
      &:hover {
        i {
          animation: bounce-right 1s infinite;
        }
      }
    }
  }
}

// 添加农业建议样式
.farming-advice {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px dashed #E6E6E6;
  
  .advice-title {
    display: flex;
    align-items: center;
    font-size: 18px;
    margin-bottom: 15px;
    
    i {
      margin-right: 8px;
      font-size: 22px;
    }
  }
  
  .advice-content {
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
    animation: fadeIn 0.5s ease;
    
    &.normal {
      background-color: #F2F6FC;
      border-left: 4px solid #909399;
    }
    
    &.info {
      background-color: #F0F9EB;
      border-left: 4px solid #67C23A;
    }
    
    &.warning {
      background-color: #FDF6EC;
      border-left: 4px solid #E6A23C;
    }
    
    &.danger {
      background-color: #FEF0F0;
      border-left: 4px solid #F56C6C;
    }
    
    .advice-description {
      font-size: 16px;
      margin-bottom: 12px;
      font-weight: 500;
    }
    
    .advice-suggestions {
      ul {
        padding-left: 20px;
        margin: 0;
        
        li {
          margin-bottom: 8px;
          font-size: 14px;
          line-height: 1.5;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

// 添加天气弹窗样式
.weather-dialog {
  .dialog-alert-item, .dialog-advice-item {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    animation: fadeIn 0.5s ease;
    display: flex;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    &.normal {
      background-color: #F2F6FC;
    }
    
    &.info {
      background-color: #F0F9EB;
    }
    
    &.warning {
      background-color: #FDF6EC;
    }
    
    &.danger {
      background-color: #FEF0F0;
    }
    
    .alert-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      flex-shrink: 0;
      
      i {
        font-size: 20px;
        color: #fff;
      }
    }
    
    .alert-content {
      flex: 1;
      
      h4 {
        margin: 0 0 10px;
        font-size: 16px;
      }
      
      p {
        margin: 0 0 8px;
        font-size: 14px;
        line-height: 1.5;
        
        &.farming-suggestion {
          background-color: rgba(255, 255, 255, 0.7);
          padding: 8px;
          border-radius: 4px;
        }
      }
    }
  }
  
  .dialog-advice-item {
    flex-direction: column;
    
    h3 {
      margin: 0 0 10px;
      font-size: 18px;
      display: flex;
      align-items: center;
      
      i {
        margin-right: 8px;
      }
    }
    
    .advice-suggestions {
      margin-top: 10px;
      
      ul {
        padding-left: 20px;
        margin: 0;
        
        li {
          margin-bottom: 8px;
          font-size: 14px;
          line-height: 1.5;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.weather-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #67B26F, #4ca2cd);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  
  .weather-info {
    display: flex;
    align-items: center;
    
    .weather-icon {
      font-size: 48px;
      margin-right: 20px;
      
      i {
        filter: drop-shadow(0 2px 5px rgba(0,0,0,0.1));
      }
    }
    
    .weather-temp {
      display: flex;
      flex-direction: column;
      
      .temp-value {
        font-size: 32px;
        font-weight: bold;
        line-height: 1;
      }
      
      .weather-condition {
        font-size: 16px;
        opacity: 0.9;
        margin-top: 5px;
      }
    }
  }
  
  .weather-details {
    display: flex;
    gap: 20px;
    
    .weather-detail-item {
      display: flex;
      align-items: center;
      
      i {
        margin-right: 5px;
        font-size: 18px;
      }
    }
  }
}

.alert-item {
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  }
  
  &.danger {
    background: linear-gradient(135deg, #ffafbd, #ffc3a0);
    border-left: 4px solid #F56C6C;
  }
  
  &.warning {
    background: linear-gradient(135deg, #f6d365, #fda085);
    border-left: 4px solid #E6A23C;
  }
  
  &.info {
    background: linear-gradient(135deg, #56CCF2, #2F80ED);
    border-left: 4px solid #409EFF;
  }
  
  .alert-icon {
    font-size: 28px;
    margin-bottom: 10px;
    color: white;
    filter: drop-shadow(0 2px 5px rgba(0,0,0,0.1));
  }
  
  .alert-content {
    color: white;
    
    .alert-header {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      
      .alert-level {
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        margin-right: 8px;
        background-color: rgba(255, 255, 255, 0.3);
        
        &.danger {
          background-color: #F56C6C;
        }
        
        &.warning {
          background-color: #E6A23C;
        }
        
        &.info {
          background-color: #409EFF;
        }
      }
      
      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    p {
      margin: 0 0 10px;
      font-size: 14px;
      opacity: 0.9;
    }
    
    .alert-time {
      font-size: 12px;
      opacity: 0.8;
    }
  }
}

.no-weather-data {
  text-align: center;
  padding: 30px 0;
  
  i {
    font-size: 60px;
    color: #67C23A;
    margin-bottom: 15px;
    display: block;
  }
  
  p {
    color: #67C23A;
    font-size: 18px;
    margin-bottom: 15px;
  }
}

.farming-advice {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px dashed rgba(0, 0, 0, 0.1);
  
  .advice-title {
    display: flex;
    align-items: center;
    font-size: 20px;
    margin-bottom: 15px;
    color: #303133;
    
    i {
      margin-right: 10px;
      font-size: 24px;
      color: #67C23A;
    }
  }
  
  .advice-content {
    padding: 20px;
    border-radius: 10px;
    background-color: #F0F9EB;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    
    &.info {
      background-color: #F0F9EB;
      border-left: 4px solid #67C23A;
    }
    
    &.warning {
      background-color: #FDF6EC;
      border-left: 4px solid #E6A23C;
    }
    
    &.danger {
      background-color: #FEF0F0;
      border-left: 4px solid #F56C6C;
    }
    
    .advice-description {
      font-size: 16px;
      color: #606266;
      margin-bottom: 15px;
      line-height: 1.6;
    }
    
    .advice-suggestions {
      ul {
        padding-left: 20px;
        
        li {
          margin-bottom: 12px;
          font-size: 14px;
          color: #606266;
          position: relative;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          &::before {
            content: "";
            position: absolute;
            left: -20px;
            top: 8px;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #67C23A;
          }
        }
      }
    }
    
    .view-more-weather {
      margin-top: 15px;
      text-align: right;
    }
  }
}

/* 天气状态样式优化 */
.weather-alert {
  margin-bottom: 20px;
}

.weather-alert .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.weather-alert .header-controls {
  display: flex;
  align-items: center;
}

.weather-alert .location-info {
  margin-right: 15px;
  font-size: 14px;
  color: #606266;
}

.weather-alert .button-text {
  margin-left: 2px;
}

/* 顶部天气状态容器 */
.weather-status-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 20px;
}

.weather-current {
  flex: 1;
  min-width: 300px;
  background: linear-gradient(135deg, #67B26F, #4ca2cd);
  border-radius: 10px;
  padding: 20px;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.weather-info {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.weather-icon {
  font-size: 48px;
  margin-right: 20px;
  filter: drop-shadow(0 2px 5px rgba(0,0,0,0.1));
}

.weather-temp {
  display: flex;
  flex-direction: column;
}

.temp-value {
  font-size: 38px;
  font-weight: bold;
  line-height: 1;
}

.weather-condition {
  font-size: 16px;
  opacity: 0.9;
  margin-top: 5px;
}

.weather-details {
  display: flex;
  justify-content: space-between;
}

.weather-detail-item {
  display: flex;
  align-items: center;
  
  i {
    margin-right: 5px;
    font-size: 18px;
  }
}

/* 预报部分 */
.weather-forecast-brief {
  flex: 1;
  min-width: 300px;
  padding: 20px;
  background: linear-gradient(135deg, #E0F7FA, #B2EBF2);
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.forecast-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #01579B;
}

.forecast-items {
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.forecast-item {
  text-align: center;
  flex: 1;
  background-color: rgba(255, 255, 255, 0.5);
  padding: 15px 10px;
  border-radius: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.8);
    transform: translateY(-3px);
  }
}

.forecast-day {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #01579B;
}

.forecast-icon {
  font-size: 28px;
  margin-bottom: 10px;
  color: #0288D1;
}

.forecast-temp {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.forecast-text {
  font-size: 14px;
  color: #0288D1;
}

/* 底部布局 */
.bottom-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

/* 天气预警部分 */
.weather-alerts-section {
  flex: 1;
  min-width: 300px;
  background-color: #fdf6ec;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #303133;
  display: flex;
  align-items: center;
  
  i {
    margin-right: 8px;
    font-size: 20px;
  }
}

.no-alerts {
  text-align: center;
  padding: 20px 0;
  
  i {
    font-size: 48px;
    color: #67C23A;
    margin-bottom: 10px;
  }
  
  p {
    color: #67C23A;
    font-size: 16px;
  }
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.alert-item {
  display: flex;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  &.danger {
    background: linear-gradient(135deg, #ffafbd, #ffc3a0);
  }
  
  &.warning {
    background: linear-gradient(135deg, #f6d365, #fda085);
  }
  
  &.info {
    background: linear-gradient(135deg, #56CCF2, #2F80ED);
  }
  
  .alert-icon {
    font-size: 24px;
    margin-right: 12px;
    color: white;
    align-self: center;
  }
  
  .alert-content {
    flex: 1;
    color: white;
    
    .alert-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .alert-level {
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        margin-right: 8px;
        background-color: rgba(255, 255, 255, 0.3);
        
        &.danger {
          background-color: #F56C6C;
        }
        
        &.warning {
          background-color: #E6A23C;
        }
        
        &.info {
          background-color: #409EFF;
        }
      }
      
      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    p {
      margin: 0 0 8px;
      font-size: 14px;
      opacity: 0.9;
    }
    
    .alert-time {
      font-size: 12px;
      opacity: 0.8;
    }
  }
}

/* 天气指数部分 */
.weather-info-section {
  flex: 1;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.weather-indices {
  flex: 1;
  background-color: #f0f9eb;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.indices-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.index-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-3px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  }
}

.index-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 10px;
  background-color: #67C23A;
  color: white;
  font-size: 20px;
  
  &.suitable, &.comfortable, &.excellent, &.good {
    background-color: #67C23A;
  }
  
  &.quite-suitable, &.quite-comfortable, &.medium {
    background-color: #85ce61;
  }
  
  &.less-suitable, &.less-comfortable, &.moderate {
    background-color: #E6A23C;
  }
  
  &.unsuitable, &.uncomfortable, &.unhealthy, &.very-unhealthy {
    background-color: #F56C6C;
  }
  
  &.very-uncomfortable, &.hazardous {
    background-color: #ff4949;
  }
  
  &.strong, &.very-strong, &.hot {
    background-color: #E6A23C;
  }
  
  &.weak, &.quite-weak, &.cold {
    background-color: #409EFF;
  }
}

.index-content {
  flex: 1;
}

.index-name {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.index-category {
  font-size: 12px;
  color: #606266;
  margin-top: 3px;
}

/* 空气质量部分 */
.air-quality {
  flex: 1;
  background-color: #f2f6fc;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.air-data {
  display: flex;
  align-items: center;
}

.air-aqi {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20px;
  padding-right: 20px;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.air-number {
  font-size: 36px;
  font-weight: bold;
  line-height: 1;
  
  &.excellent {
    color: #67C23A;
  }
  
  &.good {
    color: #85ce61;
  }
  
  &.moderate {
    color: #E6A23C;
  }
  
  &.unhealthy {
    color: #F56C6C;
  }
  
  &.very-unhealthy, &.hazardous {
    color: #ff4949;
  }
  
  &.unknown {
    color: #909399;
  }
}

.air-category {
  font-size: 14px;
  margin-top: 5px;
  color: #606266;
}

.air-details {
  flex: 1;
}

.air-detail-item {
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.air-label {
  font-size: 14px;
  color: #909399;
  margin-right: 5px;
}

.air-value {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

/* 农业建议部分样式保持不变 */
.farming-advice {
  margin-top: 10px;
}

/* 暗色主题样式 */
.dark-theme {
  .home-container {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    color: #e2e8f0;
  }
  
  .carousel-item {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    color: #e2e8f0;
    
    h3 {
      color: #e2e8f0;
    }
    
    p {
      color: #a0aec0;
    }
  }
  
  .feature-nav {
    background: rgba(45, 55, 72, 0.8);
    border: 1px solid #4a5568;
    
    .nav-item {
      background: rgba(74, 85, 104, 0.6);
      border: 1px solid #4a5568;
      color: #e2e8f0;
      
      &:hover {
        background: rgba(64, 158, 255, 0.2);
        border-color: #409eff;
        transform: translateY(-5px);
      }
      
      .nav-icon {
        color: #409eff;
      }
      
      h3 {
        color: #e2e8f0;
      }
      
      p {
        color: #a0aec0;
      }
    }
  }
  
  .market-dynamics {
    background: rgba(45, 55, 72, 0.8);
    border: 1px solid #4a5568;
    
    .section-title {
      color: #e2e8f0;
      
      i {
        color: #409eff;
      }
    }
    
    .price-card {
      background: rgba(74, 85, 104, 0.6);
      border: 1px solid #4a5568;
      
      &:hover {
        background: rgba(64, 158, 255, 0.1);
        border-color: #409eff;
      }
      
      .price-header {
        .product-name {
          color: #e2e8f0;
        }
        
        .price-trend {
          &.up {
            color: #f56c6c;
          }
          
          &.down {
            color: #67c23a;
          }
        }
      }
      
      .price-info {
        .current-price {
          color: #409eff;
        }
        
        .price-change {
          color: #a0aec0;
        }
      }
      
      .price-details {
        .detail-item {
          color: #a0aec0;
          
          .label {
            color: #718096;
          }
        }
      }
    }
  }
  
  .weather-section {
    background: rgba(45, 55, 72, 0.8);
    border: 1px solid #4a5568;
    
    .section-title {
      color: #e2e8f0;
      
      i {
        color: #409eff;
      }
    }
    
    .weather-card {
      background: rgba(74, 85, 104, 0.6);
      border: 1px solid #4a5568;
      
      .weather-header {
        .location {
          color: #e2e8f0;
        }
        
        .update-time {
          color: #a0aec0;
        }
      }
      
      .weather-main {
        .temperature {
          color: #409eff;
        }
        
        .weather-desc {
          color: #a0aec0;
        }
      }
      
      .weather-details {
        .weather-detail-item {
          color: #a0aec0;
          
          i {
            color: #718096;
          }
        }
      }
    }
  }
  
  .weather-indices {
    background: rgba(45, 55, 72, 0.8);
    border: 1px solid #4a5568;
    
    .index-item {
      background: rgba(74, 85, 104, 0.6);
      
      &:hover {
        background: rgba(64, 158, 255, 0.1);
      }
      
      .index-name {
        color: #e2e8f0;
      }
      
      .index-category {
        color: #a0aec0;
      }
    }
  }
  
  .air-quality {
    background: rgba(45, 55, 72, 0.8);
    border: 1px solid #4a5568;
    
    .air-category {
      color: #a0aec0;
    }
    
    .air-label {
      color: #718096;
    }
    
    .air-value {
      color: #a0aec0;
    }
  }
  
  .farming-advice {
    border-top: 1px dashed #4a5568;
    
    .advice-title {
      color: #e2e8f0;
      
      i {
        color: #67c23a;
      }
    }
    
    .advice-content {
      background: rgba(74, 85, 104, 0.6);
      border: 1px solid #4a5568;
      
      &.info {
        background: rgba(103, 194, 58, 0.1);
        border-left: 4px solid #67c23a;
      }
      
      &.warning {
        background: rgba(230, 162, 60, 0.1);
        border-left: 4px solid #e6a23c;
      }
      
      &.danger {
        background: rgba(245, 108, 108, 0.1);
        border-left: 4px solid #f56c6c;
      }
      
      .advice-description {
        color: #a0aec0;
      }
      
      .advice-suggestions {
        ul li {
          color: #a0aec0;
          
          &::before {
            background-color: #67c23a;
          }
        }
      }
    }
  }
  
  .no-weather-data {
    p {
      color: #67c23a;
    }
  }
}
</style>