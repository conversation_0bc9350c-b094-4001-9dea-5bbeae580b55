<template>
  <div class="encyclopedia-detail">
    <!-- 阅读进度指示器 -->
    <div class="reading-progress">
      <div class="progress-bar" :style="{ width: readingProgress + '%' }"></div>
    </div>

    <!-- 农业主题背景 -->
    <div class="agriculture-background">
      <div class="wheat-pattern"></div>
      <div class="leaf-decoration"></div>
    </div>

    <div class="page-container">
      <!-- 优化的面包屑导航 -->
      <div class="breadcrumb-wrapper">
        <div class="breadcrumb-container">
          <i class="breadcrumb-icon">🌾</i>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">
              <i class="el-icon-house"></i> 首页
            </el-breadcrumb-item>
            <el-breadcrumb-item :to="{ path: '/encyclopedia' }">
              <i class="el-icon-notebook-2"></i> 农业百科
            </el-breadcrumb-item>
            <el-breadcrumb-item class="current-page">{{ article.title }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-animation">
          <div class="growing-plant">
            <div class="plant-stem"></div>
            <div class="plant-leaves"></div>
          </div>
          <p class="loading-text">正在加载农业知识...</p>
        </div>
      </div>

      <!-- 错误提示 -->
      <div v-if="error && !loading" class="error-container">
        <div class="error-card">
          <i class="error-icon">🌱</i>
          <h3>内容暂时无法加载</h3>
          <p>{{ error }}</p>
          <el-button type="primary" @click="$router.go(-1)">返回上一页</el-button>
        </div>
      </div>

      <div v-if="!loading && !error" class="article-container">
        <div class="content-wrapper">
          <!-- 文章主体 -->
          <div class="article-content">
            <!-- 文章头部 -->
            <div class="article-header">
              <div class="title-section">
                <h1 class="article-title">
                  <span class="title-icon">📖</span>
                  {{ article.title }}
                </h1>
                <div class="article-meta">
                  <div class="meta-item category-meta">
                    <i class="meta-icon">🏷️</i>
                    <el-tag class="category-tag" type="success">{{ article.categoryName || article.category }}</el-tag>
                  </div>
                  <div class="meta-item date-meta">
                    <i class="meta-icon">📅</i>
                    <span>{{ formatDate(article.publishDate) || '未知日期' }}</span>
                  </div>
                  <div class="meta-item views-meta">
                    <i class="meta-icon">👁️</i>
                    <span>{{ article.viewsCount || article.views || 0 }} 阅读</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 文章封面图片 -->
            <div v-if="article.coverImage || article.image" class="article-image-container">
              <div class="image-wrapper">
                <img
                  :src="article.coverImage || article.image"
                  :alt="article.title"
                  @error="handleImageError"
                  class="article-image" />
                <div class="image-overlay">
                  <div class="image-caption">{{ article.title }}</div>
                </div>
              </div>
            </div>

            <!-- 文章摘要 -->
            <div v-if="article.summary" class="article-summary">
              <div class="summary-header">
                <i class="summary-icon">📝</i>
                <span class="summary-label">内容摘要</span>
              </div>
              <div class="summary-content">{{ article.summary }}</div>
            </div>

            <!-- 文章正文 -->
            <div class="article-body">
              <div class="content-container" v-html="formatContent(article.content)"></div>
            </div>

            <!-- 文章标签 -->
            <div class="article-tags" v-if="article.keywordList && article.keywordList.length">
              <div class="tags-header">
                <i class="tags-icon">🏷️</i>
                <span class="tags-label">相关标签</span>
              </div>
              <div class="tags-container">
                <el-tag
                  v-for="tag in article.keywordList"
                  :key="tag"
                  class="article-tag"
                  type="success"
                  effect="plain">
                  {{ tag }}
                </el-tag>
              </div>
            </div>

            <!-- 文章操作按钮 -->
            <div class="article-actions">
              <div class="actions-container">
                <div class="action-button like-button"
                     :class="{ 'active': isLiked }"
                     @click="toggleLike">
                  <div class="button-icon">
                    <i class="action-icon">👍</i>
                  </div>
                  <div class="button-content">
                    <span class="button-text">{{ isLiked ? '已点赞' : '点赞' }}</span>
                    <span class="button-count">({{ article.likesCount || article.likes || 0 }})</span>
                  </div>
                </div>

                <div class="action-button favorite-button"
                     :class="{ 'active': isFavorited }"
                     @click="toggleFavorite">
                  <div class="button-icon">
                    <i class="action-icon">⭐</i>
                  </div>
                  <div class="button-content">
                    <span class="button-text">{{ isFavorited ? '已收藏' : '收藏' }}</span>
                    <span class="button-count">({{ article.favoritesCount || article.favorites || 0 }})</span>
                  </div>
                </div>

                <div class="action-button share-button" @click="shareArticle">
                  <div class="button-icon">
                    <i class="action-icon">📤</i>
                  </div>
                  <div class="button-content">
                    <span class="button-text">分享</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 优化的评论区 -->
          <div class="comments-section">
            <div class="comments-header">
              <div class="header-content">
                <i class="comments-icon">💬</i>
                <h3 class="comments-title">互动讨论</h3>
                <span class="comments-count">({{ comments.length }}条评论)</span>
              </div>
            </div>

            <div class="comment-form-container">
              <div class="form-header">
                <i class="form-icon">✍️</i>
                <span class="form-title">分享您的见解</span>
              </div>
              <div class="comment-form">
                <el-input
                  type="textarea"
                  :rows="4"
                  placeholder="请分享您的农业经验或对本文的看法..."
                  v-model="commentContent"
                  maxlength="500"
                  show-word-limit
                  class="comment-textarea" />
                <div class="form-actions">
                  <el-button
                    type="primary"
                    :disabled="!commentContent.trim()"
                    @click="submitComment"
                    class="submit-button">
                    <i class="el-icon-edit"></i>
                    发表评论
                  </el-button>
                </div>
              </div>
            </div>

            <div class="comments-list">
              <div
                v-for="comment in comments"
                :key="comment.id"
                class="comment-item">
                <div class="comment-avatar">
                  <div class="avatar-wrapper">
                    <el-avatar
                      :src="comment.avatar || defaultAvatar"
                      :size="45"
                      class="user-avatar">
                      {{ comment.userName ? comment.userName.substring(0, 1) : 'U' }}
                    </el-avatar>
                    <div class="avatar-badge">🌱</div>
                  </div>
                </div>
                <div class="comment-content">
                  <div class="comment-header">
                    <span class="comment-author">{{ comment.userName || '匿名用户' }}</span>
                    <span class="author-badge">农友</span>
                    <span class="comment-date">{{ formatDate(comment.createTime) }}</span>
                  </div>
                  <div class="comment-body">{{ comment.content }}</div>
                  <div class="comment-actions">
                    <div class="action-item like-action"
                         :class="{ 'active': comment.isLiked }"
                         @click="likeComment(comment.id)">
                      <i class="action-icon">👍</i>
                      <span class="action-text">{{ comment.likes || 0 }}</span>
                    </div>
                    <div class="action-item reply-action" @click="replyToComment(comment.id)">
                      <i class="action-icon">💬</i>
                      <span class="action-text">回复</span>
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="comments.length === 0" class="no-comments">
                <div class="empty-state">
                  <i class="empty-icon">🌾</i>
                  <h4 class="empty-title">还没有评论</h4>
                  <p class="empty-text">成为第一个分享见解的农友吧！</p>
                </div>
              </div>

              <div v-if="comments.length > 0 && hasMoreComments" class="load-more">
                <el-button type="text" @click="loadMoreComments" class="load-more-button">
                  <i class="el-icon-arrow-down"></i>
                  加载更多评论
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 优化的侧边栏 -->
        <div class="sidebar">
          <!-- 作者信息卡片 -->
          <div class="author-card">
            <div class="card-header">
              <i class="card-icon">👨‍🌾</i>
              <h3 class="card-title">作者信息</h3>
            </div>
            <div class="author-info">
              <div class="author-avatar-container">
                <el-avatar
                  :src="article.authorAvatar || defaultAvatar"
                  :size="60"
                  class="author-avatar">
                  {{ article.authorName ? article.authorName.substring(0, 1) : 'A' }}
                </el-avatar>
                <div class="author-badge">
                  <i class="badge-icon">🌟</i>
                </div>
              </div>
              <div class="author-details">
                <div class="author-name">{{ article.authorName || '农智汇知识库' }}</div>
                <div class="author-title">{{ article.authorBio || '农业知识专家' }}</div>
                <div class="author-stats">
                  <div class="stat-item">
                    <i class="stat-icon">📝</i>
                    <span class="stat-text">专业作者</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 相关推荐 -->
          <div class="related-articles">
            <div class="card-header">
              <i class="card-icon">🔗</i>
              <h3 class="card-title">相关推荐</h3>
            </div>
            <div class="related-list">
              <div
                v-for="item in relatedArticles"
                :key="item.id"
                class="related-item"
                @click="goToArticle(item.id)">
                <div class="related-image">
                  <img
                    :src="item.coverImage || item.image || defaultAvatar"
                    :alt="item.title"
                    @error="handleRelatedImageError($event, item)" />
                  <div class="image-overlay">
                    <i class="overlay-icon">📖</i>
                  </div>
                </div>
                <div class="related-info">
                  <div class="related-title">{{ item.title }}</div>
                  <div class="related-meta">
                    <div class="meta-item">
                      <i class="meta-icon">👁️</i>
                      <span>{{ item.viewsCount || item.views || 0 }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="relatedArticles.length === 0" class="no-related">
                <div class="empty-state">
                  <i class="empty-icon">🌱</i>
                  <p class="empty-text">暂无相关推荐</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 返回顶部按钮 -->
          <div class="back-to-top"
               v-show="showBackToTop"
               @click="scrollToTop">
            <div class="back-button">
              <i class="back-icon">⬆️</i>
              <span class="back-text">回到顶部</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import encyclopediaService from '@/api/encyclopedia'
import { getCurrentUser } from '@/services/auth'  // 导入用户信息获取方法
import { processEncyclopediaImageUrl } from '@/utils/imageUtils'

export default {
  name: 'EncyclopediaDetail',
  props: {
    id: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      article: {},
      loading: true,
      error: null,
      relatedArticles: [],
      comments: [],
      commentContent: '',
      commentPage: 1,
      commentSize: 10,
      hasMoreComments: false,
      isLiked: false,
      isFavorited: false,
      defaultAvatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
      userId: null, // 用户ID
      userInfo: null, // 完整的用户信息
      // 新增的响应式数据
      readingProgress: 0, // 阅读进度
      showBackToTop: false // 是否显示返回顶部按钮
    }
  },
  created() {
    this.getUserInfoFromStorage()
    this.fetchArticleData()
    // 浏览量增加放在fetchArticleData后实现
  },
  mounted() {
    // 浏览量已在fetchArticleData成功后增加，这里不需要再调用
    // 添加滚动监听器
    this.addScrollListeners()
  },
  beforeDestroy() {
    // 移除滚动监听器
    this.removeScrollListeners()
  },
  methods: {
    // 增加文章浏览量
    async incrementViewCount() {
      console.log('incrementViewCount: 开始增加浏览量')
      console.log('incrementViewCount: 文章ID', this.id)
      console.log('incrementViewCount: 文章对象', this.article)

      try {
        // 确保文章数据已加载
        if (!this.article || !this.article.id) {
          console.warn('incrementViewCount: 文章数据未加载完成，推迟增加浏览量');
          return;
        }

        console.log('incrementViewCount: 当前浏览量:', this.article.views);
        console.log('incrementViewCount: 准备调用API增加浏览量...');

        const response = await encyclopediaService.incrementViewCount(this.id);
        console.log('incrementViewCount: API响应:', response);

        if (response && response.code === 200) {
          console.log('incrementViewCount: API调用成功');
          // 立即更新本地浏览量显示
          if (this.article && this.article.views !== undefined) {
            const oldViews = this.article.views;
            this.article.views = oldViews + 1;
            console.log('incrementViewCount: 本地浏览量更新成功，从', oldViews, '增加到', this.article.views);
          } else {
            console.warn('incrementViewCount: 文章浏览量未定义，无法更新本地浏览量');
          }
        } else {
          console.error('incrementViewCount: API返回错误:', response);
        }
      } catch (error) {
        console.error('incrementViewCount: 调用失败:', error);
      }
    },
    
    // 从localStorage获取用户信息
    async getUserInfoFromStorage() {
      try {
        // 使用service中的getCurrentUser代替utils中的getUserInfo
        const userInfo = await getCurrentUser();
        console.log('获取到用户信息:', userInfo);
        if (userInfo) {
          this.userInfo = userInfo;
          this.userId = userInfo.id;
          return userInfo;
        } else {
          console.warn('未获取到用户信息');
          this.userInfo = null;
          this.userId = null;
          return null;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        this.userInfo = null;
        this.userId = null;
        return null;
      }
    },
    
    // 检查用户是否已登录
    checkUserLoggedIn() {
      // 重新检查用户信息，确保是最新的
      const userJson = localStorage.getItem('sfap_user');
      if (userJson) {
        try {
          const freshUserInfo = JSON.parse(userJson);
          // 更新组件中的用户信息
          if (freshUserInfo && freshUserInfo.id) {
            this.userId = freshUserInfo.id;
            this.userInfo = freshUserInfo;
            return true;
          }
        } catch (e) {
          console.error('解析用户信息失败', e);
        }
      }

      // 如果未找到有效用户，显示登录提示
      this.$confirm('需要登录才能执行此操作，是否前往登录页面？', '提示', {
        confirmButtonText: '去登录',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 记录当前页面用于登录后跳回
        localStorage.setItem('redirect_after_login', this.$route.fullPath);
        this.$router.push({
          path: '/login',
          query: { redirect: this.$route.fullPath }
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '您可以继续浏览内容'
        });
      });
      return false;
    },

    // 添加滚动监听器
    addScrollListeners() {
      window.addEventListener('scroll', this.handleScroll)
    },

    // 移除滚动监听器
    removeScrollListeners() {
      window.removeEventListener('scroll', this.handleScroll)
    },

    // 处理滚动事件
    handleScroll() {
      // 计算阅读进度
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const windowHeight = window.innerHeight
      const documentHeight = document.documentElement.scrollHeight

      // 计算阅读进度百分比
      const progress = (scrollTop / (documentHeight - windowHeight)) * 100
      this.readingProgress = Math.min(Math.max(progress, 0), 100)

      // 控制返回顶部按钮显示
      this.showBackToTop = scrollTop > 300
    },

    // 返回顶部
    scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '未知日期'
      try {
        const date = new Date(dateString)
        if (isNaN(date.getTime())) return '未知日期'
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      } catch (error) {
        return '未知日期'
      }
    },

    async fetchArticleData() {
      this.loading = true;
      this.error = null;
      
      try {
        // 先重新获取最新用户信息
        await this.getUserInfoFromStorage();
        
        // 获取文章ID
        const articleId = this.id;
        console.log('获取文章详情, 用户ID:', this.userId, '文章ID:', articleId);
        
        // 即使用户未登录也可以获取文章详情
        const response = await encyclopediaService.getEncyclopediaDetail(articleId, this.userId);
        console.log('文章详情响应:', response);
        
        if (response && response.code === 200 && response.data) {
          this.article = this.mapToViewModel(response.data);
          
          // 设置页面标题和Meta信息
          document.title = this.article.title + ' - 农智汇百科全书';
          
          // 设置已收藏和已点赞状态
          this.isFavorited = response.data.isFavorite || false;
          this.isLiked = response.data.isLiked || false;
          
          // 确保记录下初始的浏览量，便于调试
          console.log('文章初始数据 - 点赞数:', this.article.likes, 
                     '收藏数:', this.article.favorites, 
                     '浏览量:', this.article.views);
          
          // 再次显式检查点赞和收藏状态，确保状态最新
          if(this.userId) {
            await this.checkLikeStatus();
            await this.checkFavoriteStatus();
          }
          
          console.log('收藏状态:', this.isFavorited, '点赞状态:', this.isLiked);
          
          // 将相关文章映射到视图模型
          if (response.data.relatedList && response.data.relatedList.length > 0) {
            this.relatedArticles = response.data.relatedList.map(this.mapToViewModel);
          } else {
            // 如果没有相关文章数据，尝试单独获取
            this.fetchRelatedArticles();
          }
          
          // 初始化评论
          this.fetchComments();
          
          // 文章加载完成后增加浏览量
          this.$nextTick(() => {
            this.incrementViewCount();
          });
        } else {
          throw new Error(response?.message || '获取数据失败');
        }
      } catch (error) {
        console.error('获取百科详情失败', error);
        this.error = '获取文章详情失败: ' + (error.message || '请稍后重试');
        this.article = {};
      } finally {
        this.loading = false;
      }
    },
    
    async fetchRelatedArticles() {
      try {
        const response = await encyclopediaService.getRelatedEncyclopedia(this.id, this.article.categoryId, 5)
        if (response && response.code === 200 && response.data) {
          this.relatedArticles = response.data.map(item => this.mapToViewModel(item))
        }
      } catch (error) {
        console.error('获取相关文章失败', error)
        this.relatedArticles = []
      }
    },
    
    async fetchComments() {
      console.log('开始获取评论，百科ID:', this.id, '页码:', this.commentPage);
      try {
        const response = await encyclopediaService.getComments(this.id, this.commentPage, this.commentSize);
        console.log('评论响应:', response);
        
        if (response && response.code === 200 && response.data) {
          const { list, total } = response.data;
          
          // 确保list是数组
          if (Array.isArray(list)) {
            // 使用commentCache临时存储处理后的评论数据
            const commentCache = list.map(item => ({
              id: item.id,
              content: item.content,
              userName: item.userName || '匿名用户',
              userId: item.userId,
              avatar: this.processImageUrl(item.userAvatar) || this.defaultAvatar,
              createTime: this.formatDate(item.createdAt || item.createTime),
              rawTime: item.createdAt || item.createTime, // 保存原始时间用于排序
              likes: item.likesCount || item.likes || 0,
              isLiked: item.isLiked || false
            }));
            
            // 按时间排序，新评论在前
            commentCache.sort((a, b) => new Date(b.rawTime) - new Date(a.rawTime));
            
            // 更新评论列表
            this.comments = commentCache;
            this.hasMoreComments = this.comments.length < total;
            
            console.log('处理后的评论列表:', this.comments);
            
            if (this.comments.length === 0) {
              console.log('评论列表为空');
            }
          } else {
            console.error('评论列表格式错误:', list);
            this.comments = [];
            this.hasMoreComments = false;
          }
        } else {
          console.warn('评论数据为空或格式不正确', response);
          this.comments = [];
          this.hasMoreComments = false;
        }
      } catch (error) {
        console.error('获取评论失败', error);
        this.comments = [];
        this.hasMoreComments = false;
      }
    },
    
    async loadMoreComments() {
      this.commentPage++;
      try {
        console.log(`加载更多评论，页码: ${this.commentPage}`);
        const response = await encyclopediaService.getComments(this.id, this.commentPage, this.commentSize);
        
        if (response && response.code === 200 && response.data) {
          const { list, total } = response.data;
          if (Array.isArray(list) && list.length > 0) {
            const newComments = list.map(item => ({
              id: item.id,
              content: item.content,
              userName: item.userName || '匿名用户',
              userId: item.userId,
              avatar: this.processImageUrl(item.userAvatar) || this.defaultAvatar,
              createTime: this.formatDate(item.createdAt || item.createTime),
              rawTime: item.createdAt || item.createTime,
              likes: item.likesCount || item.likes || 0,
              isLiked: item.isLiked || false
            }));
            
            // 按时间排序新评论
            newComments.sort((a, b) => new Date(b.rawTime) - new Date(a.rawTime));
            
            // 添加到当前评论列表
            this.comments = [...this.comments, ...newComments];
            
            // 更新是否有更多评论
            this.hasMoreComments = this.comments.length < total;
            
            console.log(`成功加载${newComments.length}条新评论，总评论数: ${this.comments.length}`);
          } else {
            console.log('没有更多评论可加载');
            this.hasMoreComments = false;
          }
        } else {
          console.warn('加载更多评论失败，响应异常:', response);
          this.$message.info('没有更多评论了');
          this.hasMoreComments = false;
        }
      } catch (error) {
        console.error('加载更多评论失败', error);
        this.$message.error('加载评论失败，请稍后重试');
        // 还原页码
        this.commentPage--;
      }
    },
    
    async checkLikeStatus() {
      if (!this.userId) {
        console.log('用户未登录，无需检查点赞状态');
        return;
      }
      
      try {
        console.log('检查点赞状态, 文章ID:', this.id, '用户ID:', this.userId);
        const response = await encyclopediaService.checkLike(this.id, this.userId);
        console.log('点赞状态检查响应:', response);
        
        if (response && response.code === 200) {
          const liked = response.data === true;
          this.isLiked = liked;
          console.log('点赞状态更新为:', liked);
        } else {
          console.warn('点赞状态检查失败:', response);
        }
      } catch (error) {
        console.error('检查点赞状态失败', error);
      }
    },
    
    async checkFavoriteStatus() {
      try {
        const response = await encyclopediaService.checkFavorite(this.id, this.userId)
        this.isFavorited = response?.data === true
      } catch (error) {
        console.error('检查收藏状态失败', error)
      }
    },
    
    async refreshArticleData() {
      try {
        console.log('刷新文章数据，获取最新点赞数量');
        const response = await encyclopediaService.getEncyclopediaDetail(this.id, this.userId);
        console.log('刷新文章数据响应:', response);
        
        if (response && response.code === 200 && response.data) {
          // 保存当前状态
          const oldLikes = this.article.likes;
          const oldFavorites = this.article.favorites;
          const oldViews = this.article.views;
          
          // 创建文章数据的副本以进行检查
          const updatedArticle = this.mapToViewModel(response.data);
          
          // 验证收到的数据是否有效 - 确保不会将数量重置为0
          if (updatedArticle.likes === 0 && oldLikes > 0) {
            console.warn('后端返回的点赞数为0但当前显示不为0，保留当前值', oldLikes);
            updatedArticle.likes = oldLikes;
          }
          
          if (updatedArticle.favorites === 0 && oldFavorites > 0) {
            console.warn('后端返回的收藏数为0但当前显示不为0，保留当前值', oldFavorites);
            updatedArticle.favorites = oldFavorites;
          }
          
          if (updatedArticle.views === 0 && oldViews > 0) {
            console.warn('后端返回的浏览量为0但当前显示不为0，保留当前值', oldViews);
            updatedArticle.views = oldViews;
          }
          
          // 更新文章数据
          this.article = updatedArticle;
          
          console.log('文章数据更新前后对比:',
            { oldLikes, newLikes: this.article.likes, 
              oldFavorites, newFavorites: this.article.favorites,
              oldViews, newViews: this.article.views });
          
          // 更新点赞和收藏状态 - 避免覆盖本地状态
          const newIsLiked = response.data.isLiked;
          const newIsFavorited = response.data.isFavorite;
          
          if (newIsLiked !== undefined) {
            this.isLiked = newIsLiked;
          }
          
          if (newIsFavorited !== undefined) {
            this.isFavorited = newIsFavorited;
          }
          
          console.log('文章数据刷新成功，最新点赞数:', this.article.likes, 
                    '收藏数:', this.article.favorites,
                    '浏览量:', this.article.views,
                    '点赞状态:', this.isLiked,
                    '收藏状态:', this.isFavorited);
        } else {
          console.warn('刷新文章数据失败:', response);
        }
      } catch (error) {
        console.error('刷新文章数据失败:', error);
      }
    },
    
    async toggleLike() {
      // 再次检查登录状态
      if (!this.checkUserLoggedIn()) return;
      
      try {
        // 保存当前点赞状态用于恢复
        const originalLikeStatus = this.isLiked;
        const originalLikesCount = this.article.likes;
        
        if (this.isLiked) {
          // 取消点赞
          console.log('发起取消点赞请求, 用户ID:', this.userId);
          
          // 乐观更新UI
          this.isLiked = false;
          this.article.likes = Math.max(0, this.article.likes - 1);
          
          const response = await encyclopediaService.unlikeArticle(this.id, this.userId);
          console.log('取消点赞响应:', response);
          
          if (response && response.code === 200) {
            this.$message.success('取消点赞成功');
            
            // 服务器端应该已经更新了点赞数据，但我们可以在客户端保持当前计算的值
            const updatedLikesCount = this.article.likes;
            
            // 获取最新的文章数据，确保点赞数量是最新的
            setTimeout(async () => {
              await this.refreshArticleData();
              
              // 再次验证点赞数，避免服务器返回0导致数据不一致
              if (this.article.likes === 0 && updatedLikesCount > 0) {
                console.warn('刷新后点赞数为0，恢复到计算值', updatedLikesCount);
                this.article.likes = updatedLikesCount;
              }
            }, 500); // 增加延迟，确保后端数据已更新
          } else if (response && response.code === 401) {
            // 未授权，可能会话已过期
            this.$message.warning('登录已过期，请重新登录');
            this.$router.push({
              path: '/login',
              query: { redirect: this.$route.fullPath }
            });
            // 恢复原状态
            this.isLiked = originalLikeStatus;
            this.article.likes = originalLikesCount;
          } else {
            this.$message.error(response?.message || '操作失败');
            // 恢复原状态
            this.isLiked = originalLikeStatus;
            this.article.likes = originalLikesCount;
          }
        } else {
          // 点赞
          console.log('发起点赞请求, 用户ID:', this.userId);
          
          // 乐观更新UI
          this.isLiked = true;
          this.article.likes++;
          
          const response = await encyclopediaService.likeArticle(this.id, this.userId);
          console.log('点赞响应:', response);
          
          if (response && response.code === 200) {
            this.$message.success('点赞成功');
            
            // 服务器端应该已经更新了点赞数据，但我们可以在客户端保持当前计算的值
            const updatedLikesCount = this.article.likes;
            
            // 获取最新的文章数据，确保点赞数量是最新的
            setTimeout(async () => {
              await this.refreshArticleData();
              
              // 再次验证点赞数，避免服务器返回0导致数据不一致
              if (this.article.likes === 0 && updatedLikesCount > 0) {
                console.warn('刷新后点赞数为0，恢复到计算值', updatedLikesCount);
                this.article.likes = updatedLikesCount;
              }
            }, 500); // 增加延迟，确保后端数据已更新
          } else if (response && response.code === 401) {
            // 未授权，可能会话已过期
            this.$message.warning('登录已过期，请重新登录');
            this.$router.push({
              path: '/login',
              query: { redirect: this.$route.fullPath }
            });
            // 恢复原状态
            this.isLiked = originalLikeStatus;
            this.article.likes = originalLikesCount;
          } else {
            this.$message.error(response?.message || '操作失败');
            // 恢复原状态
            this.isLiked = originalLikeStatus;
            this.article.likes = originalLikesCount;
          }
        }
      } catch (error) {
        console.error('点赞操作失败', error);
        if (error.response && error.response.status === 401) {
          this.$message.warning('请先登录后再操作');
          this.$router.push({
            path: '/login',
            query: { redirect: this.$route.fullPath }
          });
        } else {
          this.$message.error('操作失败，请稍后重试');
        }
      }
    },
    
    async toggleFavorite() {
      // 再次检查登录状态
      if (!this.checkUserLoggedIn()) return;
      
      try {
        // 保存当前收藏状态用于恢复
        const originalFavoriteStatus = this.isFavorited;
        const originalFavoritesCount = this.article.favorites;
        
        if (this.isFavorited) {
          // 取消收藏
          console.log('发起取消收藏请求, 文章ID:', this.id, '用户ID:', this.userId);
          
          // 乐观更新UI
          this.isFavorited = false;
          this.article.favorites = Math.max(0, this.article.favorites - 1);
          
          // 记录操作开始时间，用于计算操作时长
          const startTime = new Date().getTime();
          
          const response = await encyclopediaService.unfavoriteArticle(this.id, this.userId);
          console.log('取消收藏响应:', response);
          
          // 计算操作耗时
          const endTime = new Date().getTime();
          const requestTime = endTime - startTime;
          console.log(`取消收藏请求耗时: ${requestTime}ms`);
          
          if (response && response.code === 200) {
            this.$message.success('取消收藏成功');
            
            // 保存更新后的计算值
            const updatedFavoritesCount = this.article.favorites;
            
            // 更新本地存储的收藏状态
            this.updateLocalFavorites(false);
            
            // 获取最新的文章数据，确保收藏数量是最新的
            setTimeout(async () => {
              // 刷新前记录当前状态
              console.log('刷新前收藏数:', this.article.favorites);
              
              await this.refreshArticleData();
              
              // 刷新后记录新状态
              console.log('刷新后收藏数:', this.article.favorites);
              
              // 再次验证收藏数，避免服务器返回0导致数据不一致
              if (this.article.favorites === 0 && updatedFavoritesCount > 0) {
                console.warn('刷新后收藏数为0，恢复到计算值', updatedFavoritesCount);
                this.article.favorites = updatedFavoritesCount;
              } else if (this.article.favorites > updatedFavoritesCount + 1) {
                console.warn('收藏数异常增加，恢复到计算值', updatedFavoritesCount);
                this.article.favorites = updatedFavoritesCount;
              }
              
              // 再次验证收藏状态
              this.checkFavoriteStatus();
            }, 1000); // 增加延迟到1秒，确保后端数据已更新
          } else if (response && response.code === 401) {
            // 未授权，可能会话已过期
            this.$message.warning('登录已过期，请重新登录');
            this.$router.push({
              path: '/login',
              query: { redirect: this.$route.fullPath }
            });
            // 恢复原状态
            this.isFavorited = originalFavoriteStatus;
            this.article.favorites = originalFavoritesCount;
          } else if (response && response.code === 404) {
            // 记录未找到，可能已经取消了收藏
            this.$message.info('该文章未收藏');
            this.isFavorited = false;
          } else {
            this.$message.error(response?.message || '操作失败');
            // 恢复原状态
            this.isFavorited = originalFavoriteStatus;
            this.article.favorites = originalFavoritesCount;
          }
        } else {
          // 添加收藏
          console.log('发起添加收藏请求, 文章ID:', this.id, '用户ID:', this.userId, '文章标题:', this.article.title);
          
          // 乐观更新UI
          this.isFavorited = true;
          this.article.favorites++;
          
          // 记录操作开始时间
          const startTime = new Date().getTime();
          
          const response = await encyclopediaService.favoriteArticle(this.id, this.userId);
          console.log('添加收藏响应:', response);
          
          // 计算操作耗时
          const endTime = new Date().getTime();
          const requestTime = endTime - startTime;
          console.log(`添加收藏请求耗时: ${requestTime}ms`);
          
          if (response && response.code === 200) {
            this.$message.success('收藏成功');
            
            // 保存更新后的计算值
            const updatedFavoritesCount = this.article.favorites;
            
            // 更新本地存储的收藏状态
            this.updateLocalFavorites(true);
            
            // 提示用户可以在个人中心查看收藏
            this.$message({
              message: '您可以在个人中心-我的收藏中查看',
              type: 'info',
              duration: 3000
            });
            
            // 获取最新的文章数据，确保收藏数量是最新的
            setTimeout(async () => {
              // 刷新前记录当前状态
              console.log('刷新前收藏数:', this.article.favorites);
              
              await this.refreshArticleData();
              
              // 刷新后记录新状态
              console.log('刷新后收藏数:', this.article.favorites);
              
              // 再次验证收藏数，避免服务器返回0导致数据不一致
              if (this.article.favorites === 0 && updatedFavoritesCount > 0) {
                console.warn('刷新后收藏数为0，恢复到计算值', updatedFavoritesCount);
                this.article.favorites = updatedFavoritesCount;
              } else if (this.article.favorites < updatedFavoritesCount - 1) {
                console.warn('收藏数异常减少，恢复到计算值', updatedFavoritesCount);
                this.article.favorites = updatedFavoritesCount;
              }
              
              // 再次验证收藏状态
              this.checkFavoriteStatus();
            }, 1000); // 增加延迟到1秒，确保后端数据已更新
          } else if (response && response.code === 401) {
            // 未授权，可能会话已过期
            this.$message.warning('登录已过期，请重新登录');
            this.$router.push({
              path: '/login',
              query: { redirect: this.$route.fullPath }
            });
            // 恢复原状态
            this.isFavorited = originalFavoriteStatus;
            this.article.favorites = originalFavoritesCount;
          } else if (response && response.code === 409) {
            // 数据已存在冲突
            this.$message.info('您已经收藏过此文章');
            this.isFavorited = true;
          } else {
            this.$message.error(response?.message || '操作失败');
            // 恢复原状态
            this.isFavorited = originalFavoriteStatus;
            this.article.favorites = originalFavoritesCount;
          }
        }
      } catch (error) {
        console.error('收藏操作失败', error);
        // 记录更多错误细节
        if (error.response) {
          console.error('错误状态码:', error.response.status);
          console.error('错误响应数据:', error.response.data);
        } else if (error.request) {
          console.error('未收到响应，请求信息:', error.request);
        } else {
          console.error('请求配置错误:', error.message);
        }
        
        if (error.response && error.response.status === 401) {
          this.$message.warning('请先登录后再操作');
          this.$router.push({
            path: '/login',
            query: { redirect: this.$route.fullPath }
          });
        } else {
          this.$message.error('操作失败，请稍后重试');
        }
      }
    },
    
    // 更新本地存储的收藏状态，方便在个人中心显示
    updateLocalFavorites(isAdd) {
      try {
        if (!this.userId) {
          console.warn('用户未登录，无法更新本地收藏');
          return;
        }
        
        // 尝试从本地存储获取收藏列表
        const favoritesKey = `user_favorites_${this.userId}`;
        let favorites = localStorage.getItem(favoritesKey);
        let favoritesList = [];
        
        if (favorites) {
          try {
            favoritesList = JSON.parse(favorites);
          } catch (e) {
            console.error('解析收藏列表失败:', e);
            favoritesList = [];
          }
        }
        
        if (isAdd) {
          // 检查是否已存在
          if (!favoritesList.some(item => item.encyclopedia_id === this.article.id)) {
            // 添加当前文章到收藏列表，匹配数据库表结构
            favoritesList.push({
              id: Date.now(), // 临时ID，与数据库不同步
              user_id: this.userId,
              encyclopedia_id: this.article.id,
              title: this.article.title,
              favorite_time: new Date().toISOString(),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              deleted: 0,
              // 以下为前端显示需要的附加字段
              summary: this.article.summary,
              image: this.article.image,
              category: this.article.category
            });
            
            console.log('本地收藏列表添加成功，文章ID:', this.article.id);
          } else {
            console.log('文章已在本地收藏列表中，无需重复添加');
          }
        } else {
          // 从收藏列表中移除，使用encyclopedia_id匹配
          const beforeLength = favoritesList.length;
          favoritesList = favoritesList.filter(item => {
            // 同时检查id和encyclopedia_id字段，兼容新旧数据格式
            return item.encyclopedia_id !== this.article.id && item.id !== this.article.id;
          });
          
          const afterLength = favoritesList.length;
          if (afterLength < beforeLength) {
            console.log('本地收藏列表移除成功，文章ID:', this.article.id);
          } else {
            console.log('文章不在本地收藏列表中，无需移除');
          }
        }
        
        // 更新本地存储
        localStorage.setItem(favoritesKey, JSON.stringify(favoritesList));
        console.log(`本地收藏列表${isAdd ? '添加' : '移除'}操作完成，当前数量:`, favoritesList.length);
      } catch (e) {
        console.error('更新本地收藏失败:', e);
      }
    },
    
    async submitComment() {
      if (!this.commentContent.trim()) {
        return;
      }
      
      if (!this.checkUserLoggedIn()) return;
      
      try {
        console.log('提交评论:', this.id, this.userId, this.commentContent);
        
        // 创建评论对象
        const comment = {
          encyclopediaId: this.id,
          userId: this.userId,
          content: this.commentContent,
          userName: this.userInfo ? this.userInfo.username : '用户',
          parentId: 0
        };
        
        const response = await encyclopediaService.addComment(comment);
        console.log('评论提交响应:', response);
        
        if (response && response.code === 200) {
          this.$message.success('评论成功');
          this.commentContent = '';
          
          // 重置评论页码到第一页，确保能看到最新评论
          this.commentPage = 1;
          
          // 强制刷新评论列表
          this.comments = [];
          await this.fetchComments();
          
          // 如果服务器没有返回最新评论，手动添加到列表顶部
          if (this.comments.length === 0 || this.comments[0].content !== comment.content) {
            const newComment = {
              id: new Date().getTime(), // 临时ID
              content: comment.content,
              userName: comment.userName,
              avatar: this.userInfo?.avatar || this.defaultAvatar,
              createTime: new Date().toISOString(),
              likes: 0,
              isLiked: false
            };
            
            // 将新评论添加到顶部
            this.comments.unshift(newComment);
          }
          
          // 确保滚动到评论区
          this.$nextTick(() => {
            const commentsSection = document.querySelector('.comments-section');
            if (commentsSection) {
              commentsSection.scrollIntoView({ behavior: 'smooth' });
            }
          });
        } else {
          this.$message.error(response?.message || '评论失败');
        }
      } catch (error) {
        console.error('提交评论失败', error);
        this.$message.error('评论提交失败，请稍后重试');
      }
    },
    
    async likeComment(commentId) {
      if (!this.checkUserLoggedIn()) return;
      
      try {
        const comment = this.comments.find(c => c.id === commentId);
        
        if (comment && comment.isLiked) {
          // 取消点赞
          console.log('评论取消点赞, 评论ID:', commentId, '用户ID:', this.userId);
          const response = await encyclopediaService.unlikeComment(commentId, this.userId);
          console.log('评论取消点赞响应:', response);
          
          if (response && response.code === 200) {
            // 更新评论点赞状态
            comment.likes = Math.max(0, comment.likes - 1);
            comment.isLiked = false;
            this.$message.success('取消点赞成功');
          } else {
            this.$message.error(response?.message || '操作失败');
          }
        } else {
          // 点赞
          console.log('评论点赞, 评论ID:', commentId, '用户ID:', this.userId);
          const response = await encyclopediaService.likeComment(commentId, this.userId);
          console.log('评论点赞响应:', response);
          
          if (response && response.code === 200) {
            // 更新评论点赞状态
            if (comment) {
              comment.likes++;
              comment.isLiked = true;
            }
            this.$message.success('点赞成功');
          } else {
            this.$message.error(response?.message || '操作失败');
          }
        }
      } catch (error) {
        console.error('评论点赞操作失败', error);
        this.$message.error('操作失败，请稍后重试');
      }
    },
    
    replyToComment(_commentId) {
      // 评论回复功能，后续可以实现
      this.$message.info('评论回复功能开发中，敬请期待')
    },
    
    shareArticle() {
      // 分享功能，后续可以实现
      this.$message.info('分享功能开发中，敬请期待')
    },
    
    goToArticle(id) {
      this.$router.push({
        name: 'EncyclopediaDetail',
        params: { id }
      })
    },
    
    mapToViewModel(data) {
      return {
        id: data.id,
        title: data.title,
        summary: data.summary || '暂无简介',
        content: data.content || '',
        category: data.categoryName || '未分类',
        parentCategory: data.parentCategoryName,
        categoryId: data.categoryId,
        parentCategoryId: data.parentCategoryId,
        date: this.formatDate(data.createTime),
        views: data.views || 0,
        likes: data.likes || 0,
        favorites: data.favorites || 0,
        image: this.processImageUrl(data.coverImage) || this.defaultAvatar,
        tags: data.tags ? data.tags.split(',') : [],
        authorName: data.authorName,
        authorAvatar: this.processImageUrl(data.authorAvatar) || this.defaultAvatar,
        authorBio: data.authorBio
      }
    },
    
    // 处理可能404的图片URL
    processImageUrl(url) {
      // 使用统一的百科图片处理函数
      return this.processEncyclopediaImageUrl(url, this.encyclopedia?.title, this.encyclopedia?.id);
    },

    // 处理百科图片URL
    processEncyclopediaImageUrl(url, title, id) {
      return processEncyclopediaImageUrl(url, title, id);
    },
    
    formatContent(content) {
      if (!content) return '<p>暂无内容</p>'
      // 简单处理换行
      return content.replace(/\n/g, '<br>')
    },
    
    handleImageError(event) {
      event.target.src = this.defaultAvatar
      event.target.classList.add('image-error')
    },

    handleRelatedImageError(event, _item) {
      event.target.src = this.defaultAvatar
      event.target.classList.add('image-error')
    }
  }
}
</script>

<style lang="scss" scoped>
// 农业主题色彩变量
$primary-green: #2E7D32;
$secondary-green: #4CAF50;
$light-green: #81C784;
$accent-green: #A5D6A7;
$earth-brown: #8D6E63;
$warm-yellow: #FFC107;
$sky-blue: #03A9F4;
$text-dark: #2C3E50;
$text-light: #5D6D7E;
$background-light: #F8F9FA;
$white: #FFFFFF;

.encyclopedia-detail {
  min-height: 100vh;
  background: linear-gradient(135deg, #E8F5E8 0%, #F1F8E9 50%, #E0F2F1 100%);
  position: relative;
  padding: 0;
  margin-top: 60px;

  .page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 2;
  }
}

// 阅读进度指示器
.reading-progress {
  position: fixed;
  top: 60px;
  left: 0;
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  z-index: 1000;

  .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, $primary-green, $secondary-green, $warm-yellow);
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
  }
}

// 农业主题背景装饰
.agriculture-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;

  .wheat-pattern {
    position: absolute;
    top: 15%;
    right: 8%;
    width: 60px;
    height: 60px;
    opacity: 0.1;
    background: $warm-yellow;
    border-radius: 50%;
  }

  .leaf-decoration {
    position: absolute;
    bottom: 15%;
    left: 8%;
    width: 50px;
    height: 50px;
    opacity: 0.1;
    background: $secondary-green;
    border-radius: 50%;
  }
}

// 面包屑导航
.breadcrumb-wrapper {
  margin-bottom: 25px;

  .breadcrumb-container {
    background: $white;
    border-radius: 12px;
    padding: 15px 20px;
    box-shadow: 0 2px 12px rgba(46, 125, 50, 0.1);
    border-left: 4px solid $primary-green;
    display: flex;
    align-items: center;

    .breadcrumb-icon {
      font-size: 18px;
      margin-right: 10px;
    }

    ::v-deep .el-breadcrumb {
      .el-breadcrumb__item {
        .el-breadcrumb__inner {
          color: $text-light;
          font-weight: 500;
          display: flex;
          align-items: center;

          i {
            margin-right: 5px;
            color: $secondary-green;
          }

          &:hover {
            color: $primary-green;
          }
        }

        &:last-child .el-breadcrumb__inner {
          color: $text-dark;
          font-weight: 600;
        }
      }

      .el-breadcrumb__separator {
        color: $light-green;
        font-weight: bold;
      }
    }
  }
}

// 加载状态
.loading-state {
  background: $white;
  border-radius: 16px;
  padding: 60px 40px;
  box-shadow: 0 4px 20px rgba(46, 125, 50, 0.1);
  text-align: center;

  .loading-animation {
    .growing-plant {
      position: relative;
      width: 60px;
      height: 80px;
      margin: 0 auto 20px;

      .plant-stem {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 4px;
        height: 40px;
        background: $primary-green;
        border-radius: 2px;
        animation: grow 2s ease-in-out infinite;
      }

      .plant-leaves {
        position: absolute;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 30px;
        background: $secondary-green;
        border-radius: 50% 0;
        animation: sway 3s ease-in-out infinite;

        &::after {
          content: '';
          position: absolute;
          top: 5px;
          right: -15px;
          width: 20px;
          height: 20px;
          background: $light-green;
          border-radius: 50% 0;
          animation: sway 3s ease-in-out infinite reverse;
        }
      }
    }

    .loading-text {
      color: $text-dark;
      font-size: 16px;
      font-weight: 500;
      margin: 0;
    }
  }
}

@keyframes grow {
  0%, 100% { height: 40px; }
  50% { height: 50px; }
}

@keyframes sway {
  0%, 100% { transform: translateX(-50%) rotate(-5deg); }
  50% { transform: translateX(-50%) rotate(5deg); }
}

// 错误状态
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;

  .error-card {
    background: $white;
    border-radius: 16px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(46, 125, 50, 0.1);
    border: 2px solid $accent-green;

    .error-icon {
      font-size: 48px;
      margin-bottom: 20px;
      display: block;
    }

    h3 {
      color: $text-dark;
      margin-bottom: 15px;
      font-size: 20px;
    }

    p {
      color: $text-light;
      margin-bottom: 25px;
      line-height: 1.6;
    }
  }
}

// 文章容器
.article-container {
  display: flex;
  gap: 30px;
  align-items: flex-start;

  .content-wrapper {
    flex: 1;
    min-width: 0;
  }

  .sidebar {
    width: 320px;
    flex-shrink: 0;
  }
}

// 文章内容
.article-content {
  background: $white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(46, 125, 50, 0.1);
  overflow: hidden;
  margin-bottom: 25px;

  .article-header {
    background: linear-gradient(135deg, $primary-green 0%, $secondary-green 100%);
    color: $white;
    padding: 30px;

    .title-section {
      .article-title {
        font-size: 32px;
        font-weight: 700;
        line-height: 1.3;
        margin: 0 0 20px 0;
        display: flex;
        align-items: center;

        .title-icon {
          font-size: 28px;
          margin-right: 12px;
        }
      }

      .article-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;

        .meta-item {
          display: flex;
          align-items: center;
          background: rgba(255, 255, 255, 0.2);
          padding: 8px 15px;
          border-radius: 20px;
          font-size: 14px;
          font-weight: 500;

          .meta-icon {
            margin-right: 8px;
            font-size: 16px;
          }

          .category-tag {
            background: $white;
            color: $primary-green;
            border: none;
            font-weight: 600;
          }
        }
      }
    }
  }
}

// 文章图片
.article-image-container {
  margin: 25px 0;

  .image-wrapper {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

    .article-image {
      width: 100%;
      height: 300px;
      object-fit: cover;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.02);
      }
    }

    .image-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
      padding: 20px;

      .image-caption {
        color: $white;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
}

// 文章摘要
.article-summary {
  margin: 25px 30px;
  padding: 20px;
  background: linear-gradient(135deg, #F1F8E9 0%, #E8F5E8 100%);
  border-radius: 12px;
  border-left: 4px solid $secondary-green;

  .summary-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .summary-icon {
      font-size: 18px;
      margin-right: 8px;
    }

    .summary-label {
      font-weight: 600;
      color: $text-dark;
      font-size: 16px;
    }
  }

  .summary-content {
    color: $text-light;
    line-height: 1.6;
    font-size: 15px;
  }
}

// 文章正文
.article-body {
  padding: 30px;

  .content-container {
    color: $text-dark;
    line-height: 1.8;
    font-size: 16px;

    h1, h2, h3, h4, h5, h6 {
      color: $primary-green;
      margin: 25px 0 15px 0;
      font-weight: 600;

      &:first-child {
        margin-top: 0;
      }
    }

    h2 {
      font-size: 24px;
      border-bottom: 2px solid $accent-green;
      padding-bottom: 8px;
    }

    h3 {
      font-size: 20px;
      position: relative;
      padding-left: 15px;

      &::before {
        content: '🌱';
        position: absolute;
        left: 0;
        top: 0;
      }
    }

    p {
      margin-bottom: 16px;
      text-align: justify;
    }

    ul, ol {
      margin: 16px 0;
      padding-left: 25px;

      li {
        margin-bottom: 8px;
        line-height: 1.6;
      }
    }

    blockquote {
      margin: 20px 0;
      padding: 15px 20px;
      background: #F1F8E9;
      border-left: 4px solid $secondary-green;
      border-radius: 0 8px 8px 0;
      font-style: italic;
      color: $text-light;
    }

    code {
      background: #F5F5F5;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      color: $primary-green;
      font-size: 14px;
    }

    pre {
      background: #F8F8F8;
      padding: 15px;
      border-radius: 8px;
      overflow-x: auto;
      margin: 16px 0;
      border: 1px solid #E0E0E0;

      code {
        background: none;
        padding: 0;
        color: $text-dark;
      }
    }
  }
}

// 文章标签
.article-tags {
  padding: 0 30px 20px;

  .tags-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .tags-icon {
      font-size: 16px;
      margin-right: 8px;
    }

    .tags-label {
      font-weight: 600;
      color: $text-dark;
      font-size: 15px;
    }
  }

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .article-tag {
      background: linear-gradient(135deg, $accent-green, $light-green);
      color: $white;
      border: none;
      padding: 6px 15px;
      border-radius: 20px;
      font-size: 13px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
      }
    }
  }
}

// 文章操作按钮
.article-actions {
  padding: 25px 30px 30px;
  border-top: 1px solid #F0F0F0;

  .actions-container {
    display: flex;
    gap: 15px;
    justify-content: center;

    .action-button {
      display: flex;
      align-items: center;
      padding: 12px 20px;
      border-radius: 25px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: $white;
      border: 2px solid $accent-green;
      color: $text-dark;
      font-weight: 500;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
      }

      &.active {
        background: linear-gradient(135deg, $primary-green, $secondary-green);
        color: $white;
        border-color: $primary-green;
      }

      .button-icon {
        margin-right: 8px;

        .action-icon {
          font-size: 18px;
        }
      }

      .button-content {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .button-text {
          font-size: 14px;
          line-height: 1;
        }

        .button-count {
          font-size: 12px;
          opacity: 0.8;
          margin-top: 2px;
        }
      }

      &.like-button:hover {
        border-color: #FF6B6B;
        color: #FF6B6B;

        &.active {
          background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
          color: $white;
        }
      }

      &.favorite-button:hover {
        border-color: $warm-yellow;
        color: $warm-yellow;

        &.active {
          background: linear-gradient(135deg, $warm-yellow, #FFD54F);
          color: $white;
        }
      }

      &.share-button:hover {
        border-color: $sky-blue;
        color: $sky-blue;
      }
    }
  }
}

// 评论区
.comments-section {
  background: $white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(46, 125, 50, 0.1);
  overflow: hidden;

  .comments-header {
    background: linear-gradient(135deg, #F1F8E9 0%, #E8F5E8 100%);
    padding: 20px 30px;
    border-bottom: 1px solid #E8F5E8;

    .header-content {
      display: flex;
      align-items: center;

      .comments-icon {
        font-size: 20px;
        margin-right: 10px;
      }

      .comments-title {
        font-size: 20px;
        font-weight: 600;
        color: $text-dark;
        margin: 0 10px 0 0;
      }

      .comments-count {
        color: $text-light;
        font-size: 14px;
      }
    }
  }

  .comment-form-container {
    padding: 25px 30px;
    border-bottom: 1px solid #F0F0F0;

    .form-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .form-icon {
        font-size: 18px;
        margin-right: 8px;
      }

      .form-title {
        font-weight: 600;
        color: $text-dark;
        font-size: 16px;
      }
    }

    .comment-form {
      .comment-textarea {
        ::v-deep .el-textarea__inner {
          border: 2px solid #E8F5E8;
          border-radius: 12px;
          padding: 15px;
          font-size: 14px;
          line-height: 1.6;
          resize: vertical;
          transition: border-color 0.3s ease;

          &:focus {
            border-color: $secondary-green;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
          }

          &::placeholder {
            color: #B0BEC5;
          }
        }
      }

      .form-actions {
        margin-top: 15px;
        text-align: right;

        .submit-button {
          background: linear-gradient(135deg, $primary-green, $secondary-green);
          border: none;
          border-radius: 20px;
          padding: 10px 25px;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
          }

          i {
            margin-right: 5px;
          }
        }
      }
    }
  }

  .comments-list {
    padding: 20px 30px 30px;

    .comment-item {
      display: flex;
      margin-bottom: 25px;
      padding-bottom: 20px;
      border-bottom: 1px solid #F5F5F5;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
      }

      .comment-avatar {
        margin-right: 15px;

        .avatar-wrapper {
          position: relative;

          .user-avatar {
            border: 3px solid $accent-green;
          }

          .avatar-badge {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 20px;
            height: 20px;
            background: $white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }
      }

      .comment-content {
        flex: 1;

        .comment-header {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          flex-wrap: wrap;
          gap: 10px;

          .comment-author {
            font-weight: 600;
            color: $text-dark;
            font-size: 15px;
          }

          .author-badge {
            background: linear-gradient(135deg, $accent-green, $light-green);
            color: $white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
          }

          .comment-date {
            color: $text-light;
            font-size: 13px;
            margin-left: auto;
          }
        }

        .comment-body {
          color: $text-dark;
          line-height: 1.6;
          margin-bottom: 12px;
          font-size: 14px;
        }

        .comment-actions {
          display: flex;
          gap: 15px;

          .action-item {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 15px;
            transition: all 0.3s ease;
            font-size: 13px;
            color: $text-light;

            &:hover {
              background: #F5F5F5;
              color: $text-dark;
            }

            &.active {
              background: linear-gradient(135deg, $accent-green, $light-green);
              color: $white;
            }

            .action-icon {
              margin-right: 5px;
              font-size: 14px;
            }
          }
        }
      }
    }

    .no-comments {
      text-align: center;
      padding: 40px 20px;

      .empty-state {
        .empty-icon {
          font-size: 48px;
          margin-bottom: 15px;
          display: block;
        }

        .empty-title {
          color: $text-dark;
          margin-bottom: 8px;
          font-size: 18px;
        }

        .empty-text {
          color: $text-light;
          font-size: 14px;
          margin: 0;
        }
      }
    }

    .load-more {
      text-align: center;
      margin-top: 20px;

      .load-more-button {
        color: $secondary-green;
        font-weight: 500;

        &:hover {
          color: $primary-green;
        }

        i {
          margin-right: 5px;
        }
      }
    }
  }
}

// 侧边栏
.sidebar {
  display: flex;
  flex-direction: column;
  gap: 25px;

  .author-card,
  .related-articles {
    background: $white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(46, 125, 50, 0.1);
    overflow: hidden;

    .card-header {
      background: linear-gradient(135deg, #F1F8E9 0%, #E8F5E8 100%);
      padding: 20px;
      border-bottom: 1px solid #E8F5E8;
      display: flex;
      align-items: center;

      .card-icon {
        font-size: 18px;
        margin-right: 10px;
      }

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: $text-dark;
        margin: 0;
      }
    }
  }

  .author-card {
    .author-info {
      padding: 25px 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      .author-avatar-container {
        position: relative;
        margin-bottom: 15px;

        .author-avatar {
          border: 3px solid $accent-green;
        }

        .author-badge {
          position: absolute;
          bottom: -5px;
          right: -5px;
          width: 24px;
          height: 24px;
          background: linear-gradient(135deg, $warm-yellow, #FFD54F);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

          .badge-icon {
            font-size: 12px;
          }
        }
      }

      .author-details {
        .author-name {
          font-size: 18px;
          font-weight: 600;
          color: $text-dark;
          margin-bottom: 5px;
        }

        .author-title {
          color: $text-light;
          font-size: 14px;
          margin-bottom: 15px;
        }

        .author-stats {
          .stat-item {
            display: flex;
            align-items: center;
            justify-content: center;
            background: #F1F8E9;
            padding: 8px 15px;
            border-radius: 20px;

            .stat-icon {
              margin-right: 5px;
              font-size: 14px;
            }

            .stat-text {
              font-size: 13px;
              color: $text-dark;
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  .related-articles {
    .related-list {
      padding: 20px;

      .related-item {
        display: flex;
        margin-bottom: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        padding: 12px;
        border-radius: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        &:hover {
          background: #F8F9FA;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .related-image {
          width: 80px;
          height: 60px;
          margin-right: 15px;
          border-radius: 8px;
          overflow: hidden;
          position: relative;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
          }

          .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(46, 125, 50, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;

            .overlay-icon {
              color: $white;
              font-size: 18px;
            }
          }

          &:hover {
            img {
              transform: scale(1.1);
            }

            .image-overlay {
              opacity: 1;
            }
          }
        }

        .related-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .related-title {
            font-size: 14px;
            font-weight: 500;
            color: $text-dark;
            line-height: 1.4;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            transition: color 0.3s ease;
          }

          .related-meta {
            .meta-item {
              display: flex;
              align-items: center;
              color: $text-light;
              font-size: 12px;

              .meta-icon {
                margin-right: 4px;
                font-size: 14px;
              }
            }
          }
        }

        &:hover .related-title {
          color: $primary-green;
        }
      }

      .no-related {
        text-align: center;
        padding: 30px 20px;

        .empty-state {
          .empty-icon {
            font-size: 36px;
            margin-bottom: 10px;
            display: block;
          }

          .empty-text {
            color: $text-light;
            font-size: 14px;
            margin: 0;
          }
        }
      }
    }
  }

  .back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 100;

    .back-button {
      background: linear-gradient(135deg, $primary-green, $secondary-green);
      color: $white;
      padding: 12px 20px;
      border-radius: 25px;
      cursor: pointer;
      box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
      }

      .back-icon {
        margin-right: 8px;
        font-size: 16px;
      }

      .back-text {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .article-container {
    gap: 20px;

    .sidebar {
      width: 280px;
    }
  }
}

@media (max-width: 768px) {
  .encyclopedia-detail {
    .page-container {
      padding: 15px;
    }
  }

  .article-container {
    flex-direction: column;
    gap: 20px;

    .sidebar {
      width: 100%;
      order: 2;
    }

    .content-wrapper {
      order: 1;
    }
  }

  .article-content {
    .article-header {
      padding: 20px;

      .title-section {
        .article-title {
          font-size: 24px;

          .title-icon {
            font-size: 20px;
            margin-right: 8px;
          }
        }

        .article-meta {
          gap: 10px;

          .meta-item {
            padding: 6px 12px;
            font-size: 13px;
          }
        }
      }
    }
  }

  .article-body {
    padding: 20px;

    .content-container {
      font-size: 15px;

      h2 {
        font-size: 20px;
      }

      h3 {
        font-size: 18px;
      }
    }
  }

  .article-actions {
    padding: 20px;

    .actions-container {
      flex-direction: column;
      gap: 10px;

      .action-button {
        justify-content: center;
        padding: 15px 20px;

        .button-content {
          align-items: center;
        }
      }
    }
  }

  .comments-section {
    .comments-header,
    .comment-form-container,
    .comments-list {
      padding-left: 20px;
      padding-right: 20px;
    }
  }

  .sidebar {
    .back-to-top {
      bottom: 20px;
      right: 20px;

      .back-button {
        padding: 10px 15px;

        .back-text {
          display: none;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .breadcrumb-wrapper {
    .breadcrumb-container {
      padding: 12px 15px;

      .breadcrumb-icon {
        font-size: 16px;
      }

      ::v-deep .el-breadcrumb {
        .el-breadcrumb__item {
          .el-breadcrumb__inner {
            font-size: 13px;

            i {
              display: none;
            }
          }
        }
      }
    }
  }

  .article-content {
    .article-header {
      padding: 15px;

      .title-section {
        .article-title {
          font-size: 20px;
        }

        .article-meta {
          .meta-item {
            padding: 5px 10px;
            font-size: 12px;
          }
        }
      }
    }
  }

  .article-body {
    padding: 15px;
  }

  .article-tags {
    padding: 0 15px 15px;
  }

  .article-actions {
    padding: 15px;
  }
}
</style>