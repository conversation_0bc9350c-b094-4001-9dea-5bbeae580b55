<template>
  <div class="add-product">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">
        <i class="el-icon-plus"></i>
        发布新商品
      </h1>
      <p class="page-subtitle">填写商品信息，发布到您的店铺</p>
    </div>

    <!-- 商品表单 -->
    <div class="form-section">
      <el-form
        ref="productForm"
        :model="productForm"
        :rules="formRules"
        label-width="120px"
        class="product-form"
      >
        <!-- 基本信息 -->
        <div class="form-group">
          <h3 class="group-title">基本信息</h3>
          
          <el-form-item label="商品名称" prop="name">
            <el-input
              v-model="productForm.name"
              placeholder="请输入商品名称"
              maxlength="100"
              show-word-limit
            ></el-input>
          </el-form-item>
          
          <el-form-item label="商品分类" prop="categoryId">
            <el-select v-model="productForm.categoryId" placeholder="请选择商品分类">
              <el-option
                v-for="category in categories"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              ></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="商品品牌" prop="brand">
            <el-input
              v-model="productForm.brand"
              placeholder="请输入商品品牌"
            ></el-input>
          </el-form-item>
          
          <el-form-item label="产地" prop="origin">
            <el-input
              v-model="productForm.origin"
              placeholder="请输入产地"
            ></el-input>
          </el-form-item>
        </div>

        <!-- 商品图片 -->
        <div class="form-group">
          <h3 class="group-title">商品图片</h3>
          
          <el-form-item label="商品主图" prop="image">
            <el-upload
              class="image-uploader"
              action="/api/upload/image"
              :show-file-list="false"
              :on-success="handleImageSuccess"
              :before-upload="beforeImageUpload"
            >
              <img v-if="productForm.image" :src="productForm.image" class="uploaded-image">
              <i v-else class="el-icon-plus image-uploader-icon"></i>
            </el-upload>
            <div class="upload-tip">建议尺寸：800x800像素，支持JPG、PNG格式，大小不超过2MB</div>
          </el-form-item>
        </div>

        <!-- 价格库存 -->
        <div class="form-group">
          <h3 class="group-title">价格库存</h3>
          
          <el-form-item label="销售价格" prop="price">
            <el-input-number
              v-model="productForm.price"
              :precision="2"
              :step="0.1"
              :min="0"
              placeholder="0.00"
            ></el-input-number>
            <span class="unit">元</span>
          </el-form-item>
          
          <el-form-item label="原价" prop="originalPrice">
            <el-input-number
              v-model="productForm.originalPrice"
              :precision="2"
              :step="0.1"
              :min="0"
              placeholder="0.00"
            ></el-input-number>
            <span class="unit">元（选填）</span>
          </el-form-item>
          
          <el-form-item label="库存数量" prop="stock">
            <el-input-number
              v-model="productForm.stock"
              :min="0"
              placeholder="0"
            ></el-input-number>
            <span class="unit">件</span>
          </el-form-item>
          
          <el-form-item label="计量单位" prop="unit">
            <el-select v-model="productForm.unit" placeholder="请选择单位">
              <el-option label="斤" value="斤"></el-option>
              <el-option label="公斤" value="公斤"></el-option>
              <el-option label="箱" value="箱"></el-option>
              <el-option label="袋" value="袋"></el-option>
              <el-option label="个" value="个"></el-option>
            </el-select>
          </el-form-item>
        </div>

        <!-- 商品详情 -->
        <div class="form-group">
          <h3 class="group-title">商品详情</h3>
          
          <el-form-item label="商品描述" prop="description">
            <el-input
              type="textarea"
              v-model="productForm.description"
              :rows="6"
              placeholder="请详细描述商品特点、规格、产地等信息"
              maxlength="1000"
              show-word-limit
            ></el-input>
          </el-form-item>
          
          <el-form-item label="保质期" prop="shelfLife">
            <el-input
              v-model="productForm.shelfLife"
              placeholder="如：30天、6个月等"
            ></el-input>
          </el-form-item>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSaveDraft" :loading="saving">保存草稿</el-button>
          <el-button type="success" @click="handlePublish" :loading="publishing">发布商品</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AddProduct',
  data() {
    return {
      productForm: {
        name: '',
        categoryId: '',
        brand: '',
        origin: '',
        image: '',
        price: null,
        originalPrice: null,
        stock: null,
        unit: '',
        description: '',
        shelfLife: ''
      },
      formRules: {
        name: [
          { required: true, message: '请输入商品名称', trigger: 'blur' }
        ],
        categoryId: [
          { required: true, message: '请选择商品分类', trigger: 'change' }
        ],
        price: [
          { required: true, message: '请输入销售价格', trigger: 'blur' }
        ],
        stock: [
          { required: true, message: '请输入库存数量', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入商品描述', trigger: 'blur' }
        ]
      },
      categories: [
        { id: 1, name: '新鲜蔬菜' },
        { id: 2, name: '时令水果' },
        { id: 3, name: '粮油调料' },
        { id: 4, name: '肉禽蛋类' },
        { id: 5, name: '水产海鲜' }
      ],
      saving: false,
      publishing: false
    }
  },
  methods: {
    handleImageSuccess(res, file) {
      this.productForm.image = URL.createObjectURL(file.raw)
    },
    
    beforeImageUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },
    
    handleCancel() {
      this.$router.go(-1)
    },
    
    async handleSaveDraft() {
      try {
        this.saving = true
        // TODO: 调用保存草稿API
        this.$message.success('草稿保存成功')
      } catch (error) {
        this.$message.error('保存失败')
      } finally {
        this.saving = false
      }
    },
    
    async handlePublish() {
      try {
        await this.$refs.productForm.validate()
        this.publishing = true
        
        // TODO: 调用发布商品API
        this.$message.success('商品发布成功')
        this.$router.push('/seller/products')
      } catch (error) {
        if (error !== false) {
          this.$message.error('发布失败')
        }
      } finally {
        this.publishing = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.add-product {
  padding: 20px;
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .page-title {
    margin: 0 0 8px 0;
    font-size: 24px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
    
    i {
      color: #f093fb;
    }
  }
  
  .page-subtitle {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.form-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 32px;
  
  .group-title {
    margin: 0 0 20px 0;
    font-size: 16px;
    color: #333;
    border-left: 4px solid #f093fb;
    padding-left: 12px;
  }
}

.image-uploader {
  .uploaded-image {
    width: 148px;
    height: 148px;
    object-fit: cover;
    border-radius: 6px;
  }
  
  .image-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 148px;
    height: 148px;
    line-height: 148px;
    text-align: center;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    
    &:hover {
      border-color: #409EFF;
    }
  }
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
}

.unit {
  margin-left: 8px;
  color: #666;
  font-size: 14px;
}

.form-actions {
  margin-top: 40px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #eee;
  
  .el-button {
    margin: 0 8px;
    min-width: 100px;
  }
}
</style>
