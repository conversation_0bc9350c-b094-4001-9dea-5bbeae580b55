<template>
  <div class="product-management agriculture-theme">
    <!-- 页面头部 -->
    <div class="page-header agriculture-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <span class="title-icon">🌾</span>
            产品管理
          </h1>
          <p class="page-subtitle">管理您的农产品信息，优化产品展示效果</p>
        </div>
        <div class="header-stats">
          <el-row :gutter="16">
            <el-col :span="6">
              <StatsCard
                label="总产品数"
                :value="productStats.total"
                icon="el-icon-goods"
                icon-type="primary"
                variant="compact"
              />
            </el-col>
            <el-col :span="6">
              <StatsCard
                label="在售中"
                :value="productStats.active"
                icon="el-icon-check"
                icon-type="success"
                variant="compact"
              />
            </el-col>
            <el-col :span="6">
              <StatsCard
                label="已下架"
                :value="productStats.inactive"
                icon="el-icon-close"
                icon-type="secondary"
                variant="compact"
              />
            </el-col>
            <el-col :span="6">
              <StatsCard
                label="库存预警"
                :value="productStats.lowStock"
                icon="el-icon-warning"
                icon-type="warning"
                variant="compact"
              />
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="header-actions">
        <button class="action-btn primary" @click="handleAddProduct">
          <i class="el-icon-plus"></i>
          <span>添加产品</span>
        </button>
        <button class="action-btn secondary" @click="importProducts">
          <i class="el-icon-upload2"></i>
          <span>批量导入</span>
        </button>
        <button class="action-btn secondary" @click="exportProducts">
          <i class="el-icon-download"></i>
          <span>导出产品</span>
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <SearchFilter
        search-placeholder="搜索产品名称、SKU或描述"
        :filters="searchForm"
        :advanced-filters="advancedFilters"
        :quick-filters="quickFilters"
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
      />
    </div>

    <!-- 批量操作 -->
    <div class="batch-actions" v-if="selectedProducts.length > 0">
      <span class="selected-info">已选择 {{ selectedProducts.length }} 个产品</span>
      <el-button size="small" @click="handleBatchStatus(1)">批量上架</el-button>
      <el-button size="small" @click="handleBatchStatus(0)">批量下架</el-button>
      <el-button size="small" @click="batchUpdateCategory">批量分类</el-button>
      <el-button size="small" @click="batchUpdatePrice">批量调价</el-button>
      <el-button size="small" @click="batchExport">导出选中</el-button>
      <el-button size="small" type="danger" @click="handleBatchDelete">批量删除</el-button>
    </div>

    <!-- 产品列表 -->
    <div class="product-list">
      <el-table
        v-loading="loading"
        :data="products"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="产品信息" min-width="220">
          <template slot-scope="scope">
            <div class="product-info">
              <div class="product-image-container">
                <el-image
                  :src="scope.row.mainImage || '/images/default-product.png'"
                  :preview-src-list="[scope.row.mainImage || '/images/default-product.png']"
                  fit="cover"
                  style="width: 60px; height: 60px; border-radius: 4px;"
                  class="product-image"
                />
                <div v-if="scope.row.isHot" class="product-badge hot">
                  <i class="el-icon-star-on"></i>
                </div>
                <div v-if="scope.row.stock <= 10" class="product-badge warning">
                  <i class="el-icon-warning"></i>
                </div>
              </div>
              <div class="product-details">
                <div class="product-name">{{ scope.row.name }}</div>
                <div class="product-desc">{{ scope.row.description }}</div>
                <div class="product-tags">
                  <el-tag v-if="scope.row.hasTraceability" size="mini" type="success">已溯源</el-tag>
                  <el-tag v-if="scope.row.isRecommended" size="mini" type="warning">推荐</el-tag>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="价格" width="120">
          <template slot-scope="scope">
            <div class="price-info">
              <div class="current-price">¥{{ scope.row.price }}</div>
              <div v-if="scope.row.originalPrice && scope.row.originalPrice > scope.row.price" class="original-price">
                ¥{{ scope.row.originalPrice }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="库存" width="100">
          <template slot-scope="scope">
            <div class="stock-info">
              <span :class="{ 'low-stock': scope.row.stock <= 10, 'out-of-stock': scope.row.stock === 0 }">
                {{ scope.row.stock }}
              </span>
              <div class="stock-status">
                <span v-if="scope.row.stock === 0" class="status-text out">缺货</span>
                <span v-else-if="scope.row.stock <= 10" class="status-text low">库存不足</span>
                <span v-else class="status-text normal">充足</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="120">
          <template slot-scope="scope">
            <div class="status-info">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
              <el-switch
                v-model="scope.row.isActive"
                :disabled="scope.row.status === 'draft'"
                @change="toggleProductStatus(scope.row)"
                active-text=""
                inactive-text=""
                style="margin-top: 4px;"
              >
              </el-switch>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="溯源关联" width="120">
          <template slot-scope="scope">
            <div class="traceability-info">
              <el-tag v-if="scope.row.hasTraceability" type="success" size="small">
                <i class="el-icon-link"></i>
                已关联
              </el-tag>
              <el-button v-else size="mini" @click="linkTraceability(scope.row)">
                <i class="el-icon-plus"></i>
                关联
              </el-button>
              <div v-if="scope.row.traceabilityCode" class="traceability-code">
                {{ scope.row.traceabilityCode }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="150">
          <template slot-scope="scope">
            <div class="time-info">
              <div class="create-time">{{ formatDate(scope.row.createdAt) }}</div>
              <div v-if="scope.row.updateTime !== scope.row.createTime" class="update-time">
                更新：{{ formatDate(scope.row.updateTime) }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template slot-scope="scope">
            <div class="action-buttons">
              <el-button size="mini" @click="handleView(scope.row)">查看</el-button>
              <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button size="mini" type="success" @click="copyProduct(scope.row)">复制</el-button>
              <el-dropdown @command="(command) => handleAction(command, scope.row)">
                <el-button size="mini">
                  更多<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="analytics">
                    <i class="el-icon-data-analysis"></i>
                    数据分析
                  </el-dropdown-item>
                  <el-dropdown-item command="promotion">
                    <i class="el-icon-star-off"></i>
                    设为推荐
                  </el-dropdown-item>
                  <el-dropdown-item command="stock">
                    <i class="el-icon-box"></i>
                    库存管理
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>
                    <i class="el-icon-delete"></i>
                    删除产品
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      />
    </div>
  </div>
</template>

<script>
import { getSellerProducts, deleteProduct, batchUpdateProductStatus } from '@/api/seller'
import StatsCard from '@/components/common/StatsCard.vue'
import SearchFilter from '@/components/common/SearchFilter.vue'

export default {
  name: 'ProductManagement',
  components: {
    StatsCard,
    SearchFilter
  },
  data() {
    return {
      loading: false,
      products: [],
      selectedProducts: [],
      categories: [],
      searchForm: {
        keyword: '',
        status: '',
        categoryId: '',
        sortBy: 'created_at',
        minPrice: null,
        maxPrice: null,
        minStock: null,
        maxStock: null,
        hasTraceability: false
      },
      
      productStats: {
        total: 0,
        active: 0,
        inactive: 0,
        lowStock: 0
      },
      pagination: {
        current: 1,
        size: 20,
        total: 0
      },
      
      // SearchFilter组件配置
      searchFilters: [
        {
          key: 'keyword',
          type: 'input',
          label: '产品名称',
          placeholder: '请输入产品名称或关键词'
        },
        {
          key: 'status',
          type: 'select',
          label: '产品状态',
          placeholder: '请选择状态',
          options: [
            { label: '全部', value: '' },
            { label: '在售', value: '1' },
            { label: '下架', value: '0' },
            { label: '草稿', value: '2' }
          ]
        },
        {
          key: 'categoryId',
          type: 'select',
          label: '产品分类',
          placeholder: '请选择分类',
          options: [
            { label: '全部分类', value: '' },
            { label: '蔬菜', value: '1' },
            { label: '水果', value: '2' },
            { label: '粮食', value: '3' }
          ]
        },
        {
          key: 'sortBy',
          type: 'select',
          label: '排序方式',
          placeholder: '请选择排序',
          options: [
            { label: '创建时间', value: 'created_at' },
            { label: '更新时间', value: 'updated_at' },
            { label: '价格升序', value: 'price_asc' },
            { label: '价格降序', value: 'price_desc' },
            { label: '库存升序', value: 'stock_asc' },
            { label: '库存降序', value: 'stock_desc' }
          ]
        }
      ],
      
      advancedFilters: [
        {
          key: 'priceRange',
          type: 'number-range',
          label: '价格范围',
          placeholder: ['最低价格', '最高价格'],
          keys: ['minPrice', 'maxPrice']
        },
        {
          key: 'stockRange',
          type: 'number-range',
          label: '库存范围',
          placeholder: ['最低库存', '最高库存'],
          keys: ['minStock', 'maxStock']
        },
        {
          key: 'hasTraceability',
          type: 'checkbox',
          label: '溯源关联',
          options: [
            { label: '仅显示已关联溯源的产品', value: true }
          ]
        }
      ],
      
      quickFilters: [
        { label: '热销产品', value: 'hot', count: 0 },
        { label: '库存预警', value: 'low_stock', count: 0 },
        { label: '新品上架', value: 'new', count: 0 },
        { label: '推荐产品', value: 'recommended', count: 0 }
      ]
    }
  },
  created() {
    this.loadProducts()
    this.loadCategories()
    this.calculateProductStats()
  },
  methods: {
    async loadProducts() {
      this.loading = true
      try {
        const params = {
          current: this.pagination.current,
          size: this.pagination.size,
          ...this.searchForm
        }
        
        const response = await getSellerProducts(params)
        if (response.code === 0 || response.code === 200) {
          this.products = response.data.records || []
          this.pagination.total = response.data.total || 0
          this.calculateProductStats()
        } else {
          this.$message.error(response.message || '获取产品列表失败')
        }
      } catch (error) {
        console.error('加载产品列表失败:', error)
        this.$message.error('加载产品列表失败')
      } finally {
        this.loading = false
      }
    },
    
    calculateProductStats() {
      this.productStats = {
        total: this.products.length,
        active: this.products.filter(p => p.status === 1).length,
        inactive: this.products.filter(p => p.status === 0).length,
        lowStock: this.products.filter(p => p.stock <= 10).length
      }
    },

    async loadCategories() {
      // TODO: 加载分类数据
      this.categories = [
        { id: 1, name: '蔬菜' },
        { id: 2, name: '水果' },
        { id: 3, name: '粮食' }
      ]
    },

    handleSearch(searchData) {
      // 更新搜索表单数据
      Object.assign(this.searchForm, searchData)
      this.pagination.current = 1
      this.loadProducts()
    },

    handleReset() {
      this.searchForm = {
        keyword: '',
        status: '',
        categoryId: '',
        sortBy: 'created_at',
        minPrice: null,
        maxPrice: null,
        minStock: null,
        maxStock: null,
        hasTraceability: false
      }
      this.pagination.current = 1
      this.loadProducts()
    },
    
    handleQuickFilter(filterValue) {
      // 处理快速筛选
      switch (filterValue) {
        case 'hot':
          this.searchForm.sortBy = 'sales_desc'
          break
        case 'low_stock':
          this.searchForm.maxStock = 10
          break
        case 'new':
          this.searchForm.sortBy = 'created_at'
          break
        case 'recommended':
          this.searchForm.isRecommended = true
          break
      }
      this.pagination.current = 1
      this.loadProducts()
    },

    handleSelectionChange(selection) {
      this.selectedProducts = selection
    },

    async handleBatchStatus(status) {
      if (this.selectedProducts.length === 0) {
        this.$message.warning('请选择要操作的产品')
        return
      }

      try {
        const ids = this.selectedProducts.map(item => item.id)
        const response = await batchUpdateProductStatus(ids, status)
        
        if (response.code === 0 || response.code === 200) {
          this.$message.success('批量操作成功')
          this.loadProducts()
        } else {
          this.$message.error(response.message || '批量操作失败')
        }
      } catch (error) {
        console.error('批量操作失败:', error)
        this.$message.error('批量操作失败')
      }
    },

    async handleBatchDelete() {
      if (this.selectedProducts.length === 0) {
        this.$message.warning('请选择要删除的产品')
        return
      }

      try {
        await this.$confirm('确定要删除选中的产品吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // TODO: 实现批量删除API
        this.$message.success('批量删除成功')
        this.loadProducts()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量删除失败:', error)
          this.$message.error('批量删除失败')
        }
      }
    },

    handleAddProduct() {
      this.$router.push('/seller/products/add')
    },
    
    importProducts() {
      this.$message.info('批量导入功能开发中...')
    },
    
    exportProducts() {
      this.$message.success('产品导出功能开发中...')
    },
    
    batchUpdateCategory() {
      this.$message.info('批量分类功能开发中...')
    },
    
    batchUpdatePrice() {
      this.$message.info('批量调价功能开发中...')
    },
    
    batchExport() {
      this.$message.success('导出选中产品功能开发中...')
    },
    
    toggleProductStatus(product) {
      const newStatus = product.isActive ? 1 : 0
      product.status = newStatus
      this.$message.success(`产品已${product.isActive ? '上架' : '下架'}`)
      this.calculateProductStats()
    },
    
    copyProduct(product) {
      this.$message.success(`正在复制产品：${product.name}`)
      // 实际项目中这里应该跳转到添加页面并预填数据
    },

    handleView(row) {
      this.$router.push(`/seller/products/${row.id}`)
    },

    handleEdit(row) {
      this.$router.push(`/seller/products/edit/${row.id}`)
    },

    async handleAction(command, row) {
      switch (command) {
        case 'analytics':
          this.$message.info(`查看 ${row.name} 的数据分析`)
          break
        case 'promotion':
          row.isRecommended = !row.isRecommended
          this.$message.success(`${row.isRecommended ? '设为' : '取消'}推荐成功`)
          break
        case 'stock':
          this.$message.info(`管理 ${row.name} 的库存`)
          break
        case 'delete':
          this.deleteProduct(row)
          break
      }
    },

    async updateProductStatus(id, status) {
      try {
        const response = await batchUpdateProductStatus([id], status)
        if (response.code === 0 || response.code === 200) {
          this.$message.success('状态更新成功')
          this.loadProducts()
        } else {
          this.$message.error(response.message || '状态更新失败')
        }
      } catch (error) {
        console.error('状态更新失败:', error)
        this.$message.error('状态更新失败')
      }
    },

    async deleteProduct(row) {
      try {
        await this.$confirm(`确定要删除产品"${row.name}"吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await deleteProduct(row.id)
        if (response.code === 0 || response.code === 200) {
          this.$message.success('删除成功')
          this.loadProducts()
        } else {
          this.$message.error(response.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      }
    },

    linkTraceability(product) {
      this.$message.info(`为产品 ${product.name} 关联溯源信息`)
      // 实际项目中这里应该打开溯源关联对话框
    },

    handleSizeChange(val) {
      this.pagination.size = val
      this.pagination.current = 1
      this.loadProducts()
    },

    handleCurrentChange(val) {
      this.pagination.current = val
      this.loadProducts()
    },

    getStatusType(status) {
      const statusMap = {
        1: 'success',
        0: 'danger',
        2: 'warning'
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        1: '在售',
        0: '下架',
        2: '草稿'
      }
      return statusMap[status] || '未知'
    },

    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';
@import '@/styles/mixins.scss';

.product-management {
  &.agriculture-theme {
    background: $agriculture-bg-gradient;
    min-height: calc(100vh - 64px);
    padding: $spacing-lg;
    @include fade-in;
  }
}

.page-header {
  &.agriculture-header {
    @include agriculture-card;
    padding: $spacing-xl;
    margin-bottom: $spacing-xl;
    border: none;
    background: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .header-content {
      flex: 1;

      .title-section {
        margin-bottom: $spacing-lg;

        .page-title {
          @include flex-center-vertical;
          gap: $spacing-sm;
          margin: 0 0 $spacing-sm 0;
          font-size: $font-size-xxxl;
          font-weight: $font-weight-bold;
          color: white;

          .title-icon {
            font-size: $font-size-xl;
            animation: bounce 2s infinite;
          }
        }

        .page-subtitle {
          margin: 0;
          color: rgba(255, 255, 255, 0.9);
          font-size: $font-size-md;
        }
      }

      .header-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: $spacing-md;

        .stat-card {
          @include flex-center-vertical;
          gap: $spacing-md;
          padding: $spacing-md;
          background: rgba(255, 255, 255, 0.15);
          border-radius: $border-radius-medium;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          transition: all $transition-medium $transition-timing-function;

          &:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
          }

          .stat-icon {
            @include agriculture-icon(40px);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 18px;

            &.total {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            &.active {
              background: linear-gradient(135deg, $success-color 0%, lighten($success-color, 10%) 100%);
            }

            &.inactive {
              background: linear-gradient(135deg, #909399 0%, lighten(#909399, 10%) 100%);
            }

            &.warning {
              background: linear-gradient(135deg, $warning-color 0%, lighten($warning-color, 10%) 100%);
            }
          }

          .stat-content {
            .stat-value {
              font-size: $font-size-xl;
              font-weight: $font-weight-bold;
              color: white;
              line-height: $line-height-tight;
            }

            .stat-label {
              font-size: $font-size-sm;
              color: rgba(255, 255, 255, 0.8);
              margin-top: 2px;
            }
          }
        }
      }
    }

    .header-actions {
      display: flex;
      gap: $spacing-md;
      align-items: flex-start;
      flex-wrap: wrap;

      .action-btn {
        @include flex-center-vertical;
        gap: $spacing-sm;
        padding: $spacing-sm $spacing-md;
        border: none;
        border-radius: $border-radius-medium;
        font-size: $font-size-sm;
        font-weight: $font-weight-medium;
        cursor: pointer;
        transition: all $transition-medium $transition-timing-function;

        &.primary {
          background: rgba(255, 255, 255, 0.2);
          color: white;
          border: 1px solid rgba(255, 255, 255, 0.3);

          &:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
          }
        }

        &.secondary {
          background: rgba(255, 255, 255, 0.1);
          color: white;
          border: 1px solid rgba(255, 255, 255, 0.2);

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
          }
        }
      }
    }
  }
}

// 搜索区域样式
.search-section {
  &.agriculture-search {
    margin-bottom: $spacing-xl;

    .search-card {
      @include agriculture-card;
      padding: $spacing-lg;
      border: none;

      .search-header {
        margin-bottom: $spacing-lg;

        .search-title {
          @include flex-center-vertical;
          gap: $spacing-sm;
          margin: 0;
          font-size: $font-size-lg;
          font-weight: $font-weight-semibold;
          color: $text-primary;

          i {
            color: $primary-color;
          }
        }
      }

      .search-form {
        .form-row {
          display: grid;
          grid-template-columns: 2fr 1fr 1fr 1fr auto;
          gap: $spacing-md;
          align-items: end;

          .form-item-keyword {
            .agriculture-input {
              width: 100%;
            }
          }

          .agriculture-select {
            width: 100%;
          }

          .form-actions {
            display: flex;
            gap: $spacing-sm;

            .search-btn {
              @include agriculture-button;
              @include flex-center-vertical;
              gap: $spacing-xs;
              padding: $spacing-sm $spacing-md;
              font-size: $font-size-sm;
              white-space: nowrap;

              &.primary {
                @include agriculture-button('primary');
              }

              &.secondary {
                @include agriculture-button('secondary');
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@include mobile {
  .product-management.agriculture-theme {
    padding: $spacing-md;

    .page-header.agriculture-header {
      padding: $spacing-lg;
      flex-direction: column;
      gap: $spacing-lg;

      .header-content {
        .title-section .page-title {
          font-size: $font-size-xl;
        }

        .header-stats {
          grid-template-columns: repeat(2, 1fr);
          gap: $spacing-sm;

          .stat-card {
            padding: $spacing-sm;

            .stat-icon {
              @include agriculture-icon(32px);
            }

            .stat-content .stat-value {
              font-size: $font-size-md;
            }
          }
        }
      }

      .header-actions {
        width: 100%;
        justify-content: center;

        .action-btn {
          flex: 1;
          min-width: 100px;
        }
      }
    }

    .search-section.agriculture-search .search-card .search-form .form-row {
      grid-template-columns: 1fr;
      gap: $spacing-md;

      .form-actions {
        justify-content: center;

        .search-btn {
          flex: 1;
        }
      }
    }
  }
}

@include tablet {
  .search-section.agriculture-search .search-card .search-form .form-row {
    grid-template-columns: 1fr 1fr auto;
  }
}

// 动画关键帧
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}
</style>
