<template>
  <div class="traceability-record-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <el-button icon="el-icon-arrow-left" @click="goBack">返回列表</el-button>
        <h1 class="page-title">
          <i class="el-icon-document-checked"></i>
          溯源记录详情
        </h1>
      </div>
      <div class="header-actions" v-if="recordDetail">
        <el-tag :type="getStatusType(recordDetail.status)" size="medium">
          {{ getStatusText(recordDetail.status) }}
        </el-tag>
        <el-button type="primary" icon="el-icon-edit" @click="handleEdit">编辑</el-button>
      </div>
    </div>

    <div v-loading="loading" class="detail-content">
      <el-row :gutter="20" v-if="recordDetail">
        <!-- 基本信息 -->
        <el-col :span="24">
          <el-card shadow="never" class="info-card">
            <div slot="header" class="card-header">
              <span class="card-title">基本信息</span>
            </div>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>溯源码：</label>
                  <span class="trace-code">{{ recordDetail.traceCode }}</span>
                  <el-button size="mini" type="text" @click="copyTraceCode">复制</el-button>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>产品名称：</label>
                  <span>{{ recordDetail.productName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>农场名称：</label>
                  <span>{{ recordDetail.farmName || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>生产者名称：</label>
                  <span>{{ recordDetail.producerName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>生产批次：</label>
                  <span>{{ recordDetail.batchNumber || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>产品规格：</label>
                  <span>{{ recordDetail.specification || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>质量等级：</label>
                  <span>{{ getQualityGradeText(recordDetail.qualityGrade) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>记录状态：</label>
                  <el-tag :type="getStatusType(recordDetail.status)" size="small">
                    {{ getStatusText(recordDetail.status) }}
                  </el-tag>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>二维码URL：</label>
                  <span>{{ recordDetail.qrCodeUrl ? '已生成' : '未生成' }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>

        <!-- 日期信息 -->
        <el-col :span="24">
          <el-card shadow="never" class="info-card">
            <div slot="header" class="card-header">
              <span class="card-title">日期信息</span>
            </div>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>创建日期：</label>
                  <span>{{ formatDate(recordDetail.creationDate) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>收获日期：</label>
                  <span>{{ formatDate(recordDetail.harvestDate) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>包装日期：</label>
                  <span>{{ formatDate(recordDetail.packagingDate) }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>记录创建时间：</label>
                  <span>{{ formatDateTime(recordDetail.createdAt) }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>最后更新时间：</label>
                  <span>{{ formatDateTime(recordDetail.updatedAt) }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>

        <!-- 生产信息 -->
        <el-col :span="24">
          <el-card shadow="never" class="info-card">
            <div slot="header" class="card-header">
              <span class="card-title">生产信息</span>
            </div>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <label>生产地址：</label>
                  <span>{{ recordDetail.productionLocation || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>生产环境：</label>
                  <span>{{ getEnvironmentText(recordDetail.productionEnvironment) }}</span>
                </div>
              </el-col>
            </el-row>
            <div class="info-item">
              <label>种植/养殖方式：</label>
              <p class="description">{{ recordDetail.cultivationMethod || '-' }}</p>
            </div>
            <div class="info-item">
              <label>使用农药/饲料：</label>
              <p class="description">{{ recordDetail.pesticidesUsed || '-' }}</p>
            </div>
          </el-card>
        </el-col>

        <!-- 质量信息 -->
        <el-col :span="24">
          <el-card shadow="never" class="info-card">
            <div slot="header" class="card-header">
              <span class="card-title">质量信息</span>
            </div>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <label>质量等级：</label>
                  <span>{{ getGradeText(recordDetail.qualityGrade) }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>检测机构：</label>
                  <span>{{ recordDetail.testingOrganization || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <div class="info-item">
              <label>质量检测报告：</label>
              <p class="description">{{ recordDetail.qualityReport || '-' }}</p>
            </div>
          </el-card>
        </el-col>

        <!-- 相关附件 -->
        <el-col :span="24" v-if="recordDetail.images && recordDetail.images.length > 0">
          <el-card shadow="never" class="info-card">
            <div slot="header" class="card-header">
              <span class="card-title">产品图片</span>
            </div>
            <div class="image-gallery">
              <el-image
                v-for="(image, index) in recordDetail.images"
                :key="index"
                :src="image.url"
                :preview-src-list="recordDetail.images.map(img => img.url)"
                class="gallery-image"
                fit="cover"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 证明文档 -->
        <el-col :span="24" v-if="recordDetail.documents && recordDetail.documents.length > 0">
          <el-card shadow="never" class="info-card">
            <div slot="header" class="card-header">
              <span class="card-title">证明文档</span>
            </div>
            <div class="document-list">
              <div
                v-for="(doc, index) in recordDetail.documents"
                :key="index"
                class="document-item"
              >
                <i class="el-icon-document"></i>
                <span class="document-name">{{ doc.name }}</span>
                <el-button size="mini" type="text" @click="downloadDocument(doc)">下载</el-button>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 备注信息 -->
        <el-col :span="24" v-if="recordDetail.remarks">
          <el-card shadow="never" class="info-card">
            <div slot="header" class="card-header">
              <span class="card-title">备注信息</span>
            </div>
            <p class="description">{{ recordDetail.remarks }}</p>
          </el-card>
        </el-col>

        <!-- 二维码展示 -->
        <el-col :span="24">
          <el-card shadow="never" class="info-card">
            <div slot="header" class="card-header">
              <span class="card-title">溯源二维码</span>
            </div>
            <div class="qrcode-section">
              <div class="qrcode-container">
                <img
                  v-if="qrCodeUrl"
                  :src="qrCodeUrl"
                  alt="溯源二维码"
                  class="qrcode-image"
                />
                <div v-else class="qrcode-placeholder">
                  <i class="el-icon-picture-outline"></i>
                  <p>二维码生成中...</p>
                </div>
              </div>
              <div class="qrcode-actions">
                <el-button @click="downloadQRCode">下载二维码</el-button>
                <el-button @click="printQRCode">打印二维码</el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 编辑对话框 -->
    <el-dialog
      title="编辑溯源记录"
      :visible.sync="editDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      :modal="true"
      :append-to-body="true"
      :z-index="3000"
      @close="editDialogVisible = false"
    >
      <traceability-record-form
        v-if="editDialogVisible"
        :record="recordDetail"
        :is-edit="true"
        @submit="handleEditSubmit"
        @cancel="handleEditCancel"
      />
    </el-dialog>
  </div>
</template>

<script>
import TraceabilityRecordForm from './components/TraceabilityRecordForm.vue'
import { getTraceabilityRecordById } from '@/api/traceability'

export default {
  name: 'TraceabilityRecordDetail',
  components: {
    TraceabilityRecordForm
  },
  data() {
    return {
      loading: false,
      recordDetail: null,
      editDialogVisible: false,
      qrCodeUrl: ''
    }
  },
  mounted() {
    this.loadRecordDetail()
  },
  methods: {
    // 加载记录详情
    async loadRecordDetail() {
      this.loading = true
      try {
        const id = this.$route.params.id
        const response = await getTraceabilityRecordById(id)
        if (response.success) {
          this.recordDetail = response.data
          this.generateQRCodeUrl()
        } else {
          this.$message.error('记录不存在')
          this.goBack()
        }
      } catch (error) {
        this.$message.error('加载记录详情失败')
        this.goBack()
      } finally {
        this.loading = false
      }
    },

    // 生成二维码URL
    generateQRCodeUrl() {
      if (this.recordDetail && this.recordDetail.traceCode) {
        this.qrCodeUrl = `/uploads/qrcodes/qr_${this.recordDetail.traceCode}.png`
      }
    },

    // 返回列表
    goBack() {
      this.$router.push('/seller/traceability/records')
    },

    // 编辑记录
    handleEdit() {
      this.editDialogVisible = true
    },

    // 编辑提交
    handleEditSubmit() {
      this.editDialogVisible = false
      this.loadRecordDetail()
    },

    // 编辑取消
    handleEditCancel() {
      this.editDialogVisible = false
    },

    // 复制溯源码
    copyTraceCode() {
      const traceCode = this.recordDetail.traceCode
      if (navigator.clipboard) {
        navigator.clipboard.writeText(traceCode).then(() => {
          this.$message.success('溯源码已复制到剪贴板')
        })
      } else {
        // 兼容旧浏览器
        const textArea = document.createElement('textarea')
        textArea.value = traceCode
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('溯源码已复制到剪贴板')
      }
    },

    // 下载文档
    downloadDocument(doc) {
      const link = document.createElement('a')
      link.href = doc.url
      link.download = doc.name
      link.click()
    },

    // 下载二维码
    downloadQRCode() {
      if (this.qrCodeUrl) {
        const link = document.createElement('a')
        link.href = this.qrCodeUrl
        link.download = `qr_${this.recordDetail.traceCode}.png`
        link.click()
      }
    },

    // 打印二维码
    printQRCode() {
      if (this.qrCodeUrl) {
        const printWindow = window.open('', '_blank')
        printWindow.document.write(`
          <html>
            <head>
              <title>溯源二维码 - ${this.recordDetail.traceCode}</title>
              <style>
                body { text-align: center; font-family: Arial, sans-serif; }
                .qrcode { margin: 20px; }
                .info { margin: 10px 0; }
              </style>
            </head>
            <body>
              <div class="info">
                <h2>${this.recordDetail.productName}</h2>
                <p>溯源码: ${this.recordDetail.traceCode}</p>
              </div>
              <div class="qrcode">
                <img src="${this.qrCodeUrl}" alt="溯源二维码" />
              </div>
            </body>
          </html>
        `)
        printWindow.document.close()
        printWindow.print()
      }
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'info',      // 草稿
        1: 'warning',   // 待审核
        2: 'success'    // 已发布
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '草稿',
        1: '待审核',
        2: '已发布'
      }
      return statusMap[status] || '未知'
    },

    // 获取质量等级文本
    getQualityGradeText(grade) {
      const gradeMap = {
        'premium': '特级',
        'first': '一级',
        'second': '二级',
        'qualified': '合格'
      }
      return gradeMap[grade] || grade || '-'
    },

    // 获取环境文本
    getEnvironmentText(environment) {
      const environmentMap = {
        greenhouse: '温室大棚',
        outdoor: '露天种植',
        hydroponic: '水培种植',
        organic: '有机种植'
      }
      return environmentMap[environment] || environment || '-'
    },

    // 获取等级文本
    getGradeText(grade) {
      const gradeMap = {
        premium: '特级',
        first: '一级',
        second: '二级',
        qualified: '合格'
      }
      return gradeMap[grade] || grade || '-'
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleDateString()
    },

    // 格式化日期时间
    formatDateTime(datetime) {
      if (!datetime) return '-'
      return new Date(datetime).toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
.traceability-record-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-content {
    display: flex;
    align-items: center;

    .page-title {
      margin: 0 0 0 15px;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      
      i {
        margin-right: 8px;
        color: #409eff;
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}

.detail-content {
  .info-card {
    margin-bottom: 20px;
    border-radius: 8px;

    .card-header {
      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .info-item {
      margin-bottom: 15px;

      label {
        font-weight: 600;
        color: #606266;
        margin-right: 8px;
      }

      .trace-code {
        font-family: monospace;
        background: #f5f7fa;
        padding: 2px 6px;
        border-radius: 4px;
        margin-right: 8px;
      }

      .description {
        margin: 5px 0 0 0;
        line-height: 1.6;
        color: #606266;
      }
    }
  }

  .image-gallery {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .gallery-image {
      width: 120px;
      height: 120px;
      border-radius: 6px;
      cursor: pointer;
    }
  }

  .document-list {
    .document-item {
      display: flex;
      align-items: center;
      padding: 10px;
      border: 1px solid #ebeef5;
      border-radius: 6px;
      margin-bottom: 10px;

      i {
        margin-right: 10px;
        color: #409eff;
        font-size: 18px;
      }

      .document-name {
        flex: 1;
        color: #606266;
      }
    }
  }

  .qrcode-section {
    display: flex;
    align-items: center;
    gap: 30px;

    .qrcode-container {
      .qrcode-image {
        width: 200px;
        height: 200px;
        border: 1px solid #ebeef5;
        border-radius: 8px;
      }

      .qrcode-placeholder {
        width: 200px;
        height: 200px;
        border: 2px dashed #dcdfe6;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #909399;

        i {
          font-size: 48px;
          margin-bottom: 10px;
        }
      }
    }

    .qrcode-actions {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .traceability-record-detail {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;

    .header-content {
      margin-bottom: 15px;
    }

    .header-actions {
      justify-content: flex-end;
    }
  }

  .qrcode-section {
    flex-direction: column;
    align-items: center;
  }
}
</style>
