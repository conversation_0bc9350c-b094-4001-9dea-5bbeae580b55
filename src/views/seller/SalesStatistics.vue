<template>
  <div class="sales-statistics">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">销售统计</h1>
        <p class="page-subtitle">深度分析销售数据，洞察业务趋势</p>
      </div>
      <div class="header-actions">
        <el-button icon="el-icon-download" @click="handleExportSalesReport">
          导出销售报表
        </el-button>
        <el-button icon="el-icon-refresh" @click="refreshSalesData">
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 时间和筛选器 -->
    <div class="filters-section">
      <el-card class="filter-card">
        <div class="filter-content">
          <div class="filter-row">
            <div class="filter-item">
              <label>时间范围：</label>
              <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
                <el-radio-button label="today">今日</el-radio-button>
                <el-radio-button label="week">本周</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="quarter">本季度</el-radio-button>
                <el-radio-button label="year">本年</el-radio-button>
                <el-radio-button label="custom">自定义</el-radio-button>
              </el-radio-group>
            </div>
            <div v-if="timeRange === 'custom'" class="filter-item">
              <el-date-picker
                v-model="customDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="handleCustomDateChange"
              />
            </div>
          </div>
          <div class="filter-row">
            <div class="filter-item">
              <label>产品分类：</label>
              <el-select v-model="selectedCategory" placeholder="全部分类" clearable @change="handleCategoryChange">
                <el-option label="全部分类" value=""></el-option>
                <el-option v-for="category in categories" :key="category.id" :label="category.name" :value="category.id"></el-option>
              </el-select>
            </div>
            <div class="filter-item">
              <label>销售渠道：</label>
              <el-select v-model="selectedChannel" placeholder="全部渠道" clearable @change="handleChannelChange">
                <el-option label="全部渠道" value=""></el-option>
                <el-option label="线上商城" value="online"></el-option>
                <el-option label="线下门店" value="offline"></el-option>
                <el-option label="批发渠道" value="wholesale"></el-option>
              </el-select>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 销售概览卡片 -->
    <div class="sales-overview">
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="overview-card total-sales">
            <div class="card-content">
              <div class="card-icon">
                <i class="el-icon-money"></i>
              </div>
              <div class="card-info">
                <div class="card-value">¥{{ formatNumber(salesOverview.totalSales) }}</div>
                <div class="card-label">总销售额</div>
                <div class="card-trend" :class="{ positive: salesOverview.salesTrend > 0 }">
                  <i :class="salesOverview.salesTrend > 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                  {{ Math.abs(salesOverview.salesTrend) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="overview-card avg-order">
            <div class="card-content">
              <div class="card-icon">
                <i class="el-icon-shopping-cart-2"></i>
              </div>
              <div class="card-info">
                <div class="card-value">¥{{ formatNumber(salesOverview.avgOrderValue) }}</div>
                <div class="card-label">平均订单价值</div>
                <div class="card-trend" :class="{ positive: salesOverview.avgOrderTrend > 0 }">
                  <i :class="salesOverview.avgOrderTrend > 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                  {{ Math.abs(salesOverview.avgOrderTrend) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="overview-card conversion-rate">
            <div class="card-content">
              <div class="card-icon">
                <i class="el-icon-pie-chart"></i>
              </div>
              <div class="card-info">
                <div class="card-value">{{ salesOverview.conversionRate }}%</div>
                <div class="card-label">转化率</div>
                <div class="card-trend" :class="{ positive: salesOverview.conversionTrend > 0 }">
                  <i :class="salesOverview.conversionTrend > 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                  {{ Math.abs(salesOverview.conversionTrend) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
          <el-card class="overview-card profit-margin">
            <div class="card-content">
              <div class="card-icon">
                <i class="el-icon-trophy"></i>
              </div>
              <div class="card-info">
                <div class="card-value">{{ salesOverview.profitMargin }}%</div>
                <div class="card-label">利润率</div>
                <div class="card-trend" :class="{ positive: salesOverview.profitTrend > 0 }">
                  <i :class="salesOverview.profitTrend > 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                  {{ Math.abs(salesOverview.profitTrend) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 销售趋势分析 -->
    <div class="sales-trend-section">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
          <el-card class="chart-card">
            <div slot="header" class="card-header">
              <span>销售趋势分析</span>
              <div class="header-controls">
                <el-radio-group v-model="trendChartType" size="small" @change="updateTrendChart">
                  <el-radio-button label="sales">销售额</el-radio-button>
                  <el-radio-button label="orders">订单量</el-radio-button>
                  <el-radio-button label="both">双轴对比</el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <div class="chart-container" ref="salesTrendChart" style="height: 400px;"></div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
          <el-card class="chart-card">
            <div slot="header" class="card-header">
              <span>销售渠道分布</span>
            </div>
            <div class="chart-container" ref="channelChart" style="height: 400px;"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 产品销售分析 -->
    <div class="product-analysis-section">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card class="chart-card">
            <div slot="header" class="card-header">
              <span>热销产品TOP10</span>
              <el-button size="small" @click="viewAllProducts">查看全部</el-button>
            </div>
            <div class="chart-container" ref="topProductsChart" style="height: 350px;"></div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <el-card class="chart-card">
            <div slot="header" class="card-header">
              <span>产品分类销售占比</span>
            </div>
            <div class="chart-container" ref="categoryChart" style="height: 350px;"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 销售详细数据表格 -->
    <div class="sales-details-section">
      <el-card class="table-card">
        <div slot="header" class="card-header">
          <span>销售明细</span>
          <div class="header-controls">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索产品名称"
              size="small"
              style="width: 200px; margin-right: 10px;"
              @input="handleSearch"
            >
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
            <el-button size="small" @click="exportSalesDetails">导出明细</el-button>
          </div>
        </div>
        
        <el-table
          :data="salesDetails"
          v-loading="tableLoading"
          style="width: 100%"
          :default-sort="{prop: 'sales', order: 'descending'}"
        >
          <el-table-column prop="productName" label="产品名称" min-width="150">
            <template slot-scope="scope">
              <div class="product-info">
                <img :src="scope.row.productImage" class="product-image" />
                <span>{{ scope.row.productName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="category" label="分类" width="100"></el-table-column>
          <el-table-column prop="sales" label="销售额" width="120" sortable>
            <template slot-scope="scope">
              ¥{{ formatNumber(scope.row.sales) }}
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="销量" width="80" sortable></el-table-column>
          <el-table-column prop="avgPrice" label="平均单价" width="100">
            <template slot-scope="scope">
              ¥{{ scope.row.avgPrice }}
            </template>
          </el-table-column>
          <el-table-column prop="profit" label="利润" width="120" sortable>
            <template slot-scope="scope">
              ¥{{ formatNumber(scope.row.profit) }}
            </template>
          </el-table-column>
          <el-table-column prop="profitMargin" label="利润率" width="100">
            <template slot-scope="scope">
              <span :class="getProfitMarginClass(scope.row.profitMargin)">
                {{ scope.row.profitMargin }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="growth" label="增长率" width="100">
            <template slot-scope="scope">
              <span :class="{ 'growth-positive': scope.row.growth > 0, 'growth-negative': scope.row.growth < 0 }">
                <i :class="scope.row.growth > 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                {{ Math.abs(scope.row.growth) }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" @click="viewProductDetail(scope.row)">详情</el-button>
              <el-button size="mini" type="primary" @click="viewProductAnalysis(scope.row)">分析</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="table-pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          >
          </el-pagination>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
// import { getSellerStats, getOrderStatistics } from '@/api/seller'

export default {
  name: 'SalesStatistics',
  data() {
    return {
      loading: false,
      tableLoading: false,
      timeRange: 'month',
      customDateRange: null,
      selectedCategory: '',
      selectedChannel: '',
      trendChartType: 'both',
      searchKeyword: '',
      
      // 分类数据
      categories: [
        { id: 1, name: '新鲜蔬菜' },
        { id: 2, name: '优质水果' },
        { id: 3, name: '有机粮食' },
        { id: 4, name: '畜禽产品' },
        { id: 5, name: '水产海鲜' }
      ],
      
      // 销售概览数据
      salesOverview: {
        totalSales: 0,
        salesTrend: 0,
        avgOrderValue: 0,
        avgOrderTrend: 0,
        conversionRate: 0,
        conversionTrend: 0,
        profitMargin: 0,
        profitTrend: 0
      },
      
      // 图表数据
      salesTrendData: [],
      channelData: [],
      topProductsData: [],
      categoryData: [],
      
      // 表格数据
      salesDetails: [],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      }
    }
  },
  
  mounted() {
    this.initData()
    this.initCharts()
  },
  
  beforeDestroy() {
    // 销毁图表实例
    if (this.salesTrendChart) this.salesTrendChart.dispose()
    if (this.channelChart) this.channelChart.dispose()
    if (this.topProductsChart) this.topProductsChart.dispose()
    if (this.categoryChart) this.categoryChart.dispose()
  },
  
  methods: {
    async initData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadSalesOverview(),
          this.loadSalesTrend(),
          this.loadChannelData(),
          this.loadTopProducts(),
          this.loadCategoryData(),
          this.loadSalesDetails()
        ])
      } catch (error) {
        this.$message.error('数据加载失败：' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    async loadSalesOverview() {
      // 模拟销售概览数据
      this.salesOverview = {
        totalSales: 256780,
        salesTrend: 15.6,
        avgOrderValue: 186,
        avgOrderTrend: 8.2,
        conversionRate: 4.2,
        conversionTrend: 2.1,
        profitMargin: 28.5,
        profitTrend: 3.4
      }
    },
    
    async loadSalesTrend() {
      // 模拟销售趋势数据
      this.salesTrendData = [
        { date: '01-01', sales: 12000, orders: 45, profit: 3600 },
        { date: '01-02', sales: 15000, orders: 52, profit: 4500 },
        { date: '01-03', sales: 18000, orders: 61, profit: 5400 },
        { date: '01-04', sales: 14000, orders: 48, profit: 4200 },
        { date: '01-05', sales: 22000, orders: 73, profit: 6600 },
        { date: '01-06', sales: 19000, orders: 65, profit: 5700 },
        { date: '01-07', sales: 25000, orders: 82, profit: 7500 }
      ]
    },
    
    async loadChannelData() {
      // 模拟销售渠道数据
      this.channelData = [
        { channel: '线上商城', sales: 156780, percentage: 61.0 },
        { channel: '线下门店', sales: 78900, percentage: 30.7 },
        { channel: '批发渠道', sales: 21100, percentage: 8.3 }
      ]
    },
    
    async loadTopProducts() {
      // 模拟热销产品数据
      this.topProductsData = [
        { name: '有机苹果', sales: 23400 },
        { name: '新鲜蔬菜包', sales: 18900 },
        { name: '农家土鸡蛋', sales: 15600 },
        { name: '优质大米', sales: 13400 },
        { name: '时令水果', sales: 9800 },
        { name: '有机蔬菜', sales: 8900 },
        { name: '新鲜牛奶', sales: 7800 },
        { name: '土鸡肉', sales: 6700 },
        { name: '海鲜套餐', sales: 5600 },
        { name: '坚果礼盒', sales: 4500 }
      ]
    },
    
    async loadCategoryData() {
      // 模拟产品分类数据
      this.categoryData = [
        { category: '新鲜蔬菜', sales: 78900, percentage: 30.7 },
        { category: '优质水果', sales: 65400, percentage: 25.5 },
        { category: '有机粮食', sales: 45600, percentage: 17.8 },
        { category: '畜禽产品', sales: 34500, percentage: 13.4 },
        { category: '水产海鲜', sales: 32380, percentage: 12.6 }
      ]
    },
    
    async loadSalesDetails() {
      this.tableLoading = true
      try {
        // 模拟销售明细数据
        this.salesDetails = [
          {
            productName: '有机苹果',
            productImage: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=fresh_organic_red_apples_in_basket&image_size=square',
            category: '优质水果',
            sales: 23400,
            quantity: 234,
            avgPrice: 100,
            profit: 7020,
            profitMargin: 30.0,
            growth: 15.6
          },
          {
            productName: '新鲜蔬菜包',
            productImage: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=fresh_vegetable_package_organic&image_size=square',
            category: '新鲜蔬菜',
            sales: 18900,
            quantity: 189,
            avgPrice: 100,
            profit: 5670,
            profitMargin: 30.0,
            growth: 12.3
          },
          {
            productName: '农家土鸡蛋',
            productImage: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=farm_fresh_eggs_in_carton&image_size=square',
            category: '畜禽产品',
            sales: 15600,
            quantity: 156,
            avgPrice: 100,
            profit: 4680,
            profitMargin: 30.0,
            growth: 8.9
          },
          {
            productName: '优质大米',
            productImage: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=premium_white_rice_grains&image_size=square',
            category: '有机粮食',
            sales: 13400,
            quantity: 134,
            avgPrice: 100,
            profit: 4020,
            profitMargin: 30.0,
            growth: 5.2
          },
          {
            productName: '时令水果',
            productImage: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=seasonal_mixed_fruits_colorful&image_size=square',
            category: '优质水果',
            sales: 9800,
            quantity: 98,
            avgPrice: 100,
            profit: 2940,
            profitMargin: 30.0,
            growth: -2.1
          }
        ]
        this.pagination.total = this.salesDetails.length
      } finally {
        this.tableLoading = false
      }
    },
    
    initCharts() {
      this.$nextTick(() => {
        this.initSalesTrendChart()
        this.initChannelChart()
        this.initTopProductsChart()
        this.initCategoryChart()
      })
    },
    
    initSalesTrendChart() {
      if (!this.$refs.salesTrendChart) return
      
      this.salesTrendChart = echarts.init(this.$refs.salesTrendChart)
      this.updateTrendChart()
    },
    
    updateTrendChart() {
      if (!this.salesTrendChart) return
      
      let option = {}
      
      if (this.trendChartType === 'sales') {
        option = {
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: this.salesTrendData.map(item => item.date)
          },
          yAxis: {
            type: 'value',
            name: '销售额(元)'
          },
          series: [{
            name: '销售额',
            type: 'line',
            smooth: true,
            data: this.salesTrendData.map(item => item.sales),
            itemStyle: { color: '#409EFF' },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
              ])
            }
          }]
        }
      } else if (this.trendChartType === 'orders') {
        option = {
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: this.salesTrendData.map(item => item.date)
          },
          yAxis: {
            type: 'value',
            name: '订单量'
          },
          series: [{
            name: '订单量',
            type: 'bar',
            data: this.salesTrendData.map(item => item.orders),
            itemStyle: { color: '#67C23A' }
          }]
        }
      } else {
        option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            }
          },
          legend: {
            data: ['销售额', '订单量']
          },
          xAxis: {
            type: 'category',
            data: this.salesTrendData.map(item => item.date)
          },
          yAxis: [
            {
              type: 'value',
              name: '销售额(元)',
              position: 'left'
            },
            {
              type: 'value',
              name: '订单量',
              position: 'right'
            }
          ],
          series: [
            {
              name: '销售额',
              type: 'line',
              yAxisIndex: 0,
              smooth: true,
              data: this.salesTrendData.map(item => item.sales),
              itemStyle: { color: '#409EFF' }
            },
            {
              name: '订单量',
              type: 'bar',
              yAxisIndex: 1,
              data: this.salesTrendData.map(item => item.orders),
              itemStyle: { color: '#67C23A' }
            }
          ]
        }
      }
      
      this.salesTrendChart.setOption(option)
    },
    
    initChannelChart() {
      if (!this.$refs.channelChart) return
      
      this.channelChart = echarts.init(this.$refs.channelChart)
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [{
          name: '销售渠道',
          type: 'pie',
          radius: '60%',
          data: this.channelData.map(item => ({
            value: item.sales,
            name: item.channel
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      
      this.channelChart.setOption(option)
    },
    
    initTopProductsChart() {
      if (!this.$refs.topProductsChart) return
      
      this.topProductsChart = echarts.init(this.$refs.topProductsChart)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: this.topProductsData.map(item => item.name)
        },
        series: [{
          name: '销售额',
          type: 'bar',
          data: this.topProductsData.map(item => item.sales),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#E6A23C' },
              { offset: 1, color: '#F56C6C' }
            ])
          }
        }]
      }
      
      this.topProductsChart.setOption(option)
    },
    
    initCategoryChart() {
      if (!this.$refs.categoryChart) return
      
      this.categoryChart = echarts.init(this.$refs.categoryChart)
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
        },
        legend: {
          bottom: '0%',
          left: 'center'
        },
        series: [{
          name: '产品分类',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: this.categoryData.map(item => ({
            value: item.sales,
            name: item.category
          }))
        }]
      }
      
      this.categoryChart.setOption(option)
    },
    
    handleTimeRangeChange(value) {
      this.timeRange = value
      if (value !== 'custom') {
        this.customDateRange = null
        this.refreshSalesData()
      }
    },
    
    handleCustomDateChange(dates) {
      this.customDateRange = dates
      this.refreshSalesData()
    },
    
    handleCategoryChange() {
      this.refreshSalesData()
    },
    
    handleChannelChange() {
      this.refreshSalesData()
    },
    
    handleSearch() {
      // 实现搜索逻辑
      this.loadSalesDetails()
    },
    
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.loadSalesDetails()
    },
    
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.loadSalesDetails()
    },
    
    refreshSalesData() {
      this.initData()
    },
    
    formatNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return num.toLocaleString()
    },
    
    getProfitMarginClass(margin) {
      if (margin >= 30) return 'profit-high'
      if (margin >= 20) return 'profit-medium'
      return 'profit-low'
    },
    
    viewAllProducts() {
      this.$router.push('/seller/products')
    },
    
    viewProductDetail(row) {
      this.$router.push(`/seller/products/${row.productId}`)
    },
    
    viewProductAnalysis(_row) {
      this.$message.info('产品分析功能开发中...')
    },
    
    handleExportSalesReport() {
      this.$message.success('销售报表导出功能开发中...')
    },
    
    exportSalesDetails() {
      this.$message.success('销售明细导出功能开发中...')
    }
  }
}
</script>

<style lang="scss" scoped>
// 主题色彩变量
$primary-blue: #409EFF;
$success-green: #67C23A;
$warning-orange: #E6A23C;
$danger-red: #F56C6C;
$info-gray: #909399;

// 背景色
$bg-primary: #f8fafc;
$bg-secondary: #f1f5f9;
$bg-card: #ffffff;

// 文字色
$text-primary: #1f2937;
$text-secondary: #6b7280;
$text-light: #9ca3af;

// 边框和阴影
$border-light: #e5e7eb;
$shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
$shadow-normal: 0 4px 6px rgba(0, 0, 0, 0.1);

.sales-statistics {
  padding: 0;
  background-color: $bg-primary;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: linear-gradient(135deg, $success-green 0%, lighten($success-green, 10%) 100%);
    border-radius: 12px;
    color: white;
    box-shadow: $shadow-normal;

    .header-content {
      .page-title {
        font-size: 28px;
        font-weight: 600;
        margin: 0 0 8px 0;
      }

      .page-subtitle {
        font-size: 16px;
        opacity: 0.9;
        margin: 0;
      }
    }

    .header-actions {
      .el-button {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          border-color: rgba(255, 255, 255, 0.5);
        }
      }
    }
  }

  .filters-section {
    margin-bottom: 24px;

    .filter-card {
      border: none;
      box-shadow: $shadow-light;

      .filter-content {
        .filter-row {
          display: flex;
          align-items: center;
          gap: 20px;
          margin-bottom: 16px;
          flex-wrap: wrap;

          &:last-child {
            margin-bottom: 0;
          }

          .filter-item {
            display: flex;
            align-items: center;
            gap: 12px;

            label {
              font-weight: 500;
              color: $text-primary;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }

  .sales-overview {
    margin-bottom: 24px;

    .overview-card {
      border: none;
      box-shadow: $shadow-light;
      transition: all 0.3s ease;
      overflow: hidden;
      position: relative;

      &:hover {
        transform: translateY(-2px);
        box-shadow: $shadow-normal;
      }

      &.total-sales {
        border-left: 4px solid $primary-blue;
      }

      &.avg-order {
        border-left: 4px solid $success-green;
      }

      &.conversion-rate {
        border-left: 4px solid $warning-orange;
      }

      &.profit-margin {
        border-left: 4px solid $danger-red;
      }

      .card-content {
        display: flex;
        align-items: center;
        padding: 8px;

        .card-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          background: linear-gradient(135deg, $primary-blue 0%, lighten($primary-blue, 20%) 100%);
          color: white;
          font-size: 24px;
        }

        .card-info {
          flex: 1;

          .card-value {
            font-size: 24px;
            font-weight: 700;
            color: $text-primary;
            margin-bottom: 4px;
          }

          .card-label {
            font-size: 14px;
            color: $text-secondary;
            margin-bottom: 8px;
          }

          .card-trend {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
            color: $danger-red;

            &.positive {
              color: $success-green;
            }
          }
        }
      }
    }
  }

  .sales-trend-section,
  .product-analysis-section {
    margin-bottom: 24px;

    .chart-card {
      border: none;
      box-shadow: $shadow-light;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;
        color: $text-primary;

        .header-controls {
          display: flex;
          align-items: center;
          gap: 12px;
        }
      }

      .chart-container {
        width: 100%;
      }
    }
  }

  .sales-details-section {
    .table-card {
      border: none;
      box-shadow: $shadow-light;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;
        color: $text-primary;

        .header-controls {
          display: flex;
          align-items: center;
        }
      }

      .product-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .product-image {
          width: 40px;
          height: 40px;
          border-radius: 6px;
          object-fit: cover;
        }
      }

      .profit-high {
        color: $success-green;
        font-weight: 600;
      }

      .profit-medium {
        color: $warning-orange;
        font-weight: 600;
      }

      .profit-low {
        color: $danger-red;
        font-weight: 600;
      }

      .growth-positive {
        color: $success-green;
      }

      .growth-negative {
        color: $danger-red;
      }

      .table-pagination {
        margin-top: 20px;
        text-align: right;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sales-statistics {
    .page-header {
      flex-direction: column;
      gap: 16px;
      text-align: center;

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }

    .filters-section .filter-content .filter-row {
      flex-direction: column;
      align-items: stretch;

      .filter-item {
        justify-content: center;
      }
    }
  }
}
</style>