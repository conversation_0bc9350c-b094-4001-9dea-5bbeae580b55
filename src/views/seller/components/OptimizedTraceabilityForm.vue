<template>
  <div class="optimized-traceability-form">
    <!-- 进度指示器 -->
    <div class="progress-indicator">
      <el-steps :active="currentStep" align-center>
        <el-step 
          v-for="(step, index) in steps" 
          :key="index"
          :title="step.title" 
          :description="step.description"
          :icon="step.icon"
        />
      </el-steps>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <el-form
        ref="traceabilityForm"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="traceability-form"
      >
        <!-- 步骤1：种植/养殖环节 -->
        <div v-show="currentStep === 0" class="step-content">
          <div class="step-header">
            <h3><i class="el-icon-s-opportunity"></i> 种植/养殖环节</h3>
            <p>记录农产品的种植或养殖过程信息</p>
          </div>

          <div class="form-grid">
            <el-form-item label="农场名称" prop="farmName" class="form-item-half">
              <el-input
                v-model="formData.farmName"
                placeholder="请输入农场或养殖场名称"
                :maxlength="100"
                show-word-limit
                clearable
              />
            </el-form-item>

            <el-form-item label="生产者" prop="producerName" class="form-item-half">
              <el-input
                v-model="formData.producerName"
                placeholder="请输入生产者姓名"
                :maxlength="50"
                show-word-limit
                clearable
              />
            </el-form-item>

            <el-form-item label="种植日期" prop="plantingDate" class="form-item-half">
              <el-date-picker
                v-model="formData.plantingDate"
                type="date"
                placeholder="选择种植/养殖开始日期"
                class="full-width"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>

            <el-form-item label="采收日期" prop="harvestDate" class="form-item-half">
              <el-date-picker
                v-model="formData.harvestDate"
                type="date"
                placeholder="选择采收/出栏日期"
                class="full-width"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>

            <el-form-item label="种植环境" prop="environment" class="form-item-full">
              <el-input
                v-model="formData.environment"
                type="textarea"
                :rows="3"
                placeholder="请描述种植环境：土壤类型、气候条件、地理位置等"
                :maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="投入品使用" prop="inputs" class="form-item-full">
              <el-input
                v-model="formData.inputs"
                type="textarea"
                :rows="3"
                placeholder="请详细记录肥料、农药、饲料等投入品的使用情况"
                :maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </div>
        </div>

        <!-- 步骤2：生产加工环节 -->
        <div v-show="currentStep === 1" class="step-content">
          <div class="step-header">
            <h3><i class="el-icon-s-tools"></i> 生产加工环节</h3>
            <p>记录产品的加工处理过程</p>
          </div>

          <div class="form-grid">
            <el-form-item label="加工日期" prop="processingDate" class="form-item-half">
              <el-date-picker
                v-model="formData.processingDate"
                type="date"
                placeholder="选择加工日期"
                class="full-width"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>

            <el-form-item label="加工方式" prop="processingMethod" class="form-item-half">
              <el-select
                v-model="formData.processingMethod"
                placeholder="请选择加工方式"
                clearable
                class="full-width"
              >
                <el-option label="清洗分拣" value="清洗分拣" />
                <el-option label="切割处理" value="切割处理" />
                <el-option label="烘干处理" value="烘干处理" />
                <el-option label="冷冻处理" value="冷冻处理" />
                <el-option label="腌制处理" value="腌制处理" />
                <el-option label="无加工" value="无加工" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>

            <el-form-item label="加工工艺" prop="processingTechnology" class="form-item-full">
              <el-input
                v-model="formData.processingTechnology"
                type="textarea"
                :rows="3"
                placeholder="请详细描述加工工艺流程、技术标准等"
                :maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="质量控制" prop="qualityControl" class="form-item-full">
              <el-input
                v-model="formData.qualityControl"
                type="textarea"
                :rows="3"
                placeholder="请描述质量控制措施、检验标准等"
                :maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </div>
        </div>

        <!-- 步骤3：包装环节 -->
        <div v-show="currentStep === 2" class="step-content">
          <div class="step-header">
            <h3><i class="el-icon-box"></i> 包装环节</h3>
            <p>记录产品的包装信息</p>
          </div>

          <div class="form-grid">
            <el-form-item label="包装日期" prop="packagingDate" class="form-item-half">
              <el-date-picker
                v-model="formData.packagingDate"
                type="date"
                placeholder="选择包装日期"
                class="full-width"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>

            <el-form-item label="包装规格" prop="packagingSpec" class="form-item-half">
              <el-input
                v-model="formData.packagingSpec"
                placeholder="如：500g/袋、1kg/盒"
                clearable
              />
            </el-form-item>

            <el-form-item label="包装材料" prop="packagingMaterial" class="form-item-full">
              <el-input
                v-model="formData.packagingMaterial"
                placeholder="请输入包装材料类型，如：食品级PE袋、纸箱、真空包装等"
                clearable
              />
            </el-form-item>

            <el-form-item label="批次号" prop="batchNumber" class="form-item-half">
              <el-input
                v-model="formData.batchNumber"
                placeholder="请输入生产批次号"
                clearable
              />
            </el-form-item>

            <el-form-item label="保质期" prop="shelfLife" class="form-item-half">
              <el-input
                v-model="formData.shelfLife"
                placeholder="如：30天、6个月"
                clearable
              />
            </el-form-item>
          </div>
        </div>

        <!-- 步骤4：运输环节 -->
        <div v-show="currentStep === 3" class="step-content">
          <div class="step-header">
            <h3><i class="el-icon-truck"></i> 运输环节</h3>
            <p>记录产品的运输和储存信息</p>
          </div>

          <div class="form-grid">
            <el-form-item label="运输方式" prop="transportMethod" class="form-item-half">
              <el-select
                v-model="formData.transportMethod"
                placeholder="请选择运输方式"
                clearable
                class="full-width"
              >
                <el-option label="冷链运输" value="冷链运输" />
                <el-option label="常温运输" value="常温运输" />
                <el-option label="保温运输" value="保温运输" />
                <el-option label="快递配送" value="快递配送" />
                <el-option label="自提" value="自提" />
              </el-select>
            </el-form-item>

            <el-form-item label="运输温度" prop="transportTemperature" class="form-item-half">
              <el-input
                v-model="formData.transportTemperature"
                placeholder="如：2-8℃、常温、冷冻"
                clearable
              />
            </el-form-item>

            <el-form-item label="储存条件" prop="storageConditions" class="form-item-full">
              <el-input
                v-model="formData.storageConditions"
                type="textarea"
                :rows="2"
                placeholder="请描述储存温度、湿度、环境等条件"
                :maxlength="300"
                show-word-limit
              />
            </el-form-item>
          </div>
        </div>

        <!-- 步骤5：销售环节 -->
        <div v-show="currentStep === 4" class="step-content">
          <div class="step-header">
            <h3><i class="el-icon-s-shop"></i> 销售环节</h3>
            <p>记录产品的销售和质量认证信息</p>
          </div>

          <div class="form-grid">
            <el-form-item label="质量等级" prop="qualityGrade" class="form-item-half">
              <el-select
                v-model="formData.qualityGrade"
                placeholder="请选择质量等级"
                clearable
                class="full-width"
              >
                <el-option label="特级" value="特级" />
                <el-option label="一级" value="一级" />
                <el-option label="二级" value="二级" />
                <el-option label="三级" value="三级" />
              </el-select>
            </el-form-item>

            <el-form-item label="销售渠道" prop="salesChannel" class="form-item-half">
              <el-select
                v-model="formData.salesChannel"
                placeholder="请选择销售渠道"
                clearable
                class="full-width"
              >
                <el-option label="线上平台" value="线上平台" />
                <el-option label="实体店铺" value="实体店铺" />
                <el-option label="批发市场" value="批发市场" />
                <el-option label="直销" value="直销" />
              </el-select>
            </el-form-item>

            <el-form-item label="认证信息" prop="certifications" class="form-item-full">
              <el-input
                v-model="formData.certifications"
                type="textarea"
                :rows="2"
                placeholder="请输入获得的认证信息，如：有机认证、绿色食品认证、无公害认证等"
                :maxlength="300"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="质检报告" prop="qualityReport" class="form-item-full">
              <el-input
                v-model="formData.qualityReport"
                type="textarea"
                :rows="3"
                placeholder="请输入质量检测结果，包括农药残留、重金属、微生物等检测数据"
                :maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="备注信息" prop="additionalNotes" class="form-item-full">
              <el-input
                v-model="formData.additionalNotes"
                type="textarea"
                :rows="2"
                placeholder="请输入其他需要说明的信息"
                :maxlength="300"
                show-word-limit
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <div class="action-left">
        <el-button 
          v-if="currentStep > 0" 
          @click="prevStep"
          icon="el-icon-arrow-left"
        >
          上一步
        </el-button>
      </div>
      
      <div class="action-center">
        <el-button 
          type="info" 
          @click="saveDraft"
          :loading="draftSaving"
          icon="el-icon-document"
        >
          保存草稿
        </el-button>
        
        <el-button 
          type="warning" 
          @click="autoFill"
          icon="el-icon-magic-stick"
        >
          智能填充
        </el-button>
      </div>
      
      <div class="action-right">
        <el-button 
          v-if="currentStep < steps.length - 1" 
          type="primary" 
          @click="nextStep"
          icon="el-icon-arrow-right"
        >
          下一步
        </el-button>
        
        <el-button 
          v-else 
          type="success" 
          @click="submitForm"
          :loading="submitting"
          icon="el-icon-check"
        >
          完成填写
        </el-button>
      </div>
    </div>

    <!-- 帮助提示对话框 -->
    <el-dialog
      title="填写帮助"
      :visible.sync="helpDialogVisible"
      width="600px"
    >
      <div class="help-content">
        <h4>{{ steps[currentStep] && steps[currentStep].title }}填写指南</h4>
        <div v-html="steps[currentStep] && steps[currentStep].help"></div>
      </div>
      <span slot="footer">
        <el-button @click="helpDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'OptimizedTraceabilityForm',
  props: {
    // 产品ID（编辑模式）
    productId: {
      type: [String, Number],
      default: null
    },
    // 初始数据
    initialData: {
      type: Object,
      default: () => ({})
    },
    // 是否为编辑模式
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentStep: 0,
      submitting: false,
      draftSaving: false,
      helpDialogVisible: false,

      // 步骤配置
      steps: [
        {
          title: '种植/养殖',
          description: '记录生产源头信息',
          icon: 'el-icon-s-opportunity',
          help: `
            <p><strong>种植/养殖环节</strong>是溯源的起点，需要记录：</p>
            <ul>
              <li><strong>农场名称：</strong>生产基地的正式名称</li>
              <li><strong>生产者：</strong>负责种植/养殖的人员姓名</li>
              <li><strong>种植日期：</strong>播种或幼苗移栽的日期</li>
              <li><strong>采收日期：</strong>收获或出栏的日期</li>
              <li><strong>种植环境：</strong>详细描述土壤、气候、地理位置等</li>
              <li><strong>投入品使用：</strong>记录所有肥料、农药、饲料的使用情况</li>
            </ul>
          `
        },
        {
          title: '生产加工',
          description: '记录加工处理过程',
          icon: 'el-icon-s-tools',
          help: `
            <p><strong>生产加工环节</strong>记录产品的处理过程：</p>
            <ul>
              <li><strong>加工日期：</strong>开始加工处理的日期</li>
              <li><strong>加工方式：</strong>选择主要的加工处理方式</li>
              <li><strong>加工工艺：</strong>详细描述工艺流程和技术标准</li>
              <li><strong>质量控制：</strong>记录质量控制措施和检验标准</li>
            </ul>
          `
        },
        {
          title: '包装',
          description: '记录包装信息',
          icon: 'el-icon-box',
          help: `
            <p><strong>包装环节</strong>确保产品安全和标识：</p>
            <ul>
              <li><strong>包装日期：</strong>完成包装的日期</li>
              <li><strong>包装规格：</strong>单个包装的重量或数量</li>
              <li><strong>包装材料：</strong>使用的包装材料类型</li>
              <li><strong>批次号：</strong>生产批次的唯一标识</li>
              <li><strong>保质期：</strong>产品的保质期限</li>
            </ul>
          `
        },
        {
          title: '运输',
          description: '记录运输储存信息',
          icon: 'el-icon-truck',
          help: `
            <p><strong>运输环节</strong>保证产品品质：</p>
            <ul>
              <li><strong>运输方式：</strong>选择合适的运输方式</li>
              <li><strong>运输温度：</strong>运输过程中的温度要求</li>
              <li><strong>储存条件：</strong>详细描述储存环境要求</li>
            </ul>
          `
        },
        {
          title: '销售',
          description: '记录销售和认证信息',
          icon: 'el-icon-s-shop',
          help: `
            <p><strong>销售环节</strong>是溯源的终点：</p>
            <ul>
              <li><strong>质量等级：</strong>产品的质量分级</li>
              <li><strong>销售渠道：</strong>主要的销售渠道</li>
              <li><strong>认证信息：</strong>获得的各类认证证书</li>
              <li><strong>质检报告：</strong>质量检测的详细结果</li>
              <li><strong>备注信息：</strong>其他需要说明的信息</li>
            </ul>
          `
        }
      ],

      // 表单数据
      formData: {
        // 种植/养殖环节
        farmName: '',
        producerName: '',
        plantingDate: null,
        harvestDate: null,
        environment: '',
        inputs: '',

        // 生产加工环节
        processingDate: null,
        processingMethod: '',
        processingTechnology: '',
        qualityControl: '',

        // 包装环节
        packagingDate: null,
        packagingSpec: '',
        packagingMaterial: '',
        batchNumber: '',
        shelfLife: '',

        // 运输环节
        transportMethod: '',
        transportTemperature: '',
        storageConditions: '',

        // 销售环节
        qualityGrade: '',
        salesChannel: '',
        certifications: '',
        qualityReport: '',
        additionalNotes: ''
      },

      // 表单验证规则
      formRules: {
        farmName: [
          { required: true, message: '请输入农场名称', trigger: 'blur' },
          { min: 2, max: 100, message: '农场名称长度在2到100个字符', trigger: 'blur' }
        ],
        producerName: [
          { required: true, message: '请输入生产者姓名', trigger: 'blur' },
          { min: 2, max: 50, message: '生产者姓名长度在2到50个字符', trigger: 'blur' }
        ],
        plantingDate: [
          { required: true, message: '请选择种植日期', trigger: 'change' }
        ],
        harvestDate: [
          { required: true, message: '请选择采收日期', trigger: 'change' }
        ],
        environment: [
          { required: true, message: '请描述种植环境', trigger: 'blur' },
          { min: 10, max: 500, message: '环境描述长度在10到500个字符', trigger: 'blur' }
        ]
      }
    }
  },

  mounted() {
    this.initForm()
  },

  methods: {
    // 初始化表单
    initForm() {
      if (this.initialData && Object.keys(this.initialData).length > 0) {
        this.formData = { ...this.formData, ...this.initialData }
      }

      // 如果是编辑模式，加载现有数据
      if (this.isEdit && this.productId) {
        this.loadTraceabilityData()
      }

      // 尝试从本地存储恢复草稿
      this.loadDraft()
    },

    // 加载溯源数据（编辑模式）
    async loadTraceabilityData() {
      try {
        // 这里调用API获取现有的溯源数据
        // const response = await traceabilityApi.getTraceabilityEvents(this.productId)
        // if (response.success) {
        //   this.parseEventsToFormData(response.data)
        // }
      } catch (error) {
        console.error('加载溯源数据失败:', error)
        this.$message.error('加载溯源数据失败')
      }
    },

    // 下一步
    nextStep() {
      if (this.validateCurrentStep()) {
        this.currentStep++
        this.saveDraftSilently()
      }
    },

    // 上一步
    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },

    // 验证当前步骤
    validateCurrentStep() {
      const stepFields = this.getStepFields(this.currentStep)
      let isValid = true

      // 验证当前步骤的必填字段
      stepFields.forEach(field => {
        if (this.formRules[field]) {
          const rules = this.formRules[field]
          const requiredRule = rules.find(rule => rule.required)
          if (requiredRule && !this.formData[field]) {
            this.$message.error(requiredRule.message)
            isValid = false
          }
        }
      })

      return isValid
    },

    // 获取步骤对应的字段
    getStepFields(step) {
      const stepFieldsMap = {
        0: ['farmName', 'producerName', 'plantingDate', 'harvestDate', 'environment'],
        1: ['processingDate', 'processingMethod', 'processingTechnology'],
        2: ['packagingDate', 'packagingMaterial', 'batchNumber'],
        3: ['transportMethod', 'storageConditions'],
        4: ['qualityGrade', 'salesChannel']
      }
      return stepFieldsMap[step] || []
    },

    // 保存草稿
    async saveDraft() {
      this.draftSaving = true
      try {
        // 保存到本地存储
        const draftKey = `traceability_draft_${this.productId || 'new'}`
        localStorage.setItem(draftKey, JSON.stringify({
          formData: this.formData,
          currentStep: this.currentStep,
          timestamp: Date.now()
        }))

        this.$message.success('草稿保存成功')
      } catch (error) {
        console.error('保存草稿失败:', error)
        this.$message.error('保存草稿失败')
      } finally {
        this.draftSaving = false
      }
    },

    // 静默保存草稿
    saveDraftSilently() {
      try {
        const draftKey = `traceability_draft_${this.productId || 'new'}`
        localStorage.setItem(draftKey, JSON.stringify({
          formData: this.formData,
          currentStep: this.currentStep,
          timestamp: Date.now()
        }))
      } catch (error) {
        console.error('静默保存草稿失败:', error)
      }
    },

    // 加载草稿
    loadDraft() {
      try {
        const draftKey = `traceability_draft_${this.productId || 'new'}`
        const draft = localStorage.getItem(draftKey)

        if (draft) {
          const draftData = JSON.parse(draft)
          const timeDiff = Date.now() - draftData.timestamp

          // 如果草稿在24小时内，询问是否恢复
          if (timeDiff < 24 * 60 * 60 * 1000) {
            this.$confirm('检测到未完成的草稿，是否恢复？', '恢复草稿', {
              confirmButtonText: '恢复',
              cancelButtonText: '重新开始',
              type: 'info'
            }).then(() => {
              this.formData = { ...this.formData, ...draftData.formData }
              this.currentStep = draftData.currentStep || 0
              this.$message.success('草稿已恢复')
            }).catch(() => {
              // 用户选择重新开始，清除草稿
              localStorage.removeItem(draftKey)
            })
          }
        }
      } catch (error) {
        console.error('加载草稿失败:', error)
      }
    },

    // 智能填充
    autoFill() {
      this.$confirm('智能填充将使用常用信息填充表单，是否继续？', '智能填充', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        this.performAutoFill()
      })
    },

    // 执行智能填充
    performAutoFill() {
      const commonData = {
        processingMethod: '清洗分拣',
        packagingMaterial: '食品级PE袋',
        transportMethod: '冷链运输',
        transportTemperature: '2-8℃',
        storageConditions: '阴凉干燥处保存，避免阳光直射',
        salesChannel: '线上平台',
        qualityGrade: '一级'
      }

      // 只填充空字段
      Object.keys(commonData).forEach(key => {
        if (!this.formData[key]) {
          this.formData[key] = commonData[key]
        }
      })

      this.$message.success('智能填充完成')
    },

    // 提交表单
    async submitForm() {
      try {
        // 验证所有步骤
        for (let i = 0; i < this.steps.length; i++) {
          const stepFields = this.getStepFields(i)
          for (const field of stepFields) {
            if (this.formRules[field]) {
              const rules = this.formRules[field]
              const requiredRule = rules.find(rule => rule.required)
              if (requiredRule && !this.formData[field]) {
                this.currentStep = i
                this.$message.error(`第${i + 1}步：${requiredRule.message}`)
                return
              }
            }
          }
        }

        this.submitting = true

        // 转换表单数据为事件数据
        const eventsData = this.convertFormDataToEvents()

        // 触发提交事件
        this.$emit('submit', {
          formData: this.formData,
          eventsData: eventsData
        })

        // 清除草稿
        const draftKey = `traceability_draft_${this.productId || 'new'}`
        localStorage.removeItem(draftKey)

      } catch (error) {
        console.error('提交表单失败:', error)
        this.$message.error('提交失败：' + error.message)
      } finally {
        this.submitting = false
      }
    },

    // 转换表单数据为事件数据
    convertFormDataToEvents() {
      const events = []

      // 种植/养殖事件
      if (this.formData.plantingDate) {
        events.push({
          eventType: 'PLANTING',
          eventDate: this.formData.plantingDate,
          description: `种植环境：${this.formData.environment}，投入品：${this.formData.inputs}`,
          location: this.formData.farmName,
          responsiblePerson: this.formData.producerName,
          eventSequence: 1
        })
      }

      // 采收事件
      if (this.formData.harvestDate) {
        events.push({
          eventType: 'HARVESTING',
          eventDate: this.formData.harvestDate,
          description: '产品采收完成',
          location: this.formData.farmName,
          responsiblePerson: this.formData.producerName,
          eventSequence: 2
        })
      }

      // 加工事件
      if (this.formData.processingDate) {
        events.push({
          eventType: 'PROCESSING',
          eventDate: this.formData.processingDate,
          description: `加工方式：${this.formData.processingMethod}，工艺：${this.formData.processingTechnology}，质量控制：${this.formData.qualityControl}`,
          eventSequence: 3
        })
      }

      // 包装事件
      if (this.formData.packagingDate) {
        events.push({
          eventType: 'PACKAGING',
          eventDate: this.formData.packagingDate,
          description: `包装规格：${this.formData.packagingSpec}，包装材料：${this.formData.packagingMaterial}，批次号：${this.formData.batchNumber}`,
          eventSequence: 4
        })
      }

      return events
    },

    // 显示帮助
    showHelp() {
      this.helpDialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.optimized-traceability-form {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;

  .progress-indicator {
    margin-bottom: 30px;

    .el-steps {
      margin-bottom: 20px;
    }
  }

  .form-content {
    background: #fff;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    min-height: 500px;

    .step-content {
      .step-header {
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid #f0f0f0;

        h3 {
          color: #409eff;
          font-size: 20px;
          margin: 0 0 8px 0;
          display: flex;
          align-items: center;

          i {
            margin-right: 8px;
            font-size: 22px;
          }
        }

        p {
          color: #666;
          margin: 0;
          font-size: 14px;
        }
      }

      .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;

        .form-item-full {
          grid-column: 1 / -1;
        }

        .form-item-half {
          grid-column: span 1;
        }

        .full-width {
          width: 100%;
        }

        @media (max-width: 768px) {
          grid-template-columns: 1fr;

          .form-item-half {
            grid-column: 1;
          }
        }
      }
    }
  }

  .form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding: 20px 30px;
    background: #f8f9fa;
    border-radius: 8px;

    .action-left,
    .action-right {
      flex: 1;
    }

    .action-center {
      flex: 2;
      display: flex;
      justify-content: center;
      gap: 10px;
    }

    .action-right {
      display: flex;
      justify-content: flex-end;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 15px;

      .action-left,
      .action-center,
      .action-right {
        flex: none;
        width: 100%;
        display: flex;
        justify-content: center;
      }
    }
  }

  .help-content {
    h4 {
      color: #409eff;
      margin-bottom: 15px;
    }

    ul {
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        line-height: 1.6;

        strong {
          color: #333;
        }
      }
    }
  }
}

// Element UI 样式覆盖
.el-form-item {
  margin-bottom: 20px;

  .el-form-item__label {
    font-weight: 500;
    color: #333;
  }

  .el-input__inner,
  .el-textarea__inner {
    border-radius: 6px;
    border: 1px solid #dcdfe6;
    transition: all 0.3s;

    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }

  .el-select {
    width: 100%;
  }
}

.el-steps {
  .el-step__title {
    font-size: 14px;
    font-weight: 500;
  }

  .el-step__description {
    font-size: 12px;
  }
}

.el-button {
  border-radius: 6px;
  padding: 10px 20px;
  font-weight: 500;

  &.el-button--primary {
    background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #66b3ff 0%, #409eff 100%);
    }
  }

  &.el-button--success {
    background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
    border: none;

    &:hover {
      background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
    }
  }
}
</style>
