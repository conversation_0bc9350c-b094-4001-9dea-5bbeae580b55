<template>
  <div class="traceability-record-form">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关联产品" prop="productId">
              <el-select
                v-model="form.productId"
                placeholder="选择要关联的产品"
                filterable
                style="width: 100%"
                @change="handleProductChange"
              >
                <el-option
                  v-for="product in productList"
                  :key="product.id"
                  :label="product.name"
                  :value="product.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="溯源码" prop="traceCode">
              <el-input
                v-model="form.traceCode"
                placeholder="系统自动生成"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品名称" prop="productName">
              <el-input
                v-model="form.productName"
                placeholder="输入产品名称"
                :maxlength="255"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="农场名称" prop="farmName">
              <el-input
                v-model="form.farmName"
                placeholder="输入农场/生产基地名称"
                :maxlength="255"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生产者名称" prop="producerName">
              <el-input
                v-model="form.producerName"
                placeholder="输入生产者名称"
                :maxlength="255"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="批次号" prop="batchNumber">
              <el-input
                v-model="form.batchNumber"
                placeholder="输入生产批次号"
                :maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品规格" prop="specification">
              <el-input
                v-model="form.specification"
                placeholder="输入产品规格（如：500g/包）"
                :maxlength="255"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="质量等级" prop="qualityGrade">
              <el-select v-model="form.qualityGrade" placeholder="选择质量等级" style="width: 100%">
                <el-option label="特级" value="premium" />
                <el-option label="一级" value="first" />
                <el-option label="二级" value="second" />
                <el-option label="合格" value="qualified" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 日期信息 -->
      <div class="form-section">
        <h3 class="section-title">日期信息</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="创建日期" prop="creationDate">
              <el-date-picker
                v-model="form.creationDate"
                type="date"
                placeholder="选择创建日期"
                style="width: 100%"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收获日期" prop="harvestDate">
              <el-date-picker
                v-model="form.harvestDate"
                type="date"
                placeholder="选择收获日期"
                style="width: 100%"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="包装日期" prop="packagingDate">
              <el-date-picker
                v-model="form.packagingDate"
                type="date"
                placeholder="选择包装日期"
                style="width: 100%"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 状态信息 -->
      <div class="form-section">
        <h3 class="section-title">状态信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="记录状态" prop="status">
              <el-select v-model="form.status" placeholder="选择状态" style="width: 100%">
                <el-option label="草稿" :value="0" />
                <el-option label="待审核" :value="1" />
                <el-option label="已发布" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="二维码URL" prop="qrCodeUrl">
              <el-input
                v-model="form.qrCodeUrl"
                placeholder="系统自动生成"
                :disabled="true"
                :maxlength="255"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 生产信息 -->
      <div class="form-section">
        <h3 class="section-title">生产信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生产地址" prop="productionLocation">
              <el-input v-model="form.productionLocation" placeholder="输入生产地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生产环境" prop="productionEnvironment">
              <el-select v-model="form.productionEnvironment" placeholder="选择生产环境" style="width: 100%">
                <el-option label="温室大棚" value="greenhouse" />
                <el-option label="露天种植" value="outdoor" />
                <el-option label="水培种植" value="hydroponic" />
                <el-option label="有机种植" value="organic" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="种植/养殖方式" prop="cultivationMethod">
          <el-input
            v-model="form.cultivationMethod"
            type="textarea"
            :rows="3"
            placeholder="详细描述种植或养殖方式"
          />
        </el-form-item>

        <el-form-item label="使用农药/饲料" prop="pesticidesUsed">
          <el-input
            v-model="form.pesticidesUsed"
            type="textarea"
            :rows="3"
            placeholder="详细记录使用的农药、化肥或饲料信息"
          />
        </el-form-item>
      </div>

      <!-- 质量信息 -->
      <div class="form-section">
        <h3 class="section-title">质量信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="质量等级" prop="qualityGrade">
              <el-select v-model="form.qualityGrade" placeholder="选择质量等级" style="width: 100%">
                <el-option label="特级" value="premium" />
                <el-option label="一级" value="first" />
                <el-option label="二级" value="second" />
                <el-option label="合格" value="qualified" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测机构" prop="testingOrganization">
              <el-input v-model="form.testingOrganization" placeholder="输入检测机构名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="质量检测报告" prop="qualityReport">
          <el-input
            v-model="form.qualityReport"
            type="textarea"
            :rows="3"
            placeholder="输入质量检测报告内容或检测结果"
          />
        </el-form-item>
      </div>

      <!-- 附件上传 -->
      <div class="form-section">
        <h3 class="section-title">相关附件</h3>
        <el-form-item label="产品图片">
          <el-upload
            class="upload-demo"
            action="/api/upload/image"
            :headers="uploadHeaders"
            :on-success="handleImageSuccess"
            :on-remove="handleImageRemove"
            :file-list="imageList"
            list-type="picture-card"
            accept="image/*"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>

        <el-form-item label="证明文档">
          <el-upload
            class="upload-demo"
            action="/api/upload/document"
            :headers="uploadHeaders"
            :on-success="handleDocumentSuccess"
            :on-remove="handleDocumentRemove"
            :file-list="documentList"
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传pdf/doc/docx文件，且不超过10MB</div>
          </el-upload>
        </el-form-item>
      </div>

      <!-- 备注信息 -->
      <div class="form-section">
        <h3 class="section-title">备注信息</h3>
        <el-form-item label="备注说明" prop="remarks">
          <el-input
            v-model="form.remarks"
            type="textarea"
            :rows="4"
            placeholder="输入其他需要说明的信息"
          />
        </el-form-item>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button @click="handleSaveDraft">保存草稿</el-button>
      <el-button type="primary" @click="handleSubmit">{{ isEdit ? '更新' : '创建' }}</el-button>
    </div>
  </div>
</template>

<script>
import { createSellerRecord, updateSellerRecord, getSellerProducts } from '@/api/traceability'

export default {
  name: 'TraceabilityRecordForm',
  props: {
    record: {
      type: Object,
      default: null
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      productList: [],
      imageList: [],
      documentList: [],
      form: {
        // 数据库必填字段
        productId: null,           // product_id (bigint, NOT NULL)
        traceCode: '',             // trace_code (varchar(128), NOT NULL) - 系统生成
        productName: '',           // product_name (varchar(255), NOT NULL)
        producerId: null,          // producer_id (bigint, NOT NULL) - 当前用户ID

        // 数据库可选字段
        farmName: '',              // farm_name (varchar(255), NULL)
        producerName: '',          // producer_name (varchar(255), NULL)
        batchNumber: '',           // batch_number (varchar(100), NULL)
        specification: '',         // specification (varchar(255), NULL)
        qualityGrade: '',          // quality_grade (varchar(50), NULL)
        creationDate: '',          // creation_date (date, NULL)
        harvestDate: '',           // harvest_date (date, NULL)
        packagingDate: '',         // packaging_date (date, NULL)
        qrCodeUrl: '',             // qr_code_url (varchar(255), NULL) - 系统生成
        status: 0                  // status (tinyint, DEFAULT 0)
      },
      rules: {
        // 必填字段验证（对应数据库NOT NULL字段）
        productId: [
          { required: true, message: '请选择关联产品', trigger: 'change' }
        ],
        productName: [
          { required: true, message: '请输入产品名称', trigger: 'blur' },
          { min: 1, max: 255, message: '产品名称长度在1到255个字符', trigger: 'blur' }
        ],

        // 可选字段验证（对应数据库NULL字段）
        farmName: [
          { max: 255, message: '农场名称长度不能超过255个字符', trigger: 'blur' }
        ],
        producerName: [
          { max: 255, message: '生产者名称长度不能超过255个字符', trigger: 'blur' }
        ],
        batchNumber: [
          { max: 100, message: '批次号长度不能超过100个字符', trigger: 'blur' }
        ],
        specification: [
          { max: 255, message: '产品规格长度不能超过255个字符', trigger: 'blur' }
        ],
        qualityGrade: [
          { max: 50, message: '质量等级长度不能超过50个字符', trigger: 'blur' }
        ],
        qrCodeUrl: [
          { max: 255, message: 'URL长度不能超过255个字符', trigger: 'blur' }
        ]
      },
      uploadHeaders: {
        'Authorization': 'Bearer ' + this.$store.getters.token
      }
    }
  },
  mounted() {
    this.loadProducts()
    if (this.isEdit && this.record) {
      this.initForm()
    }
  },
  methods: {
    // 加载产品列表
    async loadProducts() {
      try {
        const response = await getSellerProducts()
        if (response.success) {
          this.productList = response.data
        }
      } catch (error) {
        // 加载产品列表失败
      }
    },

    // 初始化表单
    initForm() {
      Object.keys(this.form).forEach(key => {
        if (this.record[key] !== undefined) {
          this.form[key] = this.record[key]
        }
      })
      
      // 初始化附件列表
      if (this.record.images) {
        this.imageList = this.record.images.map(img => ({
          name: img.name,
          url: img.url
        }))
      }
      
      if (this.record.documents) {
        this.documentList = this.record.documents.map(doc => ({
          name: doc.name,
          url: doc.url
        }))
      }
    },

    // 产品选择改变
    handleProductChange(productId) {
      const product = this.productList.find(p => p.id === productId)
      if (product) {
        // 可以根据产品信息自动填充一些字段
      }
    },

    // 图片上传成功
    handleImageSuccess(response, file, fileList) {
      if (response.success) {
        this.imageList = fileList
        this.$message.success('图片上传成功')
      } else {
        this.$message.error('图片上传失败')
      }
    },

    // 图片移除
    handleImageRemove(file, fileList) {
      this.imageList = fileList
    },

    // 文档上传成功
    handleDocumentSuccess(response, file, fileList) {
      if (response.success) {
        this.documentList = fileList
        this.$message.success('文档上传成功')
      } else {
        this.$message.error('文档上传失败')
      }
    },

    // 文档移除
    handleDocumentRemove(file, fileList) {
      this.documentList = fileList
    },

    // 保存草稿
    async handleSaveDraft() {
      this.form.status = 'draft'
      await this.submitForm()
    },

    // 提交表单
    async handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          await this.submitForm()
        }
      })
    },

    // 提交表单数据
    async submitForm() {
      this.loading = true
      try {
        const formData = {
          ...this.form,
          images: this.imageList.map(img => ({
            name: img.name,
            url: img.url
          })),
          documents: this.documentList.map(doc => ({
            name: doc.name,
            url: doc.url
          }))
        }

        let response
        if (this.isEdit) {
          response = await updateSellerRecord(this.record.id, formData)
        } else {
          response = await createSellerRecord(formData)
        }

        if (response.success) {
          this.$message.success(this.isEdit ? '更新成功' : '创建成功')
          this.$emit('submit')
        } else {
          this.$message.error(response.message || '操作失败')
        }
      } catch (error) {
        this.$message.error('操作失败')
      } finally {
        this.loading = false
      }
    },

    // 取消
    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.traceability-record-form {
  .form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: #fafafa;
    border-radius: 6px;

    .section-title {
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }
  }

  .form-actions {
    text-align: right;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;

    .el-button {
      margin-left: 10px;
    }
  }

  .upload-demo {
    .el-upload__tip {
      margin-top: 5px;
      font-size: 12px;
      color: #909399;
    }
  }
}
</style>
