<template>
  <div class="seller-sidebar-wrapper">
    <!-- 移动端菜单按钮 -->
    <button
      v-if="isMobile"
      @click="$emit('toggle-mobile')"
      class="mobile-menu-btn"
      :class="{ 'menu-open': mobileOpen }"
    >
      <i :class="mobileOpen ? 'el-icon-close' : 'el-icon-s-unfold'"></i>
    </button>

    <!-- 移动端遮罩层 -->
    <div
      v-if="mobileOpen && isMobile"
      class="mobile-overlay"
      @click="$emit('toggle-mobile')"
    ></div>

    <!-- 侧边栏主体 -->
    <div
      class="seller-sidebar"
      :class="{
        'sidebar-collapsed': collapsed,
        'sidebar-mobile': isMobile,
        'sidebar-open': mobileOpen
      }"
    >
      <!-- 侧边栏头部 -->
      <div class="sidebar-header">
        <div class="logo-section" :class="{ 'logo-collapsed': collapsed }">
          <div class="logo-icon">
            <i class="el-icon-shop"></i>
          </div>
          <div v-if="!collapsed" class="logo-text">
            <h1>销售中心</h1>
            <p>SFAP农品汇</p>
          </div>
        </div>

        <button
          v-if="!isMobile"
          class="collapse-btn"
          @click="$emit('toggle-collapse')"
        >
          <i :class="collapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-left'"></i>
        </button>
      </div>

      <!-- 搜索栏 -->
      <div v-if="!collapsed" class="search-section">
        <el-input
          v-model="searchQuery"
          placeholder="搜索菜单..."
          prefix-icon="el-icon-search"
          size="small"
          class="search-input"
        />
      </div>

      <!-- 导航菜单 -->
      <div class="nav-menu">
        <div v-for="item in filteredMenuItems" :key="item.id" class="menu-item-wrapper">
          <button
            class="menu-item"
            :class="{
              'menu-item-active': activeMenu === item.id,
              'menu-item-collapsed': collapsed
            }"
            @click="handleMenuClick(item)"
          >
            <div class="menu-item-content">
              <div class="menu-icon">
                <i :class="item.icon"></i>
              </div>
              <span v-if="!collapsed" class="menu-label">{{ item.name }}</span>
            </div>

            <div v-if="!collapsed" class="menu-extras">
              <span v-if="item.badge" class="menu-badge">{{ item.badge }}</span>
              <i
                v-if="item.children && item.children.length > 0"
                class="menu-arrow"
                :class="{ 'menu-arrow-expanded': expandedItems.includes(item.id) }"
              >
                ▶
              </i>
            </div>

            <!-- 折叠状态下的徽章 -->
            <div v-if="collapsed && item.badge" class="collapsed-badge">
              <span>{{ item.badge }}</span>
            </div>

            <!-- 折叠状态下的提示 -->
            <div v-if="collapsed" class="collapsed-tooltip">
              {{ item.name }}
              <span v-if="item.badge" class="tooltip-badge">{{ item.badge }}</span>
              <div class="tooltip-arrow"></div>
            </div>
          </button>

          <!-- 子菜单 -->
          <transition name="submenu-slide">
            <div
              v-if="!collapsed && item.children && expandedItems.includes(item.id)"
              class="submenu"
            >
              <button
                v-for="child in item.children"
                :key="child.id"
                class="submenu-item"
                :class="{ 'submenu-item-active': activeMenu === child.id }"
                @click="handleMenuClick(child)"
              >
                <div class="submenu-item-content">
                  <div class="submenu-icon">
                    <i :class="child.icon"></i>
                  </div>
                  <span class="submenu-label">{{ child.name }}</span>
                </div>
                <span v-if="child.badge" class="submenu-badge">{{ child.badge }}</span>
              </button>
            </div>
          </transition>
        </div>
      </div>

      <!-- 用户信息区域 -->
      <div class="sidebar-footer">
        <div class="user-section" :class="{ 'user-collapsed': collapsed }">
          <div v-if="!collapsed" class="user-info">
            <div class="user-avatar">
              <span>销</span>
            </div>
            <div class="user-details">
              <p class="user-name">{{ userInfo.name || '销售者' }}</p>
              <p class="user-role">销售管理员</p>
            </div>
            <div class="user-status"></div>
          </div>

          <div v-else class="user-collapsed-avatar">
            <div class="user-avatar">
              <span>销</span>
            </div>
            <div class="user-status-collapsed"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getUserInfo } from '@/utils/auth'

export default {
  name: 'SellerSidebar',
  props: {
    collapsed: {
      type: Boolean,
      default: false
    },
    mobileOpen: {
      type: Boolean,
      default: false
    },
    isMobile: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      searchQuery: '',
      activeMenu: 'dashboard',
      expandedItems: [],
      menuItems: [
        {
          id: "dashboard",
          name: "仪表板",
          icon: "el-icon-data-board",
          route: "/seller/dashboard"
        },
        {
          id: "shop",
          name: "商店管理",
          icon: "el-icon-shop",
          route: "/seller/shop"
        },
        {
          id: "products",
          name: "产品管理",
          icon: "el-icon-goods",
          route: "/seller/products",
          children: [
            { id: "products-list", name: "产品列表", icon: "el-icon-tickets", route: "/seller/products" },
            { id: "products-add", name: "添加产品", icon: "el-icon-circle-plus", route: "/seller/products/add" },
            { id: "products-categories", name: "产品分类", icon: "el-icon-menu", route: "/seller/products/categories" }
          ]
        },
        {
          id: "orders",
          name: "订单管理",
          icon: "el-icon-document",
          route: "/seller/orders",
          badge: "5",
          children: [
            { id: "orders-list", name: "订单列表", icon: "el-icon-tickets", route: "/seller/orders" },
            { id: "orders-pending", name: "待处理订单", icon: "el-icon-warning", route: "/seller/orders/pending", badge: "3" },
            { id: "orders-shipped", name: "已发货订单", icon: "el-icon-truck", route: "/seller/orders/shipped" }
          ]
        },
        {
          id: "traceability",
          name: "溯源管理",
          icon: "el-icon-link",
          route: "/seller/traceability-center",
          children: [
            { id: "traceability-center", name: "溯源中心", icon: "el-icon-data-line", route: "/seller/traceability-center" },
            { id: "traceability-records", name: "溯源记录", icon: "el-icon-document", route: "/seller/traceability" },
            { id: "traceability-create", name: "创建溯源", icon: "el-icon-circle-plus", route: "/seller/traceability/create" },
            { id: "traceability-audit", name: "审核状态", icon: "el-icon-view", route: "/seller/traceability/audit" }
          ]
        },
        {
          id: "customers",
          name: "客户管理",
          icon: "el-icon-user",
          route: "/seller/customers",
          children: [
            { id: "customers-list", name: "客户列表", icon: "el-icon-user", route: "/seller/customers" },
            { id: "customers-reviews", name: "客户评价", icon: "el-icon-chat-line-square", route: "/seller/reviews", badge: "2" },
            { id: "customers-feedback", name: "客户反馈", icon: "el-icon-message", route: "/seller/feedback" }
          ]
        },
        {
          id: "statistics",
          name: "数据统计",
          icon: "el-icon-data-analysis",
          route: "/seller/statistics",
          children: [
            { id: "statistics-sales", name: "销售统计", icon: "el-icon-money", route: "/seller/statistics/sales" },
            { id: "statistics-products", name: "产品统计", icon: "el-icon-pie-chart", route: "/seller/statistics/products" },
            { id: "statistics-customers", name: "客户统计", icon: "el-icon-user", route: "/seller/statistics/customers" }
          ]
        },
        {
          id: "settings",
          name: "设置",
          icon: "el-icon-setting",
          route: "/seller/settings"
        }
      ]
    }
  },
  computed: {
    userInfo() {
      return getUserInfo() || {}
    },
    filteredMenuItems() {
      if (!this.searchQuery) {
        return this.menuItems
      }

      return this.menuItems.filter(item => {
        const matchesName = item.name.toLowerCase().includes(this.searchQuery.toLowerCase())
        const matchesChildren = item.children && item.children.some(child =>
          child.name.toLowerCase().includes(this.searchQuery.toLowerCase())
        )
        return matchesName || matchesChildren
      })
    }
  },
  mounted() {
    this.updateActiveMenu()
  },
  watch: {
    '$route'() {
      this.updateActiveMenu()
    }
  },
  methods: {
    updateActiveMenu() {
      const currentPath = this.$route.path

      // 查找匹配的菜单项
      for (const item of this.menuItems) {
        if (item.route === currentPath) {
          this.activeMenu = item.id
          return
        }

        if (item.children) {
          for (const child of item.children) {
            if (child.route === currentPath) {
              this.activeMenu = child.id
              // 自动展开父菜单
              if (!this.expandedItems.includes(item.id)) {
                this.expandedItems.push(item.id)
              }
              return
            }
          }
        }
      }
    },

    handleMenuClick(item) {
      if (item.children && item.children.length > 0) {
        this.toggleExpanded(item.id)
      } else {
        this.activeMenu = item.id
        this.$emit('menu-click', item.id)

        // 路由跳转
        if (item.route && item.route !== this.$route.path) {
          this.$router.push(item.route)
        }
      }
    },

    toggleExpanded(itemId) {
      const index = this.expandedItems.indexOf(itemId)
      if (index > -1) {
        this.expandedItems.splice(index, 1)
      } else {
        this.expandedItems.push(itemId)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 主题色彩变量（与溯源中心保持一致）
$primary-blue: #3b82f6;
$light-blue: #60a5fa;
$dark-blue: #1d4ed8;
$success-green: #10b981;
$warning-orange: #f59e0b;

// 背景色
$bg-primary: #f8fafc;
$bg-secondary: #f1f5f9;
$bg-card: #ffffff;

// 文字色
$text-primary: #1f2937;
$text-secondary: #6b7280;
$text-light: #9ca3af;

// 边框和阴影
$border-light: #e5e7eb;
$shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);

// 动画
$transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
$transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

.seller-sidebar-wrapper {
  position: relative;
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  position: fixed;
  top: 20px;
  left: 20px;
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, $primary-blue, $dark-blue);
  border: none;
  border-radius: 12px;
  color: #ffffff;
  font-size: 20px;
  cursor: pointer;
  z-index: 1060;
  transition: $transition-normal;
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
  }

  &:active {
    transform: scale(0.95);
  }

  &.menu-open {
    background: linear-gradient(135deg, #ef4444, #dc2626);
  }

  @media (min-width: 768px) {
    display: none;
  }
}

/* 移动端遮罩层 */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1030;
  transition: opacity $transition-fast;
}

/* 侧边栏主体 */
.seller-sidebar {
  position: fixed;
  top: 64px; // 从顶部导航栏下方开始
  left: 0;
  height: calc(100vh - 64px); // 减去顶部导航栏高度
  width: 288px;
  background-color: $bg-card;
  border-right: 1px solid $border-light;
  z-index: 1040;
  transition: $transition-normal;
  display: flex;
  flex-direction: column;
  box-shadow: $shadow-light;

  &.sidebar-collapsed {
    width: 80px;
  }

  &.sidebar-mobile {
    transform: translateX(-100%);
    z-index: 1050;

    &.sidebar-open {
      transform: translateX(0);
    }
  }

  @media (min-width: 768px) {
    position: fixed;
    transform: translateX(0) !important;
  }
}

/* 侧边栏头部 */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid $border-light;
  background-color: rgba(248, 250, 252, 0.6);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 10px;

  &.logo-collapsed {
    justify-content: center;
  }
}

.logo-icon {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, $primary-blue, $dark-blue);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: $shadow-light;

  i {
    color: #ffffff;
    font-size: 20px;
  }
}

.logo-text {
  h1 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: $text-primary;
  }

  p {
    margin: 0;
    font-size: 12px;
    color: $text-secondary;
  }
}

.collapse-btn {
  padding: 6px;
  border-radius: 6px;
  background: transparent;
  border: none;
  transition: $transition-fast;

  &:hover {
    background-color: $bg-secondary;
  }

  i {
    font-size: 16px;
    color: $text-secondary;
  }
}

/* 搜索栏 */
.search-section {
  padding: 12px 16px;

  .search-input {
    ::v-deep .el-input__inner {
      background-color: $bg-primary;
      border-color: #e2e8f0;
      font-size: 14px;

      &:focus {
        border-color: $primary-blue;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
      }
    }
  }
}

/* 导航菜单 */
.nav-menu {
  flex: 1;
  padding: 8px 12px;
  overflow-y: auto;

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
  }
}

.menu-item-wrapper {
  margin-bottom: 2px;
}

.menu-item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  border-radius: 8px;
  background: transparent;
  border: none;
  text-align: left;
  transition: $transition-fast;
  cursor: pointer;
  position: relative;

  &:hover {
    background-color: $bg-secondary;
    transform: translateX(2px);
  }

  &.menu-item-active {
    background-color: #dbeafe;
    color: $dark-blue;

    .menu-icon {
      color: $primary-blue;
    }
  }

  &.menu-item-collapsed {
    justify-content: center;
    padding: 10px 8px;
  }
}

.menu-item-content {
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 0;
}

.menu-icon {
  font-size: 18px;
  color: $text-secondary;
  flex-shrink: 0;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
}

.menu-extras {
  display: flex;
  align-items: center;
  gap: 8px;
}

.menu-badge {
  padding: 2px 6px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 10px;
  background-color: $warning-orange;
  color: #ffffff;
}

.menu-arrow {
  font-size: 12px;
  color: $text-light;
  transition: transform 0.2s ease;

  &.menu-arrow-expanded {
    transform: rotate(90deg);
  }
}

/* 折叠状态样式 */
.collapsed-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: $warning-orange;
  border: 2px solid #ffffff;

  span {
    font-size: 10px;
    font-weight: 500;
    color: #ffffff;
  }
}

.collapsed-tooltip {
  position: absolute;
  left: 100%;
  margin-left: 8px;
  padding: 8px 12px;
  background-color: $text-primary;
  color: #ffffff;
  font-size: 12px;
  border-radius: 6px;
  opacity: 0;
  visibility: hidden;
  transition: $transition-fast;
  white-space: nowrap;
  z-index: 1050;

  .tooltip-badge {
    margin-left: 6px;
    padding: 1px 4px;
    background-color: #374151;
    border-radius: 8px;
    font-size: 10px;
  }

  .tooltip-arrow {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%) translateX(-50%) rotate(45deg);
    width: 6px;
    height: 6px;
    background-color: $text-primary;
  }
}

.menu-item:hover .collapsed-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 子菜单样式 */
.submenu {
  margin-top: 4px;
  margin-left: 16px;
  overflow: hidden;
}

.submenu-item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border-radius: 6px;
  background: transparent;
  border: none;
  text-align: left;
  transition: $transition-fast;
  cursor: pointer;
  margin-bottom: 2px;

  &:hover {
    background-color: $bg-primary;
  }

  &.submenu-item-active {
    background-color: #e0f2fe;
    color: #0369a1;

    .submenu-icon {
      color: #0284c7;
    }
  }
}

.submenu-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.submenu-icon {
  font-size: 14px;
  color: $text-light;
  width: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submenu-label {
  font-size: 13px;
  color: $text-secondary;
}

.submenu-badge {
  padding: 1px 4px;
  font-size: 10px;
  font-weight: 500;
  border-radius: 8px;
  background-color: #fef3c7;
  color: #d97706;
}

/* 子菜单动画 */
.submenu-slide-enter-active,
.submenu-slide-leave-active {
  transition: all 0.3s ease;
  max-height: 200px;
}

.submenu-slide-enter-from,
.submenu-slide-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

/* 侧边栏底部 */
.sidebar-footer {
  margin-top: auto;
  border-top: 1px solid $border-light;
}

.user-section {
  padding: 12px;
  background-color: rgba(248, 250, 252, 0.3);

  &.user-collapsed {
    display: flex;
    justify-content: center;
    padding: 12px 8px;
  }
}

.user-info {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  background-color: $bg-card;
  transition: $transition-fast;

  &:hover {
    background-color: $bg-primary;
  }
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, $success-green, #059669);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  span {
    color: #ffffff;
    font-weight: 500;
    font-size: 14px;
  }
}

.user-details {
  flex: 1;
  margin-left: 10px;
  min-width: 0;
}

.user-name {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: $text-primary;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  margin: 0;
  font-size: 12px;
  color: $text-secondary;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-status {
  width: 8px;
  height: 8px;
  background-color: $success-green;
  border-radius: 50%;
  margin-left: 8px;
  flex-shrink: 0;
}

.user-collapsed-avatar {
  position: relative;
  display: flex;
  justify-content: center;
}

.user-status-collapsed {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  background-color: $success-green;
  border-radius: 50%;
  border: 2px solid $bg-card;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .seller-sidebar {
    width: 280px;
    z-index: 1050;
  }
}

@media (max-width: 480px) {
  .seller-sidebar {
    width: 260px;
  }

  .sidebar-header {
    padding: 16px;
  }

  .logo-icon {
    width: 32px;
    height: 32px;

    i {
      font-size: 18px;
    }
  }

  .logo-text h1 {
    font-size: 14px;
  }
}
</style>
