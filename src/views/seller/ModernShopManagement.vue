<template>
  <div class="modern-shop-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <div class="header-icon">
            <i class="el-icon-office-building"></i>
          </div>
          <div class="header-text">
            <h1 class="page-title">店铺管理中心</h1>
            <p class="page-subtitle">管理您的农产品店铺信息，优化店铺设置，提升经营效率</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <div class="stats-grid">
        <stat-card
          icon="el-icon-view"
          title="店铺浏览量"
          :value="shopStats.views"
          suffix=""
          change="+156"
          trend="up"
          :loading="statsLoading"
          :animated="true"
        />
        <stat-card
          icon="el-icon-user"
          title="关注用户"
          :value="shopStats.followers"
          suffix=""
          change="+23"
          trend="up"
          :loading="statsLoading"
          :animated="true"
        />
        <stat-card
          icon="el-icon-star-on"
          title="店铺评分"
          :value="shopStats.rating"
          suffix=""
          change="+0.2"
          trend="up"
          :loading="statsLoading"
          :animated="true"
        />
        <stat-card
          icon="el-icon-trophy"
          title="店铺等级"
          :value="shopStats.level"
          suffix=""
          change="+1"
          trend="up"
          :loading="statsLoading"
          :animated="true"
        />
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions-section">
      <div class="section-header">
        <div class="section-title">
          <i class="el-icon-magic-stick"></i>
          <h2>快速操作</h2>
        </div>
      </div>
      
      <div class="actions-grid">
        <quick-action
          icon="el-icon-edit"
          title="编辑店铺信息"
          description="修改店铺基本信息、简介和标签"
          theme="green"
          @click="handleEditShopInfo"
        />
        <quick-action
          icon="el-icon-picture"
          title="店铺装修"
          description="自定义店铺主题和页面布局"
          theme="emerald"
          @click="handleShopDecoration"
        />
        <quick-action
          icon="el-icon-data-analysis"
          title="经营分析"
          description="查看店铺经营数据和分析报告"
          theme="teal"
          @click="handleBusinessAnalysis"
        />
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 加载状态 -->
      <div v-if="dataLoading" class="loading-container" v-loading="dataLoading" element-loading-text="正在加载店铺数据...">
        <div style="height: 200px;"></div>
      </div>

      <!-- 内容区域 -->
      <div v-else class="content-grid">
        <!-- 店铺信息卡片 -->
        <div class="content-section">
          <shop-info-card
            :shop-info="shopInfo"
            @upload-logo="handleUploadLogo"
            @edit-shop="handleEditShopInfo"
            @toggle-status="handleToggleShopStatus"
          />
        </div>

        <!-- 店铺设置卡片 -->
        <div class="content-section">
          <shop-settings-card
            :settings="shopSettings"
            :saving="settingsSaving"
            @setting-change="handleSettingChange"
            @save="handleSaveSettings"
            @reset="handleResetSettings"
          />
        </div>
      </div>
    </div>

    <!-- 店铺装修预览 -->
    <div class="decoration-section">
      <div class="section-header">
        <div class="section-title">
          <i class="el-icon-view"></i>
          <h2>店铺装修预览</h2>
        </div>
        <el-button type="primary" @click="handleShopDecoration">
          <i class="el-icon-edit"></i>
          编辑装修
        </el-button>
      </div>
      
      <div class="decoration-preview">
        <div class="preview-container">
          <div class="preview-header">
            <div class="shop-banner">
              <img :src="shopDecoration.banner || '/images/default-shop-banner.jpg'" alt="店铺横幅" />
              <div class="banner-overlay">
                <h3 class="shop-name">{{ shopInfo.name }}</h3>
                <p class="shop-slogan">{{ shopDecoration.slogan || '优质农产品，健康新生活' }}</p>
              </div>
            </div>
          </div>
          
          <div class="preview-content">
            <div class="theme-preview">
              <div class="theme-color" :style="{ backgroundColor: shopDecoration.primaryColor }"></div>
              <div class="theme-info">
                <span class="theme-name">{{ shopDecoration.themeName || '绿色生态主题' }}</span>
                <span class="theme-desc">{{ shopDecoration.themeDesc || '清新自然的农业主题风格' }}</span>
              </div>
            </div>
            
            <div class="layout-preview">
              <div class="layout-item" v-for="section in shopDecoration.sections" :key="section.id">
                <div class="section-icon">
                  <i :class="section.icon"></i>
                </div>
                <div class="section-info">
                  <span class="section-name">{{ section.name }}</span>
                  <span class="section-status" :class="{ 'enabled': section.enabled }">
                    {{ section.enabled ? '已启用' : '已禁用' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 经营数据概览 -->
    <div class="business-overview">
      <div class="section-header">
        <div class="section-title">
          <i class="el-icon-data-line"></i>
          <h2>经营数据概览</h2>
        </div>
        <el-button @click="handleBusinessAnalysis">
          <i class="el-icon-more"></i>
          查看详情
        </el-button>
      </div>
      
      <div class="overview-grid">
        <div class="overview-card">
          <div class="card-header">
            <i class="el-icon-shopping-cart-2"></i>
            <span>近30天订单</span>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ businessData.orders30d }}</div>
            <div class="metric-change positive">+{{ businessData.ordersChange }}%</div>
          </div>
        </div>
        
        <div class="overview-card">
          <div class="card-header">
            <i class="el-icon-coin"></i>
            <span>近30天销售额</span>
          </div>
          <div class="card-content">
            <div class="metric-value">¥{{ businessData.revenue30d }}</div>
            <div class="metric-change positive">+{{ businessData.revenueChange }}%</div>
          </div>
        </div>
        
        <div class="overview-card">
          <div class="card-header">
            <i class="el-icon-user"></i>
            <span>新增客户</span>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ businessData.newCustomers }}</div>
            <div class="metric-change positive">+{{ businessData.customersChange }}%</div>
          </div>
        </div>
        
        <div class="overview-card">
          <div class="card-header">
            <i class="el-icon-refresh"></i>
            <span>复购率</span>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ businessData.repeatRate }}%</div>
            <div class="metric-change positive">+{{ businessData.repeatChange }}%</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import StatCard from '@/components/seller/StatCard.vue'
import QuickAction from '@/components/seller/QuickAction.vue'
import ShopInfoCard from '@/components/seller/ShopInfoCard.vue'
import ShopSettingsCard from '@/components/seller/ShopSettingsCard.vue'
import { getSellerShop, updateSellerShop } from '@/api/seller-center'

export default {
  name: 'ModernShopManagement',
  components: {
    StatCard,
    QuickAction,
    ShopInfoCard,
    ShopSettingsCard
  },
  data() {
    return {
      statsLoading: false,
      settingsSaving: false,
      dataLoading: true,

      // 店铺统计数据
      shopStats: {
        views: 0,
        followers: 0,
        rating: 0,
        level: 1
      },

      // 店铺基本信息
      shopInfo: {
        id: null,
        name: '',
        logo: '',
        status: 'active',
        isOpen: true,
        ownerName: '',
        phone: '',
        address: '',
        businessHours: '',
        rating: 0,
        createdAt: null,
        description: '',
        tags: []
      },
      
      // 店铺设置
      shopSettings: {
        openTime: '08:00',
        closeTime: '20:00',
        restDays: [],
        deliveryMethods: ['express'],
        minOrderAmount: 50,
        deliveryFee: 10,
        freeDeliveryAmount: 100,
        newOrderNotification: true,
        stockAlertNotification: true,
        reviewNotification: true,
        paymentMethods: ['wechat', 'alipay'],
        autoConfirmDays: 7
      },
      
      // 店铺装修
      shopDecoration: {
        banner: '/images/shop-banner.jpg',
        slogan: '优质农产品，健康新生活',
        themeName: '绿色生态主题',
        themeDesc: '清新自然的农业主题风格',
        primaryColor: '#52c41a',
        sections: [
          { id: 1, name: '店铺横幅', icon: 'el-icon-picture', enabled: true },
          { id: 2, name: '产品展示', icon: 'el-icon-goods', enabled: true },
          { id: 3, name: '店铺公告', icon: 'el-icon-bell', enabled: true },
          { id: 4, name: '客户评价', icon: 'el-icon-star-on', enabled: false },
          { id: 5, name: '联系方式', icon: 'el-icon-phone', enabled: true }
        ]
      },
      
      // 经营数据
      businessData: {
        orders30d: 156,
        ordersChange: 23.5,
        revenue30d: '12,580',
        revenueChange: 18.2,
        newCustomers: 45,
        customersChange: 15.8,
        repeatRate: 68.5,
        repeatChange: 5.2
      }
    }
  },
  mounted() {
    this.loadShopData()
  },
  methods: {
    // 加载店铺数据
    async loadShopData() {
      this.dataLoading = true
      this.statsLoading = true

      try {
        console.log('🏪 开始加载店铺数据...')

        // 调用真实API获取店铺信息
        const response = await getSellerShop()
        console.log('📊 店铺API响应:', response)

        if (response && (response.success || response.code === 0) && response.data) {
          const shopData = response.data

          // 映射店铺基本信息
          this.shopInfo = {
            id: shopData.id,
            name: shopData.shopName || '未设置店铺名称',
            logo: shopData.shopLogo || '',
            status: shopData.status === 1 ? 'active' : 'inactive',
            isOpen: shopData.status === 1,
            ownerName: shopData.ownerName || '店主',
            phone: shopData.contactPhone || '未设置',
            address: shopData.contactAddress || '未设置',
            businessHours: shopData.businessHours || '未设置',
            rating: parseFloat(shopData.serviceRating) || 0,
            createdAt: shopData.createdAt ? new Date(shopData.createdAt) : null,
            description: shopData.shopDescription || '暂无店铺简介',
            tags: []
          }

          // 映射店铺统计数据
          this.shopStats = {
            views: 0, // 暂时使用默认值，后续可从统计API获取
            followers: 0,
            rating: parseFloat(shopData.serviceRating) || 0,
            level: 1
          }

          // 解析营业时间设置
          if (shopData.businessHours) {
            const hours = shopData.businessHours.split(' - ')
            if (hours.length === 2) {
              this.shopSettings.openTime = hours[0]
              this.shopSettings.closeTime = hours[1]
            }
          }

          console.log('✅ 店铺数据加载成功:', this.shopInfo)
        } else {
          console.warn('⚠️ 店铺数据格式异常:', response)
          this.$message.warning('店铺数据加载异常')
        }

      } catch (error) {
        console.error('❌ 加载店铺数据失败:', error)
        this.$message.error('加载店铺数据失败: ' + (error.message || '网络错误'))
      } finally {
        this.dataLoading = false
        this.statsLoading = false
      }
    },
    
    // 上传店铺Logo
    handleUploadLogo() {
      this.$message.info('上传店铺Logo功能')
    },
    
    // 编辑店铺信息
    handleEditShopInfo() {
      this.$prompt('请输入新的店铺名称', '编辑店铺信息', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: this.shopInfo.name,
        inputValidator: (value) => {
          if (!value || value.trim().length === 0) {
            return '店铺名称不能为空'
          }
          if (value.length > 50) {
            return '店铺名称不能超过50个字符'
          }
          return true
        }
      }).then(({ value }) => {
        this.updateShopInfo({ shopName: value.trim() })
      }).catch(() => {
        // 用户取消
      })
    },
    
    // 切换店铺状态
    async handleToggleShopStatus(status) {
      try {
        const statusValue = status ? 1 : 0
        await this.updateShopInfo({ status: statusValue })
        this.shopInfo.isOpen = status
        this.shopInfo.status = status ? 'active' : 'inactive'
        this.$message.success(status ? '店铺已开启营业' : '店铺已暂停营业')
      } catch (error) {
        console.error('切换店铺状态失败:', error)
        this.$message.error('操作失败，请重试')
        // 恢复原状态
        this.shopInfo.isOpen = !status
      }
    },

    // 更新店铺信息
    async updateShopInfo(updateData) {
      try {
        console.log('🔄 更新店铺信息:', updateData)

        const response = await updateSellerShop(updateData)
        console.log('📝 更新响应:', response)

        if (response && (response.success || response.code === 0)) {
          this.$message.success('店铺信息更新成功')
          // 重新加载店铺数据
          await this.loadShopData()
          return true
        } else {
          throw new Error(response?.message || '更新失败')
        }
      } catch (error) {
        console.error('❌ 更新店铺信息失败:', error)
        this.$message.error('更新失败: ' + (error.message || '网络错误'))
        throw error
      }
    },
    
    // 店铺装修
    handleShopDecoration() {
      this.$message.info('店铺装修功能')
    },
    
    // 经营分析
    handleBusinessAnalysis() {
      this.$router.push('/seller/analytics')
    },
    
    // 设置变更
    handleSettingChange(settings) {
      console.log('设置变更:', settings)
    },
    
    // 保存设置
    async handleSaveSettings(_settings) {
      this.settingsSaving = true
      try {
        console.log('💾 保存店铺设置:', this.shopSettings)

        // 构建营业时间字符串
        const businessHours = `${this.shopSettings.openTime} - ${this.shopSettings.closeTime}`

        // 准备更新数据
        const updateData = {
          businessHours: businessHours
        }

        await this.updateShopInfo(updateData)
        this.$message.success('店铺设置保存成功')
      } catch (error) {
        console.error('❌ 保存设置失败:', error)
        this.$message.error('保存设置失败: ' + (error.message || '网络错误'))
      } finally {
        this.settingsSaving = false
      }
    },
    
    // 重置设置
    handleResetSettings() {
      this.$confirm('确定要重置所有设置吗？', '重置设置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重置为默认设置
        this.shopSettings = {
          openTime: '08:00',
          closeTime: '20:00',
          restDays: [],
          deliveryMethods: ['express'],
          minOrderAmount: 0,
          deliveryFee: 0,
          freeDeliveryAmount: 0,
          newOrderNotification: true,
          stockAlertNotification: true,
          reviewNotification: true,
          paymentMethods: ['wechat', 'alipay'],
          autoConfirmDays: 7
        }
        this.$message.success('设置已重置')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/seller-theme.scss';

.modern-shop-management {
  min-height: 100vh;
  background: $bg-primary;
  padding: $spacing-lg;

  @include mobile {
    padding: $spacing-md;
  }
}

// 页面头部
.page-header {
  margin-bottom: $spacing-xl;
  animation: fadeInUp 0.6s ease-out;

  .header-content {
    .header-info {
      display: flex;
      align-items: center;
      gap: $spacing-md;

      .header-icon {
        width: 64px;
        height: 64px;
        background: linear-gradient(135deg, $green-600, $emerald-600);
        border-radius: $radius-xl;
        display: flex;
        align-items: center;
        justify-content: center;
        color: $text-white;
        font-size: 32px;
        box-shadow: $shadow-green;
      }

      .header-text {
        flex: 1;

        .page-title {
          font-size: 32px;
          font-weight: bold;
          color: $text-primary;
          margin: 0 0 $spacing-sm 0;

          @include mobile {
            font-size: 24px;
          }
        }

        .page-subtitle {
          font-size: 16px;
          color: $text-secondary;
          margin: 0;
          line-height: 1.5;

          @include mobile {
            font-size: 14px;
          }
        }
      }
    }
  }
}

// 统计卡片区域
.stats-section {
  margin-bottom: $spacing-xl;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: $spacing-lg;

    @include mobile {
      grid-template-columns: 1fr;
      gap: $spacing-md;
    }
  }
}

// 快速操作区域
.quick-actions-section {
  margin-bottom: $spacing-xl;

  .section-header {
    margin-bottom: $spacing-lg;

    .section-title {
      display: flex;
      align-items: center;
      gap: $spacing-sm;

      i {
        font-size: 20px;
        color: $green-600;
      }

      h2 {
        font-size: 20px;
        font-weight: 600;
        color: $text-primary;
        margin: 0;
      }
    }
  }

  .actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: $spacing-lg;

    @include mobile {
      grid-template-columns: 1fr;
      gap: $spacing-md;
    }
  }
}

// 主要内容区域
.main-content {
  margin-bottom: $spacing-xl;

  .content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-xl;

    @include mobile {
      grid-template-columns: 1fr;
      gap: $spacing-lg;
    }
  }

  .content-section {
    // ShopInfoCard和ShopSettingsCard的样式在各自组件中定义
  }
}

// 店铺装修预览
.decoration-section {
  margin-bottom: $spacing-xl;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;

    .section-title {
      display: flex;
      align-items: center;
      gap: $spacing-sm;

      i {
        font-size: 20px;
        color: $green-600;
      }

      h2 {
        font-size: 20px;
        font-weight: 600;
        color: $text-primary;
        margin: 0;
      }
    }
  }

  .decoration-preview {
    @include card-style;
    padding: 0;
    overflow: hidden;

    .preview-container {
      .preview-header {
        .shop-banner {
          position: relative;
          height: 200px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .banner-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            padding: $spacing-xl;
            color: $text-white;

            .shop-name {
              font-size: 24px;
              font-weight: bold;
              margin: 0 0 $spacing-sm 0;
            }

            .shop-slogan {
              font-size: 16px;
              margin: 0;
              opacity: 0.9;
            }
          }
        }
      }

      .preview-content {
        padding: $spacing-xl;

        .theme-preview {
          display: flex;
          align-items: center;
          gap: $spacing-md;
          margin-bottom: $spacing-lg;
          padding: $spacing-md;
          background: $bg-secondary;
          border-radius: $radius-md;

          .theme-color {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid $border-light;
          }

          .theme-info {
            flex: 1;

            .theme-name {
              display: block;
              font-weight: 600;
              color: $text-primary;
              margin-bottom: $spacing-xs;
            }

            .theme-desc {
              font-size: 14px;
              color: $text-secondary;
            }
          }
        }

        .layout-preview {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: $spacing-md;

          .layout-item {
            display: flex;
            align-items: center;
            gap: $spacing-md;
            padding: $spacing-md;
            background: $green-50;
            border-radius: $radius-md;
            border-left: 4px solid $green-600;

            .section-icon {
              width: 32px;
              height: 32px;
              background: $green-600;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: $text-white;

              i {
                font-size: 16px;
              }
            }

            .section-info {
              flex: 1;

              .section-name {
                display: block;
                font-weight: 500;
                color: $text-primary;
                margin-bottom: $spacing-xs;
              }

              .section-status {
                font-size: 12px;
                color: $text-light;

                &.enabled {
                  color: $green-600;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 经营数据概览
.business-overview {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;

    .section-title {
      display: flex;
      align-items: center;
      gap: $spacing-sm;

      i {
        font-size: 20px;
        color: $green-600;
      }

      h2 {
        font-size: 20px;
        font-weight: 600;
        color: $text-primary;
        margin: 0;
      }
    }
  }

  .overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: $spacing-lg;

    .overview-card {
      @include card-style;
      padding: $spacing-lg;
      transition: $transition-normal;

      &:hover {
        transform: translateY(-2px);
        box-shadow: $shadow-lg;
      }

      .card-header {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        margin-bottom: $spacing-md;

        i {
          font-size: 20px;
          color: $green-600;
        }

        span {
          font-size: 14px;
          color: $text-secondary;
        }
      }

      .card-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        .metric-value {
          font-size: 24px;
          font-weight: bold;
          color: $text-primary;
        }

        .metric-change {
          font-size: 14px;
          font-weight: 500;

          &.positive {
            color: $green-600;
          }

          &.negative {
            color: $error-color;
          }
        }
      }
    }
  }
}

// 动画
.stats-grid .stat-card,
.actions-grid .quick-action,
.overview-grid .overview-card {
  animation: fadeInUp 0.6s ease-out both;

  @for $i from 1 through 8 {
    &:nth-child(#{$i}) {
      animation-delay: #{$i * 0.1}s;
    }
  }
}

// 加载状态样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: $text-secondary;

  p {
    margin-top: 16px;
    font-size: 14px;
  }
}
</style>
