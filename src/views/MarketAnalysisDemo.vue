<template>
  <div class="market-analysis-demo">
    <div class="demo-header">
      <h1>SFAP市场分析界面优化演示</h1>
      <p>展示第一阶段界面优化成果</p>
    </div>

    <!-- 优化前后对比 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="demo-card">
          <div slot="header">
            <span>优化前 - 原始布局</span>
          </div>
          <div class="demo-content">
            <div class="old-layout">
              <div class="old-selection">
                <span>产品选择器（顶部位置）</span>
                <div class="selection-box">类别 | 产品 | 天数</div>
              </div>
              <div class="old-charts">
                <div class="chart-box">图表区域</div>
                <div class="chart-box">分析区域</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="demo-card">
          <div slot="header">
            <span>优化后 - 新布局</span>
          </div>
          <div class="demo-content">
            <div class="new-layout">
              <div class="new-basic-selection">
                <span>基础选择器（简化）</span>
                <div class="selection-box">类别 | 产品 | 分析</div>
              </div>
              <div class="new-prediction-panel">
                <span>预测控制面板（图表附近）</span>
                <div class="panel-box">地区 | 时间 | 模型 | 预测</div>
              </div>
              <div class="new-charts">
                <div class="chart-box">图表区域</div>
                <div class="chart-box">分析区域</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能对比表 -->
    <el-card class="feature-comparison">
      <div slot="header">
        <span>功能对比表</span>
      </div>
      <el-table :data="featureComparison" border>
        <el-table-column prop="feature" label="功能项" width="200"></el-table-column>
        <el-table-column prop="before" label="优化前"></el-table-column>
        <el-table-column prop="after" label="优化后"></el-table-column>
        <el-table-column prop="improvement" label="改进说明"></el-table-column>
      </el-table>
    </el-card>

    <!-- 响应式演示 -->
    <el-card class="responsive-demo">
      <div slot="header">
        <span>响应式设计演示</span>
      </div>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="device-demo desktop">
            <h4>桌面端 (≥1200px)</h4>
            <div class="device-content">
              <div class="demo-selection">基础选择</div>
              <div class="demo-prediction">预测面板</div>
              <div class="demo-charts">
                <div class="demo-chart">图表1</div>
                <div class="demo-chart">图表2</div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="device-demo tablet">
            <h4>平板端 (768px-1199px)</h4>
            <div class="device-content">
              <div class="demo-selection">基础选择</div>
              <div class="demo-prediction">预测面板</div>
              <div class="demo-charts single-column">
                <div class="demo-chart">图表1</div>
                <div class="demo-chart">图表2</div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="device-demo mobile">
            <h4>移动端 (<768px)</h4>
            <div class="device-content">
              <div class="demo-selection">基础选择</div>
              <div class="demo-prediction collapsed">预测面板(可折叠)</div>
              <div class="demo-charts single-column">
                <div class="demo-chart">图表1</div>
                <div class="demo-chart">图表2</div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 操作按钮 -->
    <div class="demo-actions">
      <el-button type="primary" @click="goToMarket">查看实际效果</el-button>
      <el-button @click="showTechnicalDetails">技术实现详情</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MarketAnalysisDemo',
  data() {
    return {
      featureComparison: [
        {
          feature: '选择器位置',
          before: '顶部横幅下方',
          after: '基础选择在顶部，高级选择在图表附近',
          improvement: '形成功能组合，提升用户体验'
        },
        {
          feature: '地区选择',
          before: '无',
          after: '支持省份/城市级别选择',
          improvement: '增加地区维度分析能力'
        },
        {
          feature: '时间范围',
          before: '仅支持预测天数',
          after: '支持日/周/月/季度/年多种范围',
          improvement: '更灵活的时间维度分析'
        },
        {
          feature: '预测模型',
          before: '无模型选择',
          after: '支持RNN/ARIMA/对比模式',
          improvement: '为AI模型预留接口'
        },
        {
          feature: '响应式设计',
          before: '基础响应式',
          after: '针对三种屏幕尺寸优化',
          improvement: '更好的移动端体验'
        },
        {
          feature: '快速预设',
          before: '无',
          after: '短期/中期/长期预设',
          improvement: '提升操作效率'
        }
      ]
    }
  },
  methods: {
    goToMarket() {
      this.$router.push('/market')
    },
    
    showTechnicalDetails() {
      this.$alert(`
技术实现要点：

1. 组件化设计
   - 创建PredictionControlPanel.vue组件
   - 保持组件间松耦合

2. 布局优化
   - 使用CSS Grid和Flexbox
   - 响应式断点设计

3. 功能扩展
   - 地区级联选择器
   - 时间范围多选项
   - 预测模型接口预留

4. 用户体验
   - 折叠/展开功能
   - 快速预设选项
   - 加载状态反馈

5. 兼容性
   - 保持Vue 2 + Element UI架构
   - 遵循SFAP设计规范
      `, '技术实现详情', {
        confirmButtonText: '确定',
        type: 'info'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.market-analysis-demo {
  padding: 20px;
  
  .demo-header {
    text-align: center;
    margin-bottom: 30px;
    
    h1 {
      color: #409eff;
      margin-bottom: 10px;
    }
    
    p {
      color: #606266;
      font-size: 16px;
    }
  }
  
  .demo-card {
    margin-bottom: 20px;
    height: 300px;
    
    .demo-content {
      height: 100%;
    }
  }
  
  .old-layout, .new-layout {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  .old-selection, .new-basic-selection {
    padding: 10px;
    background: #f5f7fa;
    border-radius: 4px;
    text-align: center;
    
    span {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #606266;
    }
  }
  
  .new-prediction-panel {
    padding: 10px;
    background: #e1f3d8;
    border-radius: 4px;
    text-align: center;
    border: 2px solid #67c23a;
    
    span {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #67c23a;
    }
  }
  
  .selection-box, .panel-box {
    padding: 8px;
    background: white;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    font-size: 14px;
  }
  
  .old-charts, .new-charts {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
  }
  
  .chart-box {
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #409eff;
    font-weight: 500;
  }
  
  .feature-comparison {
    margin-bottom: 20px;
  }
  
  .responsive-demo {
    margin-bottom: 20px;
  }
  
  .device-demo {
    text-align: center;
    
    h4 {
      margin-bottom: 15px;
      color: #606266;
    }
    
    .device-content {
      border: 2px solid #dcdfe6;
      border-radius: 8px;
      padding: 10px;
      min-height: 200px;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    
    &.desktop .device-content {
      border-color: #67c23a;
    }
    
    &.tablet .device-content {
      border-color: #e6a23c;
    }
    
    &.mobile .device-content {
      border-color: #f56c6c;
    }
  }
  
  .demo-selection, .demo-prediction {
    padding: 6px;
    background: #f5f7fa;
    border-radius: 4px;
    font-size: 12px;
    
    &.collapsed {
      background: #fdf6ec;
      border: 1px solid #e6a23c;
    }
  }
  
  .demo-charts {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px;
    
    &.single-column {
      grid-template-columns: 1fr;
    }
  }
  
  .demo-chart {
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #409eff;
  }
  
  .demo-actions {
    text-align: center;
    margin-top: 30px;
    
    .el-button {
      margin: 0 10px;
    }
  }
}
</style>
