<template>
  <div class="seller-application-management">
    <el-card>
      <div slot="header" class="card-header">
        <h2>销售者申请管理</h2>
        <div class="stats-summary">
          <div class="stat-item">
            <span class="stat-label">总申请</span>
            <span class="stat-value">{{ total }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">待审核</span>
            <span class="stat-value pending">{{ pendingCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">已通过</span>
            <span class="stat-value approved">{{ approvedCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">已拒绝</span>
            <span class="stat-value rejected">{{ rejectedCount }}</span>
          </div>
        </div>
      </div>
      
      <!-- 筛选条件 -->
      <div class="filter-container">
        <el-input
          v-model="listQuery.keyword"
          placeholder="搜索农场名称、申请人"
          style="width: 200px; margin-right: 10px"
          clearable
          @keyup.enter.native="handleFilter">
          <i slot="prefix" class="el-icon-search"></i>
        </el-input>
        
        <el-select v-model="listQuery.status" placeholder="审核状态" clearable style="width: 120px; margin-right: 10px" @change="handleFilter">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        
        <el-date-picker
          v-model="listQuery.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px; margin-right: 10px"
          @change="handleFilter">
        </el-date-picker>
        
        <el-select v-model="listQuery.farmAreaRange" placeholder="农场面积" clearable style="width: 120px; margin-right: 10px" @change="handleFilter">
          <el-option label="50亩以下" value="0-50"></el-option>
          <el-option label="50-100亩" value="50-100"></el-option>
          <el-option label="100-500亩" value="100-500"></el-option>
          <el-option label="500亩以上" value="500+"></el-option>
        </el-select>
        
        <el-button type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
        <el-button type="success" icon="el-icon-download" @click="handleExport">导出</el-button>
      </div>
      
      <!-- 表格 -->
      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="加载中..."
        border
        fit
        highlight-current-row
        style="width: 100%">
        <el-table-column label="ID" align="center" width="80">
          <template slot-scope="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column>
        
        <el-table-column label="农场/基地名称" min-width="180">
          <template slot-scope="scope">
            {{ scope.row.farmName }}
          </template>
        </el-table-column>
        
        <el-table-column label="申请人" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.name || '未填写' }}
          </template>
        </el-table-column>

        <el-table-column label="联系电话" min-width="130">
          <template slot-scope="scope">
            {{ scope.row.phone || '未填写' }}
          </template>
        </el-table-column>

        <el-table-column label="经营范围" min-width="150">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.description" placement="top" :disabled="!scope.row.description">
              <span>{{ scope.row.description ? (scope.row.description.length > 20 ? scope.row.description.substring(0, 20) + '...' : scope.row.description) : '未填写' }}</span>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column label="农场面积" width="100" align="center">
          <template slot-scope="scope">
            {{ scope.row.farmArea ? scope.row.farmArea + '亩' : '未填写' }}
          </template>
        </el-table-column>
        
        <el-table-column label="申请时间" width="160" align="center">
          <template slot-scope="scope">
            {{ formatDate(scope.row.applyDate) }}
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="120" align="center">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="320" align="center">
          <template slot-scope="scope">
            <!-- 待审核状态：显示一键操作按钮 -->
            <template v-if="scope.row.status === 'pending' || scope.row.status === 0">
              <el-button
                size="mini"
                type="success"
                icon="el-icon-check"
                @click="handleQuickApprove(scope.row)"
                :loading="scope.row.approving">
                通过
              </el-button>
              <el-button
                size="mini"
                type="danger"
                icon="el-icon-close"
                @click="handleQuickReject(scope.row)"
                :loading="scope.row.rejecting">
                拒绝
              </el-button>
              <el-button
                size="mini"
                type="primary"
                @click="handleAudit(scope.row)">
                详细审核
              </el-button>
            </template>

            <!-- 已通过状态：显示查看销售者按钮 -->
            <el-button
              v-if="scope.row.status === 'approved' || scope.row.status === 1"
              size="mini"
              type="success"
              @click="handleViewSeller(scope.row)">
              查看销售者
            </el-button>

            <!-- 通用操作按钮 -->
            <el-button
              size="mini"
              @click="handleDetail(scope.row)">
              详情
            </el-button>

            <el-dropdown trigger="click" @command="(command) => handleMoreActions(command, scope.row)">
              <el-button size="mini" type="text">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="export">导出信息</el-dropdown-item>
                <el-dropdown-item command="contact">联系申请人</el-dropdown-item>
                <el-dropdown-item command="history" v-if="scope.row.status !== 'pending' && scope.row.status !== 0">审核历史</el-dropdown-item>
                <el-dropdown-item command="delete" divided>删除申请</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="listQuery.page"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="listQuery.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </el-card>
    
    <!-- 审核对话框 -->
    <el-dialog title="申请审核" :visible.sync="auditDialogVisible" width="500px" :modal-append-to-body="false">
      <div v-if="currentApplication" class="audit-dialog-content">
        <div class="application-info">
          <div class="info-item">
            <span class="label">申请ID：</span>
            <span>{{ currentApplication.id }}</span>
          </div>
          <div class="info-item">
            <span class="label">农场/基地名称：</span>
            <span>{{ currentApplication.farmName }}</span>
          </div>
          <div class="info-item contact-details">
            <span class="label">联系信息：</span>
            <div class="contact-content">
              <div v-if="currentApplication.contactName">姓名：{{ currentApplication.contactName }}</div>
              <div v-if="currentApplication.contactPhone">电话：{{ currentApplication.contactPhone }}</div>
              <div v-if="currentApplication.contactEmail">邮箱：{{ currentApplication.contactEmail }}</div>
              <div v-if="currentApplication.address">地址：{{ currentApplication.address }}</div>
            </div>
          </div>
          <div class="info-item">
            <span class="label">申请时间：</span>
            <span>{{ formatDate(currentApplication.applyDate) }}</span>
          </div>
          
          <div v-if="currentApplication.qualificationDocs && currentApplication.qualificationDocs.length > 0" class="info-item qualification-docs">
            <div class="label">资质证明：</div>
            <div class="docs-list">
              <div v-for="(doc, index) in currentApplication.qualificationDocs" :key="index" class="doc-item">
                <el-image 
                  :src="doc" 
                  :preview-src-list="currentApplication.qualificationDocs"
                  fit="cover"
                  class="doc-image">
                </el-image>
              </div>
            </div>
          </div>
        </div>
        
        <el-form :model="auditForm" :rules="auditRules" ref="auditForm" label-width="80px" class="audit-form">
          <el-form-item label="审核结果" prop="status">
            <el-radio-group v-model="auditForm.status">
              <el-radio :label="1">通过</el-radio>
              <el-radio :label="2">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="审核意见" prop="auditComment" v-if="auditForm.status === 2">
            <el-input
              type="textarea"
              :rows="3"
              placeholder="请输入拒绝原因"
              v-model="auditForm.auditComment">
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAudit" :loading="auditSubmitting">确认</el-button>
      </div>
    </el-dialog>
    
    <!-- 详情对话框 -->
    <el-dialog title="申请详情" :visible.sync="detailDialogVisible" width="800px" :modal-append-to-body="false">
      <div v-if="currentApplication" class="detail-dialog-content">
        <div class="application-info">
          <div class="info-item">
            <span class="label">申请ID：</span>
            <span>{{ currentApplication.id }}</span>
          </div>
          <div class="info-item">
            <span class="label">农场/基地名称：</span>
            <span>{{ currentApplication.farmName }}</span>
          </div>
          <div class="info-item contact-details">
            <span class="label">联系信息：</span>
            <div class="contact-content">
              <div v-if="currentApplication.contactName">姓名：{{ currentApplication.contactName }}</div>
              <div v-if="currentApplication.contactPhone">电话：{{ currentApplication.contactPhone }}</div>
              <div v-if="currentApplication.contactEmail">邮箱：{{ currentApplication.contactEmail }}</div>
              <div v-if="currentApplication.address">地址：{{ currentApplication.address }}</div>
            </div>
          </div>
          <div class="info-item">
            <span class="label">申请时间：</span>
            <span>{{ formatDate(currentApplication.applyDate) }}</span>
          </div>
          <div class="info-item">
            <span class="label">状态：</span>
            <el-tag :type="getStatusType(currentApplication.status)">
              {{ getStatusText(currentApplication.status) }}
            </el-tag>
          </div>
          
          <div v-if="currentApplication.status === 1 || currentApplication.status === 2" class="info-item">
            <span class="label">审核时间：</span>
            <span>{{ formatDate(currentApplication.auditDate) }}</span>
          </div>
          
          <div v-if="currentApplication.status === 2 && currentApplication.auditComment" class="info-item">
            <span class="label">拒绝原因：</span>
            <span class="audit-comment">{{ currentApplication.auditComment }}</span>
          </div>
          
          <!-- 新增详细信息 -->
          <div v-if="currentApplication.description" class="info-item">
            <span class="label">销售描述：</span>
            <div class="description-content">{{ currentApplication.description }}</div>
          </div>
          
          <div v-if="currentApplication.businessScope" class="info-item">
            <span class="label">经营范围：</span>
            <span>{{ currentApplication.businessScope }}</span>
          </div>
          
          <div v-if="currentApplication.farmArea" class="info-item">
            <span class="label">农场面积：</span>
            <span>{{ currentApplication.farmArea }} 亩</span>
          </div>
          
          <div v-if="currentApplication.establishedYear" class="info-item">
            <span class="label">成立年份：</span>
            <span>{{ currentApplication.establishedYear }}</span>
          </div>
          
          <div v-if="currentApplication.contactEmail" class="info-item">
            <span class="label">邮箱地址：</span>
            <span>{{ currentApplication.contactEmail }}</span>
          </div>
          
          <div v-if="currentApplication.legalPerson" class="info-item">
            <span class="label">法人代表：</span>
            <span>{{ currentApplication.legalPerson }}</span>
          </div>
          
          <div v-if="currentApplication.registrationNumber" class="info-item">
            <span class="label">注册号：</span>
            <span>{{ currentApplication.registrationNumber }}</span>
          </div>
          
          <div v-if="currentApplication.businessLicense" class="info-item">
            <span class="label">营业执照：</span>
            <el-image 
              :src="currentApplication.businessLicense" 
              :preview-src-list="[currentApplication.businessLicense]"
              fit="cover"
              style="width: 100px; height: 100px; border-radius: 4px;">
            </el-image>
          </div>
          
          <div v-if="currentApplication.businessPlan" class="info-item">
            <span class="label">经营计划：</span>
            <div class="description-content">{{ currentApplication.businessPlan }}</div>
          </div>
          
          <div v-if="currentApplication.expectedRevenue" class="info-item">
            <span class="label">预期年收入：</span>
            <span>{{ currentApplication.expectedRevenue }} 万元</span>
          </div>
          
          <div v-if="currentApplication.mainProducts" class="info-item">
            <span class="label">主要产品：</span>
            <div class="tags-container">
              <el-tag 
                v-for="(product, index) in currentApplication.mainProducts" 
                :key="index" 
                style="margin-right: 8px; margin-bottom: 4px;">
                {{ product }}
              </el-tag>
            </div>
          </div>
          
          <div v-if="currentApplication.certifications && currentApplication.certifications.length > 0" class="info-item">
            <span class="label">认证证书：</span>
            <div class="tags-container">
              <el-tag 
                v-for="(cert, index) in currentApplication.certifications" 
                :key="index" 
                type="success"
                style="margin-right: 8px; margin-bottom: 4px;">
                {{ cert }}
              </el-tag>
            </div>
          </div>
          
          <div v-if="currentApplication.qualificationDocs && currentApplication.qualificationDocs.length > 0" class="info-item qualification-docs">
            <div class="label">资质证明：</div>
            <div class="docs-list">
              <div v-for="(doc, index) in currentApplication.qualificationDocs" :key="index" class="doc-item">
                <el-image 
                  :src="doc" 
                  :preview-src-list="currentApplication.qualificationDocs"
                  fit="cover"
                  class="doc-image">
                </el-image>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button v-if="currentApplication && currentApplication.status !== 1" type="primary" @click="showStatusEditDialog">修改状态</el-button>
      </div>
    </el-dialog>
    
    <!-- 状态修改对话框 -->
    <el-dialog title="修改审核状态" :visible.sync="statusEditDialogVisible" width="400px" :modal-append-to-body="false">
      <el-form :model="statusEditForm" :rules="statusEditRules" ref="statusEditForm" label-width="80px">
        <el-form-item label="当前状态">
          <el-tag :type="getStatusType(currentApplication && currentApplication.status)">{{ getStatusText(currentApplication && currentApplication.status) }}</el-tag>
        </el-form-item>
        
        <el-form-item label="新状态" prop="status">
          <el-radio-group v-model="statusEditForm.status">
            <el-radio :label="0">待审核</el-radio>
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="备注" prop="comment" v-if="statusEditForm.status === 2">
          <el-input
            type="textarea"
            :rows="3"
            placeholder="请输入拒绝原因或备注"
            v-model="statusEditForm.comment">
          </el-input>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="statusEditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitStatusEdit" :loading="statusEditSubmitting">确认修改</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSellerApplicationDetail } from '@/api/admin'
import { getSellerApplicationList, auditSellerApplication } from '@/api/seller'

export default {
  name: 'SellerApplicationManagement',
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 0,
        size: 10,
        status: undefined,
        keyword: '',
        dateRange: null,
        farmAreaRange: undefined
      },
      statusOptions: [
        { label: '待审核', value: 0 },
        { label: '已通过', value: 1 },
        { label: '已拒绝', value: 2 }
      ],
      auditDialogVisible: false,
      detailDialogVisible: false,
      statusEditDialogVisible: false,
      currentApplication: null,
      auditForm: {
        status: 1,
        auditComment: ''
      },
      auditRules: {
        status: [
          { required: true, message: '请选择审核结果', trigger: 'change' }
        ],
        auditComment: [
          { required: true, message: '请输入拒绝原因', trigger: 'blur' }
        ]
      },
      auditSubmitting: false,
      statusEditForm: {
        status: 0,
        comment: ''
      },
      statusEditRules: {
        status: [
          { required: true, message: '请选择新状态', trigger: 'change' }
        ],
        comment: [
          { required: true, message: '请输入拒绝原因', trigger: 'blur' }
        ]
      },
      statusEditSubmitting: false
    }
  },
  computed: {
    pendingCount() {
      return this.list.filter(item => item.status === 'pending' || item.status === 0).length
    },
    approvedCount() {
      return this.list.filter(item => item.status === 'approved' || item.status === 1).length
    },
    rejectedCount() {
      return this.list.filter(item => item.status === 'rejected' || item.status === 2).length
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取申请列表
    async getList() {
      try {
        this.listLoading = true
        const res = await getSellerApplicationList({
          status: this.listQuery.status,
          page: this.listQuery.page,
          size: this.listQuery.size
        })
        
        if (res.code === 0 && res.data) {
          this.list = res.data.items || []
          this.total = res.data.total || 0
        } else {
          this.$message.error(res.msg || '获取申请列表失败')
          this.list = []
          this.total = 0
        }
      } catch (error) {
        console.error('获取申请列表失败', error)
        this.$message.error('获取申请列表失败，请稍后重试')
        this.list = []
        this.total = 0
      } finally {
        this.listLoading = false
      }
    },
    
    // 处理筛选
    handleFilter() {
      this.listQuery.page = 0
      this.getList()
    },
    
    // 处理每页数量变化
    handleSizeChange(val) {
      this.listQuery.size = val
      this.getList()
    },
    
    // 处理页码变化
    handleCurrentChange(val) {
      this.listQuery.page = val - 1
      this.getList()
    },
    
    // 处理审核
    async handleAudit(row) {
      try {
        const res = await getSellerApplicationDetail(row.id)
        if (res.code === 0 && res.data) {
          this.currentApplication = res.data
          this.auditForm = {
            status: 1,
            auditComment: ''
          }
          this.auditDialogVisible = true
        } else {
          this.$message.error(res.msg || '获取申请详情失败')
        }
      } catch (error) {
        console.error('获取申请详情失败', error)
        this.$message.error('获取申请详情失败，请稍后重试')
      }
    },
    
    // 处理查看详情
    async handleDetail(row) {
      try {
        const res = await getSellerApplicationDetail(row.id)
        if (res.code === 0 && res.data) {
          this.currentApplication = res.data
          this.detailDialogVisible = true
        } else {
          this.$message.error(res.msg || '获取申请详情失败')
        }
      } catch (error) {
        console.error('获取申请详情失败', error)
        this.$message.error('获取申请详情失败，请稍后重试')
      }
    },
    
    // 提交审核
    submitAudit() {
      this.$refs.auditForm.validate(async valid => {
        if (!valid) {
          return false
        }
        
        // 如果是拒绝，必须填写拒绝原因
        if (this.auditForm.status === 2 && !this.auditForm.auditComment) {
          this.$message.error('请输入拒绝原因')
          return false
        }
        
        try {
          this.auditSubmitting = true
          const res = await auditSellerApplication(this.currentApplication.id, this.auditForm)
          if (res.code === 0) {
            this.$message.success('审核成功')
            this.auditDialogVisible = false
            this.getList() // 刷新列表
          } else {
            this.$message.error(res.msg || '审核失败')
          }
        } catch (error) {
          console.error('审核失败', error)
          this.$message.error('审核失败，请稍后重试')
        } finally {
          this.auditSubmitting = false
        }
      })
    },

    // 一键通过申请
    async handleQuickApprove(row) {
      try {
        // 确认操作
        await this.$confirm(`确定要通过申请人"${row.name}"的销售者申请吗？`, '确认通过', {
          confirmButtonText: '确定通过',
          cancelButtonText: '取消',
          type: 'success'
        })

        // 设置加载状态
        this.$set(row, 'approving', true)

        // 调用审核API
        const res = await auditSellerApplication(row.id, {
          status: 'approved',
          auditComment: '管理员一键通过'
        })

        if (res.code === 0 || res.code === 200) {
          this.$message.success('申请已通过')
          this.getList() // 刷新列表
        } else {
          this.$message.error(res.msg || '操作失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('一键通过失败', error)
          this.$message.error('操作失败，请稍后重试')
        }
      } finally {
        this.$set(row, 'approving', false)
      }
    },

    // 一键拒绝申请
    async handleQuickReject(row) {
      try {
        // 输入拒绝原因
        const { value: rejectReason } = await this.$prompt('请输入拒绝原因：', '拒绝申请', {
          confirmButtonText: '确定拒绝',
          cancelButtonText: '取消',
          inputType: 'textarea',
          inputPlaceholder: '请输入拒绝原因...',
          inputValidator: (value) => {
            if (!value || value.trim().length === 0) {
              return '请输入拒绝原因'
            }
            if (value.trim().length < 5) {
              return '拒绝原因至少需要5个字符'
            }
            return true
          }
        })

        // 设置加载状态
        this.$set(row, 'rejecting', true)

        // 调用审核API
        const res = await auditSellerApplication(row.id, {
          status: 'rejected',
          auditComment: rejectReason.trim()
        })

        if (res.code === 0 || res.code === 200) {
          this.$message.success('申请已拒绝')
          this.getList() // 刷新列表
        } else {
          this.$message.error(res.msg || '操作失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('一键拒绝失败', error)
          this.$message.error('操作失败，请稍后重试')
        }
      } finally {
        this.$set(row, 'rejecting', false)
      }
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        'pending': 'warning', // 待审核
        'approved': 'success', // 通过
        'rejected': 'danger',  // 拒绝
        // 兼容旧的数字状态码
        0: 'warning', // 待审核
        1: 'success', // 通过
        2: 'danger'   // 拒绝
      }
      return statusMap[status] || 'info'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待审核',
        'approved': '已通过',
        'rejected': '已拒绝',
        // 兼容旧的数字状态码
        0: '待审核',
        1: '已通过',
        2: '已拒绝'
      }
      return statusMap[status] || '未知状态'
    },
    
    // 显示状态编辑对话框
    showStatusEditDialog() {
      this.statusEditForm = {
        status: this.currentApplication.status,
        comment: ''
      }
      this.statusEditDialogVisible = true
    },
    
    // 提交状态修改
    submitStatusEdit() {
      this.$refs.statusEditForm.validate(async valid => {
        if (!valid) {
          return false
        }
        
        // 如果是拒绝，必须填写拒绝原因
        if (this.statusEditForm.status === 2 && !this.statusEditForm.comment) {
          this.$message.error('请输入拒绝原因')
          return false
        }
        
        try {
          this.statusEditSubmitting = true
          const res = await auditSellerApplication(this.currentApplication.id, {
            status: this.statusEditForm.status,
            auditComment: this.statusEditForm.comment
          })
          if (res.code === 0) {
            this.$message.success('状态修改成功')
            this.statusEditDialogVisible = false
            this.detailDialogVisible = false
            this.getList() // 刷新列表
          } else {
            this.$message.error(res.msg || '状态修改失败')
          }
        } catch (error) {
          console.error('状态修改失败', error)
          this.$message.error('状态修改失败，请稍后重试')
        } finally {
          this.statusEditSubmitting = false
        }
      })
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '暂无'
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    
    // 重置筛选条件
    handleReset() {
      this.listQuery = {
        page: 0,
        size: 10,
        status: undefined,
        keyword: '',
        dateRange: null,
        farmAreaRange: undefined
      }
      this.getList()
    },
    
    // 导出申请数据
    handleExport() {
      this.$message.info('导出功能开发中...')
      // TODO: 实现导出功能
    },
    
    // 查看销售者详情
    handleViewSeller(row) {
      // 查看销售者详情
      this.$router.push(`/admin/seller-list?id=${row.sellerId}`)
    },
    handleMoreActions(command, row) {
      switch (command) {
        case 'export':
          this.exportApplicationInfo(row)
          break
        case 'contact':
          this.contactApplicant(row)
          break
        case 'history':
          this.viewAuditHistory(row)
          break
        case 'delete':
          this.deleteApplication(row)
          break
      }
    },
    
    // 导出申请信息
    exportApplicationInfo() {
      this.$message.info('导出申请信息功能开发中...')
      // TODO: 实现单个申请信息导出
    },
    
    // 联系申请人
    contactApplicant(row) {
      const contactInfo = `姓名：${row.contactName}\n电话：${row.contactPhone}\n邮箱：${row.contactEmail}`
      this.$alert(contactInfo, '联系方式', {
        confirmButtonText: '复制电话',
        callback: () => {
          if (row.contactPhone) {
            navigator.clipboard.writeText(row.contactPhone).then(() => {
              this.$message.success('电话号码已复制到剪贴板')
            })
          }
        }
      })
    },
    
    // 查看审核历史
    viewAuditHistory() {
      this.$message.info('审核历史功能开发中...')
      // TODO: 实现审核历史查看
    },
    
    // 删除申请
    deleteApplication() {
      this.$confirm('确定要删除这个申请吗？删除后无法恢复。', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.info('删除功能开发中...')
        // TODO: 实现删除申请功能
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.seller-application-management {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h2 {
      margin: 0;
      font-size: 18px;
      color: #333;
    }
    
    .stats-summary {
      display: flex;
      gap: 20px;
      
      .stat-item {
        text-align: center;
        
        .stat-label {
          display: block;
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
        }
        
        .stat-value {
          display: block;
          font-size: 18px;
          font-weight: bold;
          color: #333;
          
          &.pending {
            color: #e6a23c;
          }
          
          &.approved {
            color: #67c23a;
          }
          
          &.rejected {
            color: #f56c6c;
          }
        }
      }
    }
  }
  
  .filter-container {
    margin-bottom: 20px;
    
    .el-button {
      margin-left: 10px;
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .contact-info {
    font-size: 12px;
    line-height: 1.4;
    
    div {
      margin-bottom: 2px;
      color: #606266;
    }
  }
  
  .audit-dialog-content,
  .detail-dialog-content {
    .application-info {
      margin-bottom: 20px;
      
      .info-item {
        margin-bottom: 15px;
        
        .label {
          font-weight: bold;
          color: #606266;
          display: inline-block;
          width: 100px;
          vertical-align: top;
        }
        
        .audit-comment {
          color: #f56c6c;
        }
        
        &.contact-details {
          .contact-content {
            display: inline-block;
            vertical-align: top;
            
            div {
              margin-bottom: 5px;
              color: #303133;
              font-size: 14px;
            }
          }
        }
        
        .description-content {
          display: inline-block;
          vertical-align: top;
          max-width: 500px;
          line-height: 1.6;
          color: #303133;
          background-color: #f8f9fa;
          padding: 10px;
          border-radius: 4px;
          border-left: 3px solid #409eff;
        }
        
        &.qualification-docs {
          display: flex;
          
          .label {
            margin-bottom: 10px;
          }
          
          .docs-list {
            flex: 1;
            display: flex;
            flex-wrap: wrap;
            
            .doc-item {
              width: 100px;
              height: 100px;
              margin: 0 10px 10px 0;
              
              .doc-image {
                width: 100%;
                height: 100%;
                border: 1px solid #eee;
                border-radius: 4px;
              }
            }
          }
        }
        
        .tags-container {
          display: inline-block;
          vertical-align: top;
          max-width: 500px;
          line-height: 1.6;
        }
      }
    }
  }
}
</style>