<template>
  <div class="traceability-verification" :class="{ 'dark': darkMode }">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="el-icon-finished title-icon"></i>
          溯源验证
        </h1>
        <p class="page-description">验证商品溯源信息的真实性和完整性</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" icon="el-icon-search" @click="showVerifyDialog = true">
          验证溯源码
        </el-button>
        <el-button icon="el-icon-refresh" @click="refreshData">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card" v-for="(stat, index) in verificationStats" :key="stat.id" :style="{ 'animation-delay': (index * 100) + 'ms' }">
        <div class="stat-icon" :class="`stat-${stat.color}`">
          <i :class="stat.icon"></i>
        </div>
        <div class="stat-content">
          <h3 class="stat-value">{{ stat.value }}</h3>
          <p class="stat-label">{{ stat.label }}</p>
          <span class="stat-change" :class="stat.changeType">{{ stat.change }}</span>
        </div>
      </div>
    </div>

    <!-- 快速验证区域 -->
    <div class="quick-verify-section">
      <div class="verify-card">
        <div class="verify-header">
          <h3>快速验证</h3>
          <p>输入溯源码快速验证商品信息</p>
        </div>
        <div class="verify-form">
          <el-input
            v-model="quickVerifyCode"
            placeholder="请输入溯源码"
            size="large"
            class="verify-input"
            @keyup.enter="quickVerify"
          >
            <template slot="prepend">
              <i class="el-icon-postcard"></i>
            </template>
            <el-button slot="append" type="primary" @click="quickVerify" :loading="verifying">
              验证
            </el-button>
          </el-input>
        </div>
        <div v-if="quickVerifyResult" class="verify-result" :class="quickVerifyResult.status">
          <div class="result-header">
            <i :class="quickVerifyResult.icon"></i>
            <span>{{ quickVerifyResult.title }}</span>
          </div>
          <div class="result-content">
            <p>{{ quickVerifyResult.message }}</p>
            <div v-if="quickVerifyResult.details" class="result-details">
              <p><strong>商品名称：</strong>{{ quickVerifyResult.details.productName }}</p>
              <p><strong>生产者：</strong>{{ quickVerifyResult.details.producer }}</p>
              <p><strong>验证时间：</strong>{{ quickVerifyResult.details.verifyTime }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <div class="search-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索验证记录"
          prefix-icon="el-icon-search"
          class="search-input"
          @input="handleSearch"
        ></el-input>
        <el-select v-model="statusFilter" placeholder="验证状态" class="filter-select">
          <el-option label="全部状态" value=""></el-option>
          <el-option label="验证通过" value="passed"></el-option>
          <el-option label="验证失败" value="failed"></el-option>
          <el-option label="待验证" value="pending"></el-option>
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="date-picker"
        >
        </el-date-picker>
      </div>
      <div class="search-right">
        <el-button icon="el-icon-download" @click="exportData">导出报告</el-button>
        <el-button icon="el-icon-pie-chart" @click="showAnalytics">验证分析</el-button>
      </div>
    </div>

    <!-- 验证记录表格 -->
    <div class="table-container">
      <el-table
        :data="filteredVerifications"
        v-loading="loading"
        stripe
        class="verification-table"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="验证ID" width="120" sortable>
          <template slot-scope="scope">
            <el-tag type="info" size="small">VF{{ scope.row.id }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="traceCode" label="溯源码" width="150" sortable></el-table-column>
        <el-table-column prop="productName" label="商品名称" min-width="150" sortable></el-table-column>
        <el-table-column prop="queryType" label="验证类型" width="100">
          <template slot-scope="scope">
            <el-tag size="small">{{ getVerifyTypeLabel(scope.row.queryType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="result" label="验证结果" width="100">
          <template slot-scope="scope">
            <el-tag :type="getResultTagType(scope.row.result)">{{ getResultLabel(scope.row.result) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" sortable>
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">{{ getStatusLabel(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="验证信息" min-width="150"></el-table-column>
        <el-table-column prop="queryTime" label="验证时间" width="120" sortable>
          <template slot-scope="scope">
            {{ formatDate(scope.row.queryTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="viewVerification(scope.row)">查看</el-button>
            <el-button size="mini" @click="reVerify(scope.row)">重新验证</el-button>
            <el-dropdown @command="handleCommand" trigger="click">
              <el-button size="mini">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="{action: 'report', row: scope.row}">
                  生成报告
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'history', row: scope.row}">
                  验证历史
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'delete', row: scope.row}" divided>
                  删除记录
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalVerifications"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 验证对话框 -->
    <el-dialog
      title="溯源码验证"
      :visible.sync="showVerifyDialog"
      width="600px"
    >
      <el-form :model="verifyForm" :rules="verifyRules" ref="verifyForm" label-width="100px">
        <el-form-item label="溯源码" prop="traceCode">
          <el-input v-model="verifyForm.traceCode" placeholder="请输入溯源码"></el-input>
        </el-form-item>
        <el-form-item label="验证类型" prop="verifyType">
          <el-select v-model="verifyForm.verifyType" placeholder="请选择验证类型">
            <el-option label="完整性验证" value="integrity"></el-option>
            <el-option label="真实性验证" value="authenticity"></el-option>
            <el-option label="时效性验证" value="timeliness"></el-option>
            <el-option label="综合验证" value="comprehensive"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="验证说明">
          <el-input v-model="verifyForm.description" type="textarea" :rows="3" placeholder="请输入验证说明（可选）"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showVerifyDialog = false">取消</el-button>
        <el-button type="primary" @click="startVerification" :loading="verifying">开始验证</el-button>
      </div>
    </el-dialog>

    <!-- 验证详情对话框 -->
    <el-dialog
      title="验证详情"
      :visible.sync="showDetailDialog"
      width="800px"
    >
      <div v-if="selectedVerification" class="verification-detail">
        <div class="detail-header">
          <h3>{{ selectedVerification.productName }}</h3>
          <div class="header-tags">
            <el-tag :type="getStatusTagType(selectedVerification.status)">{{ getStatusLabel(selectedVerification.status) }}</el-tag>
            <el-tag>{{ getVerifyTypeLabel(selectedVerification.verifyType) }}</el-tag>
          </div>
        </div>
        
        <el-row :gutter="20" class="detail-content">
          <el-col :span="12">
            <div class="detail-section">
              <h4>基本信息</h4>
              <p><strong>验证ID：</strong>{{ selectedVerification.verifyId }}</p>
              <p><strong>溯源码：</strong>{{ selectedVerification.traceCode }}</p>
              <p><strong>验证类型：</strong>{{ getVerifyTypeLabel(selectedVerification.verifyType) }}</p>
              <p><strong>验证人：</strong>{{ selectedVerification.verifier }}</p>
              <p><strong>验证时间：</strong>{{ formatDate(selectedVerification.verifyTime) }}</p>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-section">
              <h4>验证结果</h4>
              <p><strong>验证状态：</strong>{{ getStatusLabel(selectedVerification.status) }}</p>
              <p><strong>可信度评分：</strong>
                <el-progress :percentage="selectedVerification.score" :color="getScoreColor(selectedVerification.score)"></el-progress>
              </p>
              <p><strong>验证耗时：</strong>{{ selectedVerification.duration }}ms</p>
            </div>
          </el-col>
        </el-row>
        
        <div class="detail-section">
          <h4>验证项目</h4>
          <el-table :data="verificationItems" size="small">
            <el-table-column prop="item" label="验证项" width="150"></el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.status === 'passed' ? 'success' : 'danger'" size="mini">
                  {{ scope.row.status === 'passed' ? '通过' : '失败' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="score" label="得分" width="100">
              <template slot-scope="scope">
                <span :class="scope.row.score >= 80 ? 'score-good' : scope.row.score >= 60 ? 'score-medium' : 'score-poor'">
                  {{ scope.row.score }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="说明"></el-table-column>
          </el-table>
        </div>
        
        <div class="detail-section">
          <h4>验证日志</h4>
          <div class="verification-log">
            <div v-for="(log, index) in verificationLogs" :key="index" class="log-item">
              <div class="log-time">{{ log.time }}</div>
              <div class="log-content">{{ log.content }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 验证分析对话框 -->
    <el-dialog
      title="验证分析报告"
      :visible.sync="showAnalyticsDialog"
      width="900px"
    >
      <div class="analytics-container">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="analytics-card">
              <h4>验证成功率趋势</h4>
              <div class="chart-placeholder">
                <i class="el-icon-data-line"></i>
                <p>验证成功率趋势图</p>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="analytics-card">
              <h4>验证类型分布</h4>
              <div class="chart-placeholder">
                <i class="el-icon-pie-chart"></i>
                <p>验证类型分布图</p>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="analytics-card">
              <h4>可信度评分分布</h4>
              <div class="chart-placeholder">
                <i class="el-icon-data-board"></i>
                <p>可信度评分分布柱状图</p>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  quickVerifyTraceCode,
  getAdminVerificationRecords,
  retryVerification
} from '@/api/traceability'

export default {
  name: 'TraceabilityVerification',
  data() {
    return {
      darkMode: false,
      loading: false,
      verifying: false,
      searchQuery: '',
      statusFilter: '',
      resultFilter: '',
      dateRange: [],
      currentPage: 1,
      pageSize: 20,
      totalVerifications: 0,
      showVerifyDialog: false,
      showDetailDialog: false,
      showAnalyticsDialog: false,
      selectedVerification: null,
      selectedVerifications: [],
      quickVerifyCode: '',
      quickVerifyResult: null,
      
      // 验证统计数据
      verificationStats: [
        { id: 'total', label: '总验证次数', value: '3,456', change: '+23%', changeType: 'positive', icon: 'el-icon-finished', color: 'blue' },
        { id: 'passed', label: '验证通过', value: '3,124', change: '+18%', changeType: 'positive', icon: 'el-icon-check', color: 'green' },
        { id: 'failed', label: '验证失败', value: '298', change: '+12%', changeType: 'negative', icon: 'el-icon-close', color: 'red' },
        { id: 'avg_score', label: '平均可信度', value: '87.5%', change: '+2.3%', changeType: 'positive', icon: 'el-icon-trophy', color: 'orange' }
      ],
      
      // 验证记录数据
      verifications: [],
      
      // 验证项目数据
      verificationItems: [
        { item: '溯源码格式', status: 'passed', score: 100, description: '溯源码格式正确' },
        { item: '数据完整性', status: 'passed', score: 95, description: '溯源数据完整' },
        { item: '时间一致性', status: 'passed', score: 90, description: '时间戳一致' },
        { item: '数字签名', status: 'passed', score: 88, description: '数字签名验证通过' },
        { item: '链条完整性', status: 'passed', score: 92, description: '溯源链条完整' }
      ],
      
      // 验证日志数据
      verificationLogs: [
        { time: '10:30:01', content: '开始验证溯源码 TR20240115001' },
        { time: '10:30:02', content: '验证溯源码格式 - 通过' },
        { time: '10:30:03', content: '检查数据完整性 - 通过' },
        { time: '10:30:04', content: '验证时间一致性 - 通过' },
        { time: '10:30:05', content: '验证数字签名 - 通过' },
        { time: '10:30:06', content: '验证完成，综合评分：95分' }
      ],
      
      // 表单数据
      verifyForm: {
        traceCode: '',
        verifyType: '',
        description: ''
      },
      
      // 表单验证规则
      verifyRules: {
        traceCode: [
          { required: true, message: '请输入溯源码', trigger: 'blur' },
          { min: 5, max: 20, message: '溯源码长度在 5 到 20 个字符', trigger: 'blur' }
        ],
        verifyType: [
          { required: true, message: '请选择验证类型', trigger: 'change' }
        ]
      }
    }
  },
  
  computed: {
    filteredVerifications() {
      let filtered = this.verifications
      
      // 搜索过滤
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(verification => 
          verification.productName.toLowerCase().includes(query) ||
          verification.traceCode.toLowerCase().includes(query) ||
          verification.verifyId.toLowerCase().includes(query)
        )
      }
      
      // 状态过滤
      if (this.statusFilter) {
        filtered = filtered.filter(verification => verification.status === this.statusFilter)
      }
      
      // 日期范围过滤
      if (this.dateRange && this.dateRange.length === 2) {
        const [startDate, endDate] = this.dateRange
        filtered = filtered.filter(verification => {
          const verifyDate = new Date(verification.verifyTime)
          return verifyDate >= startDate && verifyDate <= endDate
        })
      }
      
      return filtered
    }
  },
  
  watch: {
    filteredVerifications(newVal) {
      this.totalVerifications = newVal.length
    }
  },
  
  mounted() {
    this.loadVerifications()
    this.checkDarkMode()
  },
  
  methods: {
    checkDarkMode() {
      this.darkMode = localStorage.getItem('darkMode') === 'true'
    },
    
    async loadVerifications() {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          size: this.pageSize,
          keyword: this.searchQuery,
          status: this.statusFilter,
          result: this.resultFilter
        }

        const response = await getAdminVerificationRecords(params)
        if (response.data.success) {
          this.verifications = response.data.data || []
          this.totalVerifications = response.data.total || 0
        } else {
          this.$message.error(response.data.message || '加载验证记录失败')
        }
      } catch (error) {
        this.$message.error('加载验证记录失败')
      } finally {
        this.loading = false
      }
    },
    
    refreshData() {
      this.loadVerifications()
    },
    
    handleSearch() {
      // 搜索逻辑已在computed中处理
    },
    
    handleSelectionChange(selection) {
      this.selectedVerifications = selection
    },
    
    handleSizeChange(size) {
      this.pageSize = size
      this.loadVerifications()
    },
    
    handleCurrentChange(page) {
      this.currentPage = page
      this.loadVerifications()
    },
    
    async quickVerify() {
      if (!this.quickVerifyCode.trim()) {
        this.$message.warning('请输入溯源码')
        return
      }
      
      this.verifying = true
      try {
        const response = await quickVerifyTraceCode(this.quickVerifyCode)
        if (response.data.success) {
          const data = response.data.data
          if (data.valid) {
            this.quickVerifyResult = {
              status: 'success',
              icon: 'el-icon-success',
              title: '验证通过',
              message: data.message || '该溯源码验证通过，商品信息真实可靠',
              details: {
                productName: data.productName,
                producer: data.producerName,
                farmName: data.farmName,
                sourceType: data.sourceType,
                verifyTime: new Date().toLocaleString('zh-CN')
              }
            }
          } else {
            this.quickVerifyResult = {
              status: 'error',
              icon: 'el-icon-error',
              title: '验证失败',
              message: data.message || '该溯源码验证失败，请检查溯源码是否正确'
            }
          }

          // 刷新验证记录列表
          this.loadVerifications()
        } else {
          this.quickVerifyResult = {
            status: 'error',
            icon: 'el-icon-error',
            title: '验证失败',
            message: response.data.message || '验证过程出错'
          }
        }
      } catch (error) {
        this.quickVerifyResult = {
          status: 'error',
          icon: 'el-icon-error',
          title: '验证失败',
          message: '网络错误，请稍后重试'
        }
      } finally {
        this.verifying = false
      }
    },
    
    startVerification() {
      this.$refs.verifyForm.validate((valid) => {
        if (valid) {
          this.verifying = true
          // 模拟验证过程
          setTimeout(() => {
            this.verifying = false
            this.showVerifyDialog = false
            this.$message.success('验证任务已提交，请稍后查看结果')
            this.loadVerifications()
            this.resetVerifyForm()
          }, 2000)
        }
      })
    },
    
    resetVerifyForm() {
      this.verifyForm = {
        traceCode: '',
        verifyType: '',
        description: ''
      }
      if (this.$refs.verifyForm) {
        this.$refs.verifyForm.resetFields()
      }
    },
    
    viewVerification(verification) {
      this.selectedVerification = verification
      this.showDetailDialog = true
    },
    
    async reVerify(verification) {
      try {
        await this.$confirm(`确定要重新验证 ${verification.traceCode} 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await retryVerification(verification.id)
        if (response.data.success) {
          this.$message.success('重新验证任务已提交')
          this.loadVerifications()
        } else {
          this.$message.error(response.data.message || '重新验证失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('重新验证失败')
        }
      }
    },
    
    handleCommand(command) {
      const { action, row } = command
      if (action === 'report') {
        this.generateReport(row)
      } else if (action === 'history') {
        this.showHistory(row)
      } else if (action === 'delete') {
        this.deleteVerification(row)
      }
    },
    
    generateReport(_verification) {
      this.$message.info('报告生成功能开发中...')
    },
    
    showHistory(_verification) {
      this.$message.info('验证历史功能开发中...')
    },
    
    deleteVerification(verification) {
      this.$confirm(`确定要删除验证记录 ${verification.verifyId} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
        this.loadVerifications()
      })
    },
    
    exportData() {
      this.$message.info('导出功能开发中...')
    },
    
    showAnalytics() {
      this.showAnalyticsDialog = true
    },
    
    getVerifyTypeLabel(type) {
      const labels = {
        'admin_verify': '管理员验证',
        'user_query': '用户查询',
        'system_check': '系统检查',
        'integrity': '完整性验证',
        'authenticity': '真实性验证',
        'timeliness': '时效性验证',
        'comprehensive': '综合验证'
      }
      return labels[type] || type
    },

    getResultLabel(result) {
      const labels = {
        'success': '验证成功',
        'failed': '验证失败'
      }
      return labels[result] || result
    },

    getResultTagType(result) {
      const types = {
        'success': 'success',
        'failed': 'danger'
      }
      return types[result] || ''
    },
    
    getStatusLabel(status) {
      const labels = {
        'passed': '验证通过',
        'failed': '验证失败',
        'pending': '待验证'
      }
      return labels[status] || status
    },
    
    getStatusTagType(status) {
      const types = {
        'passed': 'success',
        'failed': 'danger',
        'pending': 'warning'
      }
      return types[status] || ''
    },
    
    getScoreColor(score) {
      if (score >= 80) return '#67C23A'
      if (score >= 60) return '#E6A23C'
      return '#F56C6C'
    },
    
    formatDate(dateString) {
      return new Date(dateString).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped lang="scss">
.traceability-verification {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
  
  &.dark {
    background: #1a1a1a;
    color: #ffffff;
  }
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-20px);
  opacity: 0;
  animation: slideInFromTop 0.6s ease forwards;
  
  .dark & {
    background: #2d2d2d;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

.header-content {
  .page-title {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 12px;
    
    .dark & {
      color: #ffffff;
    }
    
    .title-icon {
      color: #10b981;
      font-size: 32px;
    }
  }
  
  .page-description {
    margin: 0;
    color: #6b7280;
    font-size: 16px;
    
    .dark & {
      color: #9ca3af;
    }
  }
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: #ffffff;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transform: translateX(-50px);
  opacity: 0;
  animation: slideInFromLeft 0.6s ease forwards;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  .dark & {
    background: #2d2d2d;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  
  &.stat-blue {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: #ffffff;
  }
  
  &.stat-green {
    background: linear-gradient(135deg, #10b981, #059669);
    color: #ffffff;
  }
  
  &.stat-red {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: #ffffff;
  }
  
  &.stat-orange {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: #ffffff;
  }
}

.stat-content {
  flex: 1;
  
  .stat-value {
    margin: 0 0 4px 0;
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    
    .dark & {
      color: #ffffff;
    }
  }
  
  .stat-label {
    margin: 0 0 8px 0;
    color: #6b7280;
    font-size: 14px;
    
    .dark & {
      color: #9ca3af;
    }
  }
  
  .stat-change {
    font-size: 12px;
    font-weight: 500;
    
    &.positive {
      color: #10b981;
    }
    
    &.negative {
      color: #ef4444;
    }
  }
}

.quick-verify-section {
  margin-bottom: 24px;
}

.verify-card {
  background: #ffffff;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .dark & {
    background: #2d2d2d;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

.verify-header {
  text-align: center;
  margin-bottom: 24px;
  
  h3 {
    margin: 0 0 8px 0;
    color: #1f2937;
    font-size: 24px;
    
    .dark & {
      color: #ffffff;
    }
  }
  
  p {
    margin: 0;
    color: #6b7280;
    
    .dark & {
      color: #9ca3af;
    }
  }
}

.verify-form {
  max-width: 600px;
  margin: 0 auto 24px;
}

.verify-input {
  ::v-deep .el-input-group__prepend {
    background: #f8fafc;
    border-color: #e5e7eb;
    
    .dark & {
      background: #374151;
      border-color: #4b5563;
      color: #ffffff;
    }
  }
}

.verify-result {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  border-radius: 8px;
  
  &.success {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    
    .dark & {
      background: #0c4a6e;
      border-color: #0369a1;
    }
  }
  
  &.error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    
    .dark & {
      background: #7f1d1d;
      border-color: #dc2626;
    }
  }
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  
  i {
    font-size: 20px;
    
    &.el-icon-success {
      color: #10b981;
    }
    
    &.el-icon-error {
      color: #ef4444;
    }
  }
  
  span {
    font-weight: 600;
    font-size: 16px;
  }
}

.result-content {
  p {
    margin: 0 0 12px 0;
    color: #6b7280;
    
    .dark & {
      color: #9ca3af;
    }
  }
}

.result-details {
  p {
    margin: 4px 0;
    font-size: 14px;
    
    strong {
      color: #1f2937;
      
      .dark & {
        color: #ffffff;
      }
    }
  }
}

.search-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .dark & {
    background: #2d2d2d;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

.search-left {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-input {
  width: 300px;
}

.filter-select {
  width: 120px;
}

.date-picker {
  width: 240px;
}

.table-container {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  .dark & {
    background: #2d2d2d;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

.verification-table {
  width: 100%;
  
  ::v-deep .el-table__header {
    background: #f8fafc;
    
    .dark & {
      background: #374151;
    }
  }
}

.score-display {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .score-text {
    font-size: 12px;
    font-weight: 600;
  }
}

.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: center;
}

.verification-detail {
  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      color: #1f2937;
      
      .dark & {
        color: #ffffff;
      }
    }
    
    .header-tags {
      display: flex;
      gap: 8px;
    }
  }
  
  .detail-content {
    margin-bottom: 20px;
  }
  
  .detail-section {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 12px 0;
      color: #1f2937;
      font-size: 16px;
      
      .dark & {
        color: #ffffff;
      }
    }
    
    p {
      margin: 8px 0;
      color: #6b7280;
      
      .dark & {
        color: #9ca3af;
      }
      
      strong {
        color: #1f2937;
        
        .dark & {
          color: #ffffff;
        }
      }
    }
  }
}

.score-good {
  color: #10b981;
  font-weight: 600;
}

.score-medium {
  color: #f59e0b;
  font-weight: 600;
}

.score-poor {
  color: #ef4444;
  font-weight: 600;
}

.verification-log {
  max-height: 200px;
  overflow-y: auto;
  background: #f8fafc;
  border-radius: 6px;
  padding: 12px;
  
  .dark & {
    background: #374151;
  }
}

.log-item {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 14px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.log-time {
  color: #6b7280;
  font-family: monospace;
  min-width: 60px;
  
  .dark & {
    color: #9ca3af;
  }
}

.log-content {
  color: #1f2937;
  
  .dark & {
    color: #ffffff;
  }
}

.analytics-container {
  .analytics-card {
    background: #f8fafc;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    
    .dark & {
      background: #374151;
    }
    
    h4 {
      margin: 0 0 16px 0;
      color: #1f2937;
      
      .dark & {
        color: #ffffff;
      }
    }
  }
}

.chart-placeholder {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  
  .dark & {
    color: #9ca3af;
  }
  
  i {
    font-size: 48px;
    margin-bottom: 12px;
  }
  
  p {
    margin: 0;
    font-size: 14px;
  }
}

@keyframes slideInFromTop {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@media (max-width: 768px) {
  .traceability-verification {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .search-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .search-left {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input, .filter-select, .date-picker {
    width: 100%;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .verify-form {
    max-width: 100%;
  }
  
  .verify-result {
    max-width: 100%;
  }
  
  .detail-content {
    .el-col {
      margin-bottom: 20px;
    }
  }
}
</style>