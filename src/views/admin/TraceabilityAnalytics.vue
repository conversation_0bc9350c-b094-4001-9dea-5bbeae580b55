<template>
  <div class="traceability-analytics" :class="{ 'dark': darkMode }">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="el-icon-data-analysis title-icon"></i>
          溯源数据分析
        </h1>
        <p class="page-description">分析溯源系统的使用情况、数据质量和业务趋势</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" icon="el-icon-download" @click="exportReport">
          导出报告
        </el-button>
        <el-button icon="el-icon-refresh" @click="refreshData">
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 统计概览卡片 -->
    <div class="stats-overview">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6" v-for="stat in overviewStats" :key="stat.key">
          <el-card class="stat-card" :class="`stat-${stat.type}`" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <i :class="stat.icon"></i>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ formatNumber(stat.value) }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-change" :class="stat.change >= 0 ? 'positive' : 'negative'">
                  <i :class="stat.change >= 0 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                  {{ Math.abs(stat.change) }}%
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表分析区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 查询趋势图 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card" shadow="hover">
            <div slot="header" class="chart-header">
              <h3>查询趋势分析</h3>
              <el-radio-group v-model="queryTrendPeriod" size="small" @change="updateQueryTrend">
                <el-radio-button label="7d">7天</el-radio-button>
                <el-radio-button label="30d">30天</el-radio-button>
                <el-radio-button label="90d">90天</el-radio-button>
              </el-radio-group>
            </div>
            <div class="chart-container" ref="queryTrendChart" style="height: 300px;"></div>
          </el-card>
        </el-col>

        <!-- 产品类别分布 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card" shadow="hover">
            <div slot="header" class="chart-header">
              <h3>产品类别分布</h3>
              <el-switch
                v-model="showPercentage"
                active-text="百分比"
                inactive-text="数量"
                @change="updateCategoryChart"
              >
              </el-switch>
            </div>
            <div class="chart-container" ref="categoryChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- 地域分布图 -->
        <el-col :xs="24" :lg="16">
          <el-card class="chart-card" shadow="hover">
            <div slot="header" class="chart-header">
              <h3>地域分布热力图</h3>
              <el-select v-model="regionMetric" size="small" @change="updateRegionChart">
                <el-option label="查询次数" value="queries"></el-option>
                <el-option label="溯源记录" value="records"></el-option>
                <el-option label="认证数量" value="certifications"></el-option>
              </el-select>
            </div>
            <div class="chart-container" ref="regionChart" style="height: 400px;"></div>
          </el-card>
        </el-col>

        <!-- 质量评级分布 -->
        <el-col :xs="24" :lg="8">
          <el-card class="chart-card" shadow="hover">
            <div slot="header" class="chart-header">
              <h3>质量评级分布</h3>
            </div>
            <div class="chart-container" ref="qualityChart" style="height: 400px;"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格区域 -->
    <div class="data-tables">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 热门产品 -->
        <el-tab-pane label="热门产品" name="popular">
          <el-table
            :data="popularProducts"
            v-loading="loading.popular"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="rank" label="排名" width="80" align="center">
              <template slot-scope="scope">
                <el-tag :type="getRankType(scope.row.rank)" size="small">
                  {{ scope.row.rank }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="productName" label="产品名称" min-width="150"></el-table-column>
            <el-table-column prop="category" label="类别" width="120"></el-table-column>
            <el-table-column prop="producer" label="生产商" min-width="120"></el-table-column>
            <el-table-column prop="queryCount" label="查询次数" width="100" align="center">
              <template slot-scope="scope">
                <span class="query-count">{{ formatNumber(scope.row.queryCount) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="lastQuery" label="最近查询" width="150">
              <template slot-scope="scope">
                {{ formatDateTime(scope.row.lastQuery) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="viewProductDetail(scope.row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 活跃用户 -->
        <el-tab-pane label="活跃用户" name="users">
          <el-table
            :data="activeUsers"
            v-loading="loading.users"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="userId" label="用户ID" width="100"></el-table-column>
            <el-table-column prop="username" label="用户名" min-width="120"></el-table-column>
            <el-table-column prop="userType" label="用户类型" width="100">
              <template slot-scope="scope">
                <el-tag :type="getUserTypeTag(scope.row.userType)" size="small">
                  {{ getUserTypeText(scope.row.userType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="queryCount" label="查询次数" width="100" align="center"></el-table-column>
            <el-table-column prop="lastActive" label="最后活跃" width="150">
              <template slot-scope="scope">
                {{ formatDateTime(scope.row.lastActive) }}
              </template>
            </el-table-column>
            <el-table-column prop="location" label="地区" min-width="120"></el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 系统性能 -->
        <el-tab-pane label="系统性能" name="performance">
          <div class="performance-metrics">
            <el-row :gutter="20">
              <el-col :xs="24" :md="8">
                <div class="metric-card">
                  <div class="metric-header">
                    <h4>平均响应时间</h4>
                    <el-tag :type="getPerformanceType(performanceData.avgResponseTime)">
                      {{ performanceData.avgResponseTime }}ms
                    </el-tag>
                  </div>
                  <div class="metric-chart" ref="responseTimeChart" style="height: 200px;"></div>
                </div>
              </el-col>
              <el-col :xs="24" :md="8">
                <div class="metric-card">
                  <div class="metric-header">
                    <h4>系统可用性</h4>
                    <el-tag type="success">
                      {{ performanceData.uptime }}%
                    </el-tag>
                  </div>
                  <div class="metric-chart" ref="uptimeChart" style="height: 200px;"></div>
                </div>
              </el-col>
              <el-col :xs="24" :md="8">
                <div class="metric-card">
                  <div class="metric-header">
                    <h4>错误率</h4>
                    <el-tag :type="getErrorRateType(performanceData.errorRate)">
                      {{ performanceData.errorRate }}%
                    </el-tag>
                  </div>
                  <div class="metric-chart" ref="errorRateChart" style="height: 200px;"></div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 导出报告对话框 -->
    <el-dialog
      title="导出分析报告"
      :visible.sync="exportDialogVisible"
      width="500px"
    >
      <el-form :model="exportForm" label-width="100px">
        <el-form-item label="报告类型">
          <el-select v-model="exportForm.type" placeholder="请选择报告类型">
            <el-option label="完整报告" value="full"></el-option>
            <el-option label="查询分析" value="query"></el-option>
            <el-option label="产品分析" value="product"></el-option>
            <el-option label="用户分析" value="user"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="exportForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="导出格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio label="pdf">PDF</el-radio>
            <el-radio label="excel">Excel</el-radio>
            <el-radio label="csv">CSV</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="exportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmExport" :loading="exporting">
          {{ exporting ? '导出中...' : '确认导出' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import {
  getAdminTraceabilityStats,
  exportTraceabilityData
} from '@/api/traceability'

export default {
  name: 'TraceabilityAnalytics',
  data() {
    return {
      darkMode: false,
      loading: {
        popular: false,
        users: false
      },
      exporting: false,
      exportDialogVisible: false,
      
      // 图表实例
      charts: {
        queryTrend: null,
        category: null,
        region: null,
        quality: null,
        responseTime: null,
        uptime: null,
        errorRate: null
      },
      
      // 控制参数
      queryTrendPeriod: '30d',
      showPercentage: false,
      regionMetric: 'queries',
      activeTab: 'popular',
      
      // 统计数据
      overviewStats: [
        {
          key: 'totalQueries',
          label: '总查询次数',
          value: 0,
          change: 0,
          icon: 'el-icon-search',
          type: 'primary'
        },
        {
          key: 'totalRecords',
          label: '溯源记录数',
          value: 0,
          change: 0,
          icon: 'el-icon-document',
          type: 'success'
        },
        {
          key: 'activeUsers',
          label: '活跃用户数',
          value: 0,
          change: 0,
          icon: 'el-icon-user',
          type: 'warning'
        },
        {
          key: 'avgResponseTime',
          label: '平均响应时间(ms)',
          value: 0,
          change: 0,
          icon: 'el-icon-timer',
          type: 'info'
        }
      ],
      
      // 表格数据
      popularProducts: [],
      activeUsers: [],
      
      // 性能数据
      performanceData: {
        avgResponseTime: 0,
        uptime: 99.9,
        errorRate: 0.1
      },
      
      // 导出表单
      exportForm: {
        type: 'full',
        dateRange: [],
        format: 'pdf'
      }
    }
  },
  
  mounted() {
    this.initCharts()
    this.loadData()
    this.setupResize()
  },
  
  beforeDestroy() {
    this.destroyCharts()
    window.removeEventListener('resize', this.handleResize)
  },
  
  methods: {
    // 初始化图表
    initCharts() {
      this.$nextTick(() => {
        this.charts.queryTrend = echarts.init(this.$refs.queryTrendChart)
        this.charts.category = echarts.init(this.$refs.categoryChart)
        this.charts.region = echarts.init(this.$refs.regionChart)
        this.charts.quality = echarts.init(this.$refs.qualityChart)
        this.charts.responseTime = echarts.init(this.$refs.responseTimeChart)
        this.charts.uptime = echarts.init(this.$refs.uptimeChart)
        this.charts.errorRate = echarts.init(this.$refs.errorRateChart)
      })
    },
    
    // 销毁图表
    destroyCharts() {
      Object.values(this.charts).forEach(chart => {
        if (chart) {
          chart.dispose()
        }
      })
    },
    
    // 设置响应式
    setupResize() {
      this.handleResize = () => {
        Object.values(this.charts).forEach(chart => {
          if (chart) {
            chart.resize()
          }
        })
      }
      window.addEventListener('resize', this.handleResize)
    },
    
    // 加载数据
    async loadData() {
      try {
        const response = await getAdminTraceabilityStats()
        if (response.code === 200) {
          this.updateOverviewStats(response.data)
          this.updateQueryTrend()
          this.updateCategoryChart()
          this.updateRegionChart()
          this.updateQualityChart()
          this.updatePerformanceCharts()
          this.loadTableData(response.data)
        }
      } catch (error) {
        console.error('加载分析数据失败:', error)
        this.$message.error('加载数据失败')
      }
    },
    
    // 更新概览统计
    updateOverviewStats(data) {
      this.overviewStats.forEach(stat => {
        if (data[stat.key] !== undefined) {
          stat.value = data[stat.key].value || 0
          stat.change = data[stat.key].change || 0
        }
      })
    },
    
    // 更新查询趋势图
    updateQueryTrend() {
      const option = {
        title: {
          text: '查询趋势',
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['查询次数', '新增记录'],
          bottom: 0
        },
        xAxis: {
          type: 'category',
          data: this.generateDateRange(this.queryTrendPeriod)
        },
        yAxis: [
          {
            type: 'value',
            name: '查询次数',
            position: 'left'
          },
          {
            type: 'value',
            name: '新增记录',
            position: 'right'
          }
        ],
        series: [
          {
            name: '查询次数',
            type: 'line',
            data: this.generateMockData(this.queryTrendPeriod, 100, 500),
            smooth: true,
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: '新增记录',
            type: 'bar',
            yAxisIndex: 1,
            data: this.generateMockData(this.queryTrendPeriod, 10, 50),
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      }
      this.charts.queryTrend.setOption(option)
    },
    
    // 更新类别分布图
    updateCategoryChart() {
      const data = [
        { name: '蔬菜', value: 335 },
        { name: '水果', value: 310 },
        { name: '粮食', value: 234 },
        { name: '肉类', value: 135 },
        { name: '水产', value: 154 }
      ]
      
      const option = {
        title: {
          text: '产品类别分布',
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: this.showPercentage ? '{a} <br/>{b}: {d}%' : '{a} <br/>{b}: {c}'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: data.map(item => item.name)
        },
        series: [
          {
            name: '产品类别',
            type: 'pie',
            radius: '50%',
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      this.charts.category.setOption(option)
    },
    
    // 更新地域分布图
    updateRegionChart() {
      // 这里应该使用真实的地图数据
      const option = {
        title: {
          text: '地域分布热力图',
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'item'
        },
        visualMap: {
          min: 0,
          max: 1000,
          left: 'left',
          top: 'bottom',
          text: ['高', '低'],
          calculable: true
        },
        series: [
          {
            name: this.regionMetric,
            type: 'map',
            map: 'china',
            roam: false,
            data: [
              { name: '北京', value: 177 },
              { name: '天津', value: 42 },
              { name: '河北', value: 102 },
              { name: '山西', value: 81 },
              { name: '内蒙古', value: 47 },
              { name: '辽宁', value: 67 },
              { name: '吉林', value: 82 },
              { name: '黑龙江', value: 123 }
            ]
          }
        ]
      }
      this.charts.region.setOption(option)
    },
    
    // 更新质量评级图
    updateQualityChart() {
      const option = {
        title: {
          text: '质量评级分布',
          left: 'center',
          textStyle: {
            fontSize: 14
          }
        },
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '质量评级',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            data: [
              { value: 335, name: '优质', itemStyle: { color: '#67C23A' } },
              { value: 310, name: '良好', itemStyle: { color: '#E6A23C' } },
              { value: 234, name: '合格', itemStyle: { color: '#409EFF' } },
              { value: 135, name: '待改进', itemStyle: { color: '#F56C6C' } }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            labelLine: {
              show: false
            },
            label: {
              show: false,
              position: 'center'
            }
          }
        ]
      }
      this.charts.quality.setOption(option)
    },
    
    // 更新性能图表
    updatePerformanceCharts() {
      // 响应时间图表
      const responseTimeOption = {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [120, 132, 101, 134, 90, 230],
            type: 'line',
            smooth: true,
            itemStyle: {
              color: '#409EFF'
            }
          }
        ]
      }
      this.charts.responseTime.setOption(responseTimeOption)
      
      // 可用性图表
      const uptimeOption = {
        series: [
          {
            type: 'gauge',
            data: [{ value: this.performanceData.uptime, name: '可用性' }],
            detail: {
              formatter: '{value}%'
            }
          }
        ]
      }
      this.charts.uptime.setOption(uptimeOption)
      
      // 错误率图表
      const errorRateOption = {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: this.generateDateRange('7d')
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [0.1, 0.2, 0.05, 0.3, 0.1, 0.15, 0.08],
            type: 'bar',
            itemStyle: {
              color: '#F56C6C'
            }
          }
        ]
      }
      this.charts.errorRate.setOption(errorRateOption)
    },
    
    // 加载表格数据
    loadTableData(_data) {
      this.loading.popular = true
      this.loading.users = true
      
      // 模拟数据加载
      setTimeout(() => {
        this.popularProducts = [
          {
            rank: 1,
            productName: '有机苹果',
            category: '水果',
            producer: '山东果业',
            queryCount: 1234,
            lastQuery: new Date()
          },
          {
            rank: 2,
            productName: '绿色蔬菜',
            category: '蔬菜',
            producer: '绿源农场',
            queryCount: 987,
            lastQuery: new Date()
          }
        ]
        
        this.activeUsers = [
          {
            userId: 1001,
            username: '张三',
            userType: 'consumer',
            queryCount: 45,
            lastActive: new Date(),
            location: '北京市'
          },
          {
            userId: 1002,
            username: '李四',
            userType: 'seller',
            queryCount: 32,
            lastActive: new Date(),
            location: '上海市'
          }
        ]
        
        this.loading.popular = false
        this.loading.users = false
      }, 1000)
    },
    
    // 刷新数据
    refreshData() {
      this.loadData()
      this.$message.success('数据已刷新')
    },
    
    // 导出报告
    exportReport() {
      this.exportDialogVisible = true
    },
    
    // 确认导出
    async confirmExport() {
      this.exporting = true
      try {
        const response = await exportTraceabilityData(this.exportForm)
        if (response.code === 200) {
          this.$message.success('报告导出成功')
          this.exportDialogVisible = false
        }
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      } finally {
        this.exporting = false
      }
    },
    
    // 工具方法
    formatNumber(num) {
      return num.toLocaleString()
    },
    
    formatDateTime(date) {
      return new Date(date).toLocaleString()
    },
    
    generateDateRange(period) {
      const days = period === '7d' ? 7 : period === '30d' ? 30 : 90
      const dates = []
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        dates.push(date.toLocaleDateString())
      }
      return dates
    },
    
    generateMockData(period, min, max) {
      const days = period === '7d' ? 7 : period === '30d' ? 30 : 90
      return Array.from({ length: days }, () => 
        Math.floor(Math.random() * (max - min + 1)) + min
      )
    },
    
    getRankType(rank) {
      if (rank <= 3) return 'success'
      if (rank <= 10) return 'warning'
      return 'info'
    },
    
    getUserTypeTag(type) {
      const tags = {
        consumer: 'primary',
        seller: 'success',
        admin: 'warning'
      }
      return tags[type] || 'info'
    },
    
    getUserTypeText(type) {
      const texts = {
        consumer: '消费者',
        seller: '销售者',
        admin: '管理员'
      }
      return texts[type] || '未知'
    },
    
    getPerformanceType(value) {
      if (value < 200) return 'success'
      if (value < 500) return 'warning'
      return 'danger'
    },
    
    getErrorRateType(rate) {
      if (rate < 1) return 'success'
      if (rate < 5) return 'warning'
      return 'danger'
    },
    
    viewProductDetail(product) {
      this.$router.push(`/admin/products/${product.id}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.traceability-analytics {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
  
  &.dark {
    background-color: #1a1a1a;
    color: #ffffff;
  }
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .dark & {
    background: #2d2d2d;
  }
  
  .header-content {
    .page-title {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      
      .dark & {
        color: #ffffff;
      }
      
      .title-icon {
        margin-right: 8px;
        color: #409EFF;
      }
    }
    
    .page-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
      
      .dark & {
        color: #b0b0b0;
      }
    }
  }
}

.stats-overview {
  margin-bottom: 20px;
  
  .stat-card {
    border: none;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    &.stat-primary {
      background: linear-gradient(135deg, #409EFF, #66b1ff);
      color: white;
    }
    
    &.stat-success {
      background: linear-gradient(135deg, #67C23A, #85ce61);
      color: white;
    }
    
    &.stat-warning {
      background: linear-gradient(135deg, #E6A23C, #ebb563);
      color: white;
    }
    
    &.stat-info {
      background: linear-gradient(135deg, #909399, #a6a9ad);
      color: white;
    }
    
    .stat-content {
      display: flex;
      align-items: center;
      padding: 20px;
      
      .stat-icon {
        font-size: 32px;
        margin-right: 16px;
        opacity: 0.8;
      }
      
      .stat-info {
        flex: 1;
        
        .stat-value {
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 14px;
          opacity: 0.9;
          margin-bottom: 4px;
        }
        
        .stat-change {
          font-size: 12px;
          
          &.positive {
            color: #67C23A;
          }
          
          &.negative {
            color: #F56C6C;
          }
        }
      }
    }
  }
}

.charts-section {
  margin-bottom: 20px;
  
  .chart-card {
    border: none;
    border-radius: 8px;
    
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        margin: 0;
        font-size: 16px;
        color: #303133;
        
        .dark & {
          color: #ffffff;
        }
      }
    }
    
    .chart-container {
      width: 100%;
    }
  }
}

.data-tables {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  
  .dark & {
    background: #2d2d2d;
  }
  
  .query-count {
    font-weight: 600;
    color: #409EFF;
  }
}

.performance-metrics {
  .metric-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    .dark & {
      background: #2d2d2d;
    }
    
    .metric-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h4 {
        margin: 0;
        font-size: 14px;
        color: #303133;
        
        .dark & {
          color: #ffffff;
        }
      }
    }
    
    .metric-chart {
      width: 100%;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .traceability-analytics {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    
    .header-actions {
      width: 100%;
      display: flex;
      gap: 8px;
    }
  }
  
  .stats-overview {
    .el-col {
      margin-bottom: 16px;
    }
  }
}
</style>