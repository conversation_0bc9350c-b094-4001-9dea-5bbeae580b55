<template>
  <div class="traceability-audit">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="el-icon-s-check title-icon"></i>
          溯源审核工作台
        </h1>
        <p class="page-description">审核销售者提交的溯源记录，确保信息准确性和完整性</p>
      </div>
      <div class="header-actions">
        <el-button type="success" icon="el-icon-check" @click="batchApprove" :disabled="selectedRecords.length === 0">
          批量通过
        </el-button>
        <el-button type="danger" icon="el-icon-close" @click="batchReject" :disabled="selectedRecords.length === 0">
          批量拒绝
        </el-button>
        <el-button icon="el-icon-refresh" @click="refreshData">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 审核统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card" v-for="(stat, index) in auditStats" :key="stat.id" :style="{ 'animation-delay': (index * 100) + 'ms' }">
        <div class="stat-icon" :class="`stat-${stat.color}`">
          <i :class="stat.icon"></i>
        </div>
        <div class="stat-content">
          <h3 class="stat-value">{{ stat.value }}</h3>
          <p class="stat-label">{{ stat.label }}</p>
          <div class="stat-trend" :class="stat.trendType">
            <i :class="getTrendIcon(stat.trendType)"></i>
            <span>{{ stat.trend }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索产品名称、销售者或溯源码"
          prefix-icon="el-icon-search"
          class="search-input"
          @input="handleSearch"
        ></el-input>
        <el-select v-model="statusFilter" placeholder="审核状态" class="filter-select" @change="handleSearch">
          <el-option label="全部状态" value=""></el-option>
          <el-option label="待审核" value="1"></el-option>
          <el-option label="已通过" value="2"></el-option>
          <el-option label="已拒绝" value="3"></el-option>
        </el-select>
        <el-select v-model="priorityFilter" placeholder="优先级" class="filter-select" @change="handleSearch">
          <el-option label="全部优先级" value=""></el-option>
          <el-option label="高优先级" value="high"></el-option>
          <el-option label="中优先级" value="medium"></el-option>
          <el-option label="低优先级" value="low"></el-option>
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="date-picker"
          @change="handleSearch"
        >
        </el-date-picker>
      </div>
      <div class="filter-right">
        <el-button icon="el-icon-download" @click="exportAuditData">导出审核数据</el-button>
        <el-button icon="el-icon-setting" @click="showAuditSettings">审核设置</el-button>
      </div>
    </div>

    <!-- 审核记录表格 -->
    <div class="table-container">
      <el-table
        :data="filteredRecords"
        v-loading="loading"
        stripe
        class="audit-table"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="priority" label="优先级" width="100" sortable>
          <template slot-scope="scope">
            <el-tag :type="getPriorityTagType(scope.row.priority)" size="small">
              {{ getPriorityLabel(scope.row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="traceCode" label="溯源码" width="150" sortable>
          <template slot-scope="scope">
            <el-tag type="info" size="small">{{ scope.row.traceCode }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="productName" label="产品名称" min-width="150" sortable>
          <template slot-scope="scope">
            <div class="product-info">
              <img v-if="scope.row.productImage" :src="scope.row.productImage" class="product-image" />
              <span>{{ scope.row.productName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sellerName" label="销售者" width="120"></el-table-column>
        <el-table-column prop="category" label="产品类别" width="100"></el-table-column>
        <el-table-column prop="submitTime" label="提交时间" width="120" sortable>
          <template slot-scope="scope">
            {{ formatDate(scope.row.submitTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small">
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="completeness" label="完整度" width="100">
          <template slot-scope="scope">
            <el-progress 
              :percentage="scope.row.completeness" 
              :color="getCompletenessColor(scope.row.completeness)"
              :stroke-width="6"
              :show-text="false"
            ></el-progress>
            <span class="completeness-text">{{ scope.row.completeness }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <div class="action-buttons">
              <el-button size="mini" type="primary" @click.stop="viewRecord(scope.row)">
                查看详情
              </el-button>
              <el-button 
                size="mini" 
                type="success" 
                @click.stop="approveRecord(scope.row)"
                :disabled="scope.row.status !== 1"
              >
                通过
              </el-button>
              <el-button 
                size="mini" 
                type="danger" 
                @click.stop="rejectRecord(scope.row)"
                :disabled="scope.row.status !== 1"
              >
                拒绝
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalRecords"
      >
      </el-pagination>
    </div>

    <!-- 审核详情对话框 -->
    <el-dialog
      title="审核详情"
      :visible.sync="showAuditDialog"
      width="80%"
      :close-on-click-modal="false"
      :modal="true"
      :append-to-body="true"
      :z-index="3000"
      @close="showAuditDialog = false"
    >
      <div class="audit-detail" v-if="currentRecord">
        <el-tabs v-model="activeTab" type="card">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <div class="basic-info">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-item">
                    <label>产品名称：</label>
                    <span>{{ currentRecord.productName }}</span>
                  </div>
                  <div class="info-item">
                    <label>产品类别：</label>
                    <span>{{ currentRecord.category }}</span>
                  </div>
                  <div class="info-item">
                    <label>生产日期：</label>
                    <span>{{ currentRecord.productionDate }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <label>销售者：</label>
                    <span>{{ currentRecord.sellerName }}</span>
                  </div>
                  <div class="info-item">
                    <label>批次号：</label>
                    <span>{{ currentRecord.batchNumber }}</span>
                  </div>
                  <div class="info-item">
                    <label>提交时间：</label>
                    <span>{{ formatDate(currentRecord.submitTime) }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>

          <!-- 生产信息 -->
          <el-tab-pane label="生产信息" name="production">
            <div class="production-info">
              <el-timeline>
                <el-timeline-item
                  v-for="event in currentRecord.productionEvents"
                  :key="event.id"
                  :timestamp="event.eventTime"
                  placement="top"
                >
                  <el-card>
                    <h4>{{ event.eventName }}</h4>
                    <p>{{ event.description }}</p>
                    <div class="event-details" v-if="event.details">
                      <el-tag v-for="(value, key) in event.details" :key="key" size="small">
                        {{ key }}: {{ value }}
                      </el-tag>
                    </div>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-tab-pane>

          <!-- 认证信息 -->
          <el-tab-pane label="认证信息" name="certification">
            <div class="certification-info">
              <el-row :gutter="20">
                <el-col :span="8" v-for="cert in currentRecord.certifications" :key="cert.id">
                  <el-card class="cert-card" shadow="hover">
                    <h4>{{ cert.name }}</h4>
                    <p><strong>颁发机构：</strong>{{ cert.issuer }}</p>
                    <p><strong>证书编号：</strong>{{ cert.number }}</p>
                    <p><strong>有效期：</strong>{{ cert.issueDate }} - {{ cert.expiryDate }}</p>
                    <el-tag :type="cert.expired ? 'danger' : 'success'" size="small">
                      {{ cert.expired ? '已过期' : '有效' }}
                    </el-tag>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>

          <!-- 审核记录 -->
          <el-tab-pane label="审核记录" name="history">
            <div class="audit-history">
              <el-timeline>
                <el-timeline-item
                  v-for="record in currentRecord.auditHistory"
                  :key="record.id"
                  :timestamp="record.auditTime"
                  :type="getAuditResultType(record.result)"
                >
                  <el-card>
                    <h4>{{ getAuditResultLabel(record.result) }}</h4>
                    <p><strong>审核员：</strong>{{ record.auditorName }}</p>
                    <p v-if="record.comment"><strong>审核意见：</strong>{{ record.comment }}</p>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <span slot="footer" class="dialog-footer">
        <el-button @click="showAuditDialog = false">关闭</el-button>
        <el-button 
          type="success" 
          @click="approveCurrentRecord"
          :disabled="currentRecord && currentRecord.status !== 1"
        >
          通过审核
        </el-button>
        <el-button 
          type="danger" 
          @click="rejectCurrentRecord"
          :disabled="currentRecord && currentRecord.status !== 1"
        >
          拒绝审核
        </el-button>
      </span>
    </el-dialog>

    <!-- 拒绝审核对话框 -->
    <el-dialog
      title="拒绝审核"
      :visible.sync="showRejectDialog"
      width="500px"
      :modal="true"
      :append-to-body="true"
      :close-on-click-modal="true"
      :z-index="3000"
      @close="showRejectDialog = false"
    >
      <el-form :model="rejectForm" label-width="80px">
        <el-form-item label="拒绝原因" required>
          <el-select v-model="rejectForm.reason" placeholder="请选择拒绝原因" style="width: 100%;">
            <el-option label="信息不完整" value="incomplete"></el-option>
            <el-option label="信息不准确" value="inaccurate"></el-option>
            <el-option label="认证过期" value="expired"></el-option>
            <el-option label="格式不规范" value="format"></el-option>
            <el-option label="其他原因" value="other"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="详细说明">
          <el-input
            type="textarea"
            v-model="rejectForm.comment"
            placeholder="请详细说明拒绝原因，以便销售者修改"
            :rows="4"
          ></el-input>
        </el-form-item>
      </el-form>
      
      <span slot="footer" class="dialog-footer">
        <el-button @click="showRejectDialog = false">取消</el-button>
        <el-button type="danger" @click="confirmReject">确认拒绝</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAuditRecords, approveRecord, rejectRecord, batchApproveRecords } from '@/api/traceability'

export default {
  name: 'TraceabilityAudit',
  data() {
    return {
      loading: false,
      searchQuery: '',
      statusFilter: '',
      priorityFilter: '',
      dateRange: [],
      selectedRecords: [],
      currentPage: 1,
      pageSize: 20,
      totalRecords: 0,
      records: [],
      
      // 对话框相关
      showAuditDialog: false,
      showRejectDialog: false,
      currentRecord: null,
      activeTab: 'basic',
      
      // 拒绝表单
      rejectForm: {
        reason: '',
        comment: ''
      },
      
      // 统计数据
      auditStats: [
        {
          id: 1,
          label: '待审核',
          value: 0,
          trend: '+0',
          trendType: 'neutral',
          icon: 'el-icon-time',
          color: 'orange'
        },
        {
          id: 2,
          label: '今日已审核',
          value: 0,
          trend: '+0',
          trendType: 'positive',
          icon: 'el-icon-check',
          color: 'green'
        },
        {
          id: 3,
          label: '通过率',
          value: '0%',
          trend: '+0%',
          trendType: 'neutral',
          icon: 'el-icon-pie-chart',
          color: 'blue'
        },
        {
          id: 4,
          label: '平均处理时间',
          value: '0h',
          trend: '-0h',
          trendType: 'positive',
          icon: 'el-icon-timer',
          color: 'purple'
        }
      ]
    }
  },
  computed: {
    filteredRecords() {
      return this.records
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      await this.loadRecords()
      await this.loadStats()
    },

    async loadRecords() {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          size: this.pageSize,
          keyword: this.searchQuery,
          status: this.statusFilter,
          priority: this.priorityFilter
        }
        
        if (this.dateRange && this.dateRange.length === 2) {
          params.startDate = this.dateRange[0]
          params.endDate = this.dateRange[1]
        }
        
        const result = await getAuditRecords(params)
        if (result.success) {
          this.records = result.data.records || []
          this.totalRecords = result.data.pagination?.total || 0
        } else {
          // 使用模拟数据
          this.loadMockData()
        }
      } catch (error) {
        console.error('加载审核记录失败:', error)
        this.loadMockData()
      } finally {
        this.loading = false
      }
    },

    loadMockData() {
      // 模拟审核记录数据
      this.records = [
        {
          id: 1,
          traceCode: 'TRACE001',
          productName: '有机苹果',
          sellerName: '绿色农场',
          category: '水果',
          submitTime: '2024-03-15T10:30:00',
          status: 1, // 待审核
          priority: 'high',
          completeness: 85,
          productImage: '/static/images/apple.jpg',
          productionDate: '2024-01-15',
          batchNumber: 'A001',
          productionEvents: [
            {
              id: 1,
              eventName: '种植播种',
              eventTime: '2024-01-15T08:00:00',
              description: '使用有机种子进行播种',
              details: {
                '种子品种': '红富士',
                '播种密度': '100株/亩'
              }
            }
          ],
          certifications: [
            {
              id: 1,
              name: '有机产品认证',
              issuer: '中国有机产品认证中心',
              number: 'COPC-2024-001',
              issueDate: '2024-01-01',
              expiryDate: '2025-01-01',
              expired: false
            }
          ],
          auditHistory: [
            {
              id: 1,
              result: 'pending',
              auditorName: '管理员',
              auditTime: '2024-03-15T10:30:00',
              comment: '等待审核'
            }
          ]
        }
      ]
      this.totalRecords = this.records.length
    },

    async loadStats() {
      // 计算统计数据
      const pendingCount = this.records.filter(r => r.status === 1).length
      const approvedToday = this.records.filter(r => r.status === 2).length
      const totalProcessed = this.records.filter(r => r.status !== 1).length
      const approvalRate = totalProcessed > 0 ? Math.round((approvedToday / totalProcessed) * 100) : 0

      this.auditStats[0].value = pendingCount
      this.auditStats[1].value = approvedToday
      this.auditStats[2].value = `${approvalRate}%`
      this.auditStats[3].value = '2.5h'
    },

    refreshData() {
      this.loadData()
    },

    handleSearch() {
      this.currentPage = 1
      this.loadRecords()
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.loadRecords()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.loadRecords()
    },

    handleSelectionChange(selection) {
      this.selectedRecords = selection
    },

    handleRowClick(row) {
      this.viewRecord(row)
    },

    viewRecord(record) {
      this.currentRecord = record
      this.showAuditDialog = true
      this.activeTab = 'basic'
    },

    async approveRecord(record) {
      try {
        await this.$confirm(`确认通过"${record.productName}"的溯源记录审核吗？`, '审核确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const result = await approveRecord(record.id)
        if (result.success) {
          this.$message.success('审核通过成功')
          this.loadRecords()
        } else {
          this.$message.error(result.error || '审核失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('审核失败')
        }
      }
    },

    rejectRecord(record) {
      this.currentRecord = record
      this.rejectForm = {
        reason: '',
        comment: ''
      }
      this.showRejectDialog = true
    },

    async confirmReject() {
      if (!this.rejectForm.reason) {
        this.$message.warning('请选择拒绝原因')
        return
      }

      try {
        const result = await rejectRecord(this.currentRecord.id, this.rejectForm)
        if (result.success) {
          this.$message.success('审核拒绝成功')
          this.showRejectDialog = false
          this.loadRecords()
        } else {
          this.$message.error(result.error || '操作失败')
        }
      } catch (error) {
        this.$message.error('操作失败')
      }
    },

    approveCurrentRecord() {
      this.approveRecord(this.currentRecord)
      this.showAuditDialog = false
    },

    rejectCurrentRecord() {
      this.showAuditDialog = false
      this.rejectRecord(this.currentRecord)
    },

    async batchApprove() {
      const pendingRecords = this.selectedRecords.filter(record => record.status === 1)
      if (pendingRecords.length === 0) {
        this.$message.warning('没有可审核通过的记录')
        return
      }

      try {
        await this.$confirm(`确认批量通过 ${pendingRecords.length} 条记录的审核吗？`, '批量审核确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const recordIds = pendingRecords.map(record => record.id)
        const result = await batchApproveRecords(recordIds)
        if (result.success) {
          this.$message.success(`已批量通过 ${pendingRecords.length} 条记录`)
          this.loadRecords()
        } else {
          this.$message.error(result.error || '批量审核失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('批量审核失败')
        }
      }
    },

    async batchReject() {
      const pendingRecords = this.selectedRecords.filter(record => record.status === 1)
      if (pendingRecords.length === 0) {
        this.$message.warning('没有可拒绝的记录')
        return
      }

      this.$message.info('批量拒绝功能开发中...')
    },

    exportAuditData() {
      this.$message.info('导出功能开发中...')
    },

    showAuditSettings() {
      this.$message.info('审核设置功能开发中...')
    },

    // 工具方法
    getTrendIcon(trendType) {
      const iconMap = {
        positive: 'el-icon-top',
        negative: 'el-icon-bottom',
        neutral: 'el-icon-minus'
      }
      return iconMap[trendType] || 'el-icon-minus'
    },

    getPriorityLabel(priority) {
      const labelMap = {
        high: '高',
        medium: '中',
        low: '低'
      }
      return labelMap[priority] || '中'
    },

    getPriorityTagType(priority) {
      const typeMap = {
        high: 'danger',
        medium: 'warning',
        low: 'info'
      }
      return typeMap[priority] || 'info'
    },

    getStatusLabel(status) {
      const statusMap = {
        1: '待审核',
        2: '已通过',
        3: '已拒绝'
      }
      return statusMap[status] || '未知'
    },

    getStatusTagType(status) {
      const typeMap = {
        1: 'warning',
        2: 'success',
        3: 'danger'
      }
      return typeMap[status] || 'info'
    },

    getCompletenessColor(percentage) {
      if (percentage >= 90) return '#67c23a'
      if (percentage >= 70) return '#e6a23c'
      return '#f56c6c'
    },

    getAuditResultType(result) {
      const typeMap = {
        approved: 'success',
        rejected: 'danger',
        pending: 'warning'
      }
      return typeMap[result] || 'info'
    },

    getAuditResultLabel(result) {
      const labelMap = {
        approved: '审核通过',
        rejected: '审核拒绝',
        pending: '待审核'
      }
      return labelMap[result] || '未知'
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
.traceability-audit {
  padding: 20px;
  min-height: 100vh;
  background: var(--background-color-base);
}

// 页面头部样式
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-20px);
  opacity: 0;
  animation: slideInFromTop 0.6s ease forwards;
}

.header-content {
  .page-title {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 12px;

    .title-icon {
      color: #10b981;
      font-size: 32px;
    }
  }

  .page-description {
    margin: 0;
    color: #6b7280;
    font-size: 16px;
  }
}

.header-actions {
  display: flex;
  gap: 12px;
}

// 统计卡片样式
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: #ffffff;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transform: translateX(-50px);
  opacity: 0;
  animation: slideInFromLeft 0.6s ease forwards;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;

  &.stat-orange {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: #ffffff;
  }

  &.stat-green {
    background: linear-gradient(135deg, #10b981, #059669);
    color: #ffffff;
  }

  &.stat-blue {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: #ffffff;
  }

  &.stat-purple {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: #ffffff;
  }
}

.stat-content {
  flex: 1;

  .stat-value {
    margin: 0 0 4px 0;
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
  }

  .stat-label {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #6b7280;
  }

  .stat-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 500;

    &.positive {
      color: #10b981;
    }

    &.negative {
      color: #ef4444;
    }

    &.neutral {
      color: #6b7280;
    }
  }
}

// 筛选区域样式
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-left {
  display: flex;
  gap: 16px;
  align-items: center;
  flex: 1;
}

.search-input {
  width: 300px;
}

.filter-select {
  width: 150px;
}

.date-picker {
  width: 240px;
}

.filter-right {
  display: flex;
  gap: 12px;
}

// 表格样式
.table-container {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.audit-table {
  width: 100%;

  ::v-deep .el-table__header {
    background: #f8fafc;
  }

  ::v-deep .el-table__row {
    cursor: pointer;

    &:hover {
      background: #f1f5f9;
    }
  }
}

.product-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.product-image {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  object-fit: cover;
}

.completeness-text {
  font-size: 12px;
  color: #6b7280;
  margin-left: 8px;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

// 分页样式
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding: 20px;
}

// 审核详情对话框样式
.audit-detail {
  .basic-info {
    .info-item {
      display: flex;
      margin-bottom: 12px;

      label {
        width: 100px;
        font-weight: 600;
        color: #374151;
      }

      span {
        color: #6b7280;
      }
    }
  }

  .production-info {
    .event-details {
      margin-top: 12px;

      .el-tag {
        margin-right: 8px;
        margin-bottom: 4px;
      }
    }
  }

  .certification-info {
    .cert-card {
      margin-bottom: 16px;

      h4 {
        margin: 0 0 8px 0;
        color: #1f2937;
      }

      p {
        margin: 4px 0;
        font-size: 14px;
        color: #6b7280;
      }
    }
  }
}

// 动画效果
@keyframes slideInFromTop {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .traceability-audit {
    padding: 15px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-end;
  }

  .filter-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filter-left {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input, .filter-select, .date-picker {
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
