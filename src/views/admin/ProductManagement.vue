<template>
  <div class="product-management" :class="{ 'dark': darkMode }">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="el-icon-goods title-icon"></i>
          商品管理
        </h1>
        <p class="page-description">管理系统中的所有商品信息</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" icon="el-icon-plus" @click="showAddDialog = true">
          添加商品
        </el-button>
        <el-button icon="el-icon-refresh" @click="refreshData">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card" v-for="(stat, index) in productStats" :key="stat.id" :style="{ 'animation-delay': (index * 100) + 'ms' }">
        <div class="stat-icon" :class="`stat-${stat.color}`">
          <i :class="stat.icon"></i>
        </div>
        <div class="stat-content">
          <h3 class="stat-value">{{ stat.value }}</h3>
          <p class="stat-label">{{ stat.label }}</p>
          <span class="stat-change" :class="stat.changeType">{{ stat.change }}</span>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <div class="search-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索商品名称、描述、品牌或产地"
          prefix-icon="el-icon-search"
          class="search-input"
          @input="handleSearch"
          clearable
        ></el-input>
        <el-select v-model="categoryFilter" placeholder="商品分类" class="filter-select" clearable>
          <el-option label="全部分类" value=""></el-option>
          <el-option
            v-for="category in categories"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          ></el-option>
        </el-select>
        <el-select v-model="statusFilter" placeholder="商品状态" class="filter-select" clearable>
          <el-option label="全部状态" value=""></el-option>
          <el-option label="上架" value="1"></el-option>
          <el-option label="下架" value="0"></el-option>
        </el-select>
        <el-select v-model="sourceTypeFilter" placeholder="来源类型" class="filter-select" clearable>
          <el-option label="全部来源" value=""></el-option>
          <el-option label="管理员直购" value="admin_direct"></el-option>
          <el-option label="销售者上传" value="seller_upload"></el-option>
        </el-select>
        <el-select v-model="traceabilityFilter" placeholder="溯源状态" class="filter-select" clearable>
          <el-option label="全部商品" value=""></el-option>
          <el-option label="有溯源" value="1"></el-option>
          <el-option label="无溯源" value="0"></el-option>
        </el-select>
      </div>
      <div class="search-right">
        <el-button icon="el-icon-refresh" @click="refreshData">刷新</el-button>
        <el-button icon="el-icon-download" @click="exportData">导出数据</el-button>
        <el-button icon="el-icon-upload2" @click="importData">批量导入</el-button>
      </div>
    </div>

    <!-- 商品表格 -->
    <div class="table-container">
      <el-table
        :data="filteredProducts"
        v-loading="loading"
        stripe
        class="product-table"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="image" label="商品图片" width="100">
          <template slot-scope="scope">
            <el-image
              :src="scope.row.image"
              :alt="scope.row.name"
              class="product-image"
              fit="cover"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="商品名称" sortable min-width="150">
          <template slot-scope="scope">
            <div class="product-name-cell">
              <span class="product-name">{{ scope.row.name }}</span>
              <div class="product-tags">
                <el-tag v-if="scope.row.has_traceability" size="mini" type="success">可溯源</el-tag>
                <el-tag v-if="scope.row.source_type === 'admin_direct'" size="mini" type="primary">直购</el-tag>
                <el-tag v-if="scope.row.is_featured" size="mini" type="warning">推荐</el-tag>
                <el-tag v-if="scope.row.is_hot" size="mini" type="danger">热门</el-tag>
                <el-tag v-if="scope.row.is_new" size="mini" type="info">新品</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="category_name" label="分类" width="100">
          <template slot-scope="scope">
            <el-tag size="small">{{ scope.row.category_name || '未分类' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="brand" label="品牌" width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.brand || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="origin" label="产地" width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.origin || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="价格" sortable width="100">
          <template slot-scope="scope">
            <div class="price-cell">
              <span class="current-price">¥{{ scope.row.price }}</span>
              <span v-if="scope.row.original_price && scope.row.original_price > scope.row.price"
                    class="original-price">¥{{ scope.row.original_price }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="stock" label="库存" sortable width="100">
          <template slot-scope="scope">
            <span :class="getStockClass(scope.row.stock)">{{ scope.row.stock }}{{ scope.row.unit || '' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sales_count" label="销量" sortable width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.sales_count || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">{{ getStatusLabel(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="trace_code" label="溯源码" width="140">
          <template slot-scope="scope">
            <div v-if="scope.row.trace_code && scope.row.trace_code.trim()" class="trace-code-cell">
              <el-tooltip :content="scope.row.trace_code" placement="top">
                <span class="trace-code">{{ scope.row.trace_code.substring(0, 12) }}...</span>
              </el-tooltip>
              <el-button size="mini" type="text" @click="viewTraceability(scope.row)" icon="el-icon-view">
                查看
              </el-button>
            </div>
            <div v-else-if="scope.row.has_traceability" class="trace-code-cell">
              <el-tag size="mini" type="warning">待生成</el-tag>
              <el-button size="mini" type="text" @click="generateTraceCode(scope.row)" icon="el-icon-plus">
                生成
              </el-button>
            </div>
            <span v-else class="no-trace">
              <el-tag size="mini" type="info">无溯源</el-tag>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="seller_name" label="销售者" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.seller_name || '系统' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" sortable width="120">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="viewProduct(scope.row)">查看</el-button>
            <el-button size="mini" @click="editProduct(scope.row)">编辑</el-button>
            <el-dropdown @command="handleCommand" trigger="click">
              <el-button size="mini">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="{action: 'toggle', row: scope.row}">
                  {{ scope.row.status === 'active' ? '下架' : '上架' }}
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'delete', row: scope.row}" divided>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalProducts"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 添加/编辑商品对话框 -->
    <el-dialog
      :title="editingProduct ? '编辑商品' : '添加商品'"
      :visible.sync="showAddDialog"
      width="800px"
      @close="resetForm"
    >
      <el-form :model="productForm" :rules="productRules" ref="productForm" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品名称" prop="name">
              <el-input v-model="productForm.name" placeholder="请输入商品名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品分类" prop="category">
              <el-select v-model="productForm.category" placeholder="请选择分类">
                <el-option label="蔬菜" value="vegetables"></el-option>
                <el-option label="水果" value="fruits"></el-option>
                <el-option label="粮食" value="grains"></el-option>
                <el-option label="肉类" value="meat"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="价格" prop="price">
              <el-input-number v-model="productForm.price" :min="0" :precision="2" placeholder="请输入价格"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="库存" prop="stock">
              <el-input-number v-model="productForm.stock" :min="0" placeholder="请输入库存"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="商品描述" prop="description">
          <el-input v-model="productForm.description" type="textarea" :rows="4" placeholder="请输入商品描述"></el-input>
        </el-form-item>
        <el-form-item label="商品图片">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :http-request="handleImageUpload"
          >
            <img v-if="productForm.image" :src="productForm.image" class="uploaded-image">
            <i v-else class="el-icon-plus image-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="商品状态" prop="status">
          <el-radio-group v-model="productForm.status">
            <el-radio label="active">上架</el-radio>
            <el-radio label="inactive">下架</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 溯源信息部分（管理员直购产品必填） -->
        <el-divider content-position="left">
          <span style="color: #409EFF; font-weight: bold;">
            <i class="el-icon-s-check"></i> 溯源信息（产品直购必填）
          </span>
        </el-divider>

        <el-form-item label="农场名称" prop="farmName">
          <el-input v-model="productForm.farmName" placeholder="请输入农场名称"></el-input>
        </el-form-item>

        <el-form-item label="生产者" prop="producerName">
          <el-input v-model="productForm.producerName" placeholder="请输入生产者姓名"></el-input>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产地" prop="origin">
              <el-input v-model="productForm.origin" placeholder="请输入产地"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="品牌" prop="brand">
              <el-input v-model="productForm.brand" placeholder="请输入品牌"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 生产信息 -->
        <el-form-item label="生产信息">
          <el-card class="trace-info-card">
            <div slot="header">
              <span>生产环节信息</span>
            </div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="种植日期">
                  <el-date-picker
                    v-model="productForm.traceability.production.plantDate"
                    type="date"
                    placeholder="选择种植日期"
                    style="width: 100%">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="收获日期">
                  <el-date-picker
                    v-model="productForm.traceability.production.harvestDate"
                    type="date"
                    placeholder="选择收获日期"
                    style="width: 100%">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="种植方式">
              <el-input v-model="productForm.traceability.production.method" placeholder="请输入种植方式"></el-input>
            </el-form-item>
            <el-form-item label="使用肥料">
              <el-input v-model="productForm.traceability.production.fertilizer" placeholder="请输入使用的肥料"></el-input>
            </el-form-item>
          </el-card>
        </el-form-item>

        <!-- 加工信息 -->
        <el-form-item label="加工信息">
          <el-card class="trace-info-card">
            <div slot="header">
              <span>加工环节信息</span>
            </div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="加工日期">
                  <el-date-picker
                    v-model="productForm.traceability.processing.processDate"
                    type="date"
                    placeholder="选择加工日期"
                    style="width: 100%">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="加工厂商">
                  <el-input v-model="productForm.traceability.processing.processor" placeholder="请输入加工厂商"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="加工工艺">
              <el-input v-model="productForm.traceability.processing.process" placeholder="请输入加工工艺"></el-input>
            </el-form-item>
          </el-card>
        </el-form-item>

        <!-- 流通信息 -->
        <el-form-item label="流通信息">
          <el-card class="trace-info-card">
            <div slot="header">
              <span>流通环节信息</span>
            </div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="包装日期">
                  <el-date-picker
                    v-model="productForm.traceability.circulation.packDate"
                    type="date"
                    placeholder="选择包装日期"
                    style="width: 100%">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="运输方式">
                  <el-input v-model="productForm.traceability.circulation.transport" placeholder="请输入运输方式"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="储存条件">
              <el-input v-model="productForm.traceability.circulation.storage" placeholder="请输入储存条件"></el-input>
            </el-form-item>
          </el-card>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveProduct" :loading="saving">确定</el-button>
      </div>
    </el-dialog>

    <!-- 商品详情对话框 -->
    <el-dialog
      title="商品详情"
      :visible.sync="showDetailDialog"
      width="600px"
    >
      <div v-if="selectedProduct" class="product-detail">
        <div class="detail-image">
          <el-image :src="selectedProduct.image" fit="cover">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </div>
        <div class="detail-info">
          <h3>{{ selectedProduct.name }}</h3>
          <p><strong>分类：</strong>{{ getCategoryLabel(selectedProduct.category) }}</p>
          <p><strong>价格：</strong>¥{{ selectedProduct.price }}</p>
          <p><strong>库存：</strong>{{ selectedProduct.stock }}</p>
          <p><strong>销量：</strong>{{ selectedProduct.sales }}</p>
          <p><strong>状态：</strong>{{ getStatusLabel(selectedProduct.status) }}</p>
          <p><strong>销售者：</strong>{{ selectedProduct.seller }}</p>
          <p><strong>描述：</strong>{{ selectedProduct.description }}</p>
          <p><strong>创建时间：</strong>{{ formatDate(selectedProduct.createdAt) }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getProductsForAdmin,
  createProductByAdmin,
  updateProductByAdmin,
  deleteProductByAdmin,
  batchUpdateProductStatus,
  getProductStatsForAdmin,
  getCategoriesForAdmin,
  getSellersForAdmin,
  addTraceabilityInfoByAdmin,
  generateTraceCodeByAdmin
} from '@/api/adminProducts'

export default {
  name: 'ProductManagement',
  data() {
    return {
      darkMode: false,
      loading: false,
      saving: false,
      searchQuery: '',
      categoryFilter: '',
      statusFilter: '',
      sourceTypeFilter: '',
      traceabilityFilter: '',
      currentPage: 1,
      pageSize: 20,
      totalProducts: 0,
      showAddDialog: false,
      showDetailDialog: false,
      editingProduct: null,
      selectedProduct: null,
      selectedProducts: [],
      
      // 商品统计数据
      productStats: [
        { id: 'total', label: '商品总数', value: 0, change: '+0%', changeType: 'positive', icon: 'el-icon-goods', color: 'blue' },
        { id: 'active', label: '在售商品', value: 0, change: '+0%', changeType: 'positive', icon: 'el-icon-check', color: 'green' },
        { id: 'low_stock', label: '库存不足', value: 0, change: '+0%', changeType: 'negative', icon: 'el-icon-warning', color: 'orange' },
        { id: 'out_of_stock', label: '缺货商品', value: 0, change: '+0%', changeType: 'positive', icon: 'el-icon-close', color: 'red' }
      ],

      // 商品列表数据（从API获取）
      products: [],

      // 分类和销售者选项
      categories: [],
      sellers: [],
      
      // 表单数据
      productForm: {
        name: '',
        category: '',
        price: 0,
        stock: 0,
        description: '',
        image: '',
        status: 'active',
        // 溯源信息（管理员直购产品必填）
        farmName: '',
        producerName: '',
        origin: '',
        brand: '',
        traceability: {
          production: {
            plantDate: null,
            harvestDate: null,
            method: '',
            fertilizer: ''
          },
          processing: {
            processDate: null,
            processor: '',
            process: ''
          },
          circulation: {
            packDate: null,
            transport: '',
            storage: ''
          }
        }
      },
      
      // 表单验证规则
      productRules: {
        name: [
          { required: true, message: '请输入商品名称', trigger: 'blur' },
          { min: 2, max: 50, message: '商品名称长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择商品分类', trigger: 'change' }
        ],
        price: [
          { required: true, message: '请输入商品价格', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '价格必须大于0', trigger: 'blur' }
        ],
        stock: [
          { required: true, message: '请输入库存数量', trigger: 'blur' },
          { type: 'number', min: 0, message: '库存不能为负数', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入商品描述', trigger: 'blur' }
        ],
        // 溯源信息验证规则（管理员直购产品必填）
        farmName: [
          { required: true, message: '请输入农场名称', trigger: 'blur' }
        ],
        producerName: [
          { required: true, message: '请输入生产者姓名', trigger: 'blur' }
        ],
        origin: [
          { required: true, message: '请输入产地', trigger: 'blur' }
        ]
      }
    }
  },
  
  computed: {
    filteredProducts() {
      // 由于我们现在从API获取数据，这里直接返回products
      // 筛选逻辑已经在API调用中处理
      return this.products
    }
  },

  watch: {
    // 监听筛选条件变化，重新加载数据
    searchQuery() {
      this.handleSearch()
    },
    categoryFilter() {
      this.currentPage = 1
      this.loadProducts()
    },
    statusFilter() {
      this.currentPage = 1
      this.loadProducts()
    }
  },
  
  mounted() {
    console.log('🚀 管理员商品管理页面初始化...')
    this.checkDarkMode()
    this.loadProducts()
    this.loadProductStats()
    this.loadCategories()
    this.loadSellers()
  },
  
  methods: {
    checkDarkMode() {
      this.darkMode = localStorage.getItem('darkMode') === 'true'
    },
    
    async loadProducts() {
      this.loading = true
      try {
        console.log('🔄 开始加载商品列表...')

        const params = {
          page: this.currentPage,
          size: this.pageSize,
          keyword: this.searchQuery || undefined,
          categoryId: this.categoryFilter || undefined,
          status: this.statusFilter || undefined,
          sourceType: this.sourceTypeFilter || undefined,
          hasTraceability: this.traceabilityFilter || undefined,
          sortBy: 'created_at',
          sortOrder: 'desc'
        }

        const response = await getProductsForAdmin(params)
        console.log('📊 商品列表API响应:', response)

        if (response && response.code === 200 && response.data) {
          const rawProducts = response.data.records || []
          this.totalProducts = response.data.total || 0

          // 数据转换和调试
          this.products = rawProducts.map((product, index) => {
            // 调试前几个商品的数据结构
            if (index < 3) {
              console.log(`🔍 商品 ${index + 1} 原始数据:`, product)
              console.log(`  - has_traceability: ${product.has_traceability}`)
              console.log(`  - trace_code: ${product.trace_code}`)
              console.log(`  - source_type: ${product.source_type}`)
              console.log(`  - category_id: ${product.category_id}`)
              console.log(`  - is_featured: ${product.is_featured}`)
              console.log(`  - is_hot: ${product.is_hot}`)
              console.log(`  - is_new: ${product.is_new}`)
            }

            return {
              ...product,
              // 确保布尔值正确转换
              has_traceability: Boolean(product.has_traceability),
              is_featured: Boolean(product.is_featured),
              is_hot: Boolean(product.is_hot),
              is_new: Boolean(product.is_new),
              // 确保分类名称显示
              category_name: product.category_name || this.getCategoryNameById(product.category_id) || '未分类'
            }
          })

          console.log(`✅ 商品列表加载成功，共 ${this.totalProducts} 条数据`)
          console.log('🔍 处理后的前3个商品:', this.products.slice(0, 3))
        } else {
          console.error('❌ 商品列表API响应格式错误:', response)
          this.$message.error('获取商品列表失败')
          this.products = []
          this.totalProducts = 0
        }
      } catch (error) {
        console.error('❌ 加载商品列表失败:', error)
        this.$message.error('加载商品列表失败: ' + error.message)
        this.products = []
        this.totalProducts = 0
      } finally {
        this.loading = false
      }
    },

    async loadProductStats() {
      try {
        console.log('📊 开始加载商品统计信息...')

        const response = await getProductStatsForAdmin()
        console.log('📈 商品统计API响应:', response)

        if (response && response.code === 200 && response.data) {
          const stats = response.data

          // 更新统计数据
          this.productStats[0].value = stats.totalProducts || 0
          this.productStats[1].value = stats.activeProducts || 0
          this.productStats[2].value = stats.lowStockProducts || 0
          this.productStats[3].value = stats.inactiveProducts || 0

          console.log('✅ 商品统计信息加载成功')
        } else {
          console.error('❌ 商品统计API响应格式错误:', response)
        }
      } catch (error) {
        console.error('❌ 加载商品统计信息失败:', error)
      }
    },

    async loadCategories() {
      try {
        const response = await getCategoriesForAdmin()
        console.log('📂 分类API响应:', response)
        if (response && response.code === 200 && response.data) {
          this.categories = response.data
        }
      } catch (error) {
        console.error('❌ 加载分类列表失败:', error)
      }
    },

    async loadSellers() {
      try {
        const response = await getSellersForAdmin()
        console.log('👥 销售者API响应:', response)
        if (response && response.success && response.data && response.data.records) {
          // 过滤出销售者用户
          this.sellers = response.data.records.filter(user => user.userType === 'seller')
          console.log('👥 过滤后的销售者列表:', this.sellers)
        }
      } catch (error) {
        console.error('❌ 加载销售者列表失败:', error)
      }
    },
    
    refreshData() {
      this.loadProducts()
      this.loadProductStats()
      this.$message.success('数据已刷新')
    },
    
    handleSearch() {
      // 重置到第一页并重新加载数据
      this.currentPage = 1
      this.loadProducts()
    },
    
    handleSelectionChange(selection) {
      this.selectedProducts = selection
    },
    
    handleSizeChange(size) {
      this.pageSize = size
      this.currentPage = 1 // 重置到第一页
      this.loadProducts()
    },

    handleCurrentChange(page) {
      this.currentPage = page
      this.loadProducts()
    },
    
    viewProduct(product) {
      this.selectedProduct = product
      this.showDetailDialog = true
    },
    
    editProduct(product) {
      this.editingProduct = product
      this.productForm = { ...product }
      this.showAddDialog = true
    },
    
    handleCommand(command) {
      const { action, row } = command
      if (action === 'toggle') {
        this.toggleProductStatus(row)
      } else if (action === 'delete') {
        this.deleteProduct(row)
      }
    },
    
    async deleteProduct(product) {
      try {
        await this.$confirm(`确定要删除商品 ${product.name} 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        console.log('🗑️ 开始删除商品:', product.id)

        const response = await deleteProductByAdmin(product.id)
        console.log('🗑️ 删除商品API响应:', response)

        if (response && response.code === 200) {
          this.$message.success('删除成功')
          this.loadProducts()
          this.loadProductStats()
        } else {
          this.$message.error('删除失败: ' + (response.message || '未知错误'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('❌ 删除商品失败:', error)
          this.$message.error('删除商品失败: ' + error.message)
        }
      }
    },
    
    async toggleProductStatus(product) {
      try {
        const currentStatus = this.getStatusValue(product.status)
        const newStatus = currentStatus === 1 ? 0 : 1
        const action = newStatus === 1 ? '上架' : '下架'

        await this.$confirm(`确定要${action}商品 ${product.name} 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        console.log('🔄 开始切换商品状态:', product.id, '新状态:', newStatus)

        const response = await batchUpdateProductStatus([product.id], newStatus)
        console.log('🔄 切换状态API响应:', response)

        if (response && response.code === 200) {
          this.$message.success(`${action}成功`)
          this.loadProducts()
          this.loadProductStats()
        } else {
          this.$message.error(`${action}失败: ` + (response.message || '未知错误'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('❌ 切换商品状态失败:', error)
          this.$message.error('操作失败: ' + error.message)
        }
      }
    },
    
    async saveProduct() {
      try {
        const valid = await this.$refs.productForm.validate()
        if (!valid) return

        this.saving = true
        console.log('💾 开始保存商品:', this.productForm)

        let response
        if (this.editingProduct) {
          // 更新商品
          response = await updateProductByAdmin(this.editingProduct.id, this.productForm)
          console.log('📝 更新商品API响应:', response)
        } else {
          // 创建商品
          response = await createProductByAdmin(this.productForm)
          console.log('➕ 创建商品API响应:', response)
        }

        if (response && response.code === 200) {
          // 如果是新创建的产品，自动添加溯源信息
          if (!this.editingProduct && response.data && response.data.id) {
            try {
              console.log('🔗 为新产品添加溯源信息:', response.data.id)

              // 构建溯源数据
              const traceabilityData = {
                productId: response.data.id,
                productName: this.productForm.name,
                farmName: this.productForm.farmName,
                producerName: this.productForm.producerName,
                productionInfo: this.productForm.traceability.production,
                processingInfo: this.productForm.traceability.processing,
                circulationInfo: this.productForm.traceability.circulation
              }

              // 添加溯源信息
              const traceResponse = await addTraceabilityInfoByAdmin(response.data.id, traceabilityData)
              if (traceResponse && traceResponse.code === 200) {
                console.log('✅ 溯源信息添加成功')

                // 生成溯源码
                const codeResponse = await generateTraceCodeByAdmin(response.data.id)
                if (codeResponse && codeResponse.code === 200) {
                  console.log('✅ 溯源码生成成功:', codeResponse.data.traceCode)
                  this.$message.success('产品创建成功，溯源信息已自动添加')
                } else {
                  this.$message.warning('产品创建成功，但溯源码生成失败')
                }
              } else {
                this.$message.warning('产品创建成功，但溯源信息添加失败')
              }
            } catch (traceError) {
              console.error('❌ 添加溯源信息失败:', traceError)
              this.$message.warning('产品创建成功，但溯源信息添加失败')
            }
          } else {
            this.$message.success(this.editingProduct ? '更新成功' : '添加成功')
          }

          this.showAddDialog = false
          this.loadProducts()
          this.loadProductStats()
          this.resetForm()
        } else {
          this.$message.error('保存失败: ' + (response.message || '未知错误'))
        }
      } catch (error) {
        console.error('❌ 保存商品失败:', error)
        this.$message.error('保存商品失败: ' + error.message)
      } finally {
        this.saving = false
      }
    },
    
    resetForm() {
      this.editingProduct = null
      this.productForm = {
        name: '',
        category: '',
        price: 0,
        stock: 0,
        description: '',
        image: '',
        status: 'active',
        // 重置溯源信息
        farmName: '',
        producerName: '',
        origin: '',
        brand: '',
        traceability: {
          production: {
            plantDate: null,
            harvestDate: null,
            method: '',
            fertilizer: ''
          },
          processing: {
            processDate: null,
            processor: '',
            process: ''
          },
          circulation: {
            packDate: null,
            transport: '',
            storage: ''
          }
        }
      }
      if (this.$refs.productForm) {
        this.$refs.productForm.resetFields()
      }
    },
    
    beforeImageUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },
    
    handleImageUpload(options) {
      // 模拟图片上传
      const reader = new FileReader()
      reader.onload = (e) => {
        this.productForm.image = e.target.result
      }
      reader.readAsDataURL(options.file)
    },
    
    exportData() {
      this.$message.info('导出功能开发中...')
    },
    
    importData() {
      this.$message.info('批量导入功能开发中...')
    },
    
    getCategoryLabel(category) {
      const labels = {
        'vegetables': '蔬菜',
        'fruits': '水果',
        'grains': '粮食',
        'meat': '肉类'
      }
      return labels[category] || category
    },
    
    getStatusLabel(status) {
      // 兼容数字状态和字符串状态
      if (typeof status === 'number') {
        const labels = {
          0: '下架',
          1: '上架',
          2: '审核中'
        }
        return labels[status] || '未知'
      } else {
        const labels = {
          'active': '上架',
          'inactive': '下架',
          'out_of_stock': '缺货'
        }
        return labels[status] || status
      }
    },

    getStatusTagType(status) {
      // 兼容数字状态和字符串状态
      if (typeof status === 'number') {
        const types = {
          0: 'info',
          1: 'success',
          2: 'warning'
        }
        return types[status] || ''
      } else {
        const types = {
          'active': 'success',
          'inactive': 'info',
          'out_of_stock': 'danger'
        }
        return types[status] || ''
      }
    },

    getStatusValue(status) {
      // 将字符串状态转换为数字状态
      if (typeof status === 'number') {
        return status
      } else if (typeof status === 'string') {
        const values = {
          'active': 1,
          'inactive': 0,
          'out_of_stock': 0
        }
        return values[status]
      }
      return undefined
    },
    
    getStockClass(stock) {
      if (stock === 0) return 'stock-empty'
      if (stock < 50) return 'stock-low'
      return 'stock-normal'
    },
    
    formatDate(dateString) {
      if (!dateString) return '-'

      try {
        const date = new Date(dateString)
        if (isNaN(date.getTime())) return '-'

        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        })
      } catch (error) {
        console.error('日期格式化错误:', error)
        return '-'
      }
    },

    // 查看溯源信息
    viewTraceability(product) {
      if (!product.trace_code) {
        this.$message.warning('该商品暂无溯源信息')
        return
      }

      // 跳转到溯源记录管理页面，并传递溯源码参数
      this.$router.push({
        path: '/admin/traceability/records',
        query: { traceCode: product.trace_code }
      })
    },

    // 根据分类ID获取分类名称
    getCategoryNameById(categoryId) {
      if (!categoryId || !this.categories.length) return null
      const category = this.categories.find(cat => cat.id === categoryId)
      return category ? category.name : null
    },

    // 生成溯源码
    async generateTraceCode(product) {
      try {
        this.$message.info('正在生成溯源码...')
        const response = await generateTraceCodeByAdmin(product.id)

        if (response && response.code === 200) {
          this.$message.success('溯源码生成成功')
          // 重新加载商品列表以获取最新的溯源码
          this.loadProducts()
        } else {
          this.$message.error('溯源码生成失败')
        }
      } catch (error) {
        console.error('生成溯源码失败:', error)
        this.$message.error('溯源码生成失败')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.product-management {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
  
  &.dark {
    background: #1a1a1a;
    color: #ffffff;
  }
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-20px);
  opacity: 0;
  animation: slideInFromTop 0.6s ease forwards;
  
  .dark & {
    background: #2d2d2d;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

.header-content {
  .page-title {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 12px;
    
    .dark & {
      color: #ffffff;
    }
    
    .title-icon {
      color: #10b981;
      font-size: 32px;
    }
  }
  
  .page-description {
    margin: 0;
    color: #6b7280;
    font-size: 16px;
    
    .dark & {
      color: #9ca3af;
    }
  }
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: #ffffff;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transform: translateX(-50px);
  opacity: 0;
  animation: slideInFromLeft 0.6s ease forwards;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  .dark & {
    background: #2d2d2d;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  
  &.stat-blue {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: #ffffff;
  }
  
  &.stat-green {
    background: linear-gradient(135deg, #10b981, #059669);
    color: #ffffff;
  }
  
  &.stat-orange {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: #ffffff;
  }
  
  &.stat-red {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: #ffffff;
  }
}

.stat-content {
  flex: 1;
  
  .stat-value {
    margin: 0 0 4px 0;
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    
    .dark & {
      color: #ffffff;
    }
  }
  
  .stat-label {
    margin: 0 0 8px 0;
    color: #6b7280;
    font-size: 14px;
    
    .dark & {
      color: #9ca3af;
    }
  }
  
  .stat-change {
    font-size: 12px;
    font-weight: 500;
    
    &.positive {
      color: #10b981;
    }
    
    &.negative {
      color: #ef4444;
    }
  }
}

.search-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .dark & {
    background: #2d2d2d;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

.search-left {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-input {
  width: 300px;
}

.filter-select {
  width: 120px;
}

// 商品名称单元格样式
.product-name-cell {
  .product-name {
    display: block;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .product-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .el-tag {
      font-size: 10px;
      height: 18px;
      line-height: 16px;
    }
  }
}

// 价格单元格样式
.price-cell {
  .current-price {
    font-weight: 600;
    color: #e74c3c;
  }

  .original-price {
    font-size: 12px;
    color: #999;
    text-decoration: line-through;
    margin-left: 4px;
  }
}

// 溯源码单元格样式
.trace-code-cell {
  display: flex;
  align-items: center;
  gap: 8px;

  .trace-code {
    font-family: monospace;
    font-size: 12px;
    color: #666;
    cursor: pointer;
  }
}

.no-trace {
  color: #999;
  font-style: italic;
}

.table-container {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  .dark & {
    background: #2d2d2d;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

.product-table {
  width: 100%;
  
  ::v-deep .el-table__header {
    background: #f8fafc;
    
    .dark & {
      background: #374151;
    }
  }
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
  border-radius: 8px;
}

.price-text {
  color: #f56565;
  font-weight: 600;
}

.stock-normal {
  color: #10b981;
}

.stock-low {
  color: #f59e0b;
}

.stock-empty {
  color: #ef4444;
}

.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: center;
}

.image-uploader {
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    
    &:hover {
      border-color: #409EFF;
    }
  }
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.uploaded-image {
  width: 178px;
  height: 178px;
  display: block;
}

.product-detail {
  display: flex;
  gap: 20px;
  
  .detail-image {
    flex-shrink: 0;
    
    ::v-deep .el-image {
      width: 200px;
      height: 200px;
      border-radius: 8px;
    }
  }
  
  .detail-info {
    flex: 1;
    
    h3 {
      margin: 0 0 16px 0;
      color: #1f2937;
      
      .dark & {
        color: #ffffff;
      }
    }
    
    p {
      margin: 8px 0;
      color: #6b7280;
      
      .dark & {
        color: #9ca3af;
      }
      
      strong {
        color: #1f2937;
        
        .dark & {
          color: #ffffff;
        }
      }
    }
  }
}

@keyframes slideInFromTop {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 溯源信息卡片样式 */
.trace-info-card {
  margin-bottom: 16px;

  .el-card__header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;

    span {
      font-weight: 600;
      color: #409EFF;
    }
  }

  .el-card__body {
    padding: 20px;
  }

  .el-form-item {
    margin-bottom: 16px;
  }
}

/* 溯源信息分割线样式 */
.el-divider {
  margin: 24px 0;

  .el-divider__text {
    background-color: #fff;
    padding: 0 16px;
    font-size: 14px;

    i {
      margin-right: 8px;
    }
  }
}

@media (max-width: 768px) {
  .product-management {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .search-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .search-left {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input {
    width: 100%;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .product-detail {
    flex-direction: column;
    
    .detail-image {
      align-self: center;
    }
  }
}
</style>