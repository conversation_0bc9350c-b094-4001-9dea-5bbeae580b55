<template>
  <div class="admin-dashboard" :class="{ 'dark': darkMode, 'dashboard-loaded': isLoaded }">
    <!-- Mobile Overlay -->
    <div 
      v-if="isMobile && sidebarOpen" 
      class="mobile-overlay"
      @click="toggleSidebar"
    ></div>

    <!-- Sidebar -->
    <aside 
      class="sidebar"
      :class="{
        'sidebar-open': sidebarOpen,
        'sidebar-collapsed': sidebarCollapsed && !isMobile,
        'sidebar-mobile': isMobile
      }"
    >
      <!-- Sidebar Header -->
      <div class="sidebar-header">
        <div class="logo-section" :class="{ 'logo-collapsed': sidebarCollapsed }">
          <div class="logo-icon">
            <span>农</span>
          </div>
          <transition name="logo-fade" mode="out-in">
            <div v-if="!sidebarCollapsed" class="logo-text" key="expanded">
              <h1>农品汇</h1>
              <p>管理系统</p>
            </div>
          </transition>
        </div>
        
        <transition name="collapse-btn-fade">
          <button 
            v-if="!isMobile" 
            @click="toggleCollapse" 
            class="collapse-btn"
            :title="sidebarCollapsed ? '展开侧边栏' : '收起侧边栏'"
            key="collapse-btn"
          >
            <i :class="sidebarCollapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
          </button>
        </transition>
      </div>

      <!-- Search Bar -->
      <div v-if="!sidebarCollapsed" class="search-section">
        <el-input
          v-model="searchQuery"
          placeholder="搜索菜单..."
          prefix-icon="el-icon-search"
          size="small"
          class="search-input"
        ></el-input>
      </div>

      <!-- Navigation Menu -->
      <nav class="nav-menu">
        <div 
          v-for="item in menuItems" 
          :key="item.id"
          class="menu-item-wrapper"
        >
          <button
            @click="handleMenuClick(item)"
            class="menu-item"
            :class="{
              'menu-item-active': activeMenu === item.id,
              'menu-item-collapsed': sidebarCollapsed
            }"
          >
            <div class="menu-item-content">
              <i :class="item.icon" class="menu-icon"></i>
              <span v-if="!sidebarCollapsed" class="menu-label">{{ item.label }}</span>
            </div>
            
            <div v-if="!sidebarCollapsed" class="menu-extras">
              <span v-if="item.badge" class="menu-badge">{{ item.badge }}</span>
              <i 
                v-if="item.children" 
                class="el-icon-arrow-right menu-arrow"
                :class="{ 'menu-arrow-expanded': expandedItems.includes(item.id) }"
              ></i>
            </div>
          </button>

          <!-- Submenu -->
          <transition name="submenu-slide" @enter="onSubmenuEnter" @leave="onSubmenuLeave">
            <div 
              v-if="item.children && expandedItems.includes(item.id) && !sidebarCollapsed"
              class="submenu"
            >
              <transition-group name="submenu-item" tag="div">
                <button
                  v-for="(child, index) in item.children"
                  :key="child.id"
                  @click="handleMenuClick(child)"
                  class="submenu-item"
                  :class="{ 'submenu-item-active': activeMenu === child.id }"
                  :style="{ transitionDelay: `${index * 50}ms` }"
                >
                  <i :class="child.icon" class="submenu-icon"></i>
                  <span class="submenu-label">{{ child.label }}</span>
                  <span v-if="child.badge" class="submenu-badge">{{ child.badge }}</span>
                </button>
              </transition-group>
            </div>
          </transition>
        </div>
      </nav>

      <!-- User Profile -->
      <div class="user-profile">
        <div v-if="!sidebarCollapsed" class="user-info">
          <div class="user-avatar">
            <i class="el-icon-user"></i>
          </div>
          <div class="user-details">
            <p class="user-name">管理员</p>
            <p class="user-email"><EMAIL></p>
          </div>
        </div>
        <div v-else class="user-avatar-collapsed">
          <div class="user-avatar">
            <i class="el-icon-user"></i>
          </div>
        </div>
      </div>
    </aside>

    <!-- Main Content -->
    <div class="main-content" :class="{ 'content-collapsed': !isMobile && sidebarCollapsed }">
      <!-- Top Navigation -->
      <header class="top-nav">
        <div class="nav-left">
          <button 
            v-if="isMobile" 
            @click="toggleSidebar" 
            class="mobile-menu-btn"
            title="打开菜单"
          >
            <i class="el-icon-menu"></i>
          </button>
          <h2 class="page-title">{{ getPageTitle() }}</h2>
        </div>

        <div class="nav-right">
          <button @click="toggleTheme" class="theme-btn" :title="darkMode ? '切换到浅色模式' : '切换到深色模式'">
            <i :class="darkMode ? 'el-icon-sunny' : 'el-icon-moon'"></i>
          </button>
          
          <button class="notification-btn" title="通知">
            <i class="el-icon-bell"></i>
            <span class="notification-dot"></span>
          </button>
        </div>
      </header>

      <!-- Content Area -->
      <main class="content-area">
        <!-- Router View for Child Components -->
        <router-view></router-view>
      </main>
    </div>
  </div>
</template>

<script>
import { getUnreadApplicationCount, markApplicationsAsRead } from '@/api/admin'

export default {
  name: 'AdminDashboard',
  data() {
    return {
      // UI State
      sidebarOpen: true,
      sidebarCollapsed: false,
      darkMode: false,
      isMobile: false,
      activeMenu: 'dashboard',
      expandedItems: [],
      searchQuery: '',
      isLoaded: false,
      unreadApplicationCount: 0,
      
      // Menu Items
      menuItems: [
        {
          id: 'dashboard',
          label: '仪表盘',
          icon: 'el-icon-s-platform',
          route: '/admin/dashboard'
        },
        {
          id: 'users',
          label: '用户管理',
          icon: 'el-icon-user',
          route: '/admin/users'
        },
        {
          id: 'sellers',
          label: '销售者管理',
          icon: 'el-icon-s-shop',
          children: [
            {
              id: 'seller-applications',
              label: '申请管理',
              icon: 'el-icon-document',
              route: '/admin/seller/applications',
              badge: this.unreadApplicationCount > 0 ? this.unreadApplicationCount.toString() : null
            },
            {
              id: 'seller-list',
              label: '销售者列表',
              icon: 'el-icon-user',
              route: '/admin/sellers'
            }
          ]
        },
        {
          id: 'products',
          label: '商品管理',
          icon: 'el-icon-goods',
          children: [
            {
              id: 'product-list',
              label: '商品列表管理',
              icon: 'el-icon-s-goods',
              route: '/admin/products'
            },
            {
              id: 'admin-direct-products',
              label: '管理员直购商品',
              icon: 'el-icon-s-check',
              route: '/admin/products/direct'
            }
          ]
        },
        {
          id: 'traceability',
          label: '溯源管理',
          icon: 'el-icon-share',
          children: [
            {
              id: 'trace-center',
              label: '溯源管理中心',
              icon: 'el-icon-data-analysis',
              route: '/admin/traceability/center'
            },
            {
              id: 'trace-verify',
              label: '溯源认证',
              icon: 'el-icon-s-check',
              route: '/admin/traceability/verification'
            },
            {
              id: 'trace-records',
              label: '溯源记录管理',
              icon: 'el-icon-document',
              route: '/admin/traceability/records'
            },
            {
              id: 'trace-chain',
              label: '溯源链管理',
              icon: 'el-icon-share',
              route: '/admin/traceability/chain'
            },
            {
              id: 'trace-audit',
              label: '溯源审核',
              icon: 'el-icon-view',
              route: '/admin/traceability/audit'
            },
            {
              id: 'trace-analytics',
              label: '数据分析',
              icon: 'el-icon-pie-chart',
              route: '/admin/traceability/analytics'
            },
            {
              id: 'trace-settings',
              label: '系统配置',
              icon: 'el-icon-setting',
              route: '/admin/traceability/settings'
            }
          ]
        },
        {
          id: 'orders',
          label: '订单管理',
          icon: 'el-icon-s-order',
          route: '/admin/orders',
          badge: '12'
        },
        {
          id: 'settings',
          label: '系统设置',
          icon: 'el-icon-setting',
          route: '/admin/settings'
        }
      ],
      
      // Stats Data
      statsData: [
        { id: 'total-users', title: '总用户', value: '1,234', change: '+12%', color: 'blue' },
        { id: 'sellers', title: '销售者', value: '567', change: '+8%', color: 'green' },
        { id: 'products', title: '商品数量', value: '2,890', change: '+15%', color: 'purple' },
        { id: 'orders', title: '订单总数', value: '4,567', change: '+23%', color: 'orange' }
      ],
      
      // Recent Activities
      recentActivities: [
        { id: 'activity-1', action: '新用户注册', time: '2分钟前', type: 'user' },
        { id: 'activity-2', action: '新订单创建', time: '5分钟前', type: 'order' },
        { id: 'activity-3', action: '商品库存更新', time: '10分钟前', type: 'product' },
        { id: 'activity-4', action: '销售者申请', time: '15分钟前', type: 'seller' }
      ],
      
      // Quick Actions
      quickActions: [
        { id: 'action-1', label: '添加用户', icon: 'el-icon-user', color: 'blue' },
        { id: 'action-2', label: '创建商品', icon: 'el-icon-goods', color: 'green' },
        { id: 'action-3', label: '查看订单', icon: 'el-icon-document', color: 'purple' },
        { id: 'action-4', label: '系统设置', icon: 'el-icon-setting', color: 'orange' }
      ]
    }
  },
  
  mounted() {
    this.checkMobile()
    window.addEventListener('resize', this.checkMobile)
    
    // Apply dark mode if previously set
    if (localStorage.getItem('darkMode') === 'true') {
      this.darkMode = true
    }
    
    // Apply sidebar collapsed state if previously set
    if (localStorage.getItem('sidebarCollapsed') === 'true') {
      this.sidebarCollapsed = true
    }
    
    // 根据当前路由设置activeMenu
    this.updateActiveMenuFromRoute(this.$route)
    
    // 延迟设置加载完成状态，触发入场动画
    this.$nextTick(() => {
      setTimeout(() => {
        this.isLoaded = true
      }, 100)
    })
    
    // 获取未读申请数量
    this.fetchUnreadApplicationCount()
  },
  
  beforeDestroy() {
    window.removeEventListener('resize', this.checkMobile)
  },
  
  watch: {
    darkMode(newVal) {
      localStorage.setItem('darkMode', newVal)
      if (newVal) {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }
    },
    
    sidebarCollapsed(newVal) {
      localStorage.setItem('sidebarCollapsed', newVal)
    },
    
    isMobile(newVal) {
      if (newVal) {
        this.sidebarCollapsed = false
      }
    },
    
    '$route'(to) {
      // 根据当前路由更新activeMenu
      this.updateActiveMenuFromRoute(to)
    }
  },
  
  methods: {
    checkMobile() {
      const wasMobile = this.isMobile
      this.isMobile = window.innerWidth < 768
      
      if (this.isMobile) {
        this.sidebarOpen = false
        if (!wasMobile) {
          // 从桌面切换到移动端时，重置折叠状态
          this.sidebarCollapsed = false
        }
      } else {
        this.sidebarOpen = true
        // 从移动端切换到桌面端时，恢复之前的折叠状态
        if (wasMobile && localStorage.getItem('sidebarCollapsed') === 'true') {
          this.sidebarCollapsed = true
        }
      }
    },
    
    toggleSidebar() {
      this.sidebarOpen = !this.sidebarOpen
    },
    
    toggleCollapse() {
      this.sidebarCollapsed = !this.sidebarCollapsed
      
      // 折叠时自动收起所有展开的菜单项
      if (this.sidebarCollapsed) {
        this.expandedItems = []
      }
    },
    
    toggleTheme() {
      this.darkMode = !this.darkMode
    },
    
    handleMenuClick(item) {
      if (item.children) {
        this.toggleExpanded(item.id)
      } else {
        this.activeMenu = item.id
        if (item.route) {
          // 如果点击的是申请管理，标记为已读
          if (item.id === 'seller-applications' && this.unreadApplicationCount > 0) {
            this.markApplicationsAsRead()
          }
          this.$router.push(item.route)
        }
        if (this.isMobile) {
          this.sidebarOpen = false
        }
      }
    },
    
    toggleExpanded(itemId) {
      const index = this.expandedItems.indexOf(itemId)
      if (index > -1) {
        this.expandedItems.splice(index, 1)
      } else {
        this.expandedItems.push(itemId)
      }
    },
    
    getPageTitle() {
      // 优先从路由元信息获取标题
      if (this.$route.meta && this.$route.meta.title) {
        return this.$route.meta.title
      }
      // 回退到菜单项标题
      const currentItem = this.findMenuItemById(this.activeMenu)
      return currentItem ? currentItem.label : '仪表盘'
    },
    
    findMenuItemById(id) {
      for (const item of this.menuItems) {
        if (item.id === id) {
          return item
        }
        if (item.children) {
          for (const child of item.children) {
            if (child.id === id) {
              return child
            }
          }
        }
      }
      return null
    },
    
    // 根据路由更新activeMenu
    updateActiveMenuFromRoute(route) {
      const path = route.path
      
      // 遍历菜单项找到匹配的路由
      for (const item of this.menuItems) {
        if (item.route === path) {
          this.activeMenu = item.id
          return
        }
        
        // 检查子菜单
        if (item.children) {
          for (const child of item.children) {
            if (child.route === path) {
              this.activeMenu = child.id
              // 展开父菜单
              if (!this.expandedItems.includes(item.id)) {
                this.expandedItems.push(item.id)
              }
              return
            }
          }
        }
      }
    },
    
    // 动画事件处理
    onSubmenuEnter(el) {
      el.style.height = '0'
      el.style.opacity = '0'
      el.offsetHeight // 触发重排
      el.style.transition = 'height 0.3s ease, opacity 0.3s ease'
      el.style.height = el.scrollHeight + 'px'
      el.style.opacity = '1'
    },
    
    onSubmenuLeave(el) {
      el.style.height = el.scrollHeight + 'px'
      el.offsetHeight // 触发重排
      el.style.transition = 'height 0.3s ease, opacity 0.3s ease'
      el.style.height = '0'
      el.style.opacity = '0'
    },
    
    // 获取未读申请数量
    async fetchUnreadApplicationCount() {
      try {
        const res = await getUnreadApplicationCount()
        if (res.code === 0) {
          this.unreadApplicationCount = res.data || 0
        }
      } catch (error) {
        console.error('获取未读申请数量失败:', error)
      }
    },
    
    // 标记申请为已读
    async markApplicationsAsRead() {
      try {
        const res = await markApplicationsAsRead()
        if (res.code === 0) {
          this.unreadApplicationCount = 0
        }
      } catch (error) {
        console.error('标记申请为已读失败:', error)
      }
    }
  }
}
</script>

<style scoped>
/* Base Styles */
.admin-dashboard {
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  opacity: 0;
  transform: translateY(30px) scale(0.98);
  transition: opacity 0.8s cubic-bezier(0.34, 1.56, 0.64, 1), 
              transform 0.8s cubic-bezier(0.34, 1.56, 0.64, 1),
              background-color 0.3s ease;
  will-change: opacity, transform;
}

.admin-dashboard.dashboard-loaded {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.admin-dashboard.dark {
  background-color: #1a1a1a;
  color: #ffffff;
}

/* Mobile Overlay */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 40;
}

/* Sidebar */
.sidebar {
  position: fixed;
  top: 70px;
  left: 0;
  height: calc(100vh - 70px);
  width: 280px;
  background-color: rgba(255, 255, 255, 0.95);
  border-right: 1px solid #e5e7eb;
  z-index: 100;
  transform: translateX(-100%) scale(0.95);
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 
              0 10px 25px -3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  backdrop-filter: blur(20px) saturate(180%);
  will-change: transform, width;
}

.sidebar.sidebar-open {
  transform: translateX(0) scale(1);
}

.sidebar.sidebar-collapsed {
  width: 80px;
  transform: translateX(0) scale(1);
}

.sidebar.sidebar-mobile {
  width: 280px;
}

.dark .sidebar {
  background-color: rgba(45, 45, 45, 0.95);
  border-right-color: #404040;
}

@media (min-width: 768px) {
  .sidebar {
    transform: translateX(0);
  }
}

/* Sidebar Header */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  min-height: 80px;
}

.dark .sidebar-header {
  border-bottom-color: #404040;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  transition: all 0.3s ease;
}

.logo-section.logo-collapsed {
  justify-content: center;
  gap: 0;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.logo-text {
  transition: all 0.3s ease;
}

.logo-text h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.2;
}

.dark .logo-text h1 {
  color: #ffffff;
}

.logo-text p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
  line-height: 1;
}

.dark .logo-text p {
  color: #9ca3af;
}

.collapse-btn {
  padding: 10px;
  border: none;
  background: none;
  border-radius: 8px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  flex-shrink: 0;
}

.collapse-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
  transform: scale(1.05);
}

.dark .collapse-btn {
  color: #9ca3af;
}

.dark .collapse-btn:hover {
  background-color: #404040;
  color: #d1d5db;
}

.collapse-btn i {
  font-size: 16px;
  transition: transform 0.2s ease;
}

/* Logo 动画 */
.logo-fade-enter-active, .logo-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo-fade-enter, .logo-fade-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

/* 折叠按钮动画 */
.collapse-btn-fade-enter-active, .collapse-btn-fade-leave-active {
  transition: all 0.2s ease;
}

.collapse-btn-fade-enter, .collapse-btn-fade-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

/* 子菜单滑动动画 */
.submenu-slide-enter-active, .submenu-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.submenu-slide-enter, .submenu-slide-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.submenu-slide-enter-to, .submenu-slide-leave {
  opacity: 1;
  max-height: 500px;
  transform: translateY(0);
}

/* 子菜单项动画 */
.submenu-item-enter-active, .submenu-item-leave-active {
  transition: all 0.3s ease;
}

.submenu-item-enter, .submenu-item-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.submenu-item-move {
  transition: transform 0.3s ease;
}

/* 统计卡片动画 */
.stat-card-enter-active {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card-enter {
  opacity: 0;
  transform: translateY(30px) scale(0.9);
}

.stat-card-enter-to {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.stat-card-move {
  transition: transform 0.3s ease;
}

/* 统计网格容器 */
.stats-grid-inner {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  width: 100%;
}

/* Search Section */
.search-section {
  padding: 16px 20px;
  transition: all 0.3s ease;
}

.search-input {
  transition: all 0.3s ease;
}

.search-section .el-input__inner {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.search-section .el-input__inner:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.dark .search-section .el-input__inner {
  background-color: #374151;
  border-color: #4b5563;
  color: #ffffff;
}

.dark .search-section .el-input__inner:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

/* Navigation Menu */
.nav-menu {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.menu-item-wrapper {
  margin-bottom: 8px;
}

.menu-item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: none;
  background: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #374151;
  text-align: left;
  position: relative;
  overflow: hidden;
  transform: translateX(0);
}

.dark .menu-item {
  color: #d1d5db;
}

.menu-item:hover {
  background-color: #f3f4f6;
  transform: translateX(6px) scale(1.03);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.menu-item:active {
  transform: translateX(3px) scale(0.97);
  transition: all 0.1s ease;
}

.menu-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: linear-gradient(135deg, #10b981, #059669);
  transform: scaleY(0);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0 2px 2px 0;
}

.menu-item:hover::before {
   transform: scaleY(1);
 }
 
 .dark .menu-item:hover {
  background-color: #404040;
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

.menu-item-active {
  background: linear-gradient(135deg, #10b981, #059669) !important;
  color: #ffffff !important;
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  transform: translateX(4px) scale(1.02);
}

.menu-item-active::before {
  transform: scaleY(1);
  background: rgba(255, 255, 255, 0.3);
}

.menu-item-active:hover {
  transform: translateX(6px) scale(1.03);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.5);
}

.menu-item-collapsed {
  justify-content: center;
  padding: 12px;
}

.menu-item-collapsed .menu-extras {
  display: none;
}

.menu-item-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.menu-icon {
  font-size: 20px;
  width: 20px;
  text-align: center;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.menu-label {
  font-weight: 500;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s ease;
}

.menu-extras {
  display: flex;
  align-items: center;
  gap: 8px;
}

.menu-badge {
  background-color: #ef4444;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.menu-arrow {
  font-size: 12px;
  transition: transform 0.2s ease;
}

.menu-arrow-expanded {
  transform: rotate(90deg);
}

/* Submenu */
.submenu {
  margin-top: 8px;
  margin-left: 16px;
  border-left: 2px solid #e5e7eb;
  padding-left: 16px;
}

.dark .submenu {
  border-left-color: #404040;
}

.submenu-item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: none;
  background: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #6b7280;
  text-align: left;
  margin-bottom: 4px;
  transform: translateX(0);
}

.dark .submenu-item {
  color: #9ca3af;
}

.submenu-item:hover {
  background-color: #f3f4f6;
  color: #374151;
  transform: translateX(3px) scale(1.01);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.submenu-item:active {
  transform: translateX(1px) scale(0.99);
  transition: all 0.1s ease;
}

.dark .submenu-item:hover {
  background-color: #404040;
  color: #d1d5db;
  box-shadow: 0 1px 4px rgba(255, 255, 255, 0.08);
}

.submenu-item-active {
  background-color: #dbeafe !important;
  color: #3b82f6 !important;
}

.dark .submenu-item-active {
  background-color: #1e3a8a !important;
  color: #93c5fd !important;
}

.submenu-icon {
  font-size: 16px;
  margin-right: 8px;
}

.submenu-label {
  flex: 1;
  font-size: 13px;
}

.submenu-badge {
  background-color: #ef4444;
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 8px;
  font-weight: 500;
}

/* User Profile */
.user-profile {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
}

.dark .user-profile {
  border-top-color: #404040;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: #f9fafb;
  border-radius: 8px;
}

.dark .user-info {
  background-color: #374151;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #10b981, #3b82f6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.user-avatar-collapsed {
  display: flex;
  justify-content: center;
  padding: 12px;
}

.user-avatar-collapsed .user-avatar {
  width: 40px;
  height: 40px;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .user-name {
  color: #ffffff;
}

.user-email {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .user-email {
  color: #9ca3af;
}

/* Main Content */
.main-content {
  margin-left: 0;
  transition: margin-left 0.5s cubic-bezier(0.34, 1.56, 0.64, 1),
              transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 100vh;
  position: relative;
  z-index: 10;
  transform: translateX(0);
  will-change: margin-left, transform;
}

@media (min-width: 768px) {
  .main-content {
    margin-left: 280px;
  }
  
  .main-content.content-collapsed {
    margin-left: 80px;
  }
}

@media (max-width: 767px) {
  .main-content {
    margin-left: 0;
  }
}

/* Top Navigation */
.top-nav {
  background-color: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 50;
  backdrop-filter: blur(20px) saturate(180%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: translateY(-10px);
  animation: slideDown 0.5s ease forwards;
  animation-delay: 0.2s;
}

@keyframes slideDown {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dark .top-nav {
  background-color: rgba(45, 45, 45, 0.95);
  border-bottom-color: #404040;
}

@media (max-width: 767px) {
  .top-nav {
    padding: 12px 16px;
  }
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.mobile-menu-btn {
  padding: 10px;
  border: none;
  background: none;
  border-radius: 8px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.mobile-menu-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
  transform: scale(1.05);
}

.dark .mobile-menu-btn {
  color: #9ca3af;
}

.dark .mobile-menu-btn:hover {
  background-color: #404040;
  color: #d1d5db;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.dark .page-title {
  color: #ffffff;
}

@media (max-width: 767px) {
  .page-title {
    font-size: 18px;
  }
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.theme-btn,
.notification-btn {
  padding: 10px;
  border: none;
  background: none;
  border-radius: 8px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  position: relative;
}

.theme-btn:hover,
.notification-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
  transform: scale(1.05);
}

.dark .theme-btn,
.dark .notification-btn {
  color: #9ca3af;
}

.dark .theme-btn:hover,
.dark .notification-btn:hover {
  background-color: #404040;
  color: #d1d5db;
}

.notification-dot {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background-color: #ef4444;
  border-radius: 50%;
  border: 2px solid #ffffff;
  animation: pulse 2s infinite;
}

.dark .notification-dot {
   border-color: #2d2d2d;
 }

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

 .nav-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.theme-btn,
.notification-btn {
  padding: 8px;
  border: none;
  background: none;
  border-radius: 6px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
  position: relative;
}

.theme-btn:hover,
.notification-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.dark .theme-btn:hover,
.dark .notification-btn:hover {
  background-color: #404040;
  color: #d1d5db;
}

.notification-dot {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 8px;
  height: 8px;
  background-color: #ef4444;
  border-radius: 50%;
}

/* Content Area */
.content-area {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stats-grid-inner {
  display: contents;
}

.stat-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transform: translateX(-100px);
  opacity: 0;
  animation: slideInFromLeft 0.6s ease forwards;
}

.dark .stat-card {
  background-color: #2d2d2d;
  border-color: #404040;
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.stat-card:active {
  transform: translateY(-4px) scale(0.98);
  transition: all 0.1s ease;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-title {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.dark .stat-title {
  color: #9ca3af;
}

.stat-value {
  margin: 8px 0 0 0;
  font-size: 32px;
  font-weight: bold;
  color: #1f2937;
}

.dark .stat-value {
  color: #ffffff;
}

.stat-change {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  background-color: #dcfce7;
  color: #166534;
}

.dark .stat-change {
  background-color: #14532d;
  color: #86efac;
}

.stat-card-blue .stat-change {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.dark .stat-card-blue .stat-change {
  background-color: #1e3a8a;
  color: #93c5fd;
}

.stat-card-purple .stat-change {
  background-color: #e9d5ff;
  color: #7c3aed;
}

.dark .stat-card-purple .stat-change {
  background-color: #581c87;
  color: #c4b5fd;
}

.stat-card-orange .stat-change {
  background-color: #fed7aa;
  color: #ea580c;
}

.dark .stat-card-orange .stat-change {
  background-color: #9a3412;
  color: #fdba74;
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  opacity: 0;
  transform: translateX(-50px);
  animation: slideInFromLeft 0.6s ease forwards;
  animation-delay: 0.4s;
}

.content-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.content-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dark .content-card {
  background-color: #2d2d2d;
  border-color: #404040;
}

@keyframes slideInFromLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.dark .card-title {
  color: #ffffff;
}

/* Activities List */
.activities-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  transform: translateX(-30px);
  opacity: 0;
  animation: slideInFromLeft 0.5s ease forwards;
}

.activity-item:hover {
  background-color: #f9fafb;
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .activity-item:hover {
  background-color: #374151;
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-user {
  background-color: #dbeafe;
}

.dark .activity-user {
  background-color: #1e3a8a;
}

.activity-order {
  background-color: #dcfce7;
}

.dark .activity-order {
  background-color: #14532d;
}

.activity-product {
  background-color: #e9d5ff;
}

.dark .activity-product {
  background-color: #581c87;
}

.activity-seller {
  background-color: #fed7aa;
}

.dark .activity-seller {
  background-color: #9a3412;
}

.activity-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.activity-user .activity-dot {
  background-color: #3b82f6;
}

.activity-order .activity-dot {
  background-color: #10b981;
}

.activity-product .activity-dot {
  background-color: #8b5cf6;
}

.activity-seller .activity-dot {
  background-color: #f59e0b;
}

.activity-content {
  flex: 1;
}

.activity-action {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.dark .activity-action {
  color: #ffffff;
}

.activity-time {
  margin: 4px 0 0 0;
  font-size: 12px;
  color: #6b7280;
}

.dark .activity-time {
  color: #9ca3af;
}

/* Quick Actions */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.quick-action-btn {
  padding: 20px;
  border: 2px dashed #d1d5db;
  background: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  position: relative;
  overflow: hidden;
  transform: translateX(-40px);
  opacity: 0;
  animation: slideInFromLeft 0.5s ease forwards;
}

.dark .quick-action-btn {
  border-color: #4b5563;
}

.quick-action-btn:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.quick-action-btn:active {
  transform: translateY(-2px) scale(1.02);
  transition: all 0.1s ease;
}

.quick-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.quick-action-btn:hover::before {
  left: 100%;
}

.action-blue {
  border-color: #93c5fd;
}

.action-blue:hover {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.dark .action-blue:hover {
  background-color: #1e3a8a;
}

.action-green {
  border-color: #86efac;
}

.action-green:hover {
  border-color: #10b981;
  background-color: #f0fdf4;
}

.dark .action-green:hover {
  background-color: #14532d;
}

.action-purple {
  border-color: #c4b5fd;
}

.action-purple:hover {
  border-color: #8b5cf6;
  background-color: #faf5ff;
}

.dark .action-purple:hover {
  background-color: #581c87;
}

.action-orange {
  border-color: #fdba74;
}

.action-orange:hover {
  border-color: #f59e0b;
  background-color: #fffbeb;
}

.dark .action-orange:hover {
  background-color: #9a3412;
}

.action-icon {
  font-size: 32px;
  margin-bottom: 8px;
  color: #6b7280;
}

.action-blue .action-icon {
  color: #3b82f6;
}

.action-green .action-icon {
  color: #10b981;
}

.action-purple .action-icon {
  color: #8b5cf6;
}

.action-orange .action-icon {
  color: #f59e0b;
}

.action-label {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.dark .action-label {
  color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    width: 280px;
    top: 60px;
    height: calc(100vh - 60px);
  }
  
  .content-expanded {
    margin-left: 0;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
  
  .content-area {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .top-nav {
    padding: 12px 16px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .content-card {
    padding: 16px;
  }
}
</style>