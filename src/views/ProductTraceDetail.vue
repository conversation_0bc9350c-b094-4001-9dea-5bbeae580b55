<template>
  <div class="product-trace-detail" :class="{ 'dark-theme': darkMode }">
    <!-- 顶部导航 -->
    <div class="detail-header">
      <div class="header-left">
        <el-button 
          type="text" 
          icon="el-icon-arrow-left" 
          @click="goBack"
          class="back-btn"
        >
          返回
        </el-button>
        <div class="header-title">
          <h1 class="page-title">
            <i class="el-icon-view"></i>
            产品溯源详情
          </h1>
          <p class="trace-code">溯源码：{{ traceCode }}</p>
        </div>
      </div>
      <div class="header-right">
        <el-button 
          type="primary" 
          icon="el-icon-share" 
          @click="shareProduct"
          size="medium"
        >
          分享
        </el-button>
        <el-button 
          type="success" 
          icon="el-icon-download" 
          @click="downloadReport"
          size="medium"
        >
          下载报告
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-card shadow="hover" class="loading-card">
        <div class="loading-content">
          <i class="el-icon-loading loading-icon"></i>
          <h3 class="loading-title">正在加载溯源详情...</h3>
          <p class="loading-subtitle">请稍候，正在获取完整的产品溯源信息</p>
          <el-progress :percentage="loadingProgress" :show-text="false" class="loading-progress"></el-progress>
        </div>
      </el-card>
    </div>

    <!-- 主要内容 -->
    <div v-if="!loading && productData" class="detail-content">
      <!-- 产品基本信息卡片 -->
      <transition name="slideInFromTop" appear>
        <el-card shadow="hover" class="info-card product-info-card">
          <div slot="header" class="card-header">
            <div class="header-left">
              <i class="el-icon-goods"></i>
              <span>产品基本信息</span>
            </div>
            <div class="header-right">
              <el-tag :type="getStatusType(productData.status)" size="medium">
                {{ productData.status }}
              </el-tag>
            </div>
          </div>
          
          <div class="product-info-content">
            <div class="product-image-section">
              <div class="product-image">
                <img
                  :src="processProductImageUrl(productData.imageUrl, productData.name, productData.id)"
                  :alt="productData.name"
                  @error="handleImageError"
                />
              </div>
              <div class="qr-code">
                <img :src="productData.qrCodeUrl" alt="产品二维码" />
                <p class="qr-label">产品二维码</p>
              </div>
            </div>
            
            <div class="product-details">
              <div class="detail-row">
                <label class="detail-label">产品名称：</label>
                <span class="detail-value">{{ productData.name }}</span>
              </div>
              <div class="detail-row">
                <label class="detail-label">产品类别：</label>
                <span class="detail-value">{{ productData.category }}</span>
              </div>
              <div class="detail-row">
                <label class="detail-label">生产商：</label>
                <span class="detail-value">{{ productData.producer }}</span>
              </div>
              <div class="detail-row">
                <label class="detail-label">产地：</label>
                <span class="detail-value">{{ productData.origin }}</span>
              </div>
              <div class="detail-row">
                <label class="detail-label">生产日期：</label>
                <span class="detail-value">{{ formatDate(productData.productionDate) }}</span>
              </div>
              <div class="detail-row">
                <label class="detail-label">保质期：</label>
                <span class="detail-value">{{ productData.shelfLife }}</span>
              </div>
              <div class="detail-row">
                <label class="detail-label">规格：</label>
                <span class="detail-value">{{ productData.specification }}</span>
              </div>
              <div class="detail-row">
                <label class="detail-label">产品描述：</label>
                <span class="detail-value description">{{ productData.description }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </transition>

      <!-- 溯源链路时间轴 -->
      <transition name="slideInFromLeft" appear>
        <el-card shadow="hover" class="info-card timeline-card">
          <div slot="header" class="card-header">
            <div class="header-left">
              <i class="el-icon-connection"></i>
              <span>溯源链路时间轴</span>
            </div>
            <div class="header-right">
              <el-tag type="info" size="medium">
                {{ traceSteps.length }} 个环节
              </el-tag>
            </div>
          </div>
          
          <div class="timeline-content">
            <el-timeline>
              <el-timeline-item
                v-for="(step, index) in traceSteps"
                :key="step.id"
                :timestamp="formatDateTime(step.timestamp)"
                :type="getStepType(step.status)"
                :icon="getStepIcon(step.type)"
                placement="top"
                class="timeline-item"
              >
                <div class="step-content">
                  <div class="step-header">
                    <h4 class="step-title">{{ step.title }}</h4>
                    <el-tag :type="getStepType(step.status)" size="mini">
                      {{ step.status }}
                    </el-tag>
                  </div>
                  <p class="step-description">{{ step.description }}</p>
                  
                  <div class="step-details">
                    <div class="detail-item" v-if="step.location">
                      <i class="el-icon-location"></i>
                      <span>{{ step.location }}</span>
                    </div>
                    <div class="detail-item" v-if="step.operator">
                      <i class="el-icon-user"></i>
                      <span>{{ step.operator }}</span>
                    </div>
                    <div class="detail-item" v-if="step.temperature">
                      <i class="el-icon-thermometer"></i>
                      <span>{{ step.temperature }}°C</span>
                    </div>
                    <div class="detail-item" v-if="step.humidity">
                      <i class="el-icon-cloudy"></i>
                      <span>湿度 {{ step.humidity }}%</span>
                    </div>
                  </div>
                  
                  <!-- 环节图片 -->
                  <div v-if="step.images && step.images.length > 0" class="step-images">
                    <div 
                      v-for="(image, imgIndex) in step.images.slice(0, 3)"
                      :key="imgIndex"
                      class="step-image"
                      @click="previewImage(image, step.images)"
                    >
                      <img :src="image.url" :alt="image.description" />
                    </div>
                    <div 
                      v-if="step.images.length > 3"
                      class="more-images"
                      @click="viewAllImages(step.images)"
                    >
                      +{{ step.images.length - 3 }}
                    </div>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </transition>

      <!-- 认证信息卡片 -->
      <transition name="slideInFromRight" appear>
        <el-card shadow="hover" class="info-card certificates-card">
          <div slot="header" class="card-header">
            <div class="header-left">
              <i class="el-icon-medal"></i>
              <span>认证信息</span>
            </div>
            <div class="header-right">
              <el-tag type="success" size="medium">
                {{ certificates.length }} 项认证
              </el-tag>
            </div>
          </div>
          
          <div class="certificates-content">
            <div class="certificates-grid">
              <div 
                v-for="cert in certificates"
                :key="cert.id"
                class="certificate-item"
                @click="viewCertificate(cert)"
              >
                <div class="cert-icon">
                  <i :class="getCertIcon(cert.type)"></i>
                </div>
                <div class="cert-info">
                  <h4 class="cert-name">{{ cert.name }}</h4>
                  <p class="cert-issuer">{{ cert.issuer }}</p>
                  <div class="cert-details">
                    <span class="cert-number">证书编号：{{ cert.number }}</span>
                    <span class="cert-date">有效期至：{{ formatDate(cert.expiryDate) }}</span>
                  </div>
                </div>
                <div class="cert-status">
                  <el-tag :type="getCertStatusType(cert.status)" size="mini">
                    {{ cert.status }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </transition>

      <!-- 质检报告卡片 -->
      <transition name="slideInFromBottom" appear>
        <el-card shadow="hover" class="info-card reports-card">
          <div slot="header" class="card-header">
            <div class="header-left">
              <i class="el-icon-document"></i>
              <span>质检报告</span>
            </div>
            <div class="header-right">
              <el-tag type="info" size="medium">
                {{ qualityReports.length }} 份报告
              </el-tag>
            </div>
          </div>
          
          <div class="reports-content">
            <div class="reports-list">
              <div 
                v-for="report in qualityReports"
                :key="report.id"
                class="report-item"
              >
                <div class="report-icon">
                  <i class="el-icon-document-checked"></i>
                </div>
                <div class="report-info">
                  <h4 class="report-title">{{ report.title }}</h4>
                  <p class="report-description">{{ report.description }}</p>
                  <div class="report-meta">
                    <span class="report-date">检测日期：{{ formatDate(report.testDate) }}</span>
                    <span class="report-agency">检测机构：{{ report.agency }}</span>
                  </div>
                  
                  <!-- 检测项目结果 -->
                  <div v-if="report.testItems" class="test-items">
                    <div 
                      v-for="item in report.testItems.slice(0, 3)"
                      :key="item.name"
                      class="test-item"
                    >
                      <span class="item-name">{{ item.name }}：</span>
                      <span class="item-value" :class="getTestResultClass(item.result)">
                        {{ item.value }} {{ item.unit }}
                      </span>
                      <el-tag :type="getTestResultType(item.result)" size="mini">
                        {{ item.result }}
                      </el-tag>
                    </div>
                    <el-button 
                      v-if="report.testItems.length > 3"
                      type="text" 
                      size="mini"
                      @click="viewFullReport(report)"
                    >
                      查看完整报告 ({{ report.testItems.length }} 项)
                    </el-button>
                  </div>
                </div>
                <div class="report-actions">
                  <el-button 
                    type="text" 
                    icon="el-icon-view" 
                    @click="viewReport(report)"
                    size="mini"
                  >
                    查看
                  </el-button>
                  <el-button 
                    type="text" 
                    icon="el-icon-download" 
                    @click="downloadReport(report)"
                    size="mini"
                  >
                    下载
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </transition>

      <!-- 相关文档卡片 -->
      <transition name="slideInFromTop" appear>
        <el-card shadow="hover" class="info-card documents-card">
          <div slot="header" class="card-header">
            <div class="header-left">
              <i class="el-icon-folder"></i>
              <span>相关文档</span>
            </div>
            <div class="header-right">
              <el-tag type="warning" size="medium">
                {{ documents.length }} 个文档
              </el-tag>
            </div>
          </div>
          
          <div class="documents-content">
            <div class="documents-grid">
              <div 
                v-for="doc in documents"
                :key="doc.id"
                class="document-item"
                @click="viewDocument(doc)"
              >
                <div class="doc-icon">
                  <i :class="getDocIcon(doc.type)"></i>
                </div>
                <div class="doc-info">
                  <h4 class="doc-name">{{ doc.name }}</h4>
                  <p class="doc-description">{{ doc.description }}</p>
                  <div class="doc-meta">
                    <span class="doc-size">{{ formatFileSize(doc.size) }}</span>
                    <span class="doc-date">{{ formatDate(doc.uploadDate) }}</span>
                  </div>
                </div>
                <div class="doc-actions">
                  <el-button 
                    type="text" 
                    icon="el-icon-download" 
                    @click.stop="downloadDocument(doc)"
                    size="mini"
                  >
                    下载
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </transition>
    </div>

    <!-- 错误状态 -->
    <div v-if="!loading && error" class="error-container">
      <el-card shadow="hover" class="error-card">
        <div class="error-content">
          <i class="el-icon-warning-outline error-icon"></i>
          <h3 class="error-title">加载失败</h3>
          <p class="error-message">{{ error }}</p>
          <el-button type="primary" @click="retryLoad" icon="el-icon-refresh">
            重新加载
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      title="图片预览"
      :visible.sync="imagePreviewVisible"
      width="80%"
      center
      class="image-preview-dialog"
    >
      <div class="image-preview-content">
        <img :src="currentPreviewImage" alt="预览图片" class="preview-image" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="imagePreviewVisible = false">关闭</el-button>
        <el-button type="primary" @click="downloadCurrentImage">下载图片</el-button>
      </div>
    </el-dialog>

    <!-- 证书查看对话框 -->
    <el-dialog
      title="证书详情"
      :visible.sync="certificateDialogVisible"
      width="600px"
      center
    >
      <div v-if="currentCertificate" class="certificate-detail">
        <div class="cert-detail-header">
          <h3>{{ currentCertificate.name }}</h3>
          <el-tag :type="getCertStatusType(currentCertificate.status)">
            {{ currentCertificate.status }}
          </el-tag>
        </div>
        <div class="cert-detail-content">
          <div class="detail-row">
            <label>证书编号：</label>
            <span>{{ currentCertificate.number }}</span>
          </div>
          <div class="detail-row">
            <label>颁发机构：</label>
            <span>{{ currentCertificate.issuer }}</span>
          </div>
          <div class="detail-row">
            <label>颁发日期：</label>
            <span>{{ formatDate(currentCertificate.issueDate) }}</span>
          </div>
          <div class="detail-row">
            <label>有效期至：</label>
            <span>{{ formatDate(currentCertificate.expiryDate) }}</span>
          </div>
          <div class="detail-row">
            <label>证书描述：</label>
            <span>{{ currentCertificate.description }}</span>
          </div>
        </div>
        <div v-if="currentCertificate.imageUrl" class="cert-image">
          <img :src="currentCertificate.imageUrl" alt="证书图片" />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="certificateDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="downloadCertificate(currentCertificate)">下载证书</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTraceabilityDetail, downloadTraceabilityReport } from '@/api/traceability'
import { processProductImageUrl } from '@/utils/imageUtils'

export default {
  name: 'ProductTraceDetail',
  data() {
    return {
      darkMode: false,
      loading: true,
      loadingProgress: 0,
      error: null,
      traceCode: '',
      productData: null,
      traceSteps: [],
      certificates: [],
      qualityReports: [],
      documents: [],
      
      // 对话框状态
      imagePreviewVisible: false,
      certificateDialogVisible: false,
      currentPreviewImage: '',
      currentCertificate: null
    }
  },
  mounted() {
    this.traceCode = this.$route.params.traceCode || this.$route.query.code
    if (this.traceCode) {
      this.loadTraceabilityDetail()
    } else {
      this.error = '缺少溯源码参数'
      this.loading = false
    }
    this.loadTheme()
  },
  methods: {
    // 加载溯源详情
    async loadTraceabilityDetail() {
      this.loading = true
      this.error = null
      this.loadingProgress = 0
      
      const progressInterval = setInterval(() => {
        if (this.loadingProgress < 90) {
          this.loadingProgress += Math.random() * 15
        }
      }, 200)
      
      try {
        const response = await getTraceabilityDetail(this.traceCode)
        
        if (response.success) {
          const data = response.data
          this.productData = data.product
          this.traceSteps = data.steps || []
          this.certificates = data.certificates || []
          this.qualityReports = data.qualityReports || []
          this.documents = data.documents || []
          
          this.loadingProgress = 100
        } else {
          this.error = response.message || '加载溯源详情失败'
        }
      } catch (error) {
        console.error('加载溯源详情失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        clearInterval(progressInterval)
        this.loading = false
        this.loadingProgress = 0
      }
    },
    
    // 重新加载
    retryLoad() {
      this.loadTraceabilityDetail()
    },
    
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
    
    // 分享产品
    shareProduct() {
      const shareUrl = `${window.location.origin}/trace/${this.traceCode}`
      
      if (navigator.share) {
        navigator.share({
          title: `${this.productData.name} - 溯源信息`,
          text: `查看 ${this.productData.name} 的完整溯源信息`,
          url: shareUrl
        })
      } else {
        // 复制到剪贴板
        navigator.clipboard.writeText(shareUrl).then(() => {
          this.$message.success('分享链接已复制到剪贴板')
        })
      }
    },
    
    // 下载报告
    async downloadReport() {
      try {
        const response = await downloadTraceabilityReport(this.traceCode)
        
        // 创建下载链接
        const blob = new Blob([response.data], { type: 'application/pdf' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${this.productData.name}_溯源报告.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        this.$message.success('报告下载成功')
      } catch (error) {
        console.error('下载报告失败:', error)
        this.$message.error('下载报告失败')
      }
    },
    
    // 预览图片
    previewImage(image, allImages) {
      this.currentPreviewImage = image.url
      this.imagePreviewVisible = true
    },
    
    // 查看所有图片
    viewAllImages(images) {
      // 可以实现图片画廊功能
      this.$message.info('图片画廊功能开发中')
    },
    
    // 下载当前预览图片
    downloadCurrentImage() {
      const link = document.createElement('a')
      link.href = this.currentPreviewImage
      link.download = '溯源图片.jpg'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    
    // 查看证书
    viewCertificate(cert) {
      this.currentCertificate = cert
      this.certificateDialogVisible = true
    },
    
    // 下载证书
    downloadCertificate(cert) {
      if (cert.fileUrl) {
        const link = document.createElement('a')
        link.href = cert.fileUrl
        link.download = `${cert.name}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        this.$message.success('证书下载成功')
      } else {
        this.$message.warning('证书文件不存在')
      }
    },
    
    // 查看报告
    viewReport(report) {
      if (report.fileUrl) {
        window.open(report.fileUrl, '_blank')
      } else {
        this.$message.warning('报告文件不存在')
      }
    },
    
    // 查看完整报告
    viewFullReport(report) {
      this.$message.info('完整报告查看功能开发中')
    },
    
    // 查看文档
    viewDocument(doc) {
      if (doc.fileUrl) {
        window.open(doc.fileUrl, '_blank')
      } else {
        this.$message.warning('文档文件不存在')
      }
    },
    
    // 下载文档
    downloadDocument(doc) {
      if (doc.fileUrl) {
        const link = document.createElement('a')
        link.href = doc.fileUrl
        link.download = doc.name
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        this.$message.success('文档下载成功')
      } else {
        this.$message.warning('文档文件不存在')
      }
    },
    
    // 处理图片加载错误
    handleImageError(event) {
      event.target.src = require('@/assets/images/products/default.jpg')
    },

    // 处理产品图片URL
    processProductImageUrl(url, productName, productId) {
      return processProductImageUrl(url, productName, productId)
    },
    
    // 加载主题
    loadTheme() {
      const theme = localStorage.getItem('theme_preference')
      this.darkMode = theme === 'dark'
    },
    
    // 工具方法
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleDateString('zh-CN')
    },
    
    formatDateTime(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN')
    },
    
    formatFileSize(bytes) {
      if (!bytes) return '-'
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    },
    
    getStatusType(status) {
      const statusMap = {
        '正常': 'success',
        '预警': 'warning', 
        '异常': 'danger',
        '待检': 'info'
      }
      return statusMap[status] || 'info'
    },
    
    getStepType(status) {
      const statusMap = {
        '已完成': 'success',
        '进行中': 'primary',
        '待处理': 'warning',
        '异常': 'danger'
      }
      return statusMap[status] || 'info'
    },
    
    getStepIcon(type) {
      const iconMap = {
        '种植': 'el-icon-sunny',
        '收获': 'el-icon-goods',
        '加工': 'el-icon-setting',
        '包装': 'el-icon-box',
        '运输': 'el-icon-truck',
        '销售': 'el-icon-shopping-cart-2'
      }
      return iconMap[type] || 'el-icon-circle-check'
    },
    
    getCertIcon(type) {
      const iconMap = {
        '有机认证': 'el-icon-medal-1',
        '绿色食品': 'el-icon-medal',
        'ISO认证': 'el-icon-trophy',
        'HACCP': 'el-icon-star-on'
      }
      return iconMap[type] || 'el-icon-medal'
    },
    
    getCertStatusType(status) {
      const statusMap = {
        '有效': 'success',
        '即将到期': 'warning',
        '已过期': 'danger',
        '审核中': 'info'
      }
      return statusMap[status] || 'info'
    },
    
    getTestResultType(result) {
      const resultMap = {
        '合格': 'success',
        '不合格': 'danger',
        '待检': 'warning'
      }
      return resultMap[result] || 'info'
    },
    
    getTestResultClass(result) {
      return {
        'result-pass': result === '合格',
        'result-fail': result === '不合格',
        'result-pending': result === '待检'
      }
    },
    
    getDocIcon(type) {
      const iconMap = {
        'pdf': 'el-icon-document',
        'image': 'el-icon-picture',
        'video': 'el-icon-video-camera',
        'excel': 'el-icon-s-grid',
        'word': 'el-icon-edit-outline'
      }
      return iconMap[type] || 'el-icon-document'
    }
  }
}
</script>

<style lang="scss" scoped>
.product-trace-detail {
  min-height: 100vh;
  background: var(--background-color-base);
  padding: 20px;
  transition: all 0.3s ease;

  // 顶部导航
  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color-light);

    .header-left {
      display: flex;
      align-items: center;
      gap: 20px;

      .back-btn {
        font-size: 16px;
        color: var(--primary-color);
        
        &:hover {
          color: var(--primary-color-light);
        }
      }

      .header-title {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          color: var(--text-primary);
          margin: 0 0 4px 0;
          display: flex;
          align-items: center;
          gap: 8px;

          i {
            color: var(--primary-color);
          }
        }

        .trace-code {
          font-size: 14px;
          color: var(--text-secondary);
          margin: 0;
          font-family: 'Courier New', monospace;
        }
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  // 加载状态
  .loading-container {
    .loading-card {
      .loading-content {
        text-align: center;
        padding: 60px 30px;

        .loading-icon {
          font-size: 48px;
          color: var(--primary-color);
          margin-bottom: 20px;
          animation: spin 2s linear infinite;
        }

        .loading-title {
          font-size: 18px;
          color: var(--text-primary);
          margin: 0 0 8px 0;
          font-weight: 600;
        }

        .loading-subtitle {
          font-size: 14px;
          color: var(--text-secondary);
          margin: 0 0 30px 0;
        }

        .loading-progress {
          max-width: 300px;
          margin: 0 auto;
        }
      }
    }
  }

  // 主要内容
  .detail-content {
    display: flex;
    flex-direction: column;
    gap: 30px;

    .info-card {
      background: var(--card-background);
      border-radius: 12px;
      overflow: hidden;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--box-shadow-dark);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 30px;
        background: var(--background-color-light);
        border-bottom: 1px solid var(--border-color-light);

        .header-left {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
          font-weight: 600;
          color: var(--text-primary);

          i {
            color: var(--primary-color);
          }
        }
      }
    }
  }

  // 产品信息卡片
  .product-info-card {
    .product-info-content {
      padding: 30px;
      display: flex;
      gap: 30px;

      .product-image-section {
        display: flex;
        flex-direction: column;
        gap: 20px;
        min-width: 200px;

        .product-image {
          width: 200px;
          height: 200px;
          border-radius: 8px;
          overflow: hidden;
          border: 1px solid var(--border-color-light);

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .qr-code {
          text-align: center;

          img {
            width: 120px;
            height: 120px;
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
          }

          .qr-label {
            margin: 8px 0 0 0;
            font-size: 12px;
            color: var(--text-secondary);
          }
        }
      }

      .product-details {
        flex: 1;

        .detail-row {
          display: flex;
          margin-bottom: 16px;
          align-items: flex-start;

          .detail-label {
            min-width: 100px;
            font-weight: 500;
            color: var(--text-regular);
          }

          .detail-value {
            flex: 1;
            color: var(--text-primary);

            &.description {
              line-height: 1.6;
            }
          }
        }
      }
    }
  }

  // 时间轴卡片
  .timeline-card {
    .timeline-content {
      padding: 30px;

      .timeline-item {
        .step-content {
          .step-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .step-title {
              font-size: 16px;
              font-weight: 600;
              color: var(--text-primary);
              margin: 0;
            }
          }

          .step-description {
            color: var(--text-regular);
            margin: 0 0 12px 0;
            line-height: 1.5;
          }

          .step-details {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 12px;

            .detail-item {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              color: var(--text-secondary);

              i {
                color: var(--primary-color);
              }
            }
          }

          .step-images {
            display: flex;
            gap: 8px;
            margin-top: 12px;

            .step-image {
              width: 60px;
              height: 60px;
              border-radius: 4px;
              overflow: hidden;
              cursor: pointer;
              border: 1px solid var(--border-color-light);

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.3s ease;
              }

              &:hover img {
                transform: scale(1.1);
              }
            }

            .more-images {
              width: 60px;
              height: 60px;
              border-radius: 4px;
              border: 1px dashed var(--border-color-base);
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              color: var(--text-secondary);
              font-size: 12px;
              transition: all 0.3s ease;

              &:hover {
                border-color: var(--primary-color);
                color: var(--primary-color);
              }
            }
          }
        }
      }
    }
  }

  // 认证信息卡片
  .certificates-card {
    .certificates-content {
      padding: 30px;

      .certificates-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;

        .certificate-item {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 20px;
          border: 1px solid var(--border-color-light);
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-light);
          }

          .cert-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--success-color), var(--success-color-light));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
          }

          .cert-info {
            flex: 1;

            .cert-name {
              font-size: 16px;
              font-weight: 600;
              color: var(--text-primary);
              margin: 0 0 4px 0;
            }

            .cert-issuer {
              font-size: 14px;
              color: var(--text-regular);
              margin: 0 0 8px 0;
            }

            .cert-details {
              display: flex;
              flex-direction: column;
              gap: 2px;

              .cert-number,
              .cert-date {
                font-size: 12px;
                color: var(--text-secondary);
              }
            }
          }

          .cert-status {
            align-self: flex-start;
          }
        }
      }
    }
  }

  // 质检报告卡片
  .reports-card {
    .reports-content {
      padding: 30px;

      .reports-list {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .report-item {
          display: flex;
          gap: 16px;
          padding: 20px;
          border: 1px solid var(--border-color-light);
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover {
            border-color: var(--primary-color);
            box-shadow: var(--box-shadow-light);
          }

          .report-icon {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            background: linear-gradient(135deg, var(--info-color), var(--info-color-light));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
          }

          .report-info {
            flex: 1;

            .report-title {
              font-size: 16px;
              font-weight: 600;
              color: var(--text-primary);
              margin: 0 0 4px 0;
            }

            .report-description {
              font-size: 14px;
              color: var(--text-regular);
              margin: 0 0 8px 0;
            }

            .report-meta {
              display: flex;
              gap: 20px;
              margin-bottom: 12px;

              .report-date,
              .report-agency {
                font-size: 12px;
                color: var(--text-secondary);
              }
            }

            .test-items {
              .test-item {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 4px;
                font-size: 12px;

                .item-name {
                  color: var(--text-regular);
                }

                .item-value {
                  font-weight: 500;

                  &.result-pass {
                    color: var(--success-color);
                  }

                  &.result-fail {
                    color: var(--danger-color);
                  }

                  &.result-pending {
                    color: var(--warning-color);
                  }
                }
              }
            }
          }

          .report-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-self: flex-start;
          }
        }
      }
    }
  }

  // 文档卡片
  .documents-card {
    .documents-content {
      padding: 30px;

      .documents-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 16px;

        .document-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 16px;
          border: 1px solid var(--border-color-light);
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: var(--primary-color);
            transform: translateY(-1px);
            box-shadow: var(--box-shadow-light);
          }

          .doc-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            background: linear-gradient(135deg, var(--warning-color), var(--warning-color-light));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
          }

          .doc-info {
            flex: 1;
            min-width: 0;

            .doc-name {
              font-size: 14px;
              font-weight: 500;
              color: var(--text-primary);
              margin: 0 0 4px 0;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .doc-description {
              font-size: 12px;
              color: var(--text-regular);
              margin: 0 0 4px 0;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .doc-meta {
              display: flex;
              gap: 8px;
              font-size: 11px;
              color: var(--text-secondary);
            }
          }

          .doc-actions {
            align-self: flex-start;
          }
        }
      }
    }
  }

  // 错误状态
  .error-container {
    .error-card {
      .error-content {
        text-align: center;
        padding: 60px 30px;

        .error-icon {
          font-size: 64px;
          color: var(--danger-color);
          margin-bottom: 20px;
        }

        .error-title {
          font-size: 20px;
          color: var(--text-primary);
          margin: 0 0 8px 0;
          font-weight: 600;
        }

        .error-message {
          font-size: 14px;
          color: var(--text-secondary);
          margin: 0 0 30px 0;
        }
      }
    }
  }

  // 对话框样式
  .image-preview-dialog {
    .image-preview-content {
      text-align: center;

      .preview-image {
        max-width: 100%;
        max-height: 70vh;
        border-radius: 8px;
      }
    }
  }

  .certificate-detail {
    .cert-detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid var(--border-color-light);

      h3 {
        margin: 0;
        color: var(--text-primary);
      }
    }

    .cert-detail-content {
      .detail-row {
        display: flex;
        margin-bottom: 12px;

        label {
          min-width: 100px;
          font-weight: 500;
          color: var(--text-regular);
        }

        span {
          color: var(--text-primary);
        }
      }
    }

    .cert-image {
      margin-top: 20px;
      text-align: center;

      img {
        max-width: 100%;
        max-height: 300px;
        border-radius: 8px;
        border: 1px solid var(--border-color-light);
      }
    }
  }
}

// 动画效果
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.slideInFromTop-enter-active {
  animation: slideInFromTop 0.6s ease-out;
}

.slideInFromLeft-enter-active {
  animation: slideInFromLeft 0.6s ease-out;
}

.slideInFromRight-enter-active {
  animation: slideInFromRight 0.6s ease-out;
}

.slideInFromBottom-enter-active {
  animation: slideInFromBottom 0.6s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .product-trace-detail {
    padding: 12px;

    .detail-header {
      flex-direction: column;
      gap: 16px;
      text-align: center;

      .header-left {
        flex-direction: column;
        gap: 12px;
      }

      .header-right {
        justify-content: center;
      }
    }

    .product-info-card {
      .product-info-content {
        flex-direction: column;
        gap: 20px;

        .product-image-section {
          align-items: center;
        }
      }
    }

    .certificates-content {
      .certificates-grid {
        grid-template-columns: 1fr;
      }
    }

    .documents-content {
      .documents-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}

@media (max-width: 480px) {
  .product-trace-detail {
    .detail-header {
      .header-right {
        flex-direction: column;
        width: 100%;
      }
    }

    .info-card {
      .card-header {
        padding: 16px 20px;
      }
    }

    .product-info-content,
    .timeline-content,
    .certificates-content,
    .reports-content,
    .documents-content {
      padding: 20px;
    }
  }
}
</style>