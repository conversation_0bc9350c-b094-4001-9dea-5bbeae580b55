<template>
  <div class="consultation">
    <el-row :gutter="20">
      <!-- 左侧边栏：分类 -->
      <el-col :span="6">
        <el-card class="category-card">
          <div slot="header">
            <span>咨询分类</span>
          </div>
          <el-tree
            :data="categories"
            :props="defaultProps"
            @node-click="handleCategorySelect"
            highlight-current
            accordion
            :default-expanded-keys="[0]"
            node-key="id">
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span>{{ data.label }}</span>
              <span class="count">({{ data.count }})</span>
            </span>
          </el-tree>
        </el-card>

        <el-card class="tag-card">
          <div slot="header">
            <span>热门关键词</span>
          </div>
          <div class="tag-cloud">
            <el-tag 
              v-for="tag in hotKeywords" 
              :key="tag.id" 
              :type="tag.type" 
              effect="plain"
              @click="searchByKeyword(tag.name)"
              class="tag-item">
              {{ tag.name }}
            </el-tag>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧内容区 -->
      <el-col :span="18">
        <!-- 搜索和筛选 -->
        <el-card class="filter-card">
          <el-form :inline="true" class="filter-form">
            <el-form-item>
              <el-input 
                v-model="searchQuery" 
                placeholder="搜索农业咨询..." 
                prefix-icon="el-icon-search"
                @keyup.enter.native="handleSearch"/>
            </el-form-item>
            <el-form-item>
              <el-select v-model="sortType" placeholder="排序方式" @change="loadConsultationList">
                <el-option label="最新发布" value="latest"/>
                <el-option label="最多浏览" value="views"/>
                <el-option label="最多点赞" value="likes"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button type="success" @click="showAllConsultations">全部咨询</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 精选内容轮播 -->
        <el-card class="featured-card" v-if="featuredConsultations.length > 0">
          <div slot="header">
            <span>精选咨询</span>
          </div>
          <el-carousel :interval="4000" type="card" height="200px">
            <el-carousel-item v-for="item in featuredConsultations" :key="item.id" @click.native="goToDetail(item.id)">
              <div class="featured-item" :style="{ backgroundImage: `url(${item.coverImage || defaultImage})` }">
                <div class="featured-content">
                  <div class="featured-title">{{ item.title }}</div>
                  <div class="featured-desc">{{ item.summary }}</div>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </el-card>

        <!-- 咨询列表 -->
        <el-card class="list-card">
          <div slot="header" class="list-header">
            <span>{{ currentCategory ? currentCategory.label : '全部咨询' }}</span>
            <el-radio-group v-model="viewMode" size="small">
              <el-radio-button label="card">卡片视图</el-radio-button>
              <el-radio-button label="list">列表视图</el-radio-button>
            </el-radio-group>
          </div>

          <!-- 卡片视图 -->
          <div v-if="viewMode === 'card'" class="card-view">
            <el-row :gutter="20">
              <el-col
:xs="24"
:sm="12"
:md="8"
:lg="8"
v-for="item in consultationList"
:key="item.id"
class="card-col">
                <el-card class="consultation-card" shadow="hover" @click.native="goToDetail(item.id)">
                  <div class="card-image" :style="{ backgroundImage: `url(${item.coverImage || defaultImage})` }">
                    <div class="card-category">{{ getCategoryName(item.categoryId) }}</div>
                    <div v-if="item.isNew" class="card-tag new-tag">新</div>
                    <div v-if="item.isHot" class="card-tag hot-tag">热</div>
                  </div>
                  <div class="card-content">
                    <h3 class="card-title">{{ item.title }}</h3>
                    <p class="card-summary">{{ item.summary }}</p>
                    <div class="card-meta">
                      <span><i class="el-icon-view"/> {{ item.viewsCount }}</span>
                      <span><i class="el-icon-star-on"/> {{ item.likesCount }}</span>
                      <span><i class="el-icon-date"/> {{ formatDate(item.publishDate) }}</span>
                    </div>
                    <div class="card-keywords">
                      <el-tag
size="mini"
v-for="(tag, index) in item.keywords"
:key="index"
class="keyword-tag">
                        {{ tag }}
                      </el-tag>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <!-- 列表视图 -->
          <div v-else class="list-view">
            <div
v-for="item in consultationList"
:key="item.id"
class="list-item"
@click="goToDetail(item.id)">
              <div class="list-item-image" :style="{ backgroundImage: `url(${item.coverImage || defaultImage})` }">
                <div v-if="item.isNew" class="list-tag new-tag">新</div>
                <div v-if="item.isHot" class="list-tag hot-tag">热</div>
              </div>
              <div class="list-item-content">
                <h3 class="list-item-title">{{ item.title }}</h3>
                <p class="list-item-summary">{{ item.summary }}</p>
                <div class="list-item-meta">
                  <span class="category-badge">{{ getCategoryName(item.categoryId) }}</span>
                  <span><i class="el-icon-view"/> {{ item.viewsCount }}</span>
                  <span><i class="el-icon-star-on"/> {{ item.likesCount }}</span>
                  <span><i class="el-icon-date"/> {{ formatDate(item.publishDate) }}</span>
                </div>
                <div class="list-item-keywords">
                  <el-tag
size="mini"
v-for="(tag, index) in item.keywords"
:key="index"
class="keyword-tag">
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页器 -->
          <div class="pagination-container" v-if="total > 0">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"/>
          </div>

          <!-- 空数据状态 -->
          <el-empty v-if="consultationList.length === 0" description="暂无相关咨询内容" :image-size="200">
            <el-button type="primary" @click="showAllConsultations">查看全部咨询</el-button>
          </el-empty>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { formatDate } from '@/utils'

export default {
  name: 'Consultation',
  data() {
    return {
      searchQuery: '',
      sortType: 'latest',
      viewMode: 'card',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      categories: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      consultationList: [],
      featuredConsultations: [],
      hotKeywords: [],
      currentCategory: null,
      defaultImage: require('@/assets/default-news-image.png')
    }
  },
  created() {
    this.loadCategories()
    this.loadHotKeywords()
    this.loadConsultationList()
    this.loadFeaturedConsultations()
  },
  methods: {
    // 加载分类数据
    async loadCategories() {
      try {
        // 这里应该从API获取分类数据
        // 目前使用模拟数据
        this.categories = [
          {
            id: 0,
            label: '全部分类',
            count: 128
          },
          {
            id: 1,
            label: '种植技术',
            count: 45,
            children: [
              { id: 11, label: '粮食作物', count: 18 },
              { id: 12, label: '经济作物', count: 12 },
              { id: 13, label: '果树种植', count: 15 }
            ]
          },
          {
            id: 2,
            label: '病虫害防治',
            count: 32,
            children: [
              { id: 21, label: '病害防治', count: 15 },
              { id: 22, label: '虫害防治', count: 17 }
            ]
          },
          {
            id: 3,
            label: '农业政策',
            count: 25
          },
          {
            id: 4,
            label: '市场信息',
            count: 18
          },
          {
            id: 5,
            label: '科技创新',
            count: 8
          }
        ]
      } catch (error) {
        console.error('加载分类数据失败:', error)
        this.$message.error('加载分类数据失败')
      }
    },

    // 加载热门关键词
    async loadHotKeywords() {
      try {
        // 这里应该从API获取热门关键词
        // 目前使用模拟数据
        this.hotKeywords = [
          { id: 1, name: '有机种植', type: '' },
          { id: 2, name: '水稻种植', type: 'success' },
          { id: 3, name: '小麦病虫害', type: 'info' },
          { id: 4, name: '农业补贴', type: 'warning' },
          { id: 5, name: '农药使用', type: 'danger' },
          { id: 6, name: '土壤改良', type: '' },
          { id: 7, name: '农产品质量', type: 'success' },
          { id: 8, name: '果树嫁接', type: 'info' },
          { id: 9, name: '绿色防控', type: 'warning' },
          { id: 10, name: '农业机械', type: '' }
        ]
      } catch (error) {
        console.error('加载热门关键词失败:', error)
      }
    },

    // 加载咨询列表
    async loadConsultationList() {
      try {
        this.loading = true
        // 这里应该从API获取咨询列表
        // 构建查询参数
        const params = {
          page: this.currentPage,
          size: this.pageSize,
          sortType: this.sortType,
          query: this.searchQuery,
          categoryId: this.currentCategory ? this.currentCategory.id : null
        }

        // 模拟API调用
        setTimeout(() => {
          // 模拟数据
          this.consultationList = Array(10).fill(0).map((_, index) => {
            const id = (this.currentPage - 1) * this.pageSize + index + 1
            return {
              id,
              title: `农业咨询${id}: ${this.getRandomTitle()}`,
              summary: this.getRandomSummary(),
              content: '详细内容...',
              categoryId: this.getRandomInt(1, 5),
              publishDate: new Date(new Date().getTime() - this.getRandomInt(1, 30) * 86400000),
              viewsCount: this.getRandomInt(100, 5000),
              likesCount: this.getRandomInt(10, 200),
              isNew: id % 7 === 0,
              isHot: id % 5 === 0,
              coverImage: this.getRandomImage(),
              keywords: this.getRandomKeywords(),
              attachments: this.getRandomInt(0, 3)
            }
          })
          this.total = 128
          this.loading = false
        }, 500)
      } catch (error) {
        console.error('加载咨询列表失败:', error)
        this.$message.error('加载咨询列表失败')
        this.loading = false
      }
    },

    // 加载精选咨询
    async loadFeaturedConsultations() {
      try {
        // 这里应该从API获取精选咨询
        // 目前使用模拟数据
        setTimeout(() => {
          this.featuredConsultations = Array(5).fill(0).map((_, index) => {
            return {
              id: 1000 + index,
              title: `精选咨询${index + 1}: ${this.getRandomTitle()}`,
              summary: this.getRandomSummary(),
              coverImage: this.getRandomImage(),
              categoryId: this.getRandomInt(1, 5)
            }
          })
        }, 600)
      } catch (error) {
        console.error('加载精选咨询失败:', error)
      }
    },

    // 处理分类选择
    handleCategorySelect(data) {
      this.currentCategory = data
      this.currentPage = 1
      this.loadConsultationList()
    },

    // 按关键词搜索
    searchByKeyword(keyword) {
      this.searchQuery = keyword
      this.handleSearch()
    },

    // 处理搜索
    handleSearch() {
      this.currentPage = 1
      this.loadConsultationList()
    },

    // 显示所有咨询
    showAllConsultations() {
      this.currentCategory = null
      this.searchQuery = ''
      this.currentPage = 1
      this.loadConsultationList()
    },

    // 跳转到详情页
    goToDetail(id) {
      this.$router.push(`/consultation/${id}`)
    },

    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.loadConsultationList()
    },

    // 处理每页数量变化
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadConsultationList()
    },

    // 获取分类名称
    getCategoryName(categoryId) {
      // 递归查找分类名称
      const findCategoryName = (categories, id) => {
        for (const category of categories) {
          if (category.id === id) {
            return category.label
          }
          if (category.children) {
            const name = findCategoryName(category.children, id)
            if (name) return name
          }
        }
        return null
      }

      return findCategoryName(this.categories, categoryId) || '未分类'
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return formatDate(new Date(date), 'yyyy-MM-dd')
    },

    // 以下为模拟数据生成函数
    getRandomInt(min, max) {
      return Math.floor(Math.random() * (max - min + 1)) + min
    },

    getRandomTitle() {
      const titles = [
        '水稻种植技术详解',
        '玉米病虫害防控指南',
        '果树科学修剪方法',
        '土壤改良实用技术',
        '新型农药安全使用规范',
        '农业补贴政策解读',
        '有机农业标准与认证',
        '农产品质量安全知识',
        '智能温室建设经验',
        '农业机械选购指南'
      ]
      return titles[this.getRandomInt(0, titles.length - 1)]
    },

    getRandomSummary() {
      const summaries = [
        '本文详细介绍了水稻全生育期的种植管理技术，包括育苗、插秧、水肥管理、病虫害防治等环节。',
        '针对常见玉米病虫害，提供科学防控方案，减少农药使用，提高防治效果。',
        '分享果树科学修剪的关键技术，通过合理修剪提高光照利用率，增加产量和品质。',
        '介绍各类土壤改良方法，帮助改善土壤结构，提升肥力，为作物提供良好生长环境。',
        '详解新型农药的特点及安全使用规范，指导农民科学用药，减少农药残留。',
        '全面解读最新农业补贴政策，帮助农民了解申请条件、流程和注意事项。',
        '介绍有机农业的标准要求和认证过程，指导农民进行有机转型和认证申请。',
        '讲解农产品质量安全标准，以及如何通过规范化生产提高农产品质量安全水平。',
        '分享智能温室建设和管理的先进经验，助力现代设施农业发展。',
        '提供农业机械选购和使用的专业指导，帮助农民选择适合的农机设备。'
      ]
      return summaries[this.getRandomInt(0, summaries.length - 1)]
    },

    getRandomKeywords() {
      const allKeywords = ['种植技术', '病虫害防治', '土壤管理', '水肥一体化', '绿色防控', '有机种植', '新品种', '农业补贴', '质量标准', '农业机械', '科技创新', '市场行情']
      const count = this.getRandomInt(2, 4)
      const keywords = []
      for (let i = 0; i < count; i++) {
        const index = this.getRandomInt(0, allKeywords.length - 1)
        keywords.push(allKeywords[index])
        allKeywords.splice(index, 1)
      }
      return keywords
    },

    getRandomImage() {
      const images = [
        require('@/assets/products/rice.jpg'),
        require('@/assets/products/apple.jpg'),
        require('@/assets/products/fertilizer.jpg'),
        require('@/assets/products/seeds.jpg'),
        require('@/assets/products/pesticide.jpg'),
        require('@/assets/products/tools.jpg')
      ]
      return images[this.getRandomInt(0, images.length - 1)]
    }
  }
}
</script>

<style lang="scss" scoped>
.consultation {
  padding: 20px;

  .category-card, .tag-card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    transition: all 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .count {
      color: #909399;
      margin-left: 5px;
      font-size: 12px;
    }
  }

  .tag-cloud {
    display: flex;
    flex-wrap: wrap;

    .tag-item {
      margin: 5px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .filter-card {
    margin-bottom: 20px;
    border-radius: 8px;

    .filter-form {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
    }
  }

  .featured-card {
    margin-bottom: 20px;
    border-radius: 8px;

    .featured-item {
      height: 100%;
      background-size: cover;
      background-position: center;
      border-radius: 8px;
      position: relative;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: scale(1.02);
      }

      .featured-content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 15px;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
        border-radius: 0 0 8px 8px;
        color: white;

        .featured-title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 5px;
        }

        .featured-desc {
          font-size: 12px;
          opacity: 0.9;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }
    }
  }

  .list-card {
    border-radius: 8px;

    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    // 卡片视图样式
    .card-view {
      .card-col {
        margin-bottom: 20px;
      }

      .consultation-card {
        height: 100%;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .card-image {
          height: 160px;
          background-size: cover;
          background-position: center;
          position: relative;

          .card-category {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: rgba(64, 158, 255, 0.8);
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
          }

          .card-tag {
            position: absolute;
            top: 10px;
            right: 10px;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;

            &.new-tag {
              background-color: rgba(103, 194, 58, 0.8);
            }

            &.hot-tag {
              background-color: rgba(245, 108, 108, 0.8);
            }
          }
        }

        .card-content {
          padding: 15px;

          .card-title {
            margin: 0 0 10px;
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .card-summary {
            color: #606266;
            font-size: 14px;
            line-height: 1.5;
            height: 63px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            margin-bottom: 10px;
          }

          .card-meta {
            display: flex;
            justify-content: space-between;
            color: #909399;
            font-size: 12px;
            margin-bottom: 10px;

            span {
              display: flex;
              align-items: center;

              i {
                margin-right: 3px;
              }
            }
          }

          .card-keywords {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;

            .keyword-tag {
              margin-right: 5px;
              margin-bottom: 5px;
            }
          }
        }
      }
    }

    // 列表视图样式
    .list-view {
      .list-item {
        display: flex;
        margin-bottom: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .list-item-image {
          width: 200px;
          height: 150px;
          background-size: cover;
          background-position: center;
          position: relative;
          flex-shrink: 0;

          .list-tag {
            position: absolute;
            top: 10px;
            right: 10px;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;

            &.new-tag {
              background-color: rgba(103, 194, 58, 0.8);
            }

            &.hot-tag {
              background-color: rgba(245, 108, 108, 0.8);
            }
          }
        }

        .list-item-content {
          flex: 1;
          padding: 15px;
          overflow: hidden;

          .list-item-title {
            margin: 0 0 10px;
            font-size: 18px;
            font-weight: bold;
            color: #303133;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .list-item-summary {
            color: #606266;
            font-size: 14px;
            line-height: 1.5;
            height: 42px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            margin-bottom: 10px;
          }

          .list-item-meta {
            display: flex;
            align-items: center;
            color: #909399;
            font-size: 12px;
            margin-bottom: 10px;

            span {
              margin-right: 15px;
              display: flex;
              align-items: center;

              i {
                margin-right: 3px;
              }
            }

            .category-badge {
              background-color: #e6f1fc;
              color: #409eff;
              padding: 2px 8px;
              border-radius: 4px;
            }
          }

          .list-item-keywords {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;

            .keyword-tag {
              margin-right: 5px;
            }
          }
        }
      }
    }

    .pagination-container {
      margin-top: 20px;
      text-align: center;
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .consultation {
    .list-view {
      .list-item {
        flex-direction: column;

        .list-item-image {
          width: 100%;
        }
      }
    }
  }
}
</style> 