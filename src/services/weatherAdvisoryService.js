/**
 * 天气农事活动提示服务
 * 根据天气数据生成农事建议，并提供悬浮窗显示功能
 */

import weatherService from '@/api/weather'
import { formatDate } from '@/utils/dateFormatter'

const weatherAdvisoryService = {
  /**
   * 根据天气数据生成天气农事建议
   * @param {string} city 城市名称
   * @returns {Promise<Object>} 天气农事建议
   */
  async generateWeatherAdvisory(city) {
    try {
      console.log('Generating weather advisory for', city)
      
      // 首先尝试获取天气预警 - 预警信息优先级最高
      const alertsResponse = await weatherService.getWeatherAlerts(city)
      if (alertsResponse.data && alertsResponse.data.length > 0) {
        const alerts = alertsResponse.data
        console.log('Weather alerts found:', alerts)
        
        // 按照预警等级排序（红色预警 > 橙色预警 > 黄色预警 > 蓝色预警）
        const alertLevels = { 'Red': 4, 'Orange': 3, 'Yellow': 2, 'Blue': 1 }
        alerts.sort((a, b) => alertLevels[b.level] - alertLevels[a.level])
        
        // 获取最严重的预警
        const mostSevereAlert = alerts[0]
        
        // 根据预警类型和等级生成建议
        const suggestion = this.generateAlertSuggestion(mostSevereAlert.type, mostSevereAlert.level)
        
        return {
          title: `${mostSevereAlert.level}色${mostSevereAlert.typeName}预警`,
          description: mostSevereAlert.text,
          level: this.mapAlertLevelToNotificationLevel(mostSevereAlert.level),
          icon: this.getAlertIcon(mostSevereAlert.type),
          suggestions: suggestion,
          weatherInfo: `预警发布时间：${formatDate(new Date(mostSevereAlert.pubTime))}`
        }
      }
      
      // 获取当前天气数据
      const weatherResponse = await weatherService.getWeather(city)
      if (!weatherResponse.data || !weatherResponse.data.now) {
        throw new Error('No weather data available')
      }
      
      const weatherData = weatherResponse.data.now
      console.log('Current weather data:', weatherData)
      
      // 获取气温、天气状况、风力等信息
      const { temp, text, windDir, windScale, humidity } = weatherData
      
      // 根据当前天气状况生成不同的农事建议
      let title = '今日农事天气提醒'
      let description = `当前${city}天气${text}，气温${temp}°C，湿度${humidity}%，${windDir}${windScale}级。`
      let level = 'normal'
      let icon = 'el-icon-sunrise'
      let suggestions = []
      let weatherInfo = `更新时间：${formatDate(new Date())} | 温度：${temp}°C | 湿度：${humidity}%`
      
      // 处理高温天气
      if (temp >= 35) {
        title = '高温天气提醒'
        description = `当前${city}气温${temp}°C，属于高温天气，请注意防暑降温。`
        level = 'warning'
        icon = 'el-icon-warning'
        suggestions = [
          '农作物可能面临热害风险，建议采取遮阳措施',
          '灌溉应在清晨或傍晚进行，避开中午高温时段',
          '对温室大棚进行通风降温',
          '高温作业时注意劳动保护，避免中暑'
        ]
      } 
      // 处理低温天气
      else if (temp <= 5) {
        title = '低温天气提醒'
        description = `当前${city}气温${temp}°C，属于低温天气，请注意防寒保暖。`
        level = 'warning'
        icon = 'el-icon-cold'
        suggestions = [
          '注意保护农作物不受冻害，可覆盖地膜或防寒材料',
          '温室大棚注意保温，夜间关闭通风口',
          '果树可进行涂白防寒处理',
          '畜禽舍需加强保温，防止感冒'
        ]
      } 
      // 处理雨雪天气
      else if (text.includes('雨')) {
        let rainLevel = 'normal'
        if (text.includes('暴雨') || text.includes('大雨')) {
          title = '强降雨天气提醒'
          level = 'warning'
          rainLevel = 'heavy'
        } else if (text.includes('中雨')) {
          title = '降雨天气提醒'
          level = 'info'
          rainLevel = 'medium'
        } else {
          title = '小雨天气提醒'
          level = 'normal'
          rainLevel = 'light'
        }
        
        description = `当前${city}天气${text}，请合理安排农事活动。`
        icon = 'el-icon-umbrella'
        suggestions = this.getRainSuggestions(rainLevel)
      }
      else if (text.includes('雪')) {
        title = '雪天天气提醒'
        description = `当前${city}天气${text}，请注意防寒保暖。`
        level = 'info'
        icon = 'el-icon-heavy-rain'
        suggestions = [
          '大棚设施注意及时清除积雪，防止压塌',
          '果树注意防寒防冻',
          '室外畜禽舍加强保温措施',
          '雪后及时清理道路，确保农资运输通畅'
        ]
      }
      // 处理大风天气
      else if (parseInt(windScale) >= 6) {
        title = '大风天气提醒'
        description = `当前${city}${windDir}${windScale}级，属于大风天气，请注意防风。`
        level = 'warning'
        icon = 'el-icon-wind-power'
        suggestions = [
          '农业设施如大棚、网室应加固防风',
          '高杆作物如玉米、向日葵等需加强支撑防倒伏',
          '果园应及时采收成熟果实，避免风害造成落果',
          '推迟喷药等作业，避免药液飘移'
        ]
      }
      // 处理特殊天气
      else if (text.includes('雾') || text.includes('霾')) {
        title = `${text}天气提醒`
        description = `当前${city}天气${text}，空气质量较差，请注意防护。`
        level = 'warning'
        icon = 'el-icon-cloudy-and-sunny'
        suggestions = [
          '雾天作业时注意交通安全，提高警惕',
          '推迟农药喷施作业，等待天气转晴',
          '温室大棚注意通风换气',
          '外出作业戴好口罩，做好防护'
        ]
      }
      // 处理晴朗天气
      else if (text.includes('晴') || text.includes('多云')) {
        title = '晴好天气提醒'
        description = `当前${city}天气${text}，适宜开展多种农事活动。`
        level = 'normal'
        icon = 'el-icon-sunny'
        
        // 根据气温区间提供不同建议
        if (temp > 25 && temp < 35) {
          suggestions = [
            '适宜进行田间管理、病虫害防治等作业',
            '可进行蔬菜瓜果采收及晾晒农产品',
            '注意补充水分，适当增加灌溉次数',
            '中午高温时段避免户外作业，防止中暑'
          ]
        } else if (temp >= 15 && temp <= 25) {
          suggestions = [
            '天气适宜，可开展各类农事活动',
            '适合进行播种、移栽、施肥等作业',
            '病虫害防治效果较好，可适当喷药',
            '适宜晾晒农作物及农产品'
          ]
        } else {
          suggestions = [
            '可进行田间管理和农资准备工作',
            '适宜进行农产品采收和初加工',
            '注意随温度变化调整大棚通风时间',
            '根据土壤墒情适当进行灌溉'
          ]
        }
      } else {
        // 默认农事建议
        suggestions = [
          '根据当前天气情况，合理安排农事活动',
          '注意关注天气变化，适时调整农事计划',
          '做好农资、农具的检查和准备工作',
          '农田水利设施检查和维护'
        ]
      }
      
      return {
        title,
        description,
        level,
        icon,
        suggestions,
        weatherInfo
      }
    } catch (error) {
      console.error('Error generating weather advisory:', error)
      
      // 如果出错，返回一个通用的建议
      return {
        title: '农事天气提示',
        description: '我们无法获取当前精确的天气数据，但您仍可以查看一些通用的农事建议。',
        level: 'info',
        icon: 'el-icon-sunny',
        suggestions: [
          '定期关注天气预报，合理安排农事活动',
          '做好农田水利设施的检查和维护',
          '适时开展病虫害监测与防治',
          '农资、农具的准备和检修'
        ],
        weatherInfo: '数据更新时间：' + formatDate(new Date())
      }
    }
  },

  /**
   * 根据预警类型获取图标
   * @param {string} alertType 预警类型
   * @returns {string} 图标类
   */
  getAlertIcon(alertType) {
    const iconMap = {
      '11': 'el-icon-heavy-rain',   // 暴雨
      '12': 'el-icon-umbrella',     // 台风
      '13': 'el-icon-wind-power',   // 暴雪
      '14': 'el-icon-cold',         // 寒潮
      '15': 'el-icon-wind-power',   // 大风
      '16': 'el-icon-warning',      // 沙尘暴
      '17': 'el-icon-warning',      // 高温
      '18': 'el-icon-warning',      // 干旱
      '19': 'el-icon-lightning',    // 雷电
      '20': 'el-icon-cloudy',       // 冰雹
      '21': 'el-icon-cloudy-and-sunny', // 霾
      '22': 'el-icon-cloudy',       // 道路结冰
      '51': 'el-icon-warning',      // 森林火灾
      '52': 'el-icon-warning',      // 草原火灾
      '91': 'el-icon-warning',      // 寒冷
      '92': 'el-icon-ice-tea'       // 灰霾
    }
    
    return iconMap[alertType] || 'el-icon-warning'
  },

  /**
   * 根据预警等级生成通知级别
   * @param {string} alertLevel 预警等级
   * @returns {string} 通知级别
   */
  mapAlertLevelToNotificationLevel(alertLevel) {
    const levelMap = {
      'Red': 'danger',
      'Orange': 'warning',
      'Yellow': 'warning',
      'Blue': 'info'
    }
    
    return levelMap[alertLevel] || 'info'
  },

  /**
   * 根据预警类型和等级生成建议
   * @param {string} alertType 预警类型
   * @param {string} alertLevel 预警等级
   * @returns {Array} 建议列表
   */
  generateAlertSuggestion(alertType, alertLevel) {
    // 预警类型与建议映射
    const suggestionMap = {
      '11': [ // 暴雨
        '注意田间排水，防止农田积水浸泡作物',
        '检查水利设施，及时疏通排水沟',
        '加固大棚等设施，防止倒塌',
        '推迟户外农事作业，确保人身安全'
      ],
      '12': [ // 台风
        '加固农业设施，尤其是大棚、网室等',
        '及时收获成熟农作物，减少损失',
        '畜禽转移到安全场所，确保饲料供应',
        '农资物品存放在安全处，防止淋湿或吹散'
      ],
      '13': [ // 暴雪
        '及时清除大棚、畜禽舍屋顶积雪，防止压塌',
        '加强大棚保温和加固措施',
        '注意畜禽舍保暖，防止牲畜冻伤',
        '雪后及时检查农田和果园，处理断枝和倒伏'
      ],
      '14': [ // 寒潮
        '加强果蔬大棚保温措施，扣好草帘',
        '调整作物浇水时间，避免冻害',
        '畜禽舍增添保暖措施，防止感冒',
        '对露地蔬菜进行覆盖保护'
      ],
      '15': [ // 大风
        '加固大棚、棚架等农业设施',
        '对高杆作物进行支撑，防止倒伏',
        '推迟喷药、施肥等农事活动',
        '畜禽圈舍门窗加固，防止冷风侵袭'
      ],
      '16': [ // 沙尘暴
        '关闭温室大棚门窗，减少沙尘进入',
        '覆盖育秧床和幼苗，防止沙害',
        '畜禽舍门窗封闭，防止沙尘进入',
        '沙尘过后及时清理农作物和设施上的沙尘'
      ],
      '17': [ // 高温
        '调整灌溉时间，早晚进行浇水降温',
        '大棚作物适当遮阳，增加通风',
        '推迟在田间高温作业，防止中暑',
        '畜禽舍加强降温措施，增加饮水量'
      ],
      '18': [ // 干旱
        '合理调配水源，优先保障重要农作物用水',
        '使用地膜、秸秆等覆盖土壤保墒',
        '控制用水量，采用滴灌等节水灌溉技术',
        '根据作物需水特点，合理安排灌溉时间'
      ],
      '19': [ // 雷电
        '避免在雷雨天气进行田间作业',
        '不要在大树下、电线杆附近避雨',
        '农机具及时入库，远离金属设施',
        '做好排水准备，防止强降雨造成积水'
      ],
      '20': [ // 冰雹
        '及时将成熟的果实采收',
        '在条件允许的情况下，覆盖防冰雹网',
        '冰雹过后检查作物受损情况，及时处理',
        '做好病害防治准备，防止伤口感染'
      ],
      '21': [ // 霾
        '减少户外农事活动时间，戴好防护口罩',
        '推迟喷药施肥等作业',
        '大棚作物减少通风时间，防止霾进入',
        '霾过后及时清洗果蔬表面的灰尘'
      ],
      '22': [ // 道路结冰
        '减少不必要的外出作业，注意道路安全',
        '农产品运输车辆注意防滑，减速慢行',
        '农资运输选择合适时间，避开结冰严重时段',
        '必要外出时携带除冰工具和防滑装置'
      ],
      '51': [ // 森林火灾
        '农田附近禁止焚烧秸秆和农作物废弃物',
        '务必将农田与林地间建立隔离带',
        '储备充足的灭火设备和水源',
        '保持通讯畅通，发现火情立即报告'
      ],
      '52': [ // 草原火灾
        '禁止在草原上使用明火',
        '加强畜牧区用火管理，确保用火安全',
        '准备应急转移牲畜的预案',
        '储备足够的饲料，应对可能的转移需求'
      ],
      '91': [ // 寒冷
        '农作物覆盖保温材料，防止冻害',
        '畜禽舍加强保温措施，增加饲料供应',
        '水管道和灌溉设备做好防冻措施',
        '推迟早春农事活动，等待气温回升'
      ],
      '92': [ // 灰霾
        '减少户外作业时间，佩戴防护口罩',
        '大棚作物减少通风时间',
        '灰霾过后及时清洗蔬果表面',
        '加强作物养护，增强抵抗力'
      ]
    }
    
    // 获取对应预警类型的建议
    const suggestions = suggestionMap[alertType] || [
      '关注最新天气预报，及时了解天气变化',
      '做好相应防护措施，减少预警天气带来的损失',
      '适当调整农事活动安排，确保安全',
      '检查农业设施设备，做好防护准备'
    ]
    
    // 针对不同预警等级可以进一步调整建议的紧急程度
    if (alertLevel === 'Red' || alertLevel === 'Orange') {
      suggestions.unshift('严重预警！请立即停止户外农事活动，确保人身安全')
    }
    
    return suggestions
  },

  /**
   * 获取降雨建议
   * @param {string} rainLevel 雨量级别
   * @returns {Array} 建议列表
   */
  getRainSuggestions(rainLevel) {
    switch (rainLevel) {
      case 'heavy':
        return [
          '立即停止户外农事活动，确保人身安全',
          '检查农田排水系统，及时疏通排水沟',
          '加固大棚、棚架等设施，防止倒塌',
          '转移低洼地区的农业物资，防止淹没损失'
        ]
      case 'medium':
        return [
          '推迟喷药、施肥等农事活动',
          '检查排水设施，保持沟渠畅通',
          '雨后及时查看农田和果园受损情况',
          '注意防止病害滋生，适时进行预防'
        ]
      case 'light':
        return [
          '适当调整农事活动安排，雨后再进行田间管理',
          '雨水有利于肥料吸收，可考虑适量追肥',
          '雨后注意防范病虫害的发生',
          '雨天农作物不宜喷药'
        ]
      default:
        return [
          '根据雨量大小调整农事活动',
          '雨后检查农田排水情况',
          '注意防范雨后病虫害发生',
          '合理利用雨水资源，减少灌溉需求'
        ]
    }
  }
}

export default weatherAdvisoryService