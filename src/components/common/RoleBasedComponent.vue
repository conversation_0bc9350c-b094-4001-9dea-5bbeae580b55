<template>
  <div v-if="hasAccess">
    <slot></slot>
  </div>
</template>

<script>
import { getUserRole, hasRolePermission } from '@/utils/auth'

export default {
  name: 'RoleBasedComponent',
  props: {
    // 需要的单个角色
    requiredRole: {
      type: String,
      default: 'user'
    },
    // 需要的角色数组（满足其中一个即可）
    requiredRoles: {
      type: Array,
      default: () => []
    },
    // 是否反向判断（不包含指定角色）
    exclude: {
      type: Boolean,
      default: false
    },
    // 外部传入的用户信息
    externalUserInfo: {
      type: Object,
      default: null
    },
    // 外部传入的角色状态
    externalIsSeller: {
      type: Boolean,
      default: null
    },
    externalIsAdmin: {
      type: Boolean,
      default: null
    },
    externalIsLoggedIn: {
      type: Boolean,
      default: null
    }
  },
  watch: {
    // 监听外部传入的登录状态变化
    externalIsLoggedIn: {
      handler(newVal) {
        console.log('🔄 RoleBasedComponent - 外部登录状态变化:', newVal);
      },
      immediate: true
    },
    externalIsSeller: {
      handler(newVal) {
        console.log('🔄 RoleBasedComponent - 外部销售者状态变化:', newVal);
      },
      immediate: true
    },
    externalIsAdmin: {
      handler(newVal) {
        console.log('🔄 RoleBasedComponent - 外部管理员状态变化:', newVal);
      },
      immediate: true
    },
    externalUserInfo: {
      handler(newVal) {
        console.log('🔄 RoleBasedComponent - 外部用户信息变化:', newVal);
      },
      immediate: true,
      deep: true
    },
    // 监听权限变化，用于动态更新
    hasAccess(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.$emit('access-changed', newVal);
      }
    }
  },
  computed: {
    currentUserRole() {
      // 优先使用外部传入的用户信息
      if (this.externalUserInfo) {
        return getUserRole(this.externalUserInfo);
      }
      return getUserRole();
    },

    isLoggedIn() {
      // 如果外部明确传入了登录状态，使用外部状态
      if (typeof this.externalIsLoggedIn === 'boolean') {
        return this.externalIsLoggedIn;
      }
      // 否则根据用户角色判断
      return this.currentUserRole !== 'guest';
    },

    isSeller() {
      // 如果外部明确传入了销售者状态，使用外部状态
      if (typeof this.externalIsSeller === 'boolean') {
        return this.externalIsSeller;
      }
      return this.currentUserRole === 'seller';
    },

    isAdmin() {
      // 如果外部明确传入了管理员状态，使用外部状态
      if (typeof this.externalIsAdmin === 'boolean') {
        return this.externalIsAdmin;
      }
      return this.currentUserRole === 'admin';
    },

    hasAccess() {
      console.log('🔍 RoleBasedComponent权限检查:', {
        requiredRoles: this.requiredRoles,
        requiredRole: this.requiredRole,
        currentUserRole: this.currentUserRole,
        externalProps: {
          externalIsLoggedIn: this.externalIsLoggedIn,
          externalIsSeller: this.externalIsSeller,
          externalIsAdmin: this.externalIsAdmin,
          externalUserInfo: this.externalUserInfo
        },
        computedValues: {
          isLoggedIn: this.isLoggedIn,
          isSeller: this.isSeller,
          isAdmin: this.isAdmin
        },
        exclude: this.exclude
      });

      if (!this.isLoggedIn) {
        console.log('❌ RoleBasedComponent: 用户未登录，拒绝访问');
        console.log('🔍 详细登录状态检查:', {
          externalIsLoggedIn: this.externalIsLoggedIn,
          externalUserInfo: this.externalUserInfo,
          currentUserRole: this.currentUserRole,
          计算结果: this.isLoggedIn
        });
        return false;
      }

      let hasPermission = false;

      if (this.requiredRoles.length > 0) {
        // 检查是否包含在角色数组中
        if (this.requiredRoles.includes('seller')) {
          hasPermission = this.isSeller;
        } else if (this.requiredRoles.includes('admin')) {
          hasPermission = this.isAdmin;
        } else if (this.requiredRoles.includes('user')) {
          hasPermission = !this.isSeller && !this.isAdmin;
        } else {
          hasPermission = this.requiredRoles.includes(this.currentUserRole);
        }
      } else {
        // 检查是否有指定角色权限
        hasPermission = hasRolePermission(this.currentUserRole, this.requiredRole);
      }

      // 如果是反向判断，取反
      const result = this.exclude ? !hasPermission : hasPermission;

      console.log('✅ 权限检查结果:', {
        hasPermission,
        exclude: this.exclude,
        finalResult: result
      });

      return result;
    }
  },
  mounted() {
    // 在组件挂载后再次检查权限，确保外部状态已经传递
    this.$nextTick(() => {
      console.log('🔄 RoleBasedComponent mounted，重新检查权限');
      this.$forceUpdate();
    });
  },
  render(_h) {
    return this.hasAccess ? this.$slots.default : null;
  }
}
</script>

<style scoped>
/* 角色组件本身不需要样式 */
</style>
