<template>
  <div class="delivery-tracking">
    <div class="delivery-info">
      <div class="company">
        <span class="label">物流公司：</span>
        <span>{{ delivery.company }}</span>
      </div>
      <div class="tracking-number">
        <span class="label">物流单号：</span>
        <span>{{ delivery.trackingNumber }}</span>
      </div>
      <div class="status">
        <span class="label">物流状态：</span>
        <span :class="delivery.status">{{ getStatusText(delivery.status) }}</span>
      </div>
    </div>

    <div class="tracking-timeline">
      <el-timeline>
        <el-timeline-item
          v-for="(record, index) in delivery.tracking"
          :key="index"
          :timestamp="record.time"
          :type="getTimelineType(index)">
          <div class="tracking-record">
            <div class="location">{{ record.location }}</div>
            <div class="description">{{ record.description }}</div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>

    <div class="delivery-actions">
      <el-button type="primary" @click="handleRefresh">刷新物流信息</el-button>
      <el-button @click="handleCopyNumber">复制物流单号</el-button>
    </div>
  </div>
</template>

<script>
import { getDeliveryStatusText } from '@/services/deliveryService'

export default {
  name: 'DeliveryTracking',
  props: {
    delivery: {
      type: Object,
      required: true
    }
  },
  methods: {
    getStatusText(status) {
      return getDeliveryStatusText(status)
    },
    getTimelineType(index) {
      if (index === 0) return 'primary'
      if (this.delivery.status === 'delivered' && index === this.delivery.tracking.length - 1) return 'success'
      return ''
    },
    handleRefresh() {
      this.$emit('refresh')
    },
    handleCopyNumber() {
      const input = document.createElement('input')
      input.value = this.delivery.trackingNumber
      document.body.appendChild(input)
      input.select()
      document.execCommand('copy')
      document.body.removeChild(input)
      this.$message.success('物流单号已复制')
    }
  }
}
</script>

<style lang="scss" scoped>
.delivery-tracking {
  .delivery-info {
    background-color: #f5f7fa;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;

    > div {
      margin-bottom: 10px;
      color: #606266;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        color: #909399;
        margin-right: 10px;
      }

      .status {
        &.pending {
          color: #e6a23c;
        }

        &.shipped {
          color: #409eff;
        }

        &.delivered {
          color: #67c23a;
        }

        &.cancelled {
          color: #f56c6c;
        }
      }
    }
  }

  .tracking-timeline {
    margin-bottom: 20px;

    .tracking-record {
      .location {
        font-size: 14px;
        color: #303133;
        margin-bottom: 5px;
      }

      .description {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .delivery-actions {
    text-align: center;

    .el-button {
      margin: 0 10px;
    }
  }
}
</style> 