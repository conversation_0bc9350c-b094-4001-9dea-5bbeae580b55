<template>
  <div class="weather-detail">
    <div class="weather-loading" v-if="loading">
      <i class="el-icon-loading" />
      <p>加载天气数据中...</p>
    </div>
    
    <div class="weather-error" v-else-if="error">
      <i class="el-icon-warning-outline" />
      <p>{{ error }}</p>
      
      <!-- 添加城市搜索输入框 -->
      <div class="city-search">
        <el-input
          v-model="searchCity"
          placeholder="请输入城市名称，如：北京、上海"
          class="search-input">
          <el-button slot="append" @click="searchWeather">
            <i class="el-icon-search" />
          </el-button>
        </el-input>
        <p class="search-tip">输入城市名称，手动查询天气</p>
      </div>
      
      <el-button type="primary" size="small" @click="fetchWeatherData">重试自动定位</el-button>
    </div>
    
    <div class="weather-initial" v-else-if="!weatherData">
      <i class="el-icon-cloudy" />
      <h3>欢迎使用农业气象服务</h3>
      <p>点击下方按钮获取最新天气数据</p>
      <el-button 
        type="primary" 
        @click="fetchWeatherData" 
        :loading="loading" 
        class="fetch-button">
        <i class="el-icon-cloudy" /> 获取天气数据
      </el-button>
      <p class="weather-tip">或直接输入城市名称查询</p>
      <div class="city-search">
        <el-input
          v-model="searchCity"
          placeholder="请输入城市名称，如：北京、上海"
          class="search-input">
          <el-button slot="append" @click="searchWeather">
            <i class="el-icon-search" />
          </el-button>
        </el-input>
      </div>
      <div class="common-cities">
        <span>热门城市：</span>
        <el-tag 
          v-for="(city, index) in commonCities" 
          :key="index" 
          size="small"
          @click="quickSearchCity(city)"
          class="city-tag">
          {{ city }}
        </el-tag>
      </div>
    </div>
    
    <template v-else-if="weatherData">
      <!-- 整体内容容器 -->
      <div class="weather-content-wrapper">
        <!-- 当前天气 -->
        <div class="weather-header">
          <div class="location">
            <i class="el-icon-location-outline" />
            <span>{{ weatherData.city }}</span>
            <el-dropdown trigger="click" @command="handleCityChange" class="city-select">
              <el-button type="text" class="refresh-btn">
                <i class="el-icon-arrow-down" />
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="refresh">
                  <i class="el-icon-refresh" /> 刷新定位
                </el-dropdown-item>
                <el-dropdown-item command="search">
                  <i class="el-icon-search" /> 搜索城市
                </el-dropdown-item>
                <el-dropdown-item v-for="(city, index) in commonCities" :key="index" :command="city">
                  {{ city }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div class="weather-time">更新于 {{ weatherData.realtime.time || currentTime }}</div>
        </div>

        <!-- 城市搜索对话框 -->
        <el-dialog
          title="搜索城市"
          :visible.sync="cityDialogVisible"
          width="300px"
          append-to-body
          center>
          <el-input
            v-model="searchCity"
            placeholder="请输入城市名称，如：北京"
            @keyup.enter.native="searchWeather"
            class="search-input" />
          <span slot="footer" class="dialog-footer">
            <el-button @click="cityDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="searchWeather">确定</el-button>
          </span>
        </el-dialog>

        <el-row :gutter="20" class="current-weather-section">
          <el-col :xs="24" :sm="12">
            <el-card class="weather-now-card" shadow="hover">
              <div class="weather-now">
                <div class="weather-info">
                  <div class="weather-temp">{{ weatherData.realtime.temperature }}<span class="temp-unit">°C</span></div>
                  <div class="weather-desc">{{ weatherData.realtime.info }}</div>
                </div>
                <div class="weather-icon">
                  <i :class="getWeatherIcon(weatherData.realtime.info)" />
                </div>
              </div>
              <div class="weather-other">
                <span><i class="el-icon-odometer" /> 体感 {{ weatherData.realtime.feels_like || weatherData.realtime.temperature }}°C</span>
                <span><i class="el-icon-wind-power" /> {{ weatherData.realtime.direct }} {{ weatherData.realtime.power }}</span>
                <span><i class="el-icon-water-cup" /> 湿度 {{ weatherData.realtime.humidity }}%</span>
                <span><i class="el-icon-view" /> 能见度 {{ weatherData.realtime.visibility || 'N/A' }}</span>
                <span><i class="el-icon-cloudy" /> 空气质量 {{ getAqiText(weatherData.realtime.aqi) }}</span>
                <span><i class="el-icon-sunny" /> 紫外线 {{ weatherData.realtime.uv || 'N/A' }}</span>
              </div>
            </el-card>
          </el-col>

          <el-col :xs="24" :sm="12">
            <el-card class="weather-indices" shadow="hover">
              <div slot="header" class="card-header">
                <span>今日天气指数</span>
              </div>
              <div v-if="weatherData.indices && weatherData.indices.length > 0" class="indices-grid">
                <div v-for="(index, i) in weatherData.indices.slice(0, 6)" :key="i" class="index-item">
                  <span class="index-name">{{ index.name }}</span>
                  <el-tag size="small" :type="getTagType(index.level)" class="index-value">{{ index.value }}</el-tag>
                  <el-tooltip class="item" effect="dark" :content="index.desc" placement="top">
                    <span class="index-desc">{{ index.desc }}</span>
                  </el-tooltip>
                </div>
              </div>
              <div v-else class="no-data">
                暂无指数信息
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 7天天气预报 -->
        <el-card class="weather-forecast" shadow="hover">
          <div slot="header" class="card-header">
            <span>7天天气预报</span>
          </div>
          <div class="forecast-list">
            <div v-for="(day, index) in weatherData.future" :key="index" class="forecast-item">
              <div class="forecast-date">{{ formatDate(day.date, index === 0) }}</div>
              <div class="forecast-icon">
                <i :class="getWeatherIcon(day.weather)" />
              </div>
              <div class="forecast-weather">{{ day.weather }}</div>
              <div class="forecast-temp">{{ formatTempRange(day.temperature) }}</div>
              <div class="forecast-wind">{{ day.direct }}</div>
            </div>
          </div>
        </el-card>

        <!-- 24小时预报 (如果API提供) -->
        <!-- <el-card class="hourly-forecast" shadow="hover" v-if="weatherData.hourly && weatherData.hourly.length > 0">
          <div slot="header" class="card-header">
            <span>24小时预报</span>
          </div>
          <div class="hourly-list">
            <div v-for="(hour, index) in weatherData.hourly" :key="index" class="hourly-item">
              <div class="hourly-time">{{ formatHour(hour.time) }}</div>
              <div class="hourly-icon"><i :class="getWeatherIcon(hour.weather)" /></div>
              <div class="hourly-temp">{{ hour.temp }}°C</div>
            </div>
          </div>
        </el-card> -->

        <!-- 生活指数 -->
        <el-card class="life-indices" shadow="hover" v-if="lifeIndices && lifeIndices.life">
          <div slot="header" class="card-header">
            <span>生活指数</span>
          </div>
          <el-row :gutter="15">
            <el-col :xs="12" :sm="8" :md="6" v-for="(item, key) in lifeIndices.life" :key="key">
              <div class="life-index-item">
                <div class="life-index-header">
                  <span class="life-index-name">{{ getLifeIndexName(key) }}</span>
                  <el-tag size="mini" :type="getLifeIndexType(item.v)">{{ item.v }}</el-tag>
                </div>
                <div class="life-index-desc">{{ item.des }}</div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 农业建议 -->
        <el-card class="weather-advice" shadow="hover">
          <div slot="header" class="card-header">
            <span>农业生产建议</span>
            <el-dropdown @command="handleCityChange" trigger="click">
              <span class="el-dropdown-link">
                {{ city || '选择城市' }} <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="commonCity in commonCities" :key="commonCity" :command="commonCity">{{ commonCity }}</el-dropdown-item>
                <el-dropdown-item divided command="search">搜索其他城市</el-dropdown-item>
                <el-dropdown-item command="refresh">刷新当前位置</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div v-if="farmingAdvice" class="advice-content">
            <h3>{{ farmingAdvice.title }}</h3>
            <p>{{ farmingAdvice.description }}</p>
            <div class="suggestions-list">
              <div v-for="(suggestion, index) in farmingAdvice.suggestions" :key="index" class="suggestion-item">
                <i class="el-icon-check"></i> {{ suggestion }}
              </div>
            </div>
          </div>
          <div v-else class="advice-content">
            {{ getAgricultureAdvice(weatherData) }}
          </div>
        </el-card>
      </div>
    </template>
  </div>
</template>

<script>
import weatherService from '@/api/weather'
import format from 'date-fns/format'

export default {
  name: 'WeatherDetail',
  data() {
    return {
      weatherData: null,
      lifeIndices: null,
      loading: false,
      error: null,
      currentTime: '',
      city: '',
      timer: null,
      apiKey: process.env.VUE_APP_JUHE_WEATHER_KEY || '3148f172a046c53eb8d05f9e3c520ee7', // 使用环境变量
      searchCity: '',
      cityDialogVisible: false,
      commonCities: ['阜阳', '北京', '上海', '广州', '深圳', '杭州', '成都'],
      farmingAdvice: null // 添加农业建议数据
    }
  },
  created() {
    this.updateCurrentTime()
    this.timer = setInterval(this.updateCurrentTime, 60000)
    // 自动调用fetchWeatherData获取默认城市天气
    this.fetchWeatherData()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    async fetchWeatherData() {
      this.loading = true
      this.error = null
      
      try {
        console.log('Using API Key:', this.apiKey)
        
        let targetCity = this.city // Use the component's city state
        
        if (!targetCity) {
          // If no city is set (initial load or refresh), try IP location
          try {
            const location = await weatherService.getLocationByIp()
            if (location && location.city) {
              targetCity = location.city
              this.city = location.city // Update component state
            } else {
              targetCity = '阜阳' // Default city if IP location fails
              this.city = '阜阳'
            }
          } catch (ipError) {
            console.warn('IP location failed:', ipError)
            targetCity = '阜阳' // Default city on error
            this.city = '阜阳'
            this.$message.warning('自动定位失败，已加载默认城市天气')
          }
        }
        
        console.log('Fetching weather for city:', targetCity)
        
        // 并行获取所有天气数据，提高加载速度
        const [weatherNow, weather7d, weather24h, airNow, lifeIndices, farmingAdvice] = await Promise.all([
          weatherService.getWeatherNow(targetCity),
          weatherService.getWeather15d(targetCity),
          weatherService.getWeather24h(targetCity),
          weatherService.getAirQualityNow(targetCity),
          weatherService.getLifeIndices(targetCity),
          weatherService.getSmartFarmingAdvice(targetCity)
        ])
        
        // 整合天气数据
        this.weatherData = {
          now: weatherNow.now,
          daily: weather7d.daily,
          hourly: weather24h.hourly,
          air: airNow.now
        }
        
        this.lifeIndices = lifeIndices.daily
        this.farmingAdvice = farmingAdvice
        this.searchCity = '' // Clear search input on success
        this.city = targetCity // Ensure city state matches fetched data
        
        this.$message.success(`已成功加载${targetCity}的天气数据`)
      } catch (err) {
        console.error('Error fetching weather data:', err)
        this.error = '网络错误或服务暂时不可用，请稍后重试。'
        this.weatherData = null // Clear previous data on error
        this.lifeIndices = null
      } finally {
        this.loading = false
      }
    },
    
    searchWeather() {
      const cityToSearch = this.searchCity.trim()
      if (cityToSearch) {
        this.city = cityToSearch // Update the component's city state
        this.cityDialogVisible = false
        this.fetchWeatherData() // Fetch data for the new city
      } else {
        this.$message.warning('请输入有效的城市名称')
      }
    },
    
    handleCityChange(command) {
      if (command === 'refresh') {
        this.refreshLocation()
      } else if (command === 'search') {
        this.cityDialogVisible = true
      } else {
        this.city = command
        this.fetchWeatherData()
      }
    },
    
    refreshLocation() {
      this.city = ''
      this.fetchWeatherData()
    },
    
    updateCurrentTime() {
      this.currentTime = format(new Date(), 'yyyy-MM-dd HH:mm')
    },
    
    formatDate(dateStr, isToday = false) {
      if (isToday) return '今天'
      const date = new Date(dateStr)
      // Format to 'MM-DD 周X'
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const weekday = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()]
      return `${month}-${day} ${weekday}`
    },

    formatTempRange(tempStr) {
      // Handles formats like "20℃/12℃", "20 / 12", "20℃"
      const parts = tempStr.match(/(\d+).*?\/?\s*(\d+)?/);
      if (parts && parts[2]) {
        return `${parts[1]}° / ${parts[2]}°`;
      } else if (parts && parts[1]) {
        return `${parts[1]}°`; // Only one temp found
      }
      return tempStr; // Return original if parsing fails
    },
    
    getWeatherIcon(weather) {
      const icons = {
        '晴': 'el-icon-sunny',
        '多云': 'el-icon-partly-cloudy',
        '阴': 'el-icon-cloudy',
        '小雨': 'el-icon-light-rain',
        '中雨': 'el-icon-moderate-rain',
        '大雨': 'el-icon-heavy-rain',
        '暴雨': 'el-icon-heavy-rain',
        '雷阵雨': 'el-icon-lightning',
        '阵雨': 'el-icon-shower-rain',
        '雾': 'el-icon-fog',
        '雪': 'el-icon-snow'
      }
      
      // 处理包含多种天气状况的情况
      for (const key in icons) {
        if (weather.includes(key)) {
          return icons[key]
        }
      }
      
      return 'el-icon-cloudy' // 默认图标
    },
    
    getTagType(level) {
      const types = {
        'danger': 'danger',
        'warning': 'warning',
        'normal': 'primary',
        'success': 'success',
        'info': 'info',
        'serious': 'danger'
      }
      return types[level] || 'info'
    },
    
    getAqiText(aqi) {
      aqi = parseInt(aqi)
      if (aqi <= 50) return `优(${aqi})`
      if (aqi <= 100) return `良(${aqi})`
      if (aqi <= 150) return `轻度污染(${aqi})`
      if (aqi <= 200) return `中度污染(${aqi})`
      return `重度污染(${aqi})`
    },
    
    getAgricultureAdvice(weatherData) {
      const { realtime, future } = weatherData
      const weather = realtime.info
      const temp = parseInt(realtime.temperature)
      const humidity = parseInt(realtime.humidity)
      
      let advice = `当前天气：${weather}，温度${temp}℃，湿度${humidity}%。`
      
      // 根据天气情况提供建议
      if (weather.includes('雨')) {
        advice += '降雨天气，注意做好农作物的排涝工作，加强大棚作物的通风管理，预防病害发生。'
      } else if (weather.includes('晴') && temp > 30) {
        advice += '高温晴朗天气，注意农作物防暑防晒，及时灌溉，补充水分，做好果园遮阳措施。'
      } else if (weather.includes('雪')) {
        advice += '雪天气温低，注意防寒保暖，加强设施农业的保温措施，防止冻害。'
      } else if (weather.includes('雾')) {
        advice += '雾天能见度低，注意农田通风，减少病害发生。出行注意安全。'
      } else if (humidity > 80) {
        advice += '湿度较大，注意农作物通风防霉，适当控制大棚内湿度。'
      } else if (humidity < 30) {
        advice += '湿度较低，注意农田灌溉，增加空气湿度，防止作物水分流失过快。'
      }
      
      // 根据未来天气趋势提供建议
      if (future && future.length > 0) {
        const tomorrow = future[0]
        
        if (tomorrow.weather !== realtime.info) {
          advice += `明日天气将转为${tomorrow.weather}，`
          
          if (tomorrow.weather.includes('雨') && !weather.includes('雨')) {
            advice += '建议提前做好田间排水准备。'
          } else if (!tomorrow.weather.includes('雨') && weather.includes('雨')) {
            advice += '可安排晴天农事活动。'
          }
        }
      }
      
      return advice
    },
    
    getLifeIndexName(key) {
      const names = {
        kongtiao: '空调指数',
        guomin: '过敏指数',
        shushidu: '舒适度指数',
        chuanyi: '穿衣指数',
        diaoyu: '钓鱼指数',
        ganmao: '感冒指数',
        ziwaixian: '紫外线指数',
        xiche: '洗车指数',
        yundong: '运动指数',
        daisan: '带伞指数'
      }
      return names[key] || key
    },
    
    getLifeIndexType(value) {
      const level = {
        '较少开启': 'info',
        '易发': 'danger',
        '舒适': 'success',
        '较适宜': 'primary',
        '弱': 'info',
        '带伞': 'warning'
      }
      
      // 尝试完全匹配
      if (level[value]) {
        return level[value]
      }
      
      // 部分匹配
      if (value.includes('适宜')) return 'success'
      if (value.includes('较适宜')) return 'primary'
      if (value.includes('不宜') || value.includes('不适宜')) return 'danger'
      if (value.includes('较不宜') || value.includes('较不适宜')) return 'warning'
      
      return 'info'
    },
    
    quickSearchCity(city) {
      this.city = city
      this.fetchWeatherData()
    }
  }
}
</script>

<style lang="scss" scoped>
.weather-detail {
  padding: 20px;
  margin-top: 60px; // Increased top margin for noticeable downward shift
  background-color: #f4f6f9; // Light background for the whole page
  min-height: calc(100vh - 140px); // Adjust min-height accordingly if needed
}

.weather-content-wrapper {
  max-width: 1200px;
  margin: 0 auto; // Center content
}

.weather-loading, .weather-error, .weather-initial {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 20px;
  background-color: #fff; // White background for status cards
  border-radius: 8px;
  min-height: 300px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.06);
  margin-bottom: 20px;
  
  i {
    font-size: 50px;
    color: #409EFF;
    margin-bottom: 15px;
  }
  
  p {
    margin: 8px 0;
    color: #606266;
  }
  
  h3 {
    margin: 10px 0 15px;
    color: #303133;
  }
  
  .weather-tip {
    margin-top: 15px;
    font-size: 14px;
    color: #909399;
  }
  
  .city-search {
    margin: 15px 0;
    width: 80%;
    max-width: 350px;
    
    .search-input {
      width: 100%;
    }
    
    .search-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 5px;
    }
  }
  
  .common-cities {
    margin-top: 15px;
    max-width: 400px;
    text-align: center;
    
    span {
      color: #606266;
      margin-right: 8px;
      font-size: 14px;
    }
    
    .city-tag {
      margin: 4px;
      cursor: pointer;
      transition: background-color 0.2s;
      &:hover {
        background-color: #ecf5ff;
      }
    }
  }
  
  .el-button {
    margin-top: 20px;
  }
}

.weather-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 0;
  border-bottom: 1px solid #e4e7ed;
  
  .location {
    font-size: 22px;
    font-weight: bold;
    display: flex;
    align-items: center;
    color: #303133;
    
    i {
      margin-right: 8px;
      color: #409EFF;
      font-size: 24px;
    }
    
    .city-select {
      margin-left: 8px;
    }
    
    .refresh-btn {
      margin-left: 5px;
      color: #409EFF;
      i {
        font-size: 16px;
      }
    }
  }
  
  .weather-time {
    color: #909399;
    font-size: 13px;
  }
}

.current-weather-section {
  margin-bottom: 20px;
}

.weather-now-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.weather-now {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  
  .weather-info {
    .weather-temp {
      font-size: 56px;
      font-weight: 300; // Lighter font weight
      color: #303133;
      line-height: 1;
      .temp-unit {
        font-size: 24px;
        vertical-align: super;
        margin-left: 2px;
      }
    }
    
    .weather-desc {
      font-size: 18px;
      color: #606266;
      margin-top: 5px;
    }
  }
  
  .weather-icon {
    text-align: right;
    i {
      font-size: 70px;
      color: #409EFF;
    }
  }
}

.weather-other {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
  gap: 8px 15px; // row-gap column-gap
  font-size: 13px;
  color: #606266;
  border-top: 1px solid #eee;
  padding-top: 15px;
  
  span {
    display: flex;
    align-items: center;
    white-space: nowrap;
    i {
      margin-right: 6px;
      color: #909399;
      font-size: 16px;
    }
  }
}

.weather-indices {
  height: 100%;
  .card-header {
    font-weight: bold;
  }
  .indices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
  }
  .index-item {
    display: flex;
    align-items: center;
    padding: 5px 0;
    font-size: 13px;
    
    .index-name {
      width: 60px;
      color: #606266;
      flex-shrink: 0;
    }
    
    .index-value {
      margin: 0 8px;
    }
    
    .index-desc {
      flex: 1;
      color: #909399;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .no-data {
    color: #909399;
    font-size: 14px;
    text-align: center;
    padding: 20px 0;
  }
}

.weather-forecast {
  margin-bottom: 20px;
  .card-header {
    font-weight: bold;
  }
  .forecast-list {
    display: flex;
    justify-content: space-between;
    overflow-x: auto; // Allow horizontal scroll on small screens
    padding-bottom: 10px; // Space for scrollbar
    
    .forecast-item {
      flex: 1 0 100px; // Allow shrinking but base width 100px
      min-width: 90px;
      text-align: center;
      padding: 10px 5px;
      border-right: 1px solid #f0f2f5;
      &:last-child {
        border-right: none;
      }
      
      .forecast-date {
        font-size: 13px;
        color: #606266;
        margin-bottom: 8px;
        white-space: nowrap;
      }
      
      .forecast-icon {
        margin: 8px 0;
        i {
          font-size: 28px;
          color: #409EFF;
        }
      }
      
      .forecast-weather {
        margin-bottom: 5px;
        font-size: 14px;
        color: #303133;
      }
      
      .forecast-temp {
        color: #303133; // Less emphasis than red
        font-weight: 500;
        margin-bottom: 5px;
        font-size: 14px;
        white-space: nowrap;
      }
      
      .forecast-wind {
        color: #909399;
        font-size: 12px;
        white-space: nowrap;
      }
    }
  }
}

.life-indices {
  margin-bottom: 20px;
  .card-header {
    font-weight: bold;
  }
  .life-index-item {
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 15px;
    height: 120px; // Adjusted height
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: box-shadow 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    }
    
    .life-index-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      
      .life-index-name {
        font-weight: bold;
        color: #303133;
        font-size: 14px;
      }
      .el-tag {
        height: 20px;
        line-height: 18px;
        padding: 0 6px;
      }
    }
    
    .life-index-desc {
      color: #606266;
      font-size: 13px;
      line-height: 1.5;
      // Allow text to wrap and limit lines
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 3; // Limit to 3 lines
      -webkit-box-orient: vertical;
    }
  }
}

.weather-advice {
  .card-header {
    font-weight: bold;
  }
  .advice-content {
    line-height: 1.7;
    color: #606266;
    font-size: 14px;
  }
}

.fetch-button {
  margin: 20px 0;
  padding: 12px 25px;
  font-size: 16px;
}

// Responsive adjustments
@media (max-width: 768px) {
  .weather-now {
    flex-direction: column;
    align-items: flex-start;
    .weather-icon {
      align-self: flex-end;
      margin-top: -20px; // Adjust position
      i {
        font-size: 60px;
      }
    }
    .weather-info {
      .weather-temp {
        font-size: 48px;
      }
      .weather-desc {
        font-size: 16px;
      }
    }
  }
  .weather-other {
     grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
     gap: 5px 10px;
  }
  .indices-grid {
     grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
  }
  .life-indices .el-col {
    // Ensure 2 columns on smaller screens
    width: 50% !important;
  }
}

@media (max-width: 576px) {
  .weather-detail {
    padding: 15px;
  }
  .weather-header {
    flex-direction: column;
    align-items: flex-start;
    .location {
      font-size: 20px;
      margin-bottom: 5px;
    }
    .weather-time {
      font-size: 12px;
    }
  }
  .forecast-list {
    justify-content: flex-start; // Align items to start for scrolling
  }
  .life-indices .el-col {
    // Single column on very small screens
    width: 100% !important;
  }
  .life-index-item {
    height: auto; // Allow height to adjust
    min-height: 100px;
  }
}

</style>