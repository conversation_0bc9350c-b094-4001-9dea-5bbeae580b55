<template>
  <div class="error-handler">
    <el-alert 
      v-if="message"
      :title="message"
      type="error"
      :closable="true"
      show-icon
      :description="description"
      @close="clearError" />
  </div>
</template>

<script>
export default {
  name: '<PERSON>rro<PERSON><PERSON><PERSON><PERSON>',
  data() {
    return {
      message: '',
      description: '',
      timeout: null
    }
  },
  created() {
    // 注册全局事件监听
    this.$root.$on('app-error', this.handleError)
  },
  beforeDestroy() {
    // 移除事件监听
    this.$root.$off('app-error', this.handleError)
    if (this.timeout) {
      clearTimeout(this.timeout)
    }
  },
  methods: {
    handleError(error) {
      console.log('ErrorHandler: 捕获到错误', error)
      this.message = error.message || '发生了一个错误'
      this.description = error.description || '请稍后再试或联系管理员'
      
      // 自动清除错误
      if (this.timeout) {
        clearTimeout(this.timeout)
      }
      this.timeout = setTimeout(() => {
        this.clearError()
      }, 5000)
    },
    clearError() {
      this.message = ''
      this.description = ''
    }
  }
}
</script>

<style scoped>
.error-handler {
  position: fixed;
  top: 70px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  max-width: 600px;
  z-index: 2000;
}
</style> 