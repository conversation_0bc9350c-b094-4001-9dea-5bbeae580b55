<template>
  <div class="theme-toggle">
    <el-tooltip :content="isDarkTheme ? '切换到亮色模式' : '切换到暗色模式'" placement="bottom">
      <el-button 
        type="text" 
        class="theme-toggle-btn" 
        @click="toggleTheme"
        :aria-label="isDarkTheme ? '切换到亮色模式' : '切换到暗色模式'"
      >
        <i :class="['theme-icon', isDarkTheme ? 'el-icon-sunny' : 'el-icon-moon']"></i>
      </el-button>
    </el-tooltip>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'ThemeToggle',
  computed: {
    ...mapState(['theme']),
    isDarkTheme() {
      return this.theme === 'dark'
    }
  },
  methods: {
    ...mapActions(['toggleTheme'])
  }
}
</script>

<style lang="scss" scoped>
.theme-toggle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  .theme-toggle-btn {
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    background: var(--hover-background);
    
    &:hover {
      background: var(--primary-color);
      
      .theme-icon {
        color: white;
      }
    }
  }
  
  .theme-icon {
    font-size: 20px;
    color: var(--text-regular);
    transition: color 0.3s ease;
  }
}

// 暗色主题特殊样式 - 参考溯源中心
.dark-theme .theme-toggle {
  .theme-toggle-btn {
    background: #4a5568;
    
    &:hover {
      background: #409eff;
      
      .theme-icon {
        color: white;
      }
    }
  }
  
  .theme-icon {
    color: #a0aec0;
  }
}
</style>