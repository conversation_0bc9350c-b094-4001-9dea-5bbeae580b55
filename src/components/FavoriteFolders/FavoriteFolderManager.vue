<template>
  <div class="favorite-folder-manager">
    <!-- 收藏夹列表 -->
    <div class="folder-list">
      <div class="folder-header">
        <h3>我的收藏夹</h3>
        <el-button 
          type="primary" 
          size="small" 
          icon="el-icon-plus"
          @click="showCreateDialog = true"
        >
          新建收藏夹
        </el-button>
      </div>

      <div class="folder-items" v-loading="loading">
        <div 
          v-for="folder in folders" 
          :key="folder.id"
          class="folder-item"
          :class="{ active: selectedFolder?.id === folder.id }"
          @click="selectFolder(folder)"
        >
          <div class="folder-info">
            <i class="el-icon-folder folder-icon"></i>
            <div class="folder-details">
              <div class="folder-name">{{ folder.folderName }}</div>
              <div class="folder-count">{{ folder.productCount }} 件商品</div>
            </div>
          </div>
          
          <div class="folder-actions" v-if="!folder.isDefault">
            <el-dropdown @command="handleFolderAction">
              <span class="el-dropdown-link">
                <i class="el-icon-more"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="{action: 'rename', folder}">
                  <i class="el-icon-edit"></i> 重命名
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'clear', folder}">
                  <i class="el-icon-delete"></i> 清空
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'delete', folder}" divided>
                  <i class="el-icon-close"></i> 删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建收藏夹对话框 -->
    <el-dialog
      title="新建收藏夹"
      :visible.sync="showCreateDialog"
      width="400px"
      @close="resetCreateForm"
    >
      <el-form :model="createForm" :rules="createRules" ref="createForm">
        <el-form-item label="收藏夹名称" prop="folderName">
          <el-input 
            v-model="createForm.folderName" 
            placeholder="请输入收藏夹名称"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="createForm.description" 
            type="textarea"
            placeholder="请输入收藏夹描述（可选）"
            maxlength="200"
            show-word-limit
            :rows="3"
          ></el-input>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreateFolder" :loading="creating">
          创建
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'FavoriteFolderManager',
  data() {
    return {
      loading: false,
      creating: false,
      folders: [],
      selectedFolder: null,
      
      // 创建收藏夹
      showCreateDialog: false,
      createForm: {
        folderName: '',
        description: ''
      },
      createRules: {
        folderName: [
          { required: true, message: '请输入收藏夹名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  
  mounted() {
    this.loadFolders()
  },
  
  methods: {
    // 加载收藏夹列表
    async loadFolders() {
      this.loading = true
      try {
        // TODO: 实现获取收藏夹列表API
        // 模拟数据
        this.folders = [
          { id: 1, folderName: '默认收藏夹', productCount: 5, isDefault: 1 },
          { id: 2, folderName: '我的最爱', productCount: 3, isDefault: 0 },
          { id: 3, folderName: '待购买', productCount: 2, isDefault: 0 }
        ]
        
        // 默认选择第一个收藏夹
        if (this.folders.length > 0 && !this.selectedFolder) {
          this.selectedFolder = this.folders[0]
        }
      } catch (error) {
        this.$message.error('加载收藏夹失败：' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    // 选择收藏夹
    selectFolder(folder) {
      this.selectedFolder = folder
      this.$emit('folder-selected', folder)
    },
    
    // 处理收藏夹操作
    handleFolderAction(command) {
      const { action, folder } = command
      
      switch (action) {
        case 'rename':
          this.$message.info('重命名功能开发中')
          break
        case 'clear':
          this.handleClearFolder(folder)
          break
        case 'delete':
          this.handleDeleteFolder(folder)
          break
      }
    },
    
    // 创建收藏夹
    async handleCreateFolder() {
      try {
        await this.$refs.createForm.validate()
        
        this.creating = true
        
        // TODO: 调用创建收藏夹API
        console.log('创建收藏夹:', this.createForm)
        
        this.$message.success('创建收藏夹成功')
        this.showCreateDialog = false
        this.resetCreateForm()
        await this.loadFolders()
      } catch (error) {
        this.$message.error('创建收藏夹失败：' + error.message)
      } finally {
        this.creating = false
      }
    },
    
    // 删除收藏夹
    handleDeleteFolder(folder) {
      this.$confirm(
        `确定要删除收藏夹"${folder.folderName}"吗？收藏夹中的商品将移动到默认收藏夹。`, 
        '确认删除', 
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          // TODO: 调用删除收藏夹API
          console.log('删除收藏夹:', folder.id)
          
          this.$message.success('删除成功')
          await this.loadFolders()
        } catch (error) {
          this.$message.error('删除失败：' + error.message)
        }
      })
    },
    
    // 清空收藏夹
    handleClearFolder(folder) {
      this.$confirm(
        `确定要清空收藏夹"${folder.folderName}"吗？此操作不可恢复。`, 
        '确认清空', 
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          // TODO: 调用清空收藏夹API
          console.log('清空收藏夹:', folder.id)
          
          this.$message.success('清空成功')
          await this.loadFolders()
        } catch (error) {
          this.$message.error('清空失败：' + error.message)
        }
      })
    },
    
    // 重置创建表单
    resetCreateForm() {
      this.createForm = {
        folderName: '',
        description: ''
      }
      if (this.$refs.createForm) {
        this.$refs.createForm.resetFields()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.favorite-folder-manager {
  .folder-list {
    .folder-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h3 {
        margin: 0;
        color: #303133;
        font-size: 16px;
        font-weight: 500;
      }
    }
    
    .folder-items {
      .folder-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        margin-bottom: 8px;
        background: #fff;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: #409eff;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        }
        
        &.active {
          border-color: #409eff;
          background: #f0f9ff;
        }
        
        .folder-info {
          display: flex;
          align-items: center;
          
          .folder-icon {
            font-size: 20px;
            color: #409eff;
            margin-right: 12px;
          }
          
          .folder-details {
            .folder-name {
              font-size: 14px;
              font-weight: 500;
              color: #303133;
              margin-bottom: 4px;
            }
            
            .folder-count {
              font-size: 12px;
              color: #909399;
            }
          }
        }
        
        .folder-actions {
          .el-dropdown-link {
            color: #909399;
            cursor: pointer;
            
            &:hover {
              color: #409eff;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .favorite-folder-manager {
    .folder-list {
      .folder-header {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        
        h3 {
          text-align: center;
        }
      }
      
      .folder-items {
        .folder-item {
          padding: 10px 12px;
          
          .folder-info {
            .folder-icon {
              font-size: 18px;
              margin-right: 10px;
            }
            
            .folder-details {
              .folder-name {
                font-size: 13px;
              }
              
              .folder-count {
                font-size: 11px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
