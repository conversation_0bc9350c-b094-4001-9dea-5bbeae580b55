<template>
  <div class="category-filter">
    <div class="filter-header">
      <h3 class="filter-title">
        <i class="el-icon-menu"></i>
        商品分类
      </h3>
      <el-button 
        v-if="selectedCategories.length > 0" 
        type="text" 
        size="small" 
        @click="clearAll"
      >
        清空筛选
      </el-button>
    </div>
    
    <!-- 全部分类按钮 -->
    <div class="all-categories-button">
      <el-button 
        type="primary" 
        plain 
        size="small" 
        class="all-button"
        :class="{ active: !selectedMainCategory }"
        @click="clearCategorySelection"
      >
        <i class="el-icon-s-grid"></i>
        全部分类
      </el-button>
    </div>
    
    <!-- 主分类 -->
    <div class="main-categories">
      <div 
        v-for="category in mainCategories" 
        :key="category.id"
        class="category-item main-category"
        :class="{ active: selectedMainCategory === category.id }"
        @click="selectMainCategory(category.id)"
      >
        <div class="category-content">
          <i :class="getCategoryIcon(category)"></i>
          <span class="category-name">{{ category.name }}</span>
          <span class="product-count">({{ category.productCount || 0 }})</span>
        </div>
        <i class="el-icon-arrow-right" v-if="hasSubCategories(category.id)"></i>
      </div>
    </div>
    
    <!-- 子分类 -->
    <div v-if="subCategories.length > 0" class="sub-categories">
      <div class="sub-category-header">
        <span class="sub-title">
          <i class="el-icon-caret-right"></i>
          {{ getMainCategoryName(selectedMainCategory) }}
        </span>
        <el-button type="text" size="mini" @click="selectedMainCategory = null">
          <i class="el-icon-back"></i>
          返回
        </el-button>
      </div>
      
      <div class="sub-category-list">
        <el-checkbox-group v-model="selectedSubCategories" @change="handleSubCategoryChange">
          <div 
            v-for="subCategory in subCategories" 
            :key="subCategory.id"
            class="category-item sub-category"
          >
            <el-checkbox :label="subCategory.id">
              <div class="category-content">
                <span class="category-name">{{ subCategory.name }}</span>
                <span class="product-count">({{ subCategory.productCount || 0 }})</span>
              </div>
            </el-checkbox>
          </div>
        </el-checkbox-group>
      </div>
    </div>
    
    <!-- 快速筛选标签 -->
    <div class="quick-filters">
      <div class="filter-section">
        <h4 class="section-title">商品特性</h4>
        <div class="filter-tags">
          <el-tag 
            v-for="tag in productTags" 
            :key="tag.key"
            :type="selectedTags.includes(tag.key) ? 'success' : 'info'"
            :effect="selectedTags.includes(tag.key) ? 'dark' : 'plain'"
            size="small"
            @click="toggleTag(tag.key)"
            class="filter-tag"
          >
            {{ tag.label }}
          </el-tag>
        </div>
      </div>
      
      <div class="filter-section">
        <h4 class="section-title">价格区间</h4>
        <div class="price-ranges">
          <el-tag 
            v-for="range in priceRanges" 
            :key="range.key"
            :type="selectedPriceRange === range.key ? 'warning' : 'info'"
            :effect="selectedPriceRange === range.key ? 'dark' : 'plain'"
            size="small"
            @click="selectPriceRange(range.key)"
            class="filter-tag"
          >
            {{ range.label }}
          </el-tag>
        </div>
        
        <!-- 自定义价格区间 -->
        <div class="custom-price-range">
          <el-input-number 
            v-model="customPriceRange.min" 
            :min="0" 
            :max="customPriceRange.max || 9999"
            size="mini"
            placeholder="最低价"
            class="price-input"
          />
          <span class="price-separator">-</span>
          <el-input-number 
            v-model="customPriceRange.max" 
            :min="customPriceRange.min || 0" 
            size="mini"
            placeholder="最高价"
            class="price-input"
          />
          <el-button 
            size="mini" 
            type="primary" 
            @click="applyCustomPriceRange"
            :disabled="!customPriceRange.min && !customPriceRange.max"
          >
            确定
          </el-button>
        </div>
      </div>
      
      <div class="filter-section">
        <h4 class="section-title">产地筛选</h4>
        <div class="location-filters">
          <el-select 
            v-model="selectedLocation" 
            placeholder="选择产地" 
            size="small"
            clearable
            @change="handleLocationChange"
            class="location-select"
          >
            <el-option 
              v-for="location in locations" 
              :key="location.value"
              :label="location.label"
              :value="location.value"
            >
              <span>{{ location.label }}</span>
              <span class="location-count">({{ location.count }})</span>
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    
    <!-- 已选筛选条件 -->
    <div v-if="hasActiveFilters" class="active-filters">
      <div class="active-filters-header">
        <span class="title">已选条件</span>
        <el-button type="text" size="mini" @click="clearAll">
          清空全部
        </el-button>
      </div>
      
      <div class="active-filter-tags">
        <el-tag 
          v-for="filter in activeFilters" 
          :key="filter.key"
          closable
          size="small"
          @close="removeFilter(filter)"
          class="active-filter-tag"
        >
          {{ filter.label }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CategoryFilter',
  props: {
    categories: {
      type: Array,
      default: () => []
    },
    value: {
      type: Object,
      default: () => ({})
    },
    priceRange: {
      type: Object,
      default: () => ({ min: null, max: null })
    },
    compact: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedMainCategory: null,
      selectedSubCategories: [],
      selectedTags: [],
      selectedPriceRange: null,
      selectedLocation: null,
      customPriceRange: {
        min: null,
        max: null
      },
      
      // 商品特性标签
      productTags: [
        { key: 'hot', label: '热门商品' },
        { key: 'new', label: '新品上市' },
        { key: 'organic', label: '有机认证' },
        { key: 'local', label: '本地特产' },
        { key: 'discount', label: '促销商品' },
        { key: 'highRating', label: '高评分' }
      ],
      
      // 价格区间
      priceRanges: [
        { key: 'under10', label: '10元以下', min: 0, max: 10 },
        { key: '10to30', label: '10-30元', min: 10, max: 30 },
        { key: '30to50', label: '30-50元', min: 30, max: 50 },
        { key: '50to100', label: '50-100元', min: 50, max: 100 },
        { key: 'over100', label: '100元以上', min: 100, max: null }
      ],
      
      // 产地选项
      locations: [
        { value: 'beijing', label: '北京', count: 120 },
        { value: 'shandong', label: '山东', count: 89 },
        { value: 'henan', label: '河南', count: 76 },
        { value: 'hebei', label: '河北', count: 65 },
        { value: 'jiangsu', label: '江苏', count: 54 },
        { value: 'anhui', label: '安徽', count: 43 }
      ]
    }
  },
  computed: {
    mainCategories() {
      return this.categories.filter(cat => cat.level === 1)
    },
    
    subCategories() {
      if (!this.selectedMainCategory) return []
      return this.categories.filter(cat => cat.parent_id === this.selectedMainCategory)
    },
    
    selectedCategories() {
      const categories = []
      if (this.selectedMainCategory) {
        categories.push(this.selectedMainCategory)
      }
      categories.push(...this.selectedSubCategories)
      return categories
    },
    
    hasActiveFilters() {
      return this.selectedCategories.length > 0 || 
             this.selectedTags.length > 0 || 
             this.selectedPriceRange || 
             this.selectedLocation
    },
    
    activeFilters() {
      const filters = []
      
      // 分类筛选
      if (this.selectedMainCategory) {
        const mainCat = this.categories.find(cat => cat.id === this.selectedMainCategory)
        if (mainCat) {
          filters.push({
            key: `main-${this.selectedMainCategory}`,
            label: mainCat.name,
            type: 'category'
          })
        }
      }
      
      this.selectedSubCategories.forEach(subCatId => {
        const subCat = this.subCategories.find(cat => cat.id === subCatId)
        if (subCat) {
          filters.push({
            key: `sub-${subCatId}`,
            label: subCat.name,
            type: 'subcategory'
          })
        }
      })
      
      // 标签筛选
      this.selectedTags.forEach(tagKey => {
        const tag = this.productTags.find(t => t.key === tagKey)
        if (tag) {
          filters.push({
            key: `tag-${tagKey}`,
            label: tag.label,
            type: 'tag'
          })
        }
      })
      
      // 价格筛选
      if (this.selectedPriceRange) {
        const range = this.priceRanges.find(r => r.key === this.selectedPriceRange)
        if (range) {
          filters.push({
            key: `price-${this.selectedPriceRange}`,
            label: range.label,
            type: 'price'
          })
        }
      }
      
      // 产地筛选
      if (this.selectedLocation) {
        const location = this.locations.find(l => l.value === this.selectedLocation)
        if (location) {
          filters.push({
            key: `location-${this.selectedLocation}`,
            label: location.label,
            type: 'location'
          })
        }
      }
      
      return filters
    }
  },
  methods: {
    // 清除分类选择
    clearCategorySelection() {
      this.selectedMainCategory = null
      this.selectedSubCategories = []
      // 确保触发事件，通知父组件显示全部分类
      this.emitChange()
      
      // 直接触发一个明确的全部分类事件
      this.$emit('select-all-categories')
    },
    
    // 获取分类图标
    getCategoryIcon(category) {
      // 根据分类名称或ID返回合适的图标
      const iconMap = {
        '新鲜蔬菜': 'el-icon-cherry',
        '新鲜水果': 'el-icon-apple',
        '粮油调料': 'el-icon-food',
        '肉禽蛋奶': 'el-icon-chicken',
        '水产海鲜': 'el-icon-fish',
        '农用工具': 'el-icon-tools'
      }
      
      return category.icon || iconMap[category.name] || 'el-icon-goods'
    },
    
    // 检查是否有子分类
    hasSubCategories(categoryId) {
      return this.categories.some(cat => cat.parent_id === categoryId)
    },
    
    // 选择主分类
    selectMainCategory(categoryId) {
      if (this.selectedMainCategory === categoryId) {
        this.selectedMainCategory = null
      } else {
        this.selectedMainCategory = categoryId
        this.selectedSubCategories = []
      }
      this.emitChange()
    },
    
    handleSubCategoryChange() {
      this.emitChange()
    },
    
    toggleTag(tagKey) {
      const index = this.selectedTags.indexOf(tagKey)
      if (index > -1) {
        this.selectedTags.splice(index, 1)
      } else {
        this.selectedTags.push(tagKey)
      }
      this.emitChange()
    },
    
    selectPriceRange(rangeKey) {
      if (this.selectedPriceRange === rangeKey) {
        this.selectedPriceRange = null
      } else {
        this.selectedPriceRange = rangeKey
        this.customPriceRange = { min: null, max: null }
      }
      this.emitChange()
    },
    
    applyCustomPriceRange() {
      if (this.customPriceRange.min || this.customPriceRange.max) {
        this.selectedPriceRange = null
        this.emitChange()
      }
    },
    
    handleLocationChange() {
      this.emitChange()
    },
    
    removeFilter(filter) {
      switch (filter.type) {
        case 'category':
          this.selectedMainCategory = null
          this.selectedSubCategories = []
          break
        case 'subcategory': {
          const subCatId = parseInt(filter.key.replace('sub-', ''))
          const index = this.selectedSubCategories.indexOf(subCatId)
          if (index > -1) {
            this.selectedSubCategories.splice(index, 1)
          }
          break
        }
        case 'tag': {
          const tagKey = filter.key.replace('tag-', '')
          const tagIndex = this.selectedTags.indexOf(tagKey)
          if (tagIndex > -1) {
            this.selectedTags.splice(tagIndex, 1)
          }
          break
        }
        case 'price':
          this.selectedPriceRange = null
          this.customPriceRange = { min: null, max: null }
          break
        case 'location':
          this.selectedLocation = null
          break
      }
      this.emitChange()
    },
    
    clearAll() {
      this.selectedMainCategory = null
      this.selectedSubCategories = []
      this.selectedTags = []
      this.selectedPriceRange = null
      this.selectedLocation = null
      this.customPriceRange = { min: null, max: null }
      this.emitChange()
    },
    
    getMainCategoryName(categoryId) {
      const category = this.categories.find(cat => cat.id === categoryId)
      return category ? category.name : ''
    },
    
    emitChange() {
      let selectedPriceRange = null
      if (this.selectedPriceRange) {
        const range = this.priceRanges.find(r => r.key === this.selectedPriceRange)
        if (range) {
          selectedPriceRange = {
            min: range.min,
            max: range.max
          }
        }
      }
      
      const filterData = {
        category: this.selectedMainCategory,
        subCategories: this.selectedSubCategories,
        tags: this.selectedTags,
        priceRange: selectedPriceRange,
        customPriceRange: this.customPriceRange.min || this.customPriceRange.max ? 
          this.customPriceRange : null,
        location: this.selectedLocation
      }
      
      this.$emit('input', filterData)
      this.$emit('filter-change', filterData)
    }
  },
  
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          // 确保默认为全部分类
          this.selectedMainCategory = newVal.category || null
          this.selectedSubCategories = newVal.subCategories || []
          this.selectedTags = newVal.tags || []
          this.selectedPriceRange = newVal.priceRange ? newVal.priceRange.key : null
          this.selectedLocation = newVal.location || null
          this.customPriceRange = newVal.customPriceRange || { min: null, max: null }
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    // 确保默认选中全部分类
    this.clearCategorySelection()
  }
}
</script>

<style lang="scss" scoped>
.category-filter {
  background: white;
  border-radius: 8px;
  padding: 15px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
  
  .filter-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 8px;
      color: #409eff;
    }
  }
}

.all-categories-button {
  margin-bottom: 10px;
  
  .all-button {
    width: 100%;
    text-align: left;
    padding-left: 12px;
    
    &.active {
      background-color: #409eff;
      color: white;
      border-color: #409eff;
    }
    
    i {
      margin-right: 5px;
    }
  }
}

.main-categories {
  margin-bottom: 15px;
  
  .category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 6px;
    background-color: #f5f7fa;
    
    &:hover {
      background-color: #ecf5ff;
    }
    
    &.active {
      background-color: #409eff;
      color: white;
      
      .product-count {
        color: rgba(255, 255, 255, 0.8) !important;
      }
    }
    
    .category-content {
      display: flex;
      align-items: center;
      flex: 1;
      
      i {
        margin-right: 8px;
        font-size: 16px;
      }
      
      .category-name {
        font-weight: 500;
        margin-right: 8px;
        flex: 1;
      }
      
      .product-count {
        font-size: 12px;
        color: #909399;
        min-width: 30px;
        text-align: right;
      }
    }
  }
}

.sub-categories {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  
  .sub-category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    
    .sub-title {
      font-weight: 600;
      color: #409eff;
    }
  }
  
  .sub-category-list {
    .category-item {
      padding: 6px 0;
      
      .el-checkbox {
        width: 100%;
        
        .category-content {
          display: flex;
          justify-content: space-between;
          width: 100%;
          
          .category-name {
            flex: 1;
          }
          
          .product-count {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
}

.quick-filters {
  .filter-section {
    margin-bottom: 15px;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #606266;
      margin: 0 0 10px 0;
    }
    
    .filter-tags,
    .price-ranges {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      
      .filter-tag {
        cursor: pointer;
        transition: all 0.2s ease;
        margin-bottom: 5px;
        
        &:hover {
          transform: translateY(-1px);
        }
      }
    }
    
    .custom-price-range {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 10px;
      
      .price-input {
        width: 80px;
      }
      
      .price-separator {
        color: #909399;
      }
    }
    
    .location-filters {
      .location-select {
        width: 100%;
        
        .location-count {
          float: right;
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }
}

.active-filters {
  border-top: 1px solid #ebeef5;
  padding-top: 12px;
  margin-top: 10px;
  
  .active-filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    
    .title {
      font-size: 14px;
      font-weight: 600;
      color: #606266;
    }
  }
  
  .active-filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    
    .active-filter-tag {
      background-color: #f4f4f5;
      border-color: #e9e9eb;
      margin-bottom: 5px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .category-filter {
    padding: 15px;
  }
  
  .filter-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .quick-filters {
    .filter-section {
      .custom-price-range {
        flex-direction: column;
        align-items: stretch;
        
        .price-input {
          width: 100%;
        }
        
        .price-separator {
          text-align: center;
        }
      }
    }
  }
}
</style>