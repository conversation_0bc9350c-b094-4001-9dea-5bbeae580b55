<template>
  <div class="record-status-manager">
    <!-- 状态流程图 -->
    <div class="status-flow-section">
      <h3 class="section-title">
        <i class="el-icon-share"></i>
        状态流程
      </h3>
      
      <div class="status-flow">
        <div 
          class="status-node" 
          v-for="(status, index) in statusFlow" 
          :key="status.value"
          :class="{ 
            'active': currentStatus === status.value,
            'completed': isStatusCompleted(status.value),
            'disabled': !canTransitionTo(status.value)
          }"
        >
          <div class="node-icon">
            <i :class="status.icon"></i>
          </div>
          <div class="node-content">
            <h4>{{ status.label }}</h4>
            <p>{{ status.description }}</p>
          </div>
          
          <!-- 连接线 -->
          <div 
            v-if="index < statusFlow.length - 1" 
            class="connection-line"
            :class="{ 'active': isStatusCompleted(status.value) }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 状态操作面板 -->
    <div class="status-actions-section">
      <h3 class="section-title">
        <i class="el-icon-setting"></i>
        状态操作
      </h3>
      
      <div class="actions-grid">
        <!-- 保存草稿 -->
        <div 
          class="action-card" 
          :class="{ 'disabled': !canSaveDraft }"
          @click="saveDraft"
        >
          <div class="action-icon draft">
            <i class="el-icon-document"></i>
          </div>
          <div class="action-content">
            <h4>保存草稿</h4>
            <p>保存当前编辑内容</p>
          </div>
          <div class="action-status">
            <el-tag v-if="isDraft" type="info" size="small">当前状态</el-tag>
          </div>
        </div>

        <!-- 提交审核 -->
        <div 
          class="action-card" 
          :class="{ 'disabled': !canSubmitAudit }"
          @click="submitAudit"
        >
          <div class="action-icon submit">
            <i class="el-icon-upload2"></i>
          </div>
          <div class="action-content">
            <h4>提交审核</h4>
            <p>提交给管理员审核</p>
          </div>
          <div class="action-status">
            <el-tag v-if="isPendingAudit" type="warning" size="small">当前状态</el-tag>
          </div>
        </div>

        <!-- 撤回审核 -->
        <div 
          class="action-card" 
          :class="{ 'disabled': !canWithdrawAudit }"
          @click="withdrawAudit"
        >
          <div class="action-icon withdraw">
            <i class="el-icon-back"></i>
          </div>
          <div class="action-content">
            <h4>撤回审核</h4>
            <p>撤回审核申请</p>
          </div>
        </div>

        <!-- 重新提交 -->
        <div 
          class="action-card" 
          :class="{ 'disabled': !canResubmit }"
          @click="resubmit"
        >
          <div class="action-icon resubmit">
            <i class="el-icon-refresh"></i>
          </div>
          <div class="action-content">
            <h4>重新提交</h4>
            <p>修改后重新提交</p>
          </div>
          <div class="action-status">
            <el-tag v-if="isRejected" type="danger" size="small">需要修改</el-tag>
          </div>
        </div>

        <!-- 下架产品 -->
        <div 
          class="action-card" 
          :class="{ 'disabled': !canTakeDown }"
          @click="takeDown"
        >
          <div class="action-icon takedown">
            <i class="el-icon-remove"></i>
          </div>
          <div class="action-content">
            <h4>下架产品</h4>
            <p>从平台下架</p>
          </div>
        </div>

        <!-- 重新上架 -->
        <div 
          class="action-card" 
          :class="{ 'disabled': !canRepublish }"
          @click="republish"
        >
          <div class="action-icon republish">
            <i class="el-icon-circle-check"></i>
          </div>
          <div class="action-content">
            <h4>重新上架</h4>
            <p>重新发布产品</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 状态历史 -->
    <div class="status-history-section">
      <h3 class="section-title">
        <i class="el-icon-time"></i>
        状态历史
      </h3>
      
      <div class="history-timeline">
        <el-timeline>
          <el-timeline-item
            v-for="history in statusHistory"
            :key="history.id"
            :timestamp="formatTime(history.timestamp)"
            :type="getHistoryType(history.action)"
            placement="top"
          >
            <el-card class="history-card">
              <div class="history-content">
                <div class="history-action">
                  <i :class="getHistoryIcon(history.action)"></i>
                  <span>{{ getHistoryLabel(history.action) }}</span>
                </div>
                <div class="history-details">
                  <p v-if="history.operator"><strong>操作人：</strong>{{ history.operator }}</p>
                  <p v-if="history.comment"><strong>备注：</strong>{{ history.comment }}</p>
                  <p v-if="history.reason"><strong>原因：</strong>{{ history.reason }}</p>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <!-- 状态变更确认对话框 -->
    <el-dialog
      :title="confirmDialog.title"
      :visible.sync="showConfirmDialog"
      width="500px"
    >
      <div class="confirm-content">
        <div class="confirm-icon">
          <i :class="confirmDialog.icon" :style="{ color: confirmDialog.color }"></i>
        </div>
        <div class="confirm-text">
          <p>{{ confirmDialog.message }}</p>
          <div v-if="confirmDialog.showComment" class="comment-input">
            <el-input
              type="textarea"
              v-model="confirmDialog.comment"
              :placeholder="confirmDialog.commentPlaceholder"
              :rows="3"
            ></el-input>
          </div>
        </div>
      </div>
      
      <span slot="footer" class="dialog-footer">
        <el-button @click="showConfirmDialog = false">取消</el-button>
        <el-button 
          :type="confirmDialog.confirmType" 
          @click="confirmAction"
          :loading="actionLoading"
        >
          确认
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { 
  updateRecordStatus, 
  getRecordStatusHistory,
  submitForAudit,
  withdrawAudit,
  takeDownRecord,
  republishRecord
} from '@/api/traceability'

export default {
  name: 'RecordStatusManager',
  props: {
    recordId: {
      type: [String, Number],
      required: true
    },
    currentStatus: {
      type: [String, Number],
      default: 0
    },
    recordData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      actionLoading: false,
      showConfirmDialog: false,
      confirmDialog: {
        title: '',
        message: '',
        icon: '',
        color: '',
        confirmType: 'primary',
        action: '',
        showComment: false,
        comment: '',
        commentPlaceholder: ''
      },
      
      // 状态流程定义
      statusFlow: [
        {
          value: 0,
          label: '草稿',
          description: '编辑中的记录',
          icon: 'el-icon-edit'
        },
        {
          value: 1,
          label: '待审核',
          description: '等待管理员审核',
          icon: 'el-icon-time'
        },
        {
          value: 2,
          label: '已发布',
          description: '审核通过并发布',
          icon: 'el-icon-check'
        },
        {
          value: 3,
          label: '已拒绝',
          description: '审核未通过',
          icon: 'el-icon-close'
        },
        {
          value: 4,
          label: '已下架',
          description: '从平台下架',
          icon: 'el-icon-remove'
        }
      ],
      
      // 状态历史
      statusHistory: []
    }
  },
  computed: {
    isDraft() {
      return this.currentStatus === 0
    },
    isPendingAudit() {
      return this.currentStatus === 1
    },
    isPublished() {
      return this.currentStatus === 2
    },
    isRejected() {
      return this.currentStatus === 3
    },
    isTakenDown() {
      return this.currentStatus === 4
    },
    
    // 操作权限判断
    canSaveDraft() {
      return this.isDraft
    },
    canSubmitAudit() {
      return this.isDraft && this.isRecordComplete()
    },
    canWithdrawAudit() {
      return this.isPendingAudit
    },
    canResubmit() {
      return this.isRejected && this.isRecordComplete()
    },
    canTakeDown() {
      return this.isPublished
    },
    canRepublish() {
      return this.isTakenDown
    }
  },
  mounted() {
    this.loadStatusHistory()
  },
  methods: {
    async loadStatusHistory() {
      try {
        const result = await getRecordStatusHistory(this.recordId)
        if (result.success) {
          this.statusHistory = result.data || []
        } else {
          // 使用模拟数据
          this.statusHistory = [
            {
              id: 1,
              action: 'create',
              timestamp: '2024-03-15T10:00:00',
              operator: '销售者',
              comment: '创建溯源记录'
            },
            {
              id: 2,
              action: 'save_draft',
              timestamp: '2024-03-15T10:30:00',
              operator: '销售者',
              comment: '保存草稿'
            }
          ]
        }
      } catch (error) {
        console.error('加载状态历史失败:', error)
      }
    },

    isRecordComplete() {
      // 检查记录是否完整，可以提交审核
      const required = ['productName', 'category', 'productionDate']
      return required.every(field => this.recordData[field])
    },

    isStatusCompleted(status) {
      return status < this.currentStatus || 
             (this.currentStatus === 3 && status <= 1) || // 被拒绝时，前面的状态都算完成
             (this.currentStatus === 4 && status <= 2)    // 已下架时，发布前的状态都算完成
    },

    canTransitionTo(status) {
      // 定义状态转换规则
      const transitions = {
        0: [1], // 草稿 -> 待审核
        1: [0, 2, 3], // 待审核 -> 草稿/已发布/已拒绝
        2: [4], // 已发布 -> 已下架
        3: [1], // 已拒绝 -> 待审核
        4: [2]  // 已下架 -> 已发布
      }
      return transitions[this.currentStatus]?.includes(status) || false
    },

    // 状态操作方法
    saveDraft() {
      if (!this.canSaveDraft) return
      
      this.showConfirmDialog = true
      this.confirmDialog = {
        title: '保存草稿',
        message: '确认保存当前编辑内容为草稿吗？',
        icon: 'el-icon-document',
        color: '#909399',
        confirmType: 'primary',
        action: 'save_draft',
        showComment: true,
        comment: '',
        commentPlaceholder: '请输入保存说明（可选）'
      }
    },

    submitAudit() {
      if (!this.canSubmitAudit) {
        this.$message.warning('记录信息不完整，无法提交审核')
        return
      }
      
      this.showConfirmDialog = true
      this.confirmDialog = {
        title: '提交审核',
        message: '确认提交此记录进行审核吗？提交后将无法编辑。',
        icon: 'el-icon-upload2',
        color: '#e6a23c',
        confirmType: 'warning',
        action: 'submit_audit',
        showComment: false
      }
    },

    withdrawAudit() {
      if (!this.canWithdrawAudit) return
      
      this.showConfirmDialog = true
      this.confirmDialog = {
        title: '撤回审核',
        message: '确认撤回审核申请吗？撤回后可以继续编辑记录。',
        icon: 'el-icon-back',
        color: '#f56c6c',
        confirmType: 'danger',
        action: 'withdraw_audit',
        showComment: true,
        comment: '',
        commentPlaceholder: '请输入撤回原因'
      }
    },

    resubmit() {
      if (!this.canResubmit) {
        this.$message.warning('记录信息不完整，无法重新提交')
        return
      }
      
      this.showConfirmDialog = true
      this.confirmDialog = {
        title: '重新提交',
        message: '确认重新提交此记录进行审核吗？',
        icon: 'el-icon-refresh',
        color: '#409eff',
        confirmType: 'primary',
        action: 'resubmit',
        showComment: true,
        comment: '',
        commentPlaceholder: '请说明修改内容'
      }
    },

    takeDown() {
      if (!this.canTakeDown) return
      
      this.showConfirmDialog = true
      this.confirmDialog = {
        title: '下架产品',
        message: '确认下架此产品吗？下架后用户将无法查询到此记录。',
        icon: 'el-icon-remove',
        color: '#f56c6c',
        confirmType: 'danger',
        action: 'take_down',
        showComment: true,
        comment: '',
        commentPlaceholder: '请输入下架原因'
      }
    },

    republish() {
      if (!this.canRepublish) return
      
      this.showConfirmDialog = true
      this.confirmDialog = {
        title: '重新上架',
        message: '确认重新上架此产品吗？',
        icon: 'el-icon-circle-check',
        color: '#67c23a',
        confirmType: 'success',
        action: 'republish',
        showComment: false
      }
    },

    async confirmAction() {
      this.actionLoading = true
      
      try {
        let result
        const comment = this.confirmDialog.comment
        
        switch (this.confirmDialog.action) {
          case 'save_draft':
            result = await this.updateStatus(0, comment)
            break
          case 'submit_audit':
            result = await submitForAudit(this.recordId)
            break
          case 'withdraw_audit':
            result = await withdrawAudit(this.recordId, { reason: comment })
            break
          case 'resubmit':
            result = await submitForAudit(this.recordId, { comment })
            break
          case 'take_down':
            result = await takeDownRecord(this.recordId, { reason: comment })
            break
          case 'republish':
            result = await republishRecord(this.recordId)
            break
        }
        
        if (result && result.success) {
          this.$message.success('操作成功')
          this.$emit('status-changed', {
            action: this.confirmDialog.action,
            newStatus: result.data?.newStatus,
            comment
          })
          this.loadStatusHistory()
        } else {
          this.$message.error(result?.error || '操作失败')
        }
      } catch (error) {
        console.error('状态操作失败:', error)
        this.$message.error('操作失败')
      } finally {
        this.actionLoading = false
        this.showConfirmDialog = false
      }
    },

    async updateStatus(newStatus, comment) {
      return await updateRecordStatus(this.recordId, {
        status: newStatus,
        comment
      })
    },

    // 工具方法
    getHistoryType(action) {
      const typeMap = {
        create: 'primary',
        save_draft: 'info',
        submit_audit: 'warning',
        approve: 'success',
        reject: 'danger',
        withdraw_audit: 'info',
        take_down: 'danger',
        republish: 'success'
      }
      return typeMap[action] || 'primary'
    },

    getHistoryIcon(action) {
      const iconMap = {
        create: 'el-icon-plus',
        save_draft: 'el-icon-document',
        submit_audit: 'el-icon-upload2',
        approve: 'el-icon-check',
        reject: 'el-icon-close',
        withdraw_audit: 'el-icon-back',
        take_down: 'el-icon-remove',
        republish: 'el-icon-circle-check'
      }
      return iconMap[action] || 'el-icon-info'
    },

    getHistoryLabel(action) {
      const labelMap = {
        create: '创建记录',
        save_draft: '保存草稿',
        submit_audit: '提交审核',
        approve: '审核通过',
        reject: '审核拒绝',
        withdraw_audit: '撤回审核',
        take_down: '下架产品',
        republish: '重新上架'
      }
      return labelMap[action] || '未知操作'
    },

    formatTime(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return date.toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
.record-status-manager {
  padding: 20px;
}

// 通用标题样式
.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;

  i {
    color: #3b82f6;
  }
}

// 状态流程样式
.status-flow-section {
  margin-bottom: 30px;
  background: #ffffff;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-flow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 20px 0;
}

.status-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  max-width: 150px;
  transition: all 0.3s ease;

  &.active {
    .node-icon {
      background: linear-gradient(135deg, #409eff, #66b1ff);
      color: #ffffff;
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    }

    .node-content h4 {
      color: #409eff;
      font-weight: 600;
    }
  }

  &.completed {
    .node-icon {
      background: linear-gradient(135deg, #67c23a, #85ce61);
      color: #ffffff;
    }

    .node-content h4 {
      color: #67c23a;
    }
  }

  &.disabled {
    opacity: 0.5;

    .node-icon {
      background: #f5f7fa;
      color: #c0c4cc;
    }
  }
}

.node-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  background: #f5f7fa;
  color: #909399;
  transition: all 0.3s ease;
  margin-bottom: 12px;
}

.node-content {
  text-align: center;

  h4 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 500;
    color: #606266;
    transition: all 0.3s ease;
  }

  p {
    margin: 0;
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
  }
}

.connection-line {
  position: absolute;
  top: 30px;
  right: -50%;
  width: 100%;
  height: 2px;
  background: #e4e7ed;
  transition: all 0.3s ease;

  &.active {
    background: linear-gradient(90deg, #67c23a, #85ce61);
  }
}

// 状态操作样式
.status-actions-section {
  margin-bottom: 30px;
  background: #ffffff;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.action-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;

  &:hover:not(.disabled) {
    border-color: #409eff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;

    .action-icon {
      background: #f5f7fa;
      color: #c0c4cc;
    }
  }
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;

  &.draft {
    background: #e1f3ff;
    color: #409eff;
  }

  &.submit {
    background: #fdf6ec;
    color: #e6a23c;
  }

  &.withdraw {
    background: #fef0f0;
    color: #f56c6c;
  }

  &.resubmit {
    background: #e1f3ff;
    color: #409eff;
  }

  &.takedown {
    background: #fef0f0;
    color: #f56c6c;
  }

  &.republish {
    background: #f0f9ff;
    color: #67c23a;
  }
}

.action-content {
  flex: 1;

  h4 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 600;
    color: #303133;
  }

  p {
    margin: 0;
    font-size: 12px;
    color: #909399;
  }
}

.action-status {
  position: absolute;
  top: 8px;
  right: 8px;
}

// 状态历史样式
.status-history-section {
  background: #ffffff;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.history-timeline {
  max-height: 400px;
  overflow-y: auto;

  ::v-deep .el-timeline {
    padding-left: 0;
  }

  ::v-deep .el-timeline-item__timestamp {
    color: #409eff;
    font-weight: 500;
  }
}

.history-card {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

  ::v-deep .el-card__body {
    padding: 16px;
  }
}

.history-content {
  .history-action {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 600;
    color: #303133;

    i {
      color: #409eff;
    }
  }

  .history-details {
    p {
      margin: 4px 0;
      font-size: 14px;
      color: #606266;

      strong {
        color: #303133;
      }
    }
  }
}

// 确认对话框样式
.confirm-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;

  .confirm-icon {
    font-size: 32px;
    flex-shrink: 0;
  }

  .confirm-text {
    flex: 1;

    p {
      margin: 0 0 16px 0;
      font-size: 16px;
      color: #606266;
      line-height: 1.5;
    }

    .comment-input {
      margin-top: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .record-status-manager {
    padding: 15px;
  }

  .status-flow {
    flex-direction: column;
    gap: 20px;
  }

  .status-node {
    max-width: none;
    width: 100%;
  }

  .connection-line {
    display: none;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  .action-card {
    flex-direction: column;
    text-align: center;

    .action-status {
      position: static;
      margin-top: 8px;
    }
  }
}

@media (max-width: 480px) {
  .status-flow-section,
  .status-actions-section,
  .status-history-section {
    padding: 16px;
  }

  .node-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .action-icon {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }
}
</style>
