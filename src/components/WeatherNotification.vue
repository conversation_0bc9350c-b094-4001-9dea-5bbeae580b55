<template>
  <transition name="slide-fade">
    <div v-if="visible" class="weather-notification" :class="notificationLevel">
      <div class="notification-header">
        <i :class="notificationIcon" />
        <h3>{{ notification.title }}</h3>
        <i class="el-icon-close close-icon" @click="close" />
      </div>
      <div class="notification-content">
        <p>{{ notification.description }}</p>
        <div v-if="notification.weatherInfo" class="weather-info">
          <i class="el-icon-data-analysis"></i> {{ notification.weatherInfo }}
        </div>
        <div v-if="notification.suggestions && notification.suggestions.length > 0" class="notification-suggestions">
          <strong>农事建议：</strong>
          <ul>
            <li v-for="(suggestion, index) in notification.suggestions" :key="index" 
                class="animate__animated animate__fadeInRight"
                :style="{ animationDelay: `${index * 0.1}s` }">
              {{ suggestion }}
            </li>
          </ul>
        </div>
        <div v-else-if="notification.farmingSuggestion" class="notification-suggestions">
          <strong>农事建议：</strong>
          <p>{{ notification.farmingSuggestion }}</p>
        </div>
        <div class="notification-footer">
          <el-button size="mini" type="text" @click="$router.push('/weather'); close()">
            查看详细天气 <i class="el-icon-d-arrow-right"></i>
          </el-button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'WeatherNotification',
  props: {
    notification: {
      type: Object,
      required: true
    },
    duration: {
      type: Number,
      default: 8000 // 增加显示时间，让用户有更多时间阅读
    },
    autoClose: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      visible: false,
      timer: null
    }
  },
  computed: {
    notificationLevel() {
      return this.notification.level || 'normal'
    },
    notificationIcon() {
      return this.notification.icon || this.getIconByType(this.notification.type)
    }
  },
  methods: {
    show() {
      this.visible = true
      if (this.autoClose) {
        this.startTimer()
      }
    },
    close() {
      this.visible = false
      this.clearTimer()
      this.$emit('close')
    },
    startTimer() {
      this.clearTimer()
      this.timer = setTimeout(() => {
        this.close()
      }, this.duration)
    },
    clearTimer() {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
    },
    getIconByType(type) {
      const iconMap = {
        temperature: 'el-icon-sunrise',
        rain: 'el-icon-umbrella',
        wind: 'el-icon-wind-power',
        default: 'el-icon-cloudy'
      }
      return iconMap[type] || iconMap.default
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.show()
    })
  },
  beforeDestroy() {
    this.clearTimer()
  }
}
</script>

<style scoped>
.weather-notification {
  position: fixed;
  top: 50%; /* 修改：垂直居中 */
  transform: translateY(-50%); /* 新增：配合 top: 50% 实现垂直居中 */
  right: 20px;
  width: 380px; /* 增加宽度 */
  background-color: #fff;
  border-radius: 12px; /* 增加圆角 */
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  transition: all 0.3s ease;
  border-left: 4px solid #409EFF; /* 默认使用蓝色主题色 */
}

.weather-notification.info {
  border-left: 4px solid #409EFF; /* 信息类通知 - 蓝色 */
}

.weather-notification.info .notification-header {
  background-color: rgba(64, 158, 255, 0.1);
}

.weather-notification.warning {
  border-left: 4px solid #E6A23C; /* 警告类通知 - 橙色 */
}

.weather-notification.warning .notification-header {
  background-color: rgba(230, 162, 60, 0.1);
}

.weather-notification.error, .weather-notification.danger {
  border-left: 4px solid #F56C6C; /* 错误类通知 - 红色 */
}

.weather-notification.error .notification-header, 
.weather-notification.danger .notification-header {
  background-color: rgba(245, 108, 108, 0.1);
}

.weather-notification.normal {
  border-left: 4px solid #67C23A; /* 正常/建议类通知 - 绿色 */
}

.weather-notification.normal .notification-header {
  background-color: rgba(103, 194, 58, 0.1);
}

.notification-header {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.notification-header i {
  font-size: 22px;
  margin-right: 12px;
}

.notification-header h3 {
  flex: 1;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.notification-header .close-icon {
  cursor: pointer;
  font-size: 18px;
  margin-right: 0;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.notification-header .close-icon:hover {
  opacity: 1;
}

.notification-content {
  padding: 16px;
}

.notification-content p {
  margin: 0 0 12px;
  line-height: 1.6;
  font-size: 14px;
  color: #333;
}

.weather-info {
  margin: 12px 0;
  padding: 8px 10px;
  background-color: rgba(0, 0, 0, 0.04);
  border-radius: 4px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.weather-info i {
  margin-right: 8px;
  color: #409EFF;
}

.notification-suggestions {
  margin-top: 12px;
}

.notification-suggestions strong {
  display: block;
  margin-bottom: 8px;
  color: #303133;
  font-size: 15px;
}

.notification-suggestions ul {
  padding-left: 20px;
  margin: 0;
}

.notification-suggestions li {
  margin-bottom: 8px;
  line-height: 1.5;
  font-size: 14px;
  color: #606266;
  position: relative;
}

.notification-footer {
  margin-top: 14px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid rgba(0, 0, 0, 0.04);
  padding-top: 10px;
}

/* 动画效果 */
.slide-fade-enter-active {
  transition: all 0.5s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.5s cubic-bezier(0.5, 0, 0.8, 1);
}

.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateX(100%) translateY(-50%);
  opacity: 0;
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate__animated {
  animation-duration: 0.8s;
  animation-fill-mode: both;
}

.animate__fadeInRight {
  animation-name: fadeInRight;
}
</style>