<template>
  <div class="favorite-button-container">
    <el-dropdown 
      v-if="showFolderSelect && folders.length > 1"
      @command="handleFolderSelect"
      trigger="click"
      placement="bottom-start"
    >
      <el-button
        :type="favorited ? 'warning' : 'default'"
        :class="['favorite-button', { 'favorited': favorited }]"
        :size="size"
        :loading="loading"
        :disabled="disabled"
      >
        <i :class="favoriteIcon"></i>
        <span v-if="showText" class="favorite-text">
          {{ favorited ? '已收藏' : '收藏' }}
        </span>
        <span v-if="showCount && favoriteCount > 0" class="favorite-count">
          {{ formatCount(favoriteCount) }}
        </span>
        <i class="el-icon-arrow-down el-icon--right"></i>
      </el-button>
      
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item 
          v-for="folder in folders" 
          :key="folder.id"
          :command="folder.folderName"
          :class="{ 'is-active': currentFolder === folder.folderName }"
        >
          <i class="el-icon-folder"></i>
          {{ folder.folderName }}
          <span class="folder-count">({{ folder.productCount }})</span>
        </el-dropdown-item>
        <el-dropdown-item divided command="__create_new__">
          <i class="el-icon-plus"></i>
          新建收藏夹
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    
    <el-button
      v-else
      :type="favorited ? 'warning' : 'default'"
      :class="['favorite-button', { 'favorited': favorited }]"
      :size="size"
      :loading="loading"
      @click="handleToggleFavorite"
      :disabled="disabled"
    >
      <i :class="favoriteIcon"></i>
      <span v-if="showText" class="favorite-text">
        {{ favorited ? '已收藏' : '收藏' }}
      </span>
      <span v-if="showCount && favoriteCount > 0" class="favorite-count">
        {{ formatCount(favoriteCount) }}
      </span>
    </el-button>
  </div>
</template>

<script>
import { addFavorite, removeFavorite, checkFavoriteStatus, getUserFolders, createFolder } from '@/api/favorite'

export default {
  name: 'FavoriteButton',
  props: {
    productId: {
      type: [Number, String],
      required: true
    },
    size: {
      type: String,
      default: 'small',
      validator: value => ['mini', 'small', 'medium'].includes(value)
    },
    showText: {
      type: Boolean,
      default: true
    },
    showCount: {
      type: Boolean,
      default: true
    },
    showFolderSelect: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 初始收藏状态
    initialFavorited: {
      type: Boolean,
      default: null
    },
    // 初始收藏数量
    initialCount: {
      type: [Number, String],
      default: null
    }
  },
  
  data() {
    return {
      favorited: false,
      favoriteCount: 0,
      currentFolder: '',
      loading: false,
      folders: []
    }
  },
  
  computed: {
    favoriteIcon() {
      return this.favorited ? 'el-icon-star-on' : 'el-icon-star-off'
    }
  },
  
  watch: {
    productId: {
      immediate: true,
      handler(newId) {
        if (newId) {
          this.initFavoriteStatus()
        }
      }
    }
  },
  
  methods: {
    // 初始化收藏状态
    async initFavoriteStatus() {
      // 如果有初始值，直接使用
      if (this.initialFavorited !== null) {
        this.favorited = this.initialFavorited
      }
      if (this.initialCount !== null) {
        this.favoriteCount = Number(this.initialCount) || 0
      }
      
      // 加载收藏夹列表
      await this.loadFolders()
      
      // 如果没有初始值，则请求接口
      if (this.initialFavorited === null) {
        await this.loadFavoriteStatus()
      }
    },
    
    // 加载收藏夹列表
    async loadFolders() {
      try {
        const response = await getUserFolders()
        if (response && response.code === 200) {
          this.folders = response.data || []
        }
      } catch (error) {
        console.error('加载收藏夹列表失败:', error)
        this.folders = [{ folder_name: '默认收藏夹', count: 0 }]
      }
    },
    
    // 加载收藏状态
    async loadFavoriteStatus() {
      try {
        const response = await checkFavoriteStatus(this.productId)
        if (response.success) {
          this.favorited = response.data.favorited
          this.currentFolder = response.data.folderName || ''
          this.favoriteCount = Number(response.data.favoriteCount) || 0
        }
      } catch (error) {
        console.error('加载收藏状态失败:', error)
      }
    },
    
    // 切换收藏状态（默认收藏夹）
    async handleToggleFavorite() {
      if (this.favorited) {
        await this.handleRemoveFavorite()
      } else {
        await this.handleAddFavorite('默认收藏夹')
      }
    },
    
    // 选择收藏夹
    async handleFolderSelect(folderName) {
      if (folderName === '__create_new__') {
        this.$message.info('新建收藏夹功能开发中')
        return
      }
      
      if (this.currentFolder === folderName) {
        // 如果已经在这个收藏夹中，则取消收藏
        await this.handleRemoveFavorite()
      } else {
        // 添加到指定收藏夹
        await this.handleAddFavorite(folderName)
      }
    },
    
    // 添加收藏
    async handleAddFavorite(folderName = '默认收藏夹') {
      if (this.loading || this.disabled) {
        return
      }
      
      // 检查用户是否登录
      const userInfo = JSON.parse(localStorage.getItem('sfap_user') || '{}')
      if (!userInfo.id) {
        this.$message.warning('请先登录')
        this.$emit('need-login')
        return
      }
      
      this.loading = true
      
      try {
        const response = await addFavorite(userInfo.id, this.productId, folderName)

        // 处理Result格式的响应
        if (response && response.code === 200) {
          this.favorited = true
          this.currentFolder = folderName
          this.favoriteCount += 1

          // 更新收藏夹列表
          await this.loadFolders()

          // 触发事件
          this.$emit('favorite-changed', {
            productId: this.productId,
            favorited: this.favorited,
            folderName: folderName,
            favoriteCount: this.favoriteCount
          })

          this.$message.success(response.message || `已收藏到"${folderName}"`)
        } else {
          throw new Error(response.message || '收藏失败')
        }
      } catch (error) {
        this.$message.error('收藏失败：' + error.message)
        console.error('收藏失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 取消收藏
    async handleRemoveFavorite() {
      if (this.loading || this.disabled) {
        return
      }
      
      const userInfo = JSON.parse(localStorage.getItem('sfap_user') || '{}')
      if (!userInfo.id) {
        return
      }
      
      this.loading = true
      
      try {
        const response = await removeFavorite(userInfo.id, this.productId)
        console.log('取消收藏API响应:', response)

        // 处理Result格式的响应
        if (response && response.code === 200) {
          this.favorited = false
          this.currentFolder = ''
          this.favoriteCount = Math.max(0, this.favoriteCount - 1)

          // 更新收藏夹列表
          await this.loadFolders()

          // 触发事件
          this.$emit('favorite-changed', {
            productId: this.productId,
            favorited: this.favorited,
            folderName: '',
            favoriteCount: this.favoriteCount
          })

          this.$message.success(response.message || '已取消收藏')
        } else {
          throw new Error(response?.message || '取消收藏失败')
        }
      } catch (error) {
        this.$message.error('取消收藏失败：' + (error.message || error))
        console.error('取消收藏失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 格式化数量显示
    formatCount(count) {
      if (count < 1000) {
        return count.toString()
      } else if (count < 10000) {
        return (count / 1000).toFixed(1) + 'k'
      } else {
        return (count / 10000).toFixed(1) + 'w'
      }
    },

    // 创建新收藏夹
    async createNewFolder(folderName) {
      try {
        const response = await createFolder(folderName)
        if (response && response.code === 200) {
          await this.loadFolders()
          this.$message.success('创建收藏夹成功')
          return true
        } else {
          this.$message.error(response?.message || '创建收藏夹失败')
          return false
        }
      } catch (error) {
        console.error('创建收藏夹失败:', error)
        this.$message.error('创建收藏夹失败')
        return false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.favorite-button-container {
  display: inline-block;
  
  .favorite-button {
    border-radius: 20px;
    transition: all 0.3s ease;
    
    &.favorited {
      color: #fff;
      background-color: #e6a23c;
      border-color: #e6a23c;
      
      &:hover {
        background-color: #ebb563;
        border-color: #ebb563;
      }
      
      .el-icon-star-on {
        color: #fff;
      }
    }
    
    &:not(.favorited) {
      &:hover {
        color: #e6a23c;
        border-color: #e6a23c;
        background-color: #fdf6ec;
      }
    }
    
    .favorite-text {
      margin-left: 4px;
    }
    
    .favorite-count {
      margin-left: 4px;
      font-size: 12px;
      opacity: 0.8;
    }
  }
}

.el-dropdown-menu {
  .el-dropdown-item {
    &.is-active {
      color: #e6a23c;
      background-color: #fdf6ec;
    }
    
    .folder-count {
      color: #909399;
      font-size: 12px;
      margin-left: 4px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .favorite-button-container {
    .favorite-button {
      .favorite-text {
        display: none;
      }
      
      .favorite-count {
        margin-left: 2px;
        font-size: 11px;
      }
    }
  }
}
</style>
