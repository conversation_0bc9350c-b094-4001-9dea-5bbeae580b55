<template>
  <div class="like-button-container">
    <el-button
      :type="liked ? 'danger' : 'default'"
      :class="['like-button', { 'liked': liked, 'animate': animating }]"
      :size="size"
      :loading="loading"
      @click="handleToggleLike"
      :disabled="disabled"
    >
      <i :class="likeIcon" :style="{ fontSize: iconSize }" v-if="useElementIcon"></i>
      <span v-else class="unicode-heart" :style="{ fontSize: iconSize, color: liked ? '#f56c6c' : '#909399' }">
        {{ liked ? '♥' : '♡' }}
      </span>
      <span v-if="showText" class="like-text">
        {{ liked ? '已点赞' : '点赞' }}
      </span>
      <span v-if="showCount && likeCount > 0" class="like-count">
        {{ formatCount(likeCount) }}
      </span>
    </el-button>
    
    <!-- 点赞动画效果 -->
    <div v-if="showAnimation" class="like-animation">
      <i class="el-icon-heart animation-heart"></i>
    </div>
  </div>
</template>

<script>
import { toggleLike, isLiked, getProductLikeCount } from '@/api/productLikes'

export default {
  name: 'LikeButton',
  props: {
    productId: {
      type: [Number, String],
      required: true
    },
    size: {
      type: String,
      default: 'small',
      validator: value => ['mini', 'small', 'medium'].includes(value)
    },
    showText: {
      type: Boolean,
      default: true
    },
    showCount: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 初始点赞状态（可选，用于避免重复请求）
    initialLiked: {
      type: Boolean,
      default: null
    },
    // 初始点赞数量（可选，用于避免重复请求）
    initialCount: {
      type: [Number, String],
      default: null
    }
  },
  
  data() {
    return {
      liked: false,
      likeCount: 0,
      loading: false,
      animating: false,
      showAnimation: false
    }
  },
  
  computed: {
    likeIcon() {
      // 点赞状态：实心红色心形图标，未点赞状态：空心心形图标
      return 'el-icon-heart'
    },

    useElementIcon() {
      // 检查Element UI图标是否可用，如果不可用则使用Unicode符号
      return true // 先尝试使用Element UI图标
    },

    iconSize() {
      switch (this.size) {
        case 'mini':
          return '14px'
        case 'small':
          return '16px'
        case 'medium':
          return '18px'
        default:
          return '16px'
      }
    }
  },
  
  watch: {
    productId: {
      immediate: true,
      handler(newId) {
        if (newId) {
          this.initLikeStatus()
        }
      }
    }
  },
  
  methods: {
    // 初始化点赞状态
    async initLikeStatus() {
      // 如果有初始值，直接使用
      if (this.initialLiked !== null) {
        this.liked = this.initialLiked
      }
      if (this.initialCount !== null) {
        this.likeCount = Number(this.initialCount) || 0
      }
      
      // 如果没有初始值，则请求接口
      if (this.initialLiked === null || this.initialCount === null) {
        await this.loadLikeStatus()
      }
    },
    
    // 加载点赞状态
    async loadLikeStatus() {
      try {
        // 并行请求点赞状态和数量
        const [likedResponse, countResponse] = await Promise.all([
          this.initialLiked === null ? isLiked(this.productId) : Promise.resolve({ success: true, data: this.initialLiked }),
          this.initialCount === null ? getProductLikeCount(this.productId) : Promise.resolve({ success: true, data: this.initialCount })
        ])

        if (likedResponse.success) {
          this.liked = likedResponse.data
        }

        if (countResponse.success) {
          this.likeCount = Number(countResponse.data) || 0
        }
      } catch (error) {
        console.error('加载点赞状态失败:', error)
      }
    },
    
    // 切换点赞状态
    async handleToggleLike() {
      if (this.loading || this.disabled) {
        return
      }
      
      // 检查用户是否登录
      const userInfo = JSON.parse(localStorage.getItem('sfap_user') || '{}')
      if (!userInfo.id) {
        this.$message.warning('请先登录')
        this.$emit('need-login')
        return
      }
      
      this.loading = true
      const originalLiked = this.liked
      const originalCount = this.likeCount
      
      try {
        // 乐观更新UI
        this.liked = !this.liked
        this.likeCount += this.liked ? 1 : -1
        
        // 触发动画
        if (this.liked) {
          this.triggerLikeAnimation()
        }
        
        // 调用API
        const response = await toggleLike(this.productId)

        if (response.success) {
          // API返回的是最新的点赞状态
          this.liked = response.data

          // 重新获取准确的点赞数量
          const countResponse = await getProductLikeCount(this.productId)
          if (countResponse.success) {
            this.likeCount = Number(countResponse.data) || 0
          }

          // 触发事件
          this.$emit('like-changed', {
            productId: this.productId,
            liked: this.liked,
            likeCount: this.likeCount
          })

          // 显示提示
          this.$message.success(this.liked ? '点赞成功' : '取消点赞成功')
        } else {
          throw new Error(response.message || '操作失败')
        }
      } catch (error) {
        // 恢复原状态
        this.liked = originalLiked
        this.likeCount = originalCount
        
        this.$message.error('操作失败：' + error.message)
        console.error('点赞操作失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 触发点赞动画
    triggerLikeAnimation() {
      this.animating = true
      this.showAnimation = true
      
      setTimeout(() => {
        this.animating = false
      }, 300)
      
      setTimeout(() => {
        this.showAnimation = false
      }, 1000)
    },
    
    // 格式化数量显示
    formatCount(count) {
      if (count < 1000) {
        return count.toString()
      } else if (count < 10000) {
        return (count / 1000).toFixed(1) + 'k'
      } else {
        return (count / 10000).toFixed(1) + 'w'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.like-button-container {
  position: relative;
  display: inline-block;
  
  .like-button {
    position: relative;
    border-radius: 20px;
    transition: all 0.3s ease;
    
    &.liked {
      color: #fff;
      background-color: #f56c6c;
      border-color: #f56c6c;
      
      &:hover {
        background-color: #f78989;
        border-color: #f78989;
      }
      
      .el-icon-heart {
        color: #fff;
        // 确保实心效果
        text-shadow: none;
        -webkit-text-stroke: none;
        -webkit-text-fill-color: #fff;
        animation: heartBeat 0.6s ease-in-out;
      }
    }
    
    &:not(.liked) {
      .el-icon-heart {
        color: #909399;
        // 创建空心效果
        text-shadow: 0 0 0 1px #909399;
        -webkit-text-stroke: 1px #909399;
        -webkit-text-fill-color: transparent;
      }

      &:hover {
        color: #f56c6c;
        border-color: #f56c6c;
        background-color: #fef0f0;

        .el-icon-heart {
          color: #f56c6c;
          text-shadow: 0 0 0 1px #f56c6c;
          -webkit-text-stroke: 1px #f56c6c;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    
    &.animate {
      transform: scale(1.1);
    }
    
    .like-text {
      margin-left: 4px;
    }
    
    .like-count {
      margin-left: 4px;
      font-size: 12px;
      opacity: 0.8;
    }

    .unicode-heart {
      display: inline-block;
      transition: all 0.3s ease;
      font-weight: bold;
      line-height: 1;

      &:hover {
        transform: scale(1.1);
      }
    }
  }
  
  .like-animation {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 10;
    
    .animation-heart {
      font-size: 24px;
      color: #f56c6c;
      animation: likeAnimation 1s ease-out forwards;
    }
  }
}

@keyframes heartBeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

@keyframes likeAnimation {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .like-button-container {
    .like-button {
      .like-text {
        display: none;
      }
      
      .like-count {
        margin-left: 2px;
        font-size: 11px;
      }
    }
  }
}
</style>
