<template>
  <div class="seller-application-form">
    <el-card class="form-card">
      <div slot="header" class="card-header">
        <h2>申请成为销售者</h2>
      </div>
      
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="6" animated />
      </div>
      
      <div v-else-if="application && application.status !== undefined" class="application-status">
        <div class="status-header">
          <h3>申请状态</h3>
          <el-tag :type="getStatusType(application.status)">{{ getStatusText(application.status) }}</el-tag>
        </div>
        
        <div class="application-details">
          <div class="detail-item">
            <span class="label">农场/基地名称：</span>
            <span>{{ application.farmName }}</span>
          </div>
          <div class="detail-item">
            <span class="label">联系信息：</span>
            <span>{{ application.contactInfo }}</span>
          </div>
          <div class="detail-item">
            <span class="label">申请时间：</span>
            <span>{{ formatDate(application.applyDate) }}</span>
          </div>
          
          <div v-if="application.status === 1 || application.status === 2" class="detail-item">
            <span class="label">审核时间：</span>
            <span>{{ formatDate(application.auditDate) }}</span>
          </div>
          
          <div v-if="application.status === 2 && application.auditComment" class="detail-item">
            <span class="label">拒绝原因：</span>
            <span class="audit-comment">{{ application.auditComment }}</span>
          </div>
          
          <div v-if="application.status === 2" class="reapply-container">
            <el-button type="primary" @click="resetForm">重新申请</el-button>
          </div>
        </div>
      </div>
      
      <div v-else>
        <el-form ref="applicationForm" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="农场/基地名称" prop="farmName">
            <el-input v-model="form.farmName" placeholder="请输入农场或基地名称"></el-input>
          </el-form-item>
          
          <el-form-item label="联系信息" prop="contactInfo">
            <el-input v-model="form.contactInfo" placeholder="请输入联系方式（电话、邮箱等）"></el-input>
          </el-form-item>
          
          <el-form-item label="资质证明" prop="qualificationDocs">
            <el-upload
              class="upload-container"
              action="/api/upload"
              :on-success="handleUploadSuccess"
              :on-remove="handleRemove"
              :before-upload="beforeUpload"
              multiple
              :limit="5"
              :file-list="fileList">
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">请上传农业经营许可证、营业执照等资质证明文件，支持jpg/png格式，不超过10MB</div>
            </el-upload>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="submitForm" :loading="submitting">提交申请</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script>
import { applyForSeller, getSellerApplicationStatus } from '@/api/seller'

export default {
  name: 'SellerApplicationForm',
  data() {
    return {
      loading: true,
      submitting: false,
      application: null,
      form: {
        farmName: '',
        contactInfo: '',
        qualificationDocs: []
      },
      fileList: [],
      rules: {
        farmName: [
          { required: true, message: '请输入农场/基地名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在2到50个字符之间', trigger: 'blur' }
        ],
        contactInfo: [
          { required: true, message: '请输入联系方式', trigger: 'blur' }
        ],
        qualificationDocs: [
          { required: true, message: '请上传至少一个资质证明文件', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getApplicationStatus()
  },
  methods: {
    // 获取申请状态
    async getApplicationStatus() {
      try {
        this.loading = true
        const res = await getSellerApplicationStatus()
        if (res.code === 0 && res.data) {
          this.application = res.data
        }
      } catch (error) {
        console.error('获取申请状态失败', error)
        // 如果返回404或其他错误，表示没有申请记录，不做处理
      } finally {
        this.loading = false
      }
    },
    
    // 提交申请表单
    submitForm() {
      this.$refs.applicationForm.validate(async valid => {
        if (!valid) {
          return false
        }
        
        if (this.form.qualificationDocs.length === 0) {
          this.$message.error('请上传至少一个资质证明文件')
          return false
        }
        
        try {
          this.submitting = true
          const res = await applyForSeller(this.form)
          if (res.code === 0) {
            this.$message.success('申请提交成功')
            this.getApplicationStatus() // 刷新状态
          } else {
            this.$message.error(res.msg || '申请提交失败')
          }
        } catch (error) {
          console.error('提交申请失败', error)
          this.$message.error('提交申请失败，请稍后重试')
        } finally {
          this.submitting = false
        }
      })
    },
    
    // 重置表单
    resetForm() {
      this.application = null
      this.$refs.applicationForm && this.$refs.applicationForm.resetFields()
      this.form.qualificationDocs = []
      this.fileList = []
    },
    
    // 上传成功回调
    handleUploadSuccess(response, file, fileList) {
      if (response.code === 0 && response.data) {
        this.form.qualificationDocs.push(response.data)
        this.$message.success('上传成功')
      } else {
        this.$message.error(response.msg || '上传失败')
        // 从文件列表中移除上传失败的文件
        const index = fileList.indexOf(file)
        if (index !== -1) {
          fileList.splice(index, 1)
        }
      }
    },
    
    // 移除文件回调
    handleRemove(file, fileList) {
      const fileUrl = file.response && file.response.data
      if (fileUrl) {
        const index = this.form.qualificationDocs.indexOf(fileUrl)
        if (index !== -1) {
          this.form.qualificationDocs.splice(index, 1)
        }
      }
    },
    
    // 上传前校验
    beforeUpload(file) {
      const isImage = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt10M = file.size / 1024 / 1024 < 10
      
      if (!isImage) {
        this.$message.error('只能上传JPG/PNG格式的图片')
        return false
      }
      
      if (!isLt10M) {
        this.$message.error('图片大小不能超过10MB')
        return false
      }
      
      return true
    },
    
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'warning', // 待审核
        1: 'success', // 通过
        2: 'danger'   // 拒绝
      }
      return statusMap[status] || 'info'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已拒绝'
      }
      return statusMap[status] || '未知状态'
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '暂无'
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.seller-application-form {
  max-width: 800px;
  margin: 0 auto;
  
  .form-card {
    margin-top: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h2 {
        margin: 0;
        font-size: 18px;
        color: #333;
      }
    }
    
    .loading-container {
      padding: 20px 0;
    }
    
    .application-status {
      padding: 10px 0;
      
      .status-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        
        h3 {
          margin: 0;
          margin-right: 10px;
        }
      }
      
      .application-details {
        background-color: #f9f9f9;
        padding: 15px;
        border-radius: 4px;
        
        .detail-item {
          margin-bottom: 10px;
          
          .label {
            font-weight: bold;
            color: #606266;
          }
          
          .audit-comment {
            color: #f56c6c;
          }
        }
        
        .reapply-container {
          margin-top: 20px;
          text-align: center;
        }
      }
    }
    
    .upload-container {
      .el-upload__tip {
        line-height: 1.4;
      }
    }
  }
}
</style> 