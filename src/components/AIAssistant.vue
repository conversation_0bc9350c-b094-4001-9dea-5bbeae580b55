<template>
  <div 
    class="ai-assistant" 
    :class="{ 'ai-assistant--minimized': isMinimized }"
  >
    <div class="ai-assistant__header" @click="toggleMinimize">
      <div class="ai-assistant__avatar">
        <i class="el-icon-service" />
      </div>
      <span class="ai-assistant__title">智农助手</span>
      <div class="ai-assistant__controls">
        <i class="el-icon-minus" v-if="!isMinimized" />
        <i class="el-icon-plus" v-else />
      </div>
    </div>
    
    <transition name="slide-fade">
      <div class="ai-assistant__content" v-if="!isMinimized">
        <div class="ai-assistant__chat" ref="chatContainer">
          <div class="ai-assistant__messages">
            <!-- 调试按钮 - 移到底部，不再打扰正常对话 -->
            <div
              v-for="(message, index) in messages"
              :key="index"
              :class="[
                'ai-assistant__message',
                message.type === 'user'
                  ? 'ai-assistant__message--user'
                  : 'ai-assistant__message--assistant'
              ]"
            >
              <div class="ai-assistant__message-avatar" v-if="message.type === 'assistant'">
                <i class="el-icon-service" />
              </div>
              <div 
                class="ai-assistant__message-content" 
                :class="{
                  'typing-effect': message.type === 'assistant' && 
                                 ((index === messages.length - 1 && isTyping) || message.isStreaming)
                }"
                v-html="formatMessage(message.content)" 
              />
              <div class="ai-assistant__message-time" v-if="message.timestamp">
                {{ formatTime(message.timestamp) }}
              </div>
            </div>
            <div class="ai-assistant__typing" v-if="isTyping && (!messages.length || messages[messages.length-1].type !== 'assistant')">
              <div class="ai-assistant__message-avatar">
                <i class="el-icon-service" />
              </div>
              <div class="ai-assistant__dots">
                <span class="ai-assistant__dot" />
                <span class="ai-assistant__dot" />
                <span class="ai-assistant__dot" />
              </div>
            </div>

            <!-- 调试按钮移到这里 -->
            <!-- 默认隐藏，仅开发时显示 -->
            <el-button 
              size="mini" 
              type="primary"
              @click="addTestMessage" 
              style="margin-top: 15px; opacity: 0.6;"
              v-if="false"
            >
              添加测试消息
            </el-button>
          </div>
        </div>
        
        <div class="ai-assistant__input">
          <el-input
            v-model="userInput"
            placeholder="请输入您的问题..."
            @keyup.enter.native="sendMessage"
            :disabled="isTyping"
          >
            <el-button
              slot="append"
              icon="el-icon-s-promotion"
              @click="sendMessage"
              class="ai-assistant__send-btn"
              :loading="isTyping"
              :disabled="isTyping || !userInput.trim()"
            />
          </el-input>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import aiApi from '@/api/ai'
import { debounce } from 'lodash'

export default {
  name: 'AIAssistant',
  data() {
    return {
      isMinimized: true, // 默认最小化
      userInput: '',
      messages: [
        {
          type: 'assistant',
          content: '您好！我是智农助手，有什么可以帮您的吗？'
        }
      ],
      isTyping: false,
      sessionId: null,
      eventSource: null
    }
  },
  mounted() {
    // 从localStorage加载历史会话ID（如果有）
    this.sessionId = localStorage.getItem('ai_session_id')
  },
  methods: {
    toggleMinimize() {
      this.isMinimized = !this.isMinimized
      if (!this.isMinimized) {
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },
    formatMessage(content) {
      // 添加调试日志
      console.log('格式化消息:', content);
      
      if (!content) {
        console.warn('收到空消息内容');
        return '(空消息)';
      }
      
      // 增强的文本格式化，支持代码块和Markdown基本语法
      try {
        let formattedContent = content
          .replace(/\n/g, '<br>')
          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
          .replace(/\*(.*?)\*/g, '<em>$1</em>')
          .replace(/`([^`]+)`/g, '<code style="background:#f0f0f0;padding:2px 4px;border-radius:3px;font-family:monospace;">$1</code>');
        
        // 处理代码块
        formattedContent = formattedContent.replace(/```(\w*)\n([\s\S]*?)\n```/g, 
          '<pre style="background:#f8f8f8;padding:10px;border-radius:5px;overflow-x:auto;font-family:monospace;"><code>$2</code></pre>'
        );
        
        return formattedContent;
      } catch (error) {
        console.error('消息格式化错误:', error);
        return String(content); // 确保返回字符串
      }
    },
    scrollToBottom: debounce(function() {
      if (this.$refs.chatContainer) {
        const container = this.$refs.chatContainer;
        // 使用平滑滚动效果
        container.scrollTo({
          top: container.scrollHeight,
          behavior: 'smooth'
        });
        
        // 确保长内容也能正确滚动
        setTimeout(() => {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
          });
        }, 100);
      }
    }, 50), // 减少延迟时间以更及时滚动
    sendMessage() {
      if (!this.userInput.trim() || this.isTyping) return
      
      // 保存用户输入并清空输入框
      const userInput = this.userInput.trim()
      this.userInput = ''
      
      // 添加用户消息(添加时间戳)
      this.messages.push({
        type: 'user',
        content: userInput,
        timestamp: new Date().toISOString()
      })
      
      this.scrollToBottom()
      
      // 设置为加载状态
      this.isTyping = true
      
      // 启用流式API，不再强制使用普通API
      if (typeof EventSource !== 'undefined') {
        console.log('使用流式API')
        this.useStreamApi(userInput)
      } else {
        // 降级到普通API
        console.log('EventSource不可用，使用普通API')
        this.useRegularApi(userInput)
      }
    },
    // 使用普通API获取回答（备用）
    useRegularApi(prompt) {
      console.log('使用普通API发送请求:', prompt)
      this.isTyping = true
      
      aiApi.askQuestion(prompt, this.sessionId)
        .then(response => {
          console.log('API响应成功:', response)
          
          // 检查响应结构
          if (response && response.data) {
            console.log('收到有效响应数据:', JSON.stringify(response.data))
            
            this.sessionId = response.data.sessionId
            localStorage.setItem('ai_session_id', this.sessionId)
            
            // 添加AI回复
            const answerContent = response.data.answer || '收到了响应，但没有找到回答内容'
            console.log('显示AI回复:', answerContent)
            
            this.messages.push({
              type: 'assistant',
              content: answerContent
            })
            
            // 确保UI更新
            this.$nextTick(() => {
              console.log('UI已更新，滚动到底部')
              this.scrollToBottom()
            })
          } else {
            console.error('API响应格式不正确:', response)
            this.messages.push({
              type: 'assistant',
              content: '抱歉，服务响应格式异常，请稍后再试。'
            })
          }
        })
        .catch(error => {
          console.error('API调用错误:', error)
          console.error('错误详情:', error.response || error.message || error)
          
          // 添加错误消息
          this.messages.push({
            type: 'assistant',
            content: '抱歉，服务出现问题，请稍后再试。错误信息: ' + (error.response?.data?.message || error.message || '未知错误')
          })
        })
        .finally(() => {
          this.isTyping = false
          this.scrollToBottom()
        })
    },
    // 使用流式API获取回答
    useStreamApi(prompt) {
      console.log('开始使用流式API...');
      // 关闭之前的连接（如果存在）
      if (this.eventSource) {
        this.eventSource.close()
      }
      
      // 创建新的EventSource
      this.eventSource = aiApi.streamResponse(prompt, this.sessionId)
      console.log('EventSource已创建，URL:', this.eventSource.url);
      
      // 创建临时消息占位
      let messageIndex = this.messages.length
      this.messages.push({
        type: 'assistant',
        content: '',
        timestamp: new Date().toISOString(), // 添加时间戳
        isStreaming: true // 标记正在流式输出
      })
      
      // 添加打字指示器
      this.isTyping = true
      
      // 立即滚动到底部以显示新消息
      this.$nextTick(() => {
        this.scrollToBottom();
      });
      
      // 设置超时
      const timeoutId = setTimeout(() => {
        if (this.isTyping && (!this.messages[messageIndex] || !this.messages[messageIndex].content)) {
          console.warn('流式API响应超时')
          this.eventSource.close()
          this.useRegularApi(prompt)
          // 删除空消息
          if (this.messages[messageIndex] && !this.messages[messageIndex].content) {
            this.messages.splice(messageIndex, 1)
          }
        }
      }, 15000) // 增加超时时间到15秒
      
      // 收到的数据缓冲区
      let receivedContent = '';
      
      // 监听session事件（获取会话ID）
      this.eventSource.addEventListener('session', (event) => {
        console.log('收到session事件:', event.data);
        this.sessionId = event.data
        localStorage.setItem('ai_session_id', this.sessionId)
      })
      
      // 监听消息事件
      this.eventSource.onmessage = (event) => {
        console.log('收到消息事件:', event.data);
        
        // 清除超时
        clearTimeout(timeoutId)
        
        // 如果消息不存在，可能是已经被超时处理删除了
        if (!this.messages[messageIndex]) {
          console.warn('消息索引不存在，可能已被删除')
          return
        }
        
        // 更新缓冲区
        receivedContent += event.data;
        
        // 通过Vue的响应式系统更新内容
        // 使用Vue.set以确保响应式更新
        this.$set(this.messages[messageIndex], 'content', receivedContent);
        
        // 每次接收新内容时滚动到底部
        this.scrollToBottom()
        
        // 播放消息提示音
        this.playMessageSound()
      }
      
      // 监听结束事件
      this.eventSource.addEventListener('end', (_event) => {
        console.log('收到结束事件');
        this.isTyping = false
        
        // 标记流式结束
        if (this.messages[messageIndex]) {
          this.$set(this.messages[messageIndex], 'isStreaming', false);
        }
        
        this.eventSource.close()
        this.scrollToBottom()
        
        // 清除超时
        clearTimeout(timeoutId)
      })
      
      // 监听错误事件
      this.eventSource.onerror = (error) => {
        console.error('流式连接错误:', error);
        this.isTyping = false
        
        // 清除流式状态
        if (this.messages[messageIndex]) {
          this.$set(this.messages[messageIndex], 'isStreaming', false);
        }
        
        this.eventSource.close()
        
        // 清除超时
        clearTimeout(timeoutId)
        
        // 如果没有收到任何回复，则添加错误消息或者降级到普通API
        if (!receivedContent) {
          console.log('未收到任何内容，降级到普通API');
          // 删除空消息
          if (this.messages[messageIndex] && !this.messages[messageIndex].content) {
            this.messages.splice(messageIndex, 1)
          }
          // 尝试使用普通API
          this.useRegularApi(prompt)
        } else {
          // 如果至少接收到一些内容，则滚动到底部显示
          this.scrollToBottom()
        }
      }
    },
    // 播放消息提示音
    playMessageSound() {
      try {
        // 暂时不实现真正的声音，避免打扰用户
        // 如果需要声音，可以在这里添加代码
      } catch (e) {
        console.warn('播放提示音失败:', e)
      }
    },
    // 添加测试方法
    addTestMessage() {
      console.log('添加测试消息');
      this.messages.push({
        type: 'assistant',
        content: '这是一条测试消息，用于验证UI渲染是否正常工作。'
      });
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    // 在data中添加新方法
    formatTime(timestamp) {
      if (!timestamp) return '';
      
      const date = new Date(timestamp);
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      
      return `${hours}:${minutes}`;
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-assistant {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 350px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  height: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  
  &--minimized {
    width: 200px;
    height: 50px;
    border-radius: 25px;
  }
  
  &__header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: linear-gradient(135deg, #409EFF, #66b1ff);
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: linear-gradient(135deg, #66b1ff, #409EFF);
      transform: translateY(-2px);
    }
  }
  
  &__avatar {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    animation: pulse 2s infinite;
    
    i {
      font-size: 18px;
      animation: rotate 10s linear infinite;
    }
  }
  
  &__title {
    font-size: 16px;
    font-weight: 600;
    flex: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  &__controls {
    i {
      font-size: 16px;
      transition: transform 0.3s ease;
      
      &:hover {
        transform: scale(1.2);
      }
    }
  }
  
  &__content {
    height: calc(100% - 50px);
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
  }
  
  &__chat {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 3px;
      transition: background 0.3s ease;
      
      &:hover {
        background: rgba(0, 0, 0, 0.2);
      }
    }
  }
  
  &__messages {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  &__message {
    max-width: 80%;
    animation: fadeIn 0.3s ease;
    
    &--user {
      align-self: flex-end;
      
      .ai-assistant__message-content {
        background: #409EFF;
        color: #fff;
        border-radius: 16px 16px 4px 16px;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
      }
    }
    
    &--assistant {
      align-self: flex-start;
      display: flex;
      
      .ai-assistant__message-content {
        background: #fff;
        color: #303133;
        border-radius: 16px 16px 16px 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }
    }
  }
  
  &__message-avatar {
    width: 24px;
    height: 24px;
    background: rgba(64, 158, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    
    i {
      font-size: 14px;
      color: #409EFF;
    }
  }
  
  &__message-content {
    padding: 10px 14px;
    word-break: break-word;
    line-height: 1.5;
    transition: transform 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
  }
  
  &__message-time {
    font-size: 10px;
    color: #909399;
    margin-top: 2px;
    text-align: right;
  }
  
  &__typing {
    align-self: flex-start;
    display: flex;
    align-items: center;
    margin-top: 12px;
  }
  
  &__dots {
    display: flex;
    align-items: center;
    background: #fff;
    padding: 10px 14px;
    border-radius: 16px 16px 16px 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
  
  &__dot {
    margin-right: 4px;
    width: 8px;
    height: 8px;
    background: #409EFF;
    border-radius: 50%;
    display: inline-block;
    animation: bounce 0.8s infinite alternate;
    
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    
    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
  
  &__input {
    padding: 12px;
    background: #fff;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    
    .el-input {
      border-radius: 20px;
      overflow: hidden;
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 动画效果
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes bounce {
  from { transform: translateY(0); }
  to { transform: translateY(-4px); }
}

.slide-fade-enter-active, .slide-fade-leave-active {
  transition: all 0.3s ease;
}
.slide-fade-enter, .slide-fade-leave-to {
  transform: translateY(20px);
  opacity: 0;
}

// 打字效果
.typing-effect {
  border-right: 2px solid #409EFF;
  animation: typing 0.7s steps(1) infinite;
  white-space: pre-wrap;
  position: relative;
  
  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(transparent 95%, rgba(64, 158, 255, 0.2) 100%);
    pointer-events: none;
    opacity: 0.3;
    animation: pulse-background 2s infinite;
  }
}

@keyframes typing {
  0%, 100% { border-right-color: transparent; }
  50% { border-right-color: #409EFF; }
}

@keyframes pulse-background {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.3; }
}
</style> 