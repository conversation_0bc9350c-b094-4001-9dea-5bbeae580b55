<template>
  <div class="traceability-display">
    <!-- 加载状态 -->
    <div class="loading-container" v-if="loading">
      <div class="loading-content">
        <i class="el-icon-loading" style="font-size: 32px; color: #409eff;"></i>
        <p>正在加载溯源信息...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div class="error-container" v-else-if="error">
      <el-result
        icon="warning"
        title="查询失败"
        :sub-title="errorMessage"
      >
        <template slot="extra">
          <el-button type="primary" @click="retry">重新查询</el-button>
        </template>
      </el-result>
    </div>

    <!-- 溯源信息展示 -->
    <div class="trace-content" v-else-if="traceData">
      <!-- 产品基本信息卡片 -->
      <el-card class="product-card" shadow="hover">
        <div slot="header" class="card-header">
          <h3>产品信息</h3>
          <el-tag :type="getStatusType(traceData.status)" size="medium">
            {{ getStatusText(traceData.status) }}
          </el-tag>
        </div>
        
        <div class="product-info">
          <div class="product-images">
            <el-carousel
              v-if="traceData.product.images && traceData.product.images.length > 0"
              height="200px"
              indicator-position="outside"
            >
              <el-carousel-item
                v-for="(image, index) in traceData.product.images"
                :key="index"
              >
                <img :src="image" :alt="`产品图片${index + 1}`" class="product-image" />
              </el-carousel-item>
            </el-carousel>
            <div v-else class="no-image">
              <i class="el-icon-picture" style="font-size: 64px;"></i>
              <p>暂无产品图片</p>
            </div>
          </div>
          
          <div class="product-details">
            <div class="detail-row">
              <span class="label">产品名称：</span>
              <span class="value">{{ traceData.product.name }}</span>
            </div>
            <div class="detail-row">
              <span class="label">产品类别：</span>
              <span class="value">{{ traceData.product.category }}</span>
            </div>
            <div class="detail-row">
              <span class="label">规格等级：</span>
              <span class="value">{{ traceData.product.specification }}</span>
            </div>
            <div class="detail-row">
              <span class="label">生产日期：</span>
              <span class="value">{{ formatDate(traceData.product.productionDate) }}</span>
            </div>
            <div class="detail-row">
              <span class="label">保质期：</span>
              <span class="value">{{ traceData.product.shelfLife }}</span>
            </div>
            <div class="detail-row">
              <span class="label">生产商：</span>
              <span class="value">{{ traceData.product.producer }}</span>
            </div>
            <div class="detail-row">
              <span class="label">产地：</span>
              <span class="value">{{ traceData.product.origin }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 生产环节时间轴 -->
      <el-card class="timeline-card" shadow="hover">
        <div slot="header" class="card-header">
          <h3>生产过程</h3>
          <el-tag type="info">{{ traceData.events.length }} 个环节</el-tag>
        </div>
        
        <el-timeline class="production-timeline">
          <el-timeline-item
            v-for="(event, index) in traceData.events"
            :key="index"
            :timestamp="formatDateTime(event.eventTime)"
            :type="getEventType(event.eventType)"
            :size="index === 0 ? 'large' : 'normal'"
          >
            <div class="event-content">
              <div class="event-header">
                <h4>{{ event.eventName }}</h4>
                <el-tag size="small" :type="getEventTagType(event.eventType)">
                  {{ event.eventType }}
                </el-tag>
              </div>
              <p class="event-description">{{ event.description }}</p>
              <div class="event-details" v-if="event.details">
                <div class="detail-item" v-for="(value, key) in event.details" :key="key">
                  <span class="detail-key">{{ key }}：</span>
                  <span class="detail-value">{{ value }}</span>
                </div>
              </div>
              <div class="event-attachments" v-if="event.attachments && event.attachments.length > 0">
                <h5>相关附件：</h5>
                <div class="attachment-list">
                  <div
                    v-for="(attachment, idx) in event.attachments"
                    :key="idx"
                    class="attachment-item"
                    @click="previewAttachment(attachment)"
                  >
                    <i class="el-icon-document"></i>
                    <span>{{ attachment.name }}</span>
                  </div>
                </div>
              </div>
              <div class="event-responsible" v-if="event.responsiblePerson">
                <span class="responsible-label">负责人：</span>
                <span class="responsible-name">{{ event.responsiblePerson }}</span>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>

      <!-- 认证信息 -->
      <el-card class="certification-card" shadow="hover" v-if="traceData.certifications && traceData.certifications.length > 0">
        <div slot="header" class="card-header">
          <h3>认证信息</h3>
          <el-tag type="success">{{ traceData.certifications.length }} 项认证</el-tag>
        </div>
        
        <div class="certification-list">
          <div
            v-for="(cert, index) in traceData.certifications"
            :key="index"
            class="certification-item"
          >
            <div class="cert-icon">
              <i class="el-icon-medal" style="font-size: 32px; color: #67c23a;"></i>
            </div>
            <div class="cert-info">
              <h4>{{ cert.name }}</h4>
              <p class="cert-issuer">认证机构：{{ cert.issuer }}</p>
              <p class="cert-number">证书编号：{{ cert.number }}</p>
              <p class="cert-validity">
                有效期：{{ formatDate(cert.issueDate) }} - {{ formatDate(cert.expiryDate) }}
                <el-tag
                  :type="isValidCertification(cert.expiryDate) ? 'success' : 'danger'"
                  size="small"
                  class="validity-tag"
                >
                  {{ isValidCertification(cert.expiryDate) ? '有效' : '已过期' }}
                </el-tag>
              </p>
            </div>
            <div class="cert-actions">
              <el-button
                type="text"
                @click="viewCertificate(cert)"
                v-if="cert.certificateUrl"
              >
                查看证书
              </el-button>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 物流轨迹 -->
      <el-card class="logistics-card" shadow="hover" v-if="traceData.logistics && traceData.logistics.length > 0">
        <div slot="header" class="card-header">
          <h3>物流轨迹</h3>
          <el-tag type="warning">{{ traceData.logistics.length }} 个节点</el-tag>
        </div>
        
        <div class="logistics-timeline">
          <div
            v-for="(logistics, index) in traceData.logistics"
            :key="index"
            class="logistics-item"
            :class="{ 'current': index === 0 }"
          >
            <div class="logistics-icon">
              <i :class="getLogisticsIcon(logistics.status)"></i>
            </div>
            <div class="logistics-content">
              <div class="logistics-header">
                <h4>{{ logistics.location }}</h4>
                <span class="logistics-time">{{ formatDateTime(logistics.timestamp) }}</span>
              </div>
              <p class="logistics-status">{{ logistics.status }}</p>
              <p class="logistics-description" v-if="logistics.description">
                {{ logistics.description }}
              </p>
              <div class="logistics-conditions" v-if="logistics.conditions">
                <span class="condition-item" v-if="logistics.conditions.temperature">
                  温度：{{ logistics.conditions.temperature }}°C
                </span>
                <span class="condition-item" v-if="logistics.conditions.humidity">
                  湿度：{{ logistics.conditions.humidity }}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="primary" @click="shareTrace">
          <i class="el-icon-share"></i>
          分享溯源信息
        </el-button>
        <el-button @click="favoriteTrace" :type="isFavorited ? 'warning' : 'default'">
          <i class="el-icon-star-off"></i>
          {{ isFavorited ? '已收藏' : '收藏产品' }}
        </el-button>
        <el-button @click="feedbackTrace">
          <i class="el-icon-chat-dot-round"></i>
          反馈问题
        </el-button>
      </div>
    </div>

    <!-- 无数据状态 -->
    <div class="no-data-container" v-else>
      <el-result
        icon="info"
        title="未找到溯源信息"
        sub-title="请检查溯源码是否正确"
      >
        <template slot="extra">
          <el-button type="primary" @click="$emit('back-to-search')">重新查询</el-button>
        </template>
      </el-result>
    </div>

    <!-- 附件预览对话框 -->
    <el-dialog
      title="附件预览"
      :visible.sync="attachmentDialogVisible"
      width="80%"
    >
      <div class="attachment-preview" v-if="currentAttachment">
        <img
          v-if="isImageFile(currentAttachment.url)"
          :src="currentAttachment.url"
          :alt="currentAttachment.name"
          class="preview-image"
        />
        <div v-else class="file-info">
          <i class="el-icon-document" style="font-size: 64px;"></i>
          <p>{{ currentAttachment.name }}</p>
          <el-button type="primary" @click="downloadAttachment(currentAttachment)">
            下载文件
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 证书查看对话框 -->
    <el-dialog
      title="认证证书"
      :visible.sync="certificateDialogVisible"
      width="70%"
    >
      <div class="certificate-preview" v-if="currentCertificate">
        <img
          :src="currentCertificate.certificateUrl"
          :alt="currentCertificate.name"
          class="certificate-image"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'TraceabilityDisplayVue2',
  props: {
    traceCode: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      error: false,
      errorMessage: '',
      traceData: null,
      isFavorited: false,
      attachmentDialogVisible: false,
      currentAttachment: null,
      certificateDialogVisible: false,
      currentCertificate: null
    }
  },
  mounted() {
    if (this.traceCode) {
      this.loadTraceData()
    }
  },
  watch: {
    traceCode(newCode) {
      if (newCode) {
        this.loadTraceData()
      }
    }
  },
  methods: {
    async loadTraceData() {
      this.loading = true
      this.error = false
      
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 模拟数据
        this.traceData = {
          traceCode: this.traceCode,
          status: 'published',
          product: {
            name: '有机苹果',
            category: '水果',
            specification: '一级品 500g/个',
            productionDate: '2024-01-15',
            shelfLife: '30天',
            producer: '绿色农场有限公司',
            origin: '山东烟台',
            images: [
              require('@/assets/images/products/apple.jpg'),
              require('@/assets/images/products/apple.jpg')
            ]
          },
          events: [
            {
              eventName: '种植播种',
              eventType: '种植',
              eventTime: '2024-01-15T08:00:00',
              description: '使用有机种子进行播种，严格按照有机标准操作',
              details: {
                '种子品种': '红富士',
                '播种密度': '100株/亩',
                '土壤类型': '有机土壤'
              },
              responsiblePerson: '张三',
              attachments: [
                { name: '播种记录.pdf', url: '/files/planting.pdf' },
                { name: '土壤检测报告.jpg', url: '/images/soil-test.jpg' }
              ]
            },
            {
              eventName: '生长管理',
              eventType: '管理',
              eventTime: '2024-02-01T10:00:00',
              description: '定期浇水、施肥，使用有机肥料',
              details: {
                '肥料类型': '有机复合肥',
                '施肥量': '50kg/亩',
                '浇水频次': '每周2次'
              },
              responsiblePerson: '李四'
            },
            {
              eventName: '采摘收获',
              eventType: '收获',
              eventTime: '2024-03-01T06:00:00',
              description: '人工采摘，确保果实完整无损',
              details: {
                '采摘方式': '人工采摘',
                '采摘时间': '清晨6-8点',
                '产量': '2000kg'
              },
              responsiblePerson: '王五'
            }
          ],
          certifications: [
            {
              name: '有机产品认证',
              issuer: '中国有机产品认证中心',
              number: 'COPC-2024-001',
              issueDate: '2024-01-01',
              expiryDate: '2025-01-01',
              certificateUrl: '/images/organic-cert.jpg'
            }
          ],
          logistics: [
            {
              location: '烟台配送中心',
              status: '已发货',
              timestamp: '2024-03-02T14:00:00',
              description: '商品已从产地发出',
              conditions: {
                temperature: 5,
                humidity: 60
              }
            },
            {
              location: '济南中转站',
              status: '运输中',
              timestamp: '2024-03-02T20:00:00',
              description: '商品正在运输途中',
              conditions: {
                temperature: 4,
                humidity: 65
              }
            }
          ]
        }
        
        this.loading = false
      } catch (err) {
        this.loading = false
        this.error = true
        this.errorMessage = err.message || '加载溯源信息失败'
      }
    },

    retry() {
      this.loadTraceData()
    },

    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString('zh-CN')
    },

    formatDateTime(dateString) {
      return new Date(dateString).toLocaleString('zh-CN')
    },

    getStatusType(status) {
      const statusMap = {
        'published': 'success',
        'pending': 'warning',
        'draft': 'info',
        'rejected': 'danger'
      }
      return statusMap[status] || 'info'
    },

    getStatusText(status) {
      const statusMap = {
        'published': '已发布',
        'pending': '待审核',
        'draft': '草稿',
        'rejected': '已拒绝'
      }
      return statusMap[status] || '未知'
    },

    getEventType(eventType) {
      const typeMap = {
        '种植': 'success',
        '管理': 'primary',
        '收获': 'warning',
        '加工': 'info'
      }
      return typeMap[eventType] || 'primary'
    },

    getEventTagType(eventType) {
      const typeMap = {
        '种植': 'success',
        '管理': 'primary',
        '收获': 'warning',
        '加工': 'info'
      }
      return typeMap[eventType] || 'primary'
    },

    getLogisticsIcon(status) {
      const iconMap = {
        '已发货': 'el-icon-truck',
        '运输中': 'el-icon-truck',
        '已到达': 'el-icon-box',
        '已签收': 'el-icon-check'
      }
      return iconMap[status] || 'el-icon-box'
    },

    isValidCertification(expiryDate) {
      return new Date(expiryDate) > new Date()
    },

    isImageFile(url) {
      return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(url)
    },

    previewAttachment(attachment) {
      this.currentAttachment = attachment
      this.attachmentDialogVisible = true
    },

    downloadAttachment(attachment) {
      const link = document.createElement('a')
      link.href = attachment.url
      link.download = attachment.name
      link.click()
    },

    viewCertificate(certificate) {
      this.currentCertificate = certificate
      this.certificateDialogVisible = true
    },

    shareTrace() {
      this.$emit('share-trace', this.traceData)
      this.$message.success('分享功能开发中...')
    },

    favoriteTrace() {
      this.isFavorited = !this.isFavorited
      this.$emit('favorite-trace', this.traceData, this.isFavorited)
      this.$message.success(this.isFavorited ? '已添加到收藏' : '已取消收藏')
    },

    feedbackTrace() {
      this.$emit('feedback-trace', this.traceData)
      this.$message.success('反馈功能开发中...')
    }
  }
}
</script>

<style lang="scss" scoped>
.traceability-display {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;

  .loading-container,
  .error-container,
  .no-data-container {
    margin: 40px 0;
    text-align: center;

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      p {
        color: #606266;
        font-size: 16px;
      }
    }
  }

  .trace-content {
    .el-card {
      margin-bottom: 20px;
      border-radius: 12px;

      ::v-deep .el-card__header {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        border-radius: 12px 12px 0 0;
      }
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        color: #303133;
        font-weight: 600;
      }
    }

    // 产品信息卡片
    .product-card {
      .product-info {
        display: flex;
        gap: 20px;

        .product-images {
          flex: 0 0 300px;

          .el-carousel {
            border-radius: 8px;
            overflow: hidden;
          }

          .product-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
          }

          .no-image {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            background: #f5f7fa;
            border-radius: 8px;
            color: #909399;

            p {
              margin: 8px 0 0 0;
              font-size: 14px;
            }
          }
        }

        .product-details {
          flex: 1;

          .detail-row {
            display: flex;
            margin-bottom: 12px;
            align-items: center;

            .label {
              flex: 0 0 100px;
              color: #606266;
              font-weight: 500;
            }

            .value {
              color: #303133;
              font-weight: 400;
            }
          }
        }
      }
    }

    // 生产时间轴
    .timeline-card {
      .production-timeline {
        ::v-deep .el-timeline-item__timestamp {
          color: #409eff;
          font-weight: 500;
        }

        .event-content {
          .event-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            h4 {
              margin: 0;
              color: #303133;
              font-size: 16px;
            }
          }

          .event-description {
            color: #606266;
            margin-bottom: 12px;
            line-height: 1.6;
          }

          .event-details {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 12px;

            .detail-item {
              display: flex;
              margin-bottom: 6px;

              &:last-child {
                margin-bottom: 0;
              }

              .detail-key {
                flex: 0 0 80px;
                color: #909399;
                font-size: 13px;
              }

              .detail-value {
                color: #606266;
                font-size: 13px;
              }
            }
          }

          .event-attachments {
            margin-bottom: 12px;

            h5 {
              margin: 0 0 8px 0;
              color: #303133;
              font-size: 14px;
            }

            .attachment-list {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;

              .attachment-item {
                display: flex;
                align-items: center;
                gap: 4px;
                padding: 4px 8px;
                background: #e1f3d8;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                color: #67c23a;
                transition: all 0.3s ease;

                &:hover {
                  background: #d1e7c1;
                  transform: translateY(-1px);
                }
              }
            }
          }

          .event-responsible {
            .responsible-label {
              color: #909399;
              font-size: 13px;
            }

            .responsible-name {
              color: #409eff;
              font-weight: 500;
              font-size: 13px;
            }
          }
        }
      }
    }

    // 认证信息
    .certification-card {
      .certification-list {
        .certification-item {
          display: flex;
          align-items: flex-start;
          gap: 16px;
          padding: 16px;
          border: 1px solid #e4e7ed;
          border-radius: 8px;
          margin-bottom: 12px;
          transition: all 0.3s ease;

          &:hover {
            border-color: #67c23a;
            box-shadow: 0 2px 8px rgba(103, 194, 58, 0.1);
          }

          &:last-child {
            margin-bottom: 0;
          }

          .cert-icon {
            flex: 0 0 auto;
          }

          .cert-info {
            flex: 1;

            h4 {
              margin: 0 0 8px 0;
              color: #303133;
              font-size: 16px;
            }

            p {
              margin: 4px 0;
              font-size: 14px;
              color: #606266;

              &.cert-validity {
                display: flex;
                align-items: center;
                gap: 8px;

                .validity-tag {
                  margin-left: auto;
                }
              }
            }
          }

          .cert-actions {
            flex: 0 0 auto;
          }
        }
      }
    }

    // 物流轨迹
    .logistics-card {
      .logistics-timeline {
        .logistics-item {
          display: flex;
          align-items: flex-start;
          gap: 16px;
          padding: 16px 0;
          border-bottom: 1px solid #f0f0f0;
          position: relative;

          &:last-child {
            border-bottom: none;
          }

          &.current {
            .logistics-icon {
              background: #409eff;
              color: white;
            }
          }

          &:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 20px;
            top: 60px;
            width: 2px;
            height: calc(100% - 40px);
            background: #e4e7ed;
          }

          .logistics-icon {
            flex: 0 0 40px;
            height: 40px;
            border-radius: 50%;
            background: #f5f7fa;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #909399;
            position: relative;
            z-index: 1;
            font-size: 18px;
          }

          .logistics-content {
            flex: 1;

            .logistics-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;

              h4 {
                margin: 0;
                color: #303133;
                font-size: 16px;
              }

              .logistics-time {
                color: #909399;
                font-size: 13px;
              }
            }

            .logistics-status {
              color: #409eff;
              font-weight: 500;
              margin-bottom: 4px;
            }

            .logistics-description {
              color: #606266;
              margin-bottom: 8px;
              line-height: 1.5;
            }

            .logistics-conditions {
              display: flex;
              gap: 16px;

              .condition-item {
                font-size: 12px;
                color: #909399;
                background: #f5f7fa;
                padding: 2px 8px;
                border-radius: 4px;
              }
            }
          }
        }
      }
    }

    // 操作按钮
    .action-buttons {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin-top: 30px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 12px;
    }
  }

  // 对话框样式
  ::v-deep .el-dialog__body {
    padding: 20px;
    text-align: center;

    .attachment-preview,
    .certificate-preview {
      .preview-image,
      .certificate-image {
        max-width: 100%;
        max-height: 70vh;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .file-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        color: #606266;

        p {
          font-size: 16px;
          margin: 0;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .traceability-display {
    padding: 10px;

    .trace-content {
      .product-card {
        .product-info {
          flex-direction: column;

          .product-images {
            flex: none;
          }
        }
      }

      .certification-card {
        .certification-list {
          .certification-item {
            flex-direction: column;
            text-align: center;
          }
        }
      }

      .action-buttons {
        flex-direction: column;
      }
    }
  }
}
</style>
