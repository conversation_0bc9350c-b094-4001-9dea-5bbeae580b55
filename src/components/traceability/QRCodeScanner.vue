<template>
  <div class="qr-scanner-overlay" @click="handleOverlayClick">
    <div class="qr-scanner-modal" @click.stop>
      <!-- 头部 -->
      <div class="scanner-header">
        <h3>扫描二维码</h3>
        <el-button 
          type="text" 
          icon="el-icon-close" 
          @click="$emit('close')"
          class="close-button">
        </el-button>
      </div>

      <!-- 扫描区域 -->
      <div class="scanner-body">
        <div class="camera-container">
          <video 
            ref="video" 
            autoplay 
            playsinline
            class="camera-video">
          </video>
          <canvas 
            ref="canvas" 
            class="camera-canvas">
          </canvas>
          
          <!-- 扫描框 -->
          <div class="scan-frame">
            <div class="scan-corners">
              <div class="corner top-left"></div>
              <div class="corner top-right"></div>
              <div class="corner bottom-left"></div>
              <div class="corner bottom-right"></div>
            </div>
            <div class="scan-line"></div>
          </div>
        </div>

        <!-- 状态信息 -->
        <div class="scanner-status">
          <p v-if="!cameraReady" class="status-text">
            <i class="el-icon-loading"></i>
            正在启动摄像头...
          </p>
          <p v-else-if="scanning" class="status-text">
            <i class="el-icon-view"></i>
            请将二维码对准扫描框
          </p>
          <p v-else class="status-text error">
            <i class="el-icon-warning"></i>
            {{ errorMessage }}
          </p>
        </div>

        <!-- 操作按钮 -->
        <div class="scanner-actions">
          <el-button @click="toggleFlash" v-if="hasFlash">
            <i :class="flashOn ? 'el-icon-sunny' : 'el-icon-moon'"></i>
            {{ flashOn ? '关闭' : '开启' }}闪光灯
          </el-button>
          <el-button @click="switchCamera" v-if="hasMultipleCameras">
            <i class="el-icon-refresh"></i>
            切换摄像头
          </el-button>
        </div>
      </div>

      <!-- 底部提示 -->
      <div class="scanner-footer">
        <p class="tip-text">
          <i class="el-icon-info"></i>
          将二维码放入扫描框内，系统会自动识别
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import jsQR from 'jsqr'

export default {
  name: 'QRCodeScanner',
  data() {
    return {
      scanning: false,
      cameraReady: false,
      errorMessage: '',
      stream: null,
      animationId: null,
      flashOn: false,
      hasFlash: false,
      hasMultipleCameras: false,
      currentCameraIndex: 0,
      cameras: []
    }
  },
  mounted() {
    this.initCamera()
  },
  beforeDestroy() {
    this.stopScanning()
  },
  methods: {
    /**
     * 初始化摄像头
     */
    async initCamera() {
      try {
        // 获取可用摄像头列表
        await this.getCameraList()
        
        // 启动摄像头
        await this.startCamera()
        
        this.cameraReady = true
        this.scanning = true
        this.startScanning()
        
      } catch (error) {
        console.error('初始化摄像头失败:', error)
        this.errorMessage = this.getCameraErrorMessage(error)
        this.$emit('scan-error', error)
      }
    },

    /**
     * 获取摄像头列表
     */
    async getCameraList() {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices()
        this.cameras = devices.filter(device => device.kind === 'videoinput')
        this.hasMultipleCameras = this.cameras.length > 1
      } catch (error) {
        console.warn('获取摄像头列表失败:', error)
      }
    },

    /**
     * 启动摄像头
     */
    async startCamera() {
      const constraints = {
        video: {
          facingMode: 'environment', // 优先使用后置摄像头
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      }

      // 如果有多个摄像头，使用指定的摄像头
      if (this.cameras.length > 0) {
        constraints.video.deviceId = this.cameras[this.currentCameraIndex].deviceId
      }

      this.stream = await navigator.mediaDevices.getUserMedia(constraints)
      
      const video = this.$refs.video
      video.srcObject = this.stream
      
      // 检查是否支持闪光灯
      const track = this.stream.getVideoTracks()[0]
      const capabilities = track.getCapabilities()
      this.hasFlash = capabilities.torch === true

      return new Promise((resolve) => {
        video.onloadedmetadata = () => {
          video.play()
          resolve()
        }
      })
    },

    /**
     * 开始扫描
     */
    startScanning() {
      const video = this.$refs.video
      const canvas = this.$refs.canvas
      const context = canvas.getContext('2d')

      const scan = () => {
        if (!this.scanning) return

        if (video.readyState === video.HAVE_ENOUGH_DATA) {
          canvas.width = video.videoWidth
          canvas.height = video.videoHeight
          context.drawImage(video, 0, 0, canvas.width, canvas.height)

          const imageData = context.getImageData(0, 0, canvas.width, canvas.height)
          const code = jsQR(imageData.data, imageData.width, imageData.height, {
            inversionAttempts: 'dontInvert'
          })

          if (code) {
            this.handleScanSuccess(code.data)
            return
          }
        }

        this.animationId = requestAnimationFrame(scan)
      }

      scan()
    },

    /**
     * 停止扫描
     */
    stopScanning() {
      this.scanning = false
      
      if (this.animationId) {
        cancelAnimationFrame(this.animationId)
        this.animationId = null
      }

      if (this.stream) {
        this.stream.getTracks().forEach(track => track.stop())
        this.stream = null
      }
    },

    /**
     * 处理扫描成功
     */
    handleScanSuccess(result) {
      this.stopScanning()
      this.$emit('scan-success', result)
    },

    /**
     * 切换闪光灯
     */
    async toggleFlash() {
      if (!this.hasFlash || !this.stream) return

      try {
        const track = this.stream.getVideoTracks()[0]
        await track.applyConstraints({
          advanced: [{ torch: !this.flashOn }]
        })
        this.flashOn = !this.flashOn
      } catch (error) {
        console.error('切换闪光灯失败:', error)
        this.$message.warning('闪光灯控制失败')
      }
    },

    /**
     * 切换摄像头
     */
    async switchCamera() {
      if (!this.hasMultipleCameras) return

      try {
        this.stopScanning()
        this.currentCameraIndex = (this.currentCameraIndex + 1) % this.cameras.length
        await this.startCamera()
        this.scanning = true
        this.startScanning()
      } catch (error) {
        console.error('切换摄像头失败:', error)
        this.$message.error('切换摄像头失败')
      }
    },

    /**
     * 处理遮罩点击
     */
    handleOverlayClick() {
      this.$emit('close')
    },

    /**
     * 获取摄像头错误信息
     */
    getCameraErrorMessage(error) {
      switch (error.name) {
        case 'NotAllowedError':
          return '摄像头权限被拒绝，请允许访问摄像头'
        case 'NotFoundError':
          return '未找到摄像头设备'
        case 'NotSupportedError':
          return '当前浏览器不支持摄像头功能'
        case 'NotReadableError':
          return '摄像头被其他应用占用'
        default:
          return '摄像头启动失败：' + error.message
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.qr-scanner-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.qr-scanner-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.scanner-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);

  h3 {
    margin: 0;
    color: var(--text-primary);
  }

  .close-button {
    font-size: 20px;
    color: var(--text-secondary);
  }
}

.scanner-body {
  padding: 20px;
  text-align: center;
}

.camera-container {
  position: relative;
  width: 100%;
  height: 300px;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.camera-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.camera-canvas {
  display: none;
}

.scan-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 8px;
}

.scan-corners {
  position: relative;
  width: 100%;
  height: 100%;

  .corner {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 3px solid var(--primary-color);

    &.top-left {
      top: -3px;
      left: -3px;
      border-right: none;
      border-bottom: none;
    }

    &.top-right {
      top: -3px;
      right: -3px;
      border-left: none;
      border-bottom: none;
    }

    &.bottom-left {
      bottom: -3px;
      left: -3px;
      border-right: none;
      border-top: none;
    }

    &.bottom-right {
      bottom: -3px;
      right: -3px;
      border-left: none;
      border-top: none;
    }
  }
}

.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  animation: scanLine 2s linear infinite;
}

@keyframes scanLine {
  0% { transform: translateY(0); }
  100% { transform: translateY(196px); }
}

.scanner-status {
  margin-bottom: 20px;

  .status-text {
    margin: 0;
    color: var(--text-secondary);
    font-size: 14px;

    &.error {
      color: var(--danger-color);
    }

    i {
      margin-right: 5px;
    }
  }
}

.scanner-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;

  .el-button {
    i {
      margin-right: 5px;
    }
  }
}

.scanner-footer {
  padding: 15px 20px;
  background-color: var(--background-light);
  border-top: 1px solid var(--border-color);

  .tip-text {
    margin: 0;
    color: var(--text-secondary);
    font-size: 12px;
    text-align: center;

    i {
      margin-right: 5px;
      color: var(--primary-color);
    }
  }
}

// 移动端适配
@media screen and (max-width: 768px) {
  .qr-scanner-modal {
    width: 95%;
    margin: 10px;
  }

  .camera-container {
    height: 250px;
  }

  .scan-frame {
    width: 150px;
    height: 150px;
  }

  .scan-line {
    animation: scanLineMobile 2s linear infinite;
  }

  @keyframes scanLineMobile {
    0% { transform: translateY(0); }
    100% { transform: translateY(146px); }
  }
}
</style>
