<template>
  <div class="traceability-detail">
    <!-- 产品基本信息 -->
    <el-card class="product-info-card">
      <div class="product-header">
        <div class="product-main">
          <h2 class="product-name">{{ data.productName }}</h2>
          <div class="product-meta">
            <el-tag type="success" size="medium">{{ data.qualityGrade }}</el-tag>
            <span class="batch-number">批次：{{ data.batchNumber }}</span>
          </div>
          <p class="farm-info">
            <i class="el-icon-location"></i>
            {{ data.farmName }} - {{ data.producerName }}
          </p>
        </div>
        <div class="product-qr">
          <img v-if="data.qrCodeUrl" :src="data.qrCodeUrl" alt="溯源二维码" class="qr-image">
          <p class="trace-code">{{ data.traceCode }}</p>
        </div>
      </div>
      
      <div class="product-dates">
        <div class="date-item">
          <span class="date-label">种植日期</span>
          <span class="date-value">{{ data.creationDate }}</span>
        </div>
        <div class="date-item">
          <span class="date-label">采收日期</span>
          <span class="date-value">{{ data.harvestDate }}</span>
        </div>
        <div class="date-item">
          <span class="date-label">包装日期</span>
          <span class="date-value">{{ data.packagingDate }}</span>
        </div>
      </div>
    </el-card>

    <!-- 生产时间轴 -->
    <el-card class="timeline-card" v-if="data.timeline && data.timeline.length > 0">
      <div slot="header" class="card-header">
        <i class="el-icon-time"></i>
        生产过程
      </div>
      <traceability-timeline :events="data.timeline" />
    </el-card>

    <!-- 认证信息 -->
    <el-card class="certificates-card" v-if="data.certificates && data.certificates.length > 0">
      <div slot="header" class="card-header">
        <i class="el-icon-medal"></i>
        认证信息
      </div>
      <div class="certificates-grid">
        <div 
          v-for="cert in data.certificates" 
          :key="cert.id"
          class="certificate-item"
          :class="{ 'expired': !cert.isValid }">
          <div class="cert-header">
            <h4 class="cert-type">{{ cert.certificateType }}</h4>
            <el-tag 
              :type="cert.isValid ? 'success' : 'danger'" 
              size="mini">
              {{ cert.isValid ? '有效' : '已过期' }}
            </el-tag>
          </div>
          <div class="cert-details">
            <p><strong>证书编号：</strong>{{ cert.certificateNo }}</p>
            <p><strong>颁发机构：</strong>{{ cert.issuingAuthority }}</p>
            <p><strong>颁发日期：</strong>{{ cert.issueDate }}</p>
            <p><strong>有效期至：</strong>{{ cert.validUntil }}</p>
            <p v-if="cert.daysToExpire !== null" class="expire-info">
              <span v-if="cert.isValid && cert.daysToExpire <= 30" class="warning">
                {{ cert.daysToExpire }}天后过期
              </span>
            </p>
          </div>
          <div v-if="cert.description" class="cert-description">
            {{ cert.description }}
          </div>
          <div v-if="cert.certificateUrl" class="cert-actions">
            <el-button type="text" @click="viewCertificate(cert.certificateUrl)">
              <i class="el-icon-view"></i>
              查看证书
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 物流轨迹 -->
    <el-card class="logistics-card" v-if="data.logistics && data.logistics.length > 0">
      <div slot="header" class="card-header">
        <i class="el-icon-truck"></i>
        物流轨迹
      </div>
      <div class="logistics-timeline">
        <div
          v-for="log in data.logistics"
          :key="log.id"
          class="logistics-item"
          :class="{ 'completed': log.status === 2 }">
          <div class="logistics-icon">
            <i class="el-icon-location-outline"></i>
          </div>
          <div class="logistics-content">
            <div class="logistics-header">
              <h4>{{ log.origin }} → {{ log.destination }}</h4>
              <el-tag 
                :type="getLogisticsTagType(log.status)" 
                size="mini">
                {{ log.statusText }}
              </el-tag>
            </div>
            <div class="logistics-details">
              <p><strong>承运商：</strong>{{ log.carrierName }}</p>
              <p><strong>运输方式：</strong>{{ log.transportType }}</p>
              <p v-if="log.formattedDepartureTime">
                <strong>发货时间：</strong>{{ log.formattedDepartureTime }}
              </p>
              <p v-if="log.formattedArrivalTime">
                <strong>到达时间：</strong>{{ log.formattedArrivalTime }}
              </p>
              <div v-if="log.temperature || log.humidity" class="transport-conditions">
                <span v-if="log.temperature" class="condition-item">
                  <i class="el-icon-thermometer"></i>
                  {{ log.temperature }}
                </span>
                <span v-if="log.humidity" class="condition-item">
                  <i class="el-icon-cloudy"></i>
                  {{ log.humidity }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 查询统计 -->
    <el-card class="stats-card" v-if="data.queryStats">
      <div slot="header" class="card-header">
        <i class="el-icon-data-analysis"></i>
        查询统计
      </div>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-number">{{ data.queryStats.totalQueries || 0 }}</div>
          <div class="stat-label">总查询次数</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ data.queryStats.todayQueries || 0 }}</div>
          <div class="stat-label">今日查询</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ data.queryStats.weekQueries || 0 }}</div>
          <div class="stat-label">本周查询</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ data.queryStats.monthQueries || 0 }}</div>
          <div class="stat-label">本月查询</div>
        </div>
      </div>
      <div v-if="data.queryStats.firstQueryTime" class="query-times">
        <p><strong>首次查询：</strong>{{ formatDateTime(data.queryStats.firstQueryTime) }}</p>
        <p v-if="data.queryStats.lastQueryTime">
          <strong>最近查询：</strong>{{ formatDateTime(data.queryStats.lastQueryTime) }}
        </p>
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button type="primary" @click="shareTraceability">
        <i class="el-icon-share"></i>
        分享溯源信息
      </el-button>
      <el-button @click="downloadQRCode" v-if="data.qrCodeUrl">
        <i class="el-icon-download"></i>
        下载二维码
      </el-button>
      <el-button @click="reportIssue">
        <i class="el-icon-warning"></i>
        举报问题
      </el-button>
    </div>
  </div>
</template>

<script>
import TraceabilityTimeline from './TraceabilityTimeline'

export default {
  name: 'TraceabilityDetail',
  components: {
    TraceabilityTimeline
  },
  props: {
    data: {
      type: Object,
      required: true
    }
  },
  methods: {
    /**
     * 获取物流状态标签类型
     */
    getLogisticsTagType(status) {
      switch (status) {
        case 0: return 'info'     // 待发货
        case 1: return 'warning'  // 运输中
        case 2: return 'success'  // 已到达
        case 3: return 'danger'   // 异常
        default: return 'info'
      }
    },

    /**
     * 查看证书
     */
    viewCertificate(url) {
      window.open(url, '_blank')
    },

    /**
     * 分享溯源信息
     */
    shareTraceability() {
      const shareData = {
        title: `${this.data.productName} - 农产品溯源信息`,
        text: `来自${this.data.farmName}的${this.data.productName}，溯源码：${this.data.traceCode}`,
        url: window.location.href
      }

      if (navigator.share) {
        navigator.share(shareData).catch(err => {
          console.log('分享失败:', err)
          this.fallbackShare()
        })
      } else {
        this.fallbackShare()
      }
    },

    /**
     * 备用分享方法
     */
    fallbackShare() {
      const url = window.location.href
      if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
          this.$message.success('链接已复制到剪贴板')
        }).catch(() => {
          this.showShareDialog(url)
        })
      } else {
        this.showShareDialog(url)
      }
    },

    /**
     * 显示分享对话框
     */
    showShareDialog(url) {
      this.$alert(url, '分享链接', {
        confirmButtonText: '确定',
        callback: () => {}
      })
    },

    /**
     * 下载二维码
     */
    downloadQRCode() {
      const link = document.createElement('a')
      link.href = this.data.qrCodeUrl
      link.download = `${this.data.productName}_溯源码.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },

    /**
     * 举报问题
     */
    reportIssue() {
      this.$prompt('请描述您发现的问题', '举报问题', {
        confirmButtonText: '提交',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请详细描述问题...'
      }).then(({ value: _value }) => {
        // 这里可以调用举报API
        // _value 参数保留用于未来可能的举报内容处理
        this.$message.success('举报已提交，我们会尽快处理')
      }).catch(() => {})
    },

    /**
     * 格式化日期时间
     */
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      return new Date(dateTime).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.traceability-detail {
  .el-card {
    margin-bottom: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    &:last-child {
      margin-bottom: 0;
    }
  }

  .card-header {
    font-weight: 600;
    color: var(--text-primary);

    i {
      margin-right: 8px;
      color: var(--primary-color);
    }
  }

  // 产品信息卡片
  .product-info-card {
    .product-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20px;

      .product-main {
        flex: 1;

        .product-name {
          font-size: 24px;
          color: var(--text-primary);
          margin: 0 0 10px 0;
        }

        .product-meta {
          display: flex;
          align-items: center;
          gap: 15px;
          margin-bottom: 10px;

          .batch-number {
            color: var(--text-secondary);
            font-family: monospace;
          }
        }

        .farm-info {
          color: var(--text-secondary);
          margin: 0;

          i {
            margin-right: 5px;
            color: var(--primary-color);
          }
        }
      }

      .product-qr {
        text-align: center;

        .qr-image {
          width: 120px;
          height: 120px;
          border: 1px solid var(--border-color);
          border-radius: 8px;
        }

        .trace-code {
          margin: 10px 0 0 0;
          font-family: monospace;
          font-size: 12px;
          color: var(--text-secondary);
          word-break: break-all;
        }
      }
    }

    .product-dates {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 20px;
      padding: 20px;
      background-color: var(--background-light);
      border-radius: 8px;

      .date-item {
        text-align: center;

        .date-label {
          display: block;
          color: var(--text-secondary);
          font-size: 14px;
          margin-bottom: 5px;
        }

        .date-value {
          display: block;
          color: var(--text-primary);
          font-weight: 600;
          font-size: 16px;
        }
      }
    }
  }

  // 认证信息
  .certificates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;

    .certificate-item {
      padding: 20px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.expired {
        border-color: var(--danger-color);
        background-color: rgba(245, 108, 108, 0.05);
      }

      .cert-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .cert-type {
          margin: 0;
          color: var(--text-primary);
        }
      }

      .cert-details {
        p {
          margin: 8px 0;
          color: var(--text-secondary);
          font-size: 14px;
        }

        .expire-info .warning {
          color: var(--warning-color);
          font-weight: 600;
        }
      }

      .cert-description {
        margin: 15px 0;
        padding: 10px;
        background-color: var(--background-light);
        border-radius: 4px;
        font-size: 14px;
        color: var(--text-secondary);
      }

      .cert-actions {
        margin-top: 15px;
        text-align: right;
      }
    }
  }

  // 物流轨迹
  .logistics-timeline {
    .logistics-item {
      display: flex;
      margin-bottom: 20px;
      position: relative;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 15px;
        top: 40px;
        bottom: -20px;
        width: 2px;
        background-color: var(--border-color);
      }

      &.completed::after {
        background-color: var(--success-color);
      }

      .logistics-icon {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: var(--border-color);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        flex-shrink: 0;

        i {
          color: white;
        }
      }

      &.completed .logistics-icon {
        background-color: var(--success-color);
      }

      .logistics-content {
        flex: 1;

        .logistics-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;

          h4 {
            margin: 0;
            color: var(--text-primary);
          }
        }

        .logistics-details {
          p {
            margin: 5px 0;
            color: var(--text-secondary);
            font-size: 14px;
          }

          .transport-conditions {
            margin-top: 10px;

            .condition-item {
              display: inline-block;
              margin-right: 15px;
              color: var(--text-secondary);
              font-size: 14px;

              i {
                margin-right: 5px;
                color: var(--primary-color);
              }
            }
          }
        }
      }
    }
  }

  // 查询统计
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
    margin-bottom: 20px;

    .stat-item {
      text-align: center;
      padding: 20px;
      background-color: var(--background-light);
      border-radius: 8px;

      .stat-number {
        font-size: 24px;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 5px;
      }

      .stat-label {
        color: var(--text-secondary);
        font-size: 14px;
      }
    }
  }

  .query-times {
    p {
      margin: 5px 0;
      color: var(--text-secondary);
      font-size: 14px;
    }
  }

  // 操作按钮
  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
    padding: 20px;
    background-color: var(--background-light);
    border-radius: 12px;
  }
}

// 移动端适配
@media screen and (max-width: 768px) {
  .traceability-detail {
    .product-header {
      flex-direction: column;
      text-align: center;

      .product-main {
        margin-bottom: 20px;
      }
    }

    .product-dates {
      grid-template-columns: 1fr;
    }

    .certificates-grid {
      grid-template-columns: 1fr;
    }

    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .action-buttons {
      flex-direction: column;
    }
  }
}
</style>
