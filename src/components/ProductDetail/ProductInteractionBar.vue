<template>
  <div class="product-interaction-bar">
    <div class="interaction-main">
      <!-- 点赞按钮 -->
      <LikeButton
        :product-id="productId"
        :size="buttonSize"
        :show-text="showText"
        :show-count="showCount"
        :initial-liked="initialLiked"
        :initial-count="initialLikeCount"
        @like-changed="handleLikeChanged"
        @need-login="handleNeedLogin"
      />
      
      <!-- 收藏按钮 -->
      <FavoriteButton
        :product-id="productId"
        :size="buttonSize"
        :show-text="showText"
        :show-count="showCount"
        :show-folder-select="showFolderSelect"
        :initial-favorited="initialFavorited"
        :initial-count="initialFavoriteCount"
        @favorite-changed="handleFavoriteChanged"
        @need-login="handleNeedLogin"
      />
      
      <!-- 分享按钮 -->
      <el-dropdown 
        v-if="showShare"
        @command="handleShare"
        trigger="click"
        placement="bottom-start"
      >
        <el-button
          :size="buttonSize"
          class="share-button"
        >
          <i class="el-icon-share"></i>
          <span v-if="showText" class="share-text">分享</span>
        </el-button>
        
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="copy">
            <i class="el-icon-document-copy"></i>
            复制链接
          </el-dropdown-item>
          <el-dropdown-item command="wechat">
            <i class="el-icon-chat-dot-round"></i>
            微信分享
          </el-dropdown-item>
          <el-dropdown-item command="weibo">
            <i class="el-icon-message"></i>
            微博分享
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      
      <!-- 举报按钮 -->
      <el-button
        v-if="showReport"
        :size="buttonSize"
        type="text"
        class="report-button"
        @click="handleReport"
      >
        <i class="el-icon-warning"></i>
        <span v-if="showText" class="report-text">举报</span>
      </el-button>
    </div>
    
    <!-- 互动统计信息 -->
    <div v-if="showStats" class="interaction-stats">
      <div class="stats-item">
        <i class="el-icon-view"></i>
        <span>{{ formatCount(viewCount) }} 浏览</span>
      </div>
      <div class="stats-item">
        <i class="el-icon-heart"></i>
        <span>{{ formatCount(likeCount) }} 点赞</span>
      </div>
      <div class="stats-item">
        <i class="el-icon-star-on"></i>
        <span>{{ formatCount(favoriteCount) }} 收藏</span>
      </div>
      <div class="stats-item">
        <i class="el-icon-chat-dot-round"></i>
        <span>{{ formatCount(reviewCount) }} 评价</span>
      </div>
    </div>
  </div>
</template>

<script>
import LikeButton from '@/components/ProductInteraction/LikeButton.vue'
import FavoriteButton from '@/components/ProductInteraction/FavoriteButton.vue'

export default {
  name: 'ProductInteractionBar',
  components: {
    LikeButton,
    FavoriteButton
  },
  
  props: {
    productId: {
      type: [Number, String],
      required: true
    },
    buttonSize: {
      type: String,
      default: 'small',
      validator: value => ['mini', 'small', 'medium'].includes(value)
    },
    showText: {
      type: Boolean,
      default: true
    },
    showCount: {
      type: Boolean,
      default: true
    },
    showFolderSelect: {
      type: Boolean,
      default: true
    },
    showShare: {
      type: Boolean,
      default: true
    },
    showReport: {
      type: Boolean,
      default: true
    },
    showStats: {
      type: Boolean,
      default: true
    },
    // 初始数据
    initialLiked: {
      type: Boolean,
      default: null
    },
    initialFavorited: {
      type: Boolean,
      default: null
    },
    initialLikeCount: {
      type: [Number, String],
      default: null
    },
    initialFavoriteCount: {
      type: [Number, String],
      default: null
    },
    viewCount: {
      type: [Number, String],
      default: 0
    },
    reviewCount: {
      type: [Number, String],
      default: 0
    }
  },
  
  data() {
    return {
      likeCount: 0,
      favoriteCount: 0
    }
  },
  
  watch: {
    initialLikeCount: {
      immediate: true,
      handler(val) {
        this.likeCount = Number(val) || 0
      }
    },
    initialFavoriteCount: {
      immediate: true,
      handler(val) {
        this.favoriteCount = Number(val) || 0
      }
    }
  },
  
  methods: {
    // 处理点赞状态变化
    handleLikeChanged(data) {
      this.likeCount = data.likeCount
      this.$emit('like-changed', data)
    },
    
    // 处理收藏状态变化
    handleFavoriteChanged(data) {
      this.favoriteCount = data.favoriteCount
      this.$emit('favorite-changed', data)
    },
    
    // 处理需要登录
    handleNeedLogin() {
      this.$emit('need-login')
    },
    
    // 处理分享
    handleShare(command) {
      const productUrl = window.location.href
      const productTitle = document.title
      
      switch (command) {
        case 'copy':
          this.copyToClipboard(productUrl)
          break
        case 'wechat':
          this.shareToWechat(productUrl, productTitle)
          break
        case 'weibo':
          this.shareToWeibo(productUrl, productTitle)
          break
      }
    },
    
    // 复制到剪贴板
    copyToClipboard(text) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          this.$message.success('链接已复制到剪贴板')
        }).catch(() => {
          this.fallbackCopyToClipboard(text)
        })
      } else {
        this.fallbackCopyToClipboard(text)
      }
    },
    
    // 备用复制方法
    fallbackCopyToClipboard(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      try {
        document.execCommand('copy')
        this.$message.success('链接已复制到剪贴板')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }
      
      document.body.removeChild(textArea)
    },
    
    // 分享到微信
    shareToWechat(_url, _title) {
      // 这里可以集成微信分享SDK
      this.$message.info('请使用微信扫码分享功能')
    },
    
    // 分享到微博
    shareToWeibo(url, title) {
      const weiboUrl = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`
      window.open(weiboUrl, '_blank')
    },
    
    // 处理举报
    handleReport() {
      // 检查用户是否登录
      const userInfo = JSON.parse(localStorage.getItem('sfap_user') || '{}')
      if (!userInfo.id) {
        this.$message.warning('请先登录')
        this.handleNeedLogin()
        return
      }
      
      this.$message.info('举报功能开发中')
    },
    
    // 格式化数量显示
    formatCount(count) {
      const num = Number(count) || 0
      if (num < 1000) {
        return num.toString()
      } else if (num < 10000) {
        return (num / 1000).toFixed(1) + 'k'
      } else {
        return (num / 10000).toFixed(1) + 'w'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.product-interaction-bar {
  .interaction-main {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    
    .share-button,
    .report-button {
      border-radius: 20px;
      transition: all 0.3s ease;
      
      .share-text,
      .report-text {
        margin-left: 4px;
      }
    }
    
    .share-button {
      &:hover {
        color: #409eff;
        border-color: #409eff;
        background-color: #ecf5ff;
      }
    }
    
    .report-button {
      color: #909399;
      
      &:hover {
        color: #f56c6c;
      }
    }
  }
  
  .interaction-stats {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 0;
    border-top: 1px solid #e4e7ed;
    
    .stats-item {
      display: flex;
      align-items: center;
      color: #909399;
      font-size: 13px;
      
      i {
        margin-right: 4px;
        font-size: 14px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .product-interaction-bar {
    .interaction-main {
      gap: 8px;
      
      .share-text,
      .report-text {
        display: none;
      }
    }
    
    .interaction-stats {
      gap: 12px;
      
      .stats-item {
        font-size: 12px;
        
        i {
          font-size: 13px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .product-interaction-bar {
    .interaction-main {
      flex-wrap: wrap;
      gap: 6px;
    }
    
    .interaction-stats {
      flex-wrap: wrap;
      gap: 8px;
      
      .stats-item {
        font-size: 11px;
        
        i {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
