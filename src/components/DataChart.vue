<template>
  <div class="data-chart" ref="chartContainer">
    <div class="chart-header" v-if="title">
      <h3>{{ title }}</h3>
      <div class="chart-actions">
        <el-radio-group v-model="timeRange" size="small" v-if="showTimeRange">
          <el-radio-button label="day">日</el-radio-button>
          <el-radio-button label="week">周</el-radio-button>
          <el-radio-button label="month">月</el-radio-button>
        </el-radio-group>
        <el-button type="text" @click="handleExport" v-if="showExport">
          <i class="el-icon-download"/> 导出
        </el-button>
      </div>
    </div>
    <div class="chart-content" :style="{ height: height + 'px' }"/>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'DataChart',
  props: {
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'line',
      validator: value => ['line', 'bar', 'pie', 'scatter'].includes(value)
    },
    data: {
      type: Object,
      required: true
    },
    height: {
      type: Number,
      default: 300
    },
    showTimeRange: {
      type: Boolean,
      default: false
    },
    showExport: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null,
      timeRange: 'day'
    }
  },
  watch: {
    data: {
      handler: 'updateChart',
      deep: true
    },
    timeRange() {
      this.$emit('time-range-change', this.timeRange)
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.chart) {
      this.chart.dispose()
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()
    },
    updateChart() {
      if (!this.chart) return
      
      const option = this.getChartOption()
      this.chart.setOption(option)
    },
    getChartOption() {
      const baseOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        }
      }
      
      switch (this.type) {
        case 'line':
          return {
            ...baseOption,
            xAxis: {
              type: 'category',
              data: this.data.xAxis
            },
            yAxis: {
              type: 'value'
            },
            series: this.data.series.map(item => ({
              name: item.name,
              type: 'line',
              data: item.data,
              smooth: true,
              areaStyle: {
                opacity: 0.1
              }
            }))
          }
        case 'bar':
          return {
            ...baseOption,
            xAxis: {
              type: 'category',
              data: this.data.xAxis
            },
            yAxis: {
              type: 'value'
            },
            series: this.data.series.map(item => ({
              name: item.name,
              type: 'bar',
              data: item.data
            }))
          }
        case 'pie':
          return {
            tooltip: {
              trigger: 'item'
            },
            legend: {
              orient: 'vertical',
              left: 'left'
            },
            series: [{
              type: 'pie',
              radius: '50%',
              data: this.data.series[0].data,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }]
          }
        case 'scatter':
          return {
            ...baseOption,
            xAxis: {
              type: 'value'
            },
            yAxis: {
              type: 'value'
            },
            series: this.data.series.map(item => ({
              name: item.name,
              type: 'scatter',
              data: item.data
            }))
          }
        default:
          return {}
      }
    },
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    handleExport() {
      const url = this.chart.getDataURL()
      const link = document.createElement('a')
      link.download = `${this.title || 'chart'}.png`
      link.href = url
      link.click()
    }
  }
}
</script>

<style lang="scss" scoped>
.data-chart {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
    
    .chart-actions {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }
  
  .chart-content {
    width: 100%;
  }
}
</style> 