import request from '@/utils/request'

// =====================================================
// 商店首页相关API
// =====================================================

/**
 * 获取热门推荐商品
 * @param {Object} params - 查询参数
 * @param {number} params.limit - 限制数量，默认4
 * @returns {Promise} API响应
 */
export function getHotRecommendations(params = {}) {
  return request({
    url: '/api/mall/products/shop/hot-recommendations',
    method: 'get',
    params: {
      limit: 4,
      ...params
    }
  })
}

/**
 * 获取农产品分类快速导航
 * @param {Object} params - 查询参数
 * @param {number} params.limit - 限制数量，默认6
 * @returns {Promise} API响应
 */
export function getQuickCategories(params = {}) {
  return request({
    url: '/api/mall/products/shop/quick-categories',
    method: 'get',
    params: {
      limit: 6,
      ...params
    }
  })
}

/**
 * 获取促销活动横幅
 * @param {Object} params - 查询参数
 * @param {number} params.limit - 限制数量，默认2
 * @param {string} params.status - 状态筛选，active/inactive
 * @returns {Promise} API响应
 */
export function getPromotionBanners(params = {}) {
  return request({
    url: '/api/mall/products/shop/promotion-banners',
    method: 'get',
    params: {
      limit: 2,
      status: 'active',
      ...params
    }
  })
}

/**
 * 获取农业资讯
 * @param {Object} params - 查询参数
 * @param {number} params.limit - 限制数量，默认3
 * @param {string} params.category - 分类筛选
 * @returns {Promise} API响应
 */
export function getAgricultureNews(params = {}) {
  return request({
    url: '/api/mall/products/shop/agriculture-news',
    method: 'get',
    params: {
      limit: 3,
      ...params
    }
  })
}

/**
 * 获取优质品牌展示
 * @param {Object} params - 查询参数
 * @param {number} params.limit - 限制数量，默认3
 * @param {boolean} params.featured - 是否只获取推荐品牌
 * @returns {Promise} API响应
 */
export function getFeaturedBrands(params = {}) {
  return request({
    url: '/api/mall/products/shop/featured-brands',
    method: 'get',
    params: {
      limit: 3,
      featured: true,
      ...params
    }
  })
}

/**
 * 获取用户评价展示
 * @param {Object} params - 查询参数
 * @param {number} params.limit - 限制数量，默认3
 * @param {number} params.minRating - 最低评分筛选
 * @returns {Promise} API响应
 */
export function getFeaturedReviews(params = {}) {
  return request({
    url: '/api/mall/products/shop/featured-reviews',
    method: 'get',
    params: {
      limit: 3,
      minRating: 4,
      ...params
    }
  })
}

/**
 * 获取商店统计数据
 * @returns {Promise} API响应
 */
export function getShopStats() {
  return request({
    url: '/api/shop/stats',
    method: 'get'
  })
}

/**
 * 点击促销横幅统计
 * @param {number} bannerId - 横幅ID
 * @returns {Promise} API响应
 */
export function trackBannerClick(bannerId) {
  return request({
    url: '/api/shop/banner-click',
    method: 'post',
    data: { bannerId }
  })
}

/**
 * 点击品牌统计
 * @param {number} brandId - 品牌ID
 * @returns {Promise} API响应
 */
export function trackBrandClick(brandId) {
  return request({
    url: '/api/shop/brand-click',
    method: 'post',
    data: { brandId }
  })
}

/**
 * 增加文章阅读量
 * @param {number} articleId - 文章ID
 * @returns {Promise} API响应
 */
export function incrementArticleViews(articleId) {
  return request({
    url: `/api/shop/article/${articleId}/view`,
    method: 'post'
  })
}

// =====================================================
// 销售者模块相关API
// =====================================================

/**
 * 获取销售者快捷操作数据
 * @returns {Promise} API响应
 */
export function getSellerQuickActions() {
  return request({
    url: '/api/seller/quick-actions',
    method: 'get'
  })
}

/**
 * 获取销售者店铺统计数据
 * @returns {Promise} API响应
 */
export function getSellerShopStats() {
  return request({
    url: '/api/seller/shop/stats',
    method: 'get'
  })
}

/**
 * 获取销售者商品管理数据
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export function getSellerProducts(params = {}) {
  return request({
    url: '/api/seller/products',
    method: 'get',
    params
  })
}

/**
 * 创建溯源记录
 * @param {Object} data - 溯源记录数据
 * @returns {Promise} API响应
 */
export function createTraceRecord(data) {
  return request({
    url: '/api/seller/trace-records',
    method: 'post',
    data
  })
}

/**
 * 获取销售者销售统计
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export function getSellerSalesStats(params = {}) {
  return request({
    url: '/api/seller/sales/stats',
    method: 'get',
    params
  })
}

// =====================================================
// 管理员模块相关API
// =====================================================

/**
 * 获取管理员统计概览
 * @returns {Promise} API响应
 */
export function getAdminStatsOverview() {
  return request({
    url: '/api/admin/stats/overview',
    method: 'get'
  })
}

/**
 * 获取管理员快捷操作数据
 * @returns {Promise} API响应
 */
export function getAdminQuickActions() {
  return request({
    url: '/api/admin/quick-actions',
    method: 'get'
  })
}

// =====================================================
// 用户模块相关API
// =====================================================

/**
 * 获取用户快捷操作数据
 * @returns {Promise} API响应
 */
export function getUserQuickActions() {
  return request({
    url: '/api/user/quick-actions',
    method: 'get'
  })
}

/**
 * 获取销售者申请状态
 * @returns {Promise} API响应
 */
export function getSellerApplicationStatus() {
  return request({
    url: '/api/user/seller-application/status',
    method: 'get'
  })
}
