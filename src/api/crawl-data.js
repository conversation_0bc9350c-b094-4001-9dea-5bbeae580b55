/**
 * 爬取数据API服务
 * 提供分类、地区、产品等数据的获取接口
 */

import axios from 'axios'

// 创建专门用于AI服务的axios实例
const aiService = axios.create({
  baseURL: '/api/prediction', // 通过NGINX代理访问AI服务
  timeout: 60000, // 统一超时时间为60秒
  withCredentials: false
})

// AI服务响应拦截器
aiService.interceptors.response.use(
  response => response,
  error => {
    console.error('AI服务请求失败:', error)
    return Promise.reject(error)
  }
)

const API_BASE = '/api/v1/crawl_data'

/**
 * 获取农产品分类列表
 * @returns {Promise} 分类列表
 */
export function getCategories() {
  return aiService({
    url: `${API_BASE}/categories`,
    method: 'get'
  })
}

/**
 * 获取地区列表
 * @param {number} level - 地区层级（1省2市3县）
 * @returns {Promise} 地区列表
 */
export function getRegions(level = null) {
  return aiService({
    url: `${API_BASE}/regions`,
    method: 'get',
    params: { level }
  })
}

/**
 * 搜索产品
 * @param {Object} params - 搜索参数
 * @param {string} params.keyword - 搜索关键词
 * @param {string} params.category - 分类名称
 * @param {number} params.limit - 限制数量
 * @returns {Promise} 产品列表
 */
export function searchProducts(params = {}) {
  return aiService({
    url: `${API_BASE}/products`,
    method: 'get',
    params
  })
}

/**
 * 获取分类下有价格数据的具体产品
 * @param {string} categoryName - 分类名称
 * @returns {Promise} 产品列表
 */
export async function getProductsByCategory(categoryName) {
  try {
    const response = await searchProducts({
      category: categoryName,
      limit: 100
    })

    const products = response.data.products || []

    // 只返回有价格数据的产品
    return products.filter(product => product.price_count > 0)
  } catch (error) {
    console.error('获取分类产品失败:', error)
    return []
  }
}

/**
 * 获取历史价格数据
 * @param {Object} params - 查询参数
 * @param {string} params.product_name - 产品名称
 * @param {string} params.region_name - 地区名称
 * @param {string} params.start_date - 开始日期
 * @param {string} params.end_date - 结束日期
 * @param {number} params.limit - 限制数量
 * @returns {Promise} 历史价格数据
 */
export function getPriceHistory(params) {
  return aiService({
    url: `${API_BASE}/prices/history`,
    method: 'get',
    params
  })
}

/**
 * 获取最新价格数据
 * @param {Object} params - 查询参数
 * @param {string} params.category - 分类名称
 * @param {string} params.region - 地区名称
 * @param {number} params.limit - 限制数量
 * @returns {Promise} 最新价格数据
 */
export function getLatestPrices(params = {}) {
  return aiService({
    url: `${API_BASE}/prices/latest`,
    method: 'get',
    params
  })
}

/**
 * 验证分类和地区组合的数据可用性
 * @param {string} category - 分类名称
 * @param {string} region - 地区名称
 * @returns {Promise} 数据可用性信息
 */
export async function validateDataAvailability(category, region) {
  try {
    const response = await getPriceHistory({
      product_name: category,
      region_name: region,
      limit: 1
    })
    
    const historyData = response.data.history_data || []
    const qualityReport = response.data.quality_report || {}
    
    return {
      available: historyData.length > 0,
      dataCount: historyData.length,
      qualityScore: qualityReport.quality_score || 0,
      latestDate: historyData.length > 0 ? historyData[0].date : null,
      message: historyData.length > 0 
        ? `找到 ${historyData.length} 条历史数据` 
        : '暂无历史数据'
    }
  } catch (error) {
    console.error('数据可用性验证失败:', error)
    return {
      available: false,
      dataCount: 0,
      qualityScore: 0,
      latestDate: null,
      message: '数据验证失败'
    }
  }
}

/**
 * 获取分类的产品统计信息
 * @param {string} categoryName - 分类名称
 * @returns {Promise} 产品统计信息
 */
export async function getCategoryProductStats(categoryName) {
  try {
    const response = await searchProducts({
      keyword: categoryName,
      limit: 100
    })
    
    const products = response.data.products || []
    
    // 统计有价格数据的产品
    const productsWithData = products.filter(p => p.price_count > 0)
    const totalPriceRecords = products.reduce((sum, p) => sum + (p.price_count || 0), 0)
    
    return {
      totalProducts: products.length,
      productsWithData: productsWithData.length,
      totalPriceRecords,
      dataAvailability: productsWithData.length / Math.max(products.length, 1)
    }
  } catch (error) {
    console.error('获取分类产品统计失败:', error)
    return {
      totalProducts: 0,
      productsWithData: 0,
      totalPriceRecords: 0,
      dataAvailability: 0
    }
  }
}

/**
 * 获取地区的价格数据统计信息
 * @param {string} regionName - 地区名称
 * @returns {Promise} 地区价格统计信息
 */
export async function getRegionPriceStats(regionName) {
  try {
    const response = await getLatestPrices({
      region: regionName,
      limit: 100
    })
    
    const prices = response.data.latest_prices || []
    
    // 按分类统计
    const categoryStats = {}
    prices.forEach(price => {
      const category = price.category_name
      if (!categoryStats[category]) {
        categoryStats[category] = {
          count: 0,
          products: new Set()
        }
      }
      categoryStats[category].count++
      categoryStats[category].products.add(price.product_name)
    })
    
    return {
      totalPriceRecords: prices.length,
      categoriesCount: Object.keys(categoryStats).length,
      categoryStats: Object.fromEntries(
        Object.entries(categoryStats).map(([cat, stats]) => [
          cat, 
          {
            priceCount: stats.count,
            productCount: stats.products.size
          }
        ])
      )
    }
  } catch (error) {
    console.error('获取地区价格统计失败:', error)
    return {
      totalPriceRecords: 0,
      categoriesCount: 0,
      categoryStats: {}
    }
  }
}

/**
 * 格式化分类数据为选择器选项（返回具体产品）
 * @param {Array} categories - 原始分类数据
 * @returns {Promise<Array>} 格式化的产品选项数组
 */
export async function formatCategoriesForSelector(categories) {
  if (!Array.isArray(categories)) {
    console.warn('formatCategoriesForSelector: categories不是数组', categories)
    return []
  }

  const productOptions = []

  // 遍历每个分类，获取其下的具体产品
  for (const category of categories) {
    if (category.price_count > 0) {
      try {
        const products = await getProductsByCategory(category.name)

        // 将产品添加到选项中
        products.forEach(product => {
          productOptions.push({
            label: `${product.product_name} (${category.name}, ${product.price_count}条数据)`,
            value: product.product_name,
            categoryName: category.name,
            productCount: 1,
            priceCount: product.price_count,
            disabled: product.price_count === 0,
            latestDate: product.latest_date
          })
        })
      } catch (error) {
        console.error(`获取分类 ${category.name} 的产品失败:`, error)
      }
    }
  }

  // 按价格数据量排序
  return productOptions.sort((a, b) => b.priceCount - a.priceCount)
}

/**
 * 格式化分类数据为选择器选项（原始分类版本）
 * @param {Array} categories - 原始分类数据
 * @returns {Array} 格式化的选项数组
 */
export function formatCategoriesForSelectorOriginal(categories) {
  if (!Array.isArray(categories)) {
    console.warn('formatCategoriesForSelector: categories不是数组', categories)
    return []
  }

  return categories.map(category => ({
    label: `${category.name} (${category.product_count}个产品)`,
    value: category.name,
    productCount: category.product_count,
    priceCount: category.price_count,
    disabled: category.product_count === 0,
    pinyin: category.pinyin
  }))
}

/**
 * 格式化地区数据为选择器选项
 * @param {Array} regions - 原始地区数据
 * @returns {Array} 格式化的选项数组
 */
export function formatRegionsForSelector(regions) {
  if (!Array.isArray(regions)) {
    console.warn('formatRegionsForSelector: regions不是数组', regions)
    return []
  }

  return regions.map(region => ({
    label: `${region.full_name || region.name} (${region.price_count}条数据)`,
    value: region.name,
    fullName: region.full_name,
    level: region.level,
    priceCount: region.price_count,
    disabled: region.price_count === 0
  }))
}

/**
 * 根据层级分组地区数据
 * @param {Array} regions - 地区数据
 * @returns {Object} 按层级分组的地区数据
 */
export function groupRegionsByLevel(regions) {
  const grouped = {
    provinces: [], // 省份 (level 1)
    cities: [],    // 城市 (level 2)
    counties: []   // 县区 (level 3)
  }
  
  regions.forEach(region => {
    switch (region.level) {
      case 1:
        grouped.provinces.push(region)
        break
      case 2:
        grouped.cities.push(region)
        break
      case 3:
        grouped.counties.push(region)
        break
    }
  })
  
  return grouped
}

/**
 * 搜索分类和地区
 * @param {Array} items - 要搜索的数组
 * @param {string} keyword - 搜索关键词
 * @returns {Array} 搜索结果
 */
export function searchItems(items, keyword) {
  if (!keyword) return items
  
  const lowerKeyword = keyword.toLowerCase()
  return items.filter(item => {
    const name = (item.name || item.label || '').toLowerCase()
    const pinyin = (item.pinyin || '').toLowerCase()
    const fullName = (item.fullName || '').toLowerCase()
    
    return name.includes(lowerKeyword) || 
           pinyin.includes(lowerKeyword) || 
           fullName.includes(lowerKeyword)
  })
}
