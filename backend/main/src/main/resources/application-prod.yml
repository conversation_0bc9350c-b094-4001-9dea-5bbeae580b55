# 生产环境配置文件
# 服务器IP: **************
# 前端端口: 8200, 后端端口: 8081

server:
  port: 8081
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    max-threads: 200
    min-spare-threads: 10
    max-connections: 10000
    connection-timeout: 60000  # 增加连接超时时间到60秒
    max-swallow-size: 2MB
    max-http-form-post-size: 2MB
    accept-count: 100
    max-keep-alive-requests: 100

spring:
  application:
    name: agriculture-mall-prod
  main:
    allow-bean-definition-overriding: true
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************************************************************************
    username: root
    password: fan13965711955
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      validation-timeout: 5000
      leak-detection-threshold: 60000
      connection-test-query: SELECT 1
      test-while-idle: true
      test-on-borrow: true
  
  # Redis配置
  redis:
    host: **************
    port: 6379
    password:
    database: 0
    timeout: 30000ms  # 增加超时时间
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # MVC配置
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    static-path-pattern: /uploads/**
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      enabled: true
      file-size-threshold: 2KB
  
  # 静态资源配置
  web:
    resources:
      static-locations: file:/www/wwwroot/test.com/backend/uploads/
      cache:
        period: 3600
  
  # 安全配置
  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.agriculture.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 生产环境日志配置
logging:
  level:
    com.agriculture: info
    org.springframework: warn
    com.zaxxer.hikari: warn
    org.apache.ibatis: warn
    org.springframework.security: warn
    org.springframework.web.socket: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /www/wwwroot/test.com/backend/logs/agriculture-mall.log
    max-size: 100MB
    max-history: 30

# 文件上传配置（生产环境）
file:
  upload:
    path: /www/wwwroot/test.com/backend/uploads
    avatar-dir: /www/wwwroot/test.com/backend/uploads/images/avatars
    product-dir: /www/wwwroot/test.com/backend/uploads/images/products
    news-dir: /www/wwwroot/test.com/backend/uploads/images/news
    encyclopedia-dir: /www/wwwroot/test.com/backend/uploads/images/encyclopedia

# 生产环境应用配置
app:
  domain: http://**************:8200
  cors:
    allowed-origins: http://**************,http://**************:8200,http://**************:8081,http://localhost:8200,http://127.0.0.1:8200,https://**************,https://**************:8200
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS,PATCH,HEAD
    allowed-headers: "*"
    allow-credentials: true
    max-age: 3600
    expose-headers: "Authorization,Content-Length,X-Requested-With"

# 阿里云百炼平台配置
dashscope:
  api-key: ${DASHSCOPE_API_KEY:sk-25e75ffd687945edae31e76ceeedcc14}
  app-id: be7d7128f33d47898fae7e3d9e19a4c6
  endpoint: https://dashscope.aliyuncs.com/api/v1/

# Python微服务配置
python:
  price:
    service:
      url: http://**************:5001
      timeout: 30000
      retry-count: 3

# AI服务配置
ai:
  service:
    url: http://**************:5000
    timeout: 30000
    retry-count: 3

# WebSocket配置
websocket:
  enabled: true
  path: /ws
  allowed-origins: http://**************:8200,http://localhost:8200
