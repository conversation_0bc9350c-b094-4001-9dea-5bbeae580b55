package com.agriculture.utils;

import com.agriculture.entity.User;
import com.agriculture.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * 认证工具类
 * 用于处理用户认证和权限验证
 */
@Slf4j
@Component
public class AuthUtils {

    private static UserService userService;

    @Autowired
    public void setUserService(UserService userService) {
        AuthUtils.userService = userService;
    }

    /**
     * 从请求中获取当前用户ID
     * @param request HTTP请求
     * @return 用户ID，如果未登录返回null
     */
    public static Long getCurrentUserId(HttpServletRequest request) {
        try {
            // 1. 优先从request属性中获取（UserAuthInterceptor设置的）
            Object userIdAttr = request.getAttribute("userId");
            if (userIdAttr instanceof Long) {
                return (Long) userIdAttr;
            }

            // 2. 从X-User-Id请求头获取（与UserAuthInterceptor保持一致）
            String userIdHeader = request.getHeader("X-User-Id");
            if (userIdHeader != null && !userIdHeader.isEmpty()) {
                try {
                    Long userId = Long.parseLong(userIdHeader);

                    // 验证用户是否存在且有效
                    User user = userService.getById(userId);
                    if (user != null && user.getStatus() == 1 && user.getDeleted() == 0) {
                        return userId;
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的用户ID格式: {}", userIdHeader);
                }
            }

            // 3. 兼容原有的token方式
            String token = getTokenFromRequest(request);
            if (token != null) {
                Long userId = parseUserIdFromToken(token);
                if (userId != null) {
                    User user = userService.getById(userId);
                    if (user != null && user.getStatus() == 1 && user.getDeleted() == 0) {
                        return userId;
                    }
                }
            }

            return null;
        } catch (Exception e) {
            log.error("获取当前用户ID失败", e);
            return null;
        }
    }

    /**
     * 从请求中获取当前用户信息
     * @param request HTTP请求
     * @return 用户信息，如果未登录返回null
     */
    public static User getCurrentUser(HttpServletRequest request) {
        try {
            // 1. 优先从request属性中获取（UserAuthInterceptor设置的）
            Object userAttr = request.getAttribute("user");
            if (userAttr instanceof User) {
                return (User) userAttr;
            }

            // 2. 通过用户ID获取
            Long userId = getCurrentUserId(request);
            if (userId == null) {
                return null;
            }

            return userService.getById(userId);
        } catch (Exception e) {
            log.error("获取当前用户信息失败", e);
            return null;
        }
    }

    /**
     * 判断当前用户是否为销售者
     * @param request HTTP请求
     * @return 是否为销售者
     */
    public static boolean isSeller(HttpServletRequest request) {
        try {
            User user = getCurrentUser(request);
            if (user == null) {
                return false;
            }

            // 检查用户角色
            String role = user.getRole();
            String userType = user.getUserType();
            
            // 支持多种角色判断方式
            return "seller".equals(role) || 
                   "seller".equals(userType) || 
                   "销售者".equals(role) ||
                   "销售者".equals(userType);
        } catch (Exception e) {
            log.error("判断销售者权限失败", e);
            return false;
        }
    }

    /**
     * 判断当前用户是否为管理员
     * @param request HTTP请求
     * @return 是否为管理员
     */
    public static boolean isAdmin(HttpServletRequest request) {
        try {
            User user = getCurrentUser(request);
            if (user == null) {
                return false;
            }

            // 检查用户角色
            String role = user.getRole();
            String userType = user.getUserType();
            
            // 支持多种角色判断方式
            return "admin".equals(role) || 
                   "admin".equals(userType) || 
                   "管理员".equals(role) ||
                   "管理员".equals(userType);
        } catch (Exception e) {
            log.error("判断管理员权限失败", e);
            return false;
        }
    }

    /**
     * 判断当前用户是否为普通用户
     * @param request HTTP请求
     * @return 是否为普通用户
     */
    public static boolean isNormalUser(HttpServletRequest request) {
        try {
            User user = getCurrentUser(request);
            if (user == null) {
                return false;
            }

            // 检查用户角色
            String role = user.getRole();
            String userType = user.getUserType();
            
            // 支持多种角色判断方式
            return "user".equals(role) || 
                   "user".equals(userType) || 
                   "普通用户".equals(role) ||
                   "普通用户".equals(userType) ||
                   "normal".equals(role) ||
                   "normal".equals(userType);
        } catch (Exception e) {
            log.error("判断普通用户权限失败", e);
            return false;
        }
    }

    /**
     * 验证用户是否已登录
     * @param request HTTP请求
     * @return 是否已登录
     */
    public static boolean isAuthenticated(HttpServletRequest request) {
        return getCurrentUserId(request) != null;
    }

    /**
     * 验证用户是否有指定权限
     * @param request HTTP请求
     * @param permission 权限名称
     * @return 是否有权限
     */
    public static boolean hasPermission(HttpServletRequest request, String permission) {
        try {
            User user = getCurrentUser(request);
            if (user == null) {
                return false;
            }

            // 管理员拥有所有权限
            if (isAdmin(request)) {
                return true;
            }

            // 根据权限名称判断
            switch (permission) {
                case "seller":
                    return isSeller(request);
                case "admin":
                    return isAdmin(request);
                case "user":
                    return isNormalUser(request);
                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("验证用户权限失败 - permission: {}", permission, e);
            return false;
        }
    }

    /**
     * 从请求中获取token
     * @param request HTTP请求
     * @return token字符串
     */
    private static String getTokenFromRequest(HttpServletRequest request) {
        // 1. 从Authorization头获取
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }

        // 2. 从请求参数获取
        String tokenParam = request.getParameter("token");
        if (tokenParam != null && !tokenParam.trim().isEmpty()) {
            return tokenParam;
        }

        // 3. 从session获取用户ID（兼容现有登录方式）
        Object userIdObj = request.getSession().getAttribute("userId");
        if (userIdObj != null) {
            // 如果session中有userId，直接返回一个标识
            return "session:" + userIdObj.toString();
        }

        return null;
    }

    /**
     * 从token中解析用户ID
     * @param token token字符串
     * @return 用户ID
     */
    private static Long parseUserIdFromToken(String token) {
        try {
            // 如果是session token
            if (token.startsWith("session:")) {
                String userIdStr = token.substring(8);
                return Long.parseLong(userIdStr);
            }

            // TODO: 如果使用JWT token，在这里解析JWT
            // 目前简化处理，假设token就是用户ID
            try {
                return Long.parseLong(token);
            } catch (NumberFormatException e) {
                // 如果token不是数字，可能是JWT或其他格式
                // 这里可以添加JWT解析逻辑
                log.warn("无法解析token: {}", token);
                return null;
            }
        } catch (Exception e) {
            log.error("解析token失败: {}", token, e);
            return null;
        }
    }

    /**
     * 获取用户角色描述
     * @param user 用户对象
     * @return 角色描述
     */
    public static String getRoleDescription(User user) {
        if (user == null) {
            return "未知";
        }

        String role = user.getRole();
        String userType = user.getUserType();

        if ("admin".equals(role) || "admin".equals(userType) || "管理员".equals(role)) {
            return "管理员";
        } else if ("seller".equals(role) || "seller".equals(userType) || "销售者".equals(role)) {
            return "销售者";
        } else if ("user".equals(role) || "user".equals(userType) || "普通用户".equals(role) || "normal".equals(role)) {
            return "普通用户";
        } else {
            return "未知角色";
        }
    }

    /**
     * 记录用户操作日志
     * @param request HTTP请求
     * @param action 操作名称
     * @param description 操作描述
     */
    public static void logUserAction(HttpServletRequest request, String action, String description) {
        try {
            User user = getCurrentUser(request);
            String username = user != null ? user.getUsername() : "匿名用户";
            String userRole = user != null ? getRoleDescription(user) : "未知";
            String clientIp = getClientIpAddress(request);

            log.info("用户操作日志 - 用户: {}, 角色: {}, IP: {}, 操作: {}, 描述: {}", 
                    username, userRole, clientIp, action, description);
        } catch (Exception e) {
            log.error("记录用户操作日志失败", e);
        }
    }

    /**
     * 获取客户端IP地址
     * @param request HTTP请求
     * @return IP地址
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}
