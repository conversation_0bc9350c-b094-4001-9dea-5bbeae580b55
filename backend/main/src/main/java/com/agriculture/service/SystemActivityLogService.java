package com.agriculture.service;

import com.agriculture.entity.SystemActivityLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 系统活动日志服务接口
 *
 * <AUTHOR> Team
 * @since 2024-12-01
 */
public interface SystemActivityLogService extends IService<SystemActivityLog> {

    /**
     * 记录系统活动日志
     * @param activityType 活动类型
     * @param description 活动描述
     * @param userId 用户ID
     * @param username 用户名
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @param requestPath 请求路径
     * @param requestMethod 请求方法
     * @param requestParams 请求参数
     * @param responseStatus 响应状态码
     * @param executionTime 执行时间
     */
    void logActivity(String activityType, String description, Long userId, String username,
                    String ipAddress, String userAgent, String requestPath, String requestMethod,
                    String requestParams, Integer responseStatus, Long executionTime);

    /**
     * 记录用户登录活动
     * @param userId 用户ID
     * @param username 用户名
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @param success 是否成功
     */
    void logUserLogin(Long userId, String username, String ipAddress, String userAgent, boolean success);

    /**
     * 记录用户注册活动
     * @param userId 用户ID
     * @param username 用户名
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     */
    void logUserRegister(Long userId, String username, String ipAddress, String userAgent);

    /**
     * 记录商品操作活动
     * @param userId 用户ID
     * @param username 用户名
     * @param productId 商品ID
     * @param operation 操作类型
     * @param ipAddress IP地址
     */
    void logProductOperation(Long userId, String username, Long productId, String operation, String ipAddress);

    /**
     * 记录订单操作活动
     * @param userId 用户ID
     * @param username 用户名
     * @param orderId 订单ID
     * @param operation 操作类型
     * @param ipAddress IP地址
     */
    void logOrderOperation(Long userId, String username, Long orderId, String operation, String ipAddress);

    /**
     * 获取最近的活动日志
     * @param limit 限制数量
     * @return 活动日志列表
     */
    List<SystemActivityLog> getRecentActivities(int limit);

    /**
     * 获取指定时间范围内的活动统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 活动统计数据
     */
    List<Map<String, Object>> getActivityStatsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取用户活动统计
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 用户活动统计
     */
    List<Map<String, Object>> getUserActivityStats(Long userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 清理过期的活动日志
     * @param days 保留天数
     * @return 清理的记录数
     */
    int cleanExpiredLogs(int days);
}