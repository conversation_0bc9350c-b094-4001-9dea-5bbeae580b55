package com.agriculture.service.impl;

import com.agriculture.entity.SellerApplication;
import com.agriculture.entity.User;
import com.agriculture.mapper.SellerApplicationMapper;
import com.agriculture.mapper.UserMapper;
import com.agriculture.service.FileService;
import com.agriculture.service.SellerService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 销售者服务实现类
 */
@Service
public class SellerServiceImpl implements SellerService {

    private static final Logger log = LoggerFactory.getLogger(SellerServiceImpl.class);

    @Autowired
    private SellerApplicationMapper sellerApplicationMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private FileService fileService;

    @Override
    @Transactional
    public SellerApplication saveApplication(SellerApplication application) {
        // 设置默认状态 (pending:待审核, approved:通过, rejected:拒绝)
        application.setStatus("pending");
        
        // 设置预计完成时间（3天后）
        application.setExpectedCompletionTime(LocalDateTime.now().plusDays(3));
        
        sellerApplicationMapper.insert(application);
        return application;
    }

    @Override
    public Map<String, String> uploadCertificate(MultipartFile file, Long userId) {
        // 上传文件
        String fileId = UUID.randomUUID().toString();
        String url = fileService.uploadFile(file, "seller/certificates/" + userId + "/" + fileId);
        
        Map<String, String> result = new HashMap<>();
        result.put("fileId", fileId);
        result.put("url", url);
        
        return result;
    }

    @Override
    public boolean hasActiveApplication(Long userId) {
        return sellerApplicationMapper.findByUserIdAndStatusNot(userId, 2) != null; // 2表示拒绝状态
    }

    @Override
    public SellerApplication getActiveApplication(Long userId) {
        return sellerApplicationMapper.findByUserIdAndStatusNot(userId, 2); // 2表示拒绝状态
    }

    @Override
    @Transactional
    public boolean cancelApplication(Long userId) {
        SellerApplication application = sellerApplicationMapper.findByUserIdAndStatus(userId, 0); // 0表示pending状态
        
        if (application != null) {
            sellerApplicationMapper.deleteById(application.getId());
            return true;
        }
        
        return false;
    }

    @Override
    public IPage<SellerApplication> getApplications(int page, int size, String status) {
        Page<SellerApplication> pageParam = new Page<>(page + 1, size); // MyBatis Plus页码从1开始
        
        if (status != null && !status.isEmpty()) {
            Integer statusCode = "pending".equals(status) ? 0 : "approved".equals(status) ? 1 : 2;
            return sellerApplicationMapper.findByStatus(pageParam, statusCode);
        }
        
        return sellerApplicationMapper.findAllApplications(pageParam);
    }

    @Override
    public SellerApplication getApplicationById(Long id) {
        return sellerApplicationMapper.selectById(id);
    }

    @Override
    @Transactional
    public boolean approveApplication(Long id, String status, String reason) {
        SellerApplication application = sellerApplicationMapper.selectById(id);
        
        if (application != null) {
            // 只能审批待审核的申请
            if (!Integer.valueOf(0).equals(application.getStatus())) {
                return false;
            }
            
            // 更新申请状态 (pending:待审核, approved:通过, rejected:拒绝)
            application.setStatus(status);
            
            // 获取当前管理员ID
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            User admin = userMapper.selectByUsername(username);
            
            if (admin != null) {
                application.setApprovedBy(admin.getId());
            }
            
            application.setApprovedAt(LocalDateTime.now());
            
            // 如果拒绝，设置拒绝原因
            if ("rejected".equals(status)) {
                application.setRejectReason(reason);
            }
            
            // 如果通过，更新用户为销售者
            if ("approved".equals(status)) {
                User user = userMapper.selectById(application.getUserId());

                if (user != null) {
                    // 使用新的统一角色设置方法
                    user.setUserRole("seller");
                    user.setUpdatedAt(LocalDateTime.now());
                    userMapper.updateById(user);

                    log.info("用户 {} 已成功转换为销售者角色", user.getUsername());
                }
            }
            
            sellerApplicationMapper.updateById(application);
            
            return true;
        }
        
        return false;
    }

    @Override
    public Map<String, Object> getSellerStats(Long sellerId) {
        Map<String, Object> stats = new HashMap<>();
        
        // 获取销售者的统计数据
        // 这里可以根据实际需求添加更多统计数据
        stats.put("productCount", 0); // 商品数量
        stats.put("orderCount", 0); // 订单数量
        stats.put("totalSales", 0.0); // 总销售额
        stats.put("customerCount", 0); // 客户数量
        
        return stats;
    }

    @Override
    @Transactional
    public void updateSellerInfo(Long sellerId, Map<String, Object> data) {
        User user = userMapper.selectById(sellerId);
        
        if (user != null) {
            // 更新销售者信息
            // 这里可以根据实际需求更新更多字段
            if (data.containsKey("phone")) {
                user.setPhone((String) data.get("phone"));
            }
            
            if (data.containsKey("email")) {
                user.setEmail((String) data.get("email"));
            }
            
            userMapper.updateById(user);
        }
    }

    @Override
    public List<Map<String, String>> getSellerCertificates(Long sellerId) {
        // 获取销售者的证书列表
        // 这里需要根据实际存储方式获取证书
        List<Map<String, String>> certificates = new ArrayList<>();
        
        // 模拟数据
        // 实际应该从数据库或文件存储中获取
        Map<String, String> cert = new HashMap<>();
        cert.put("fileId", "sample-id");
        cert.put("url", "/uploads/seller/certificates/sample.jpg");
        cert.put("name", "资质证书");
        certificates.add(cert);
        
        return certificates;
    }

    @Override
    public long countTotalApplications() {
        return sellerApplicationMapper.selectCount(null);
    }

    @Override
    public long countApplicationsByStatus(String status) {
        QueryWrapper<SellerApplication> wrapper = new QueryWrapper<>();
        wrapper.eq("status", status);
        return sellerApplicationMapper.selectCount(wrapper);
    }

    @Override
    public long countTotalSellers() {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("user_type", "seller"); // 使用字符串"seller"表示销售者
        return userMapper.selectCount(wrapper);
    }

    @Override
    public long countActiveSellers() {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("user_type", "seller").eq("status", 1); // "seller"表示销售者且状态正常
        return userMapper.selectCount(wrapper);
    }

    @Override
    public Map<String, Object> getSellerDetails(Long sellerId) {
        User user = userMapper.selectById(sellerId);
        
        if (user != null) {
            Map<String, Object> details = new HashMap<>();
            details.put("id", user.getId());
            details.put("username", user.getUsername());
            details.put("nickname", user.getNickname());
            details.put("phone", user.getPhone());
            details.put("email", user.getEmail());
            details.put("status", user.getStatus());
            details.put("createdAt", user.getCreatedAt());
            
            // 获取申请信息
            SellerApplication application = sellerApplicationMapper.findTopByUserIdOrderByCreatedAtDesc(sellerId);
            
            if (application != null) {
                
                Map<String, Object> applicationDetails = new HashMap<>();
                applicationDetails.put("id", application.getId());
                applicationDetails.put("farmName", application.getFarmName());
                applicationDetails.put("contactName", application.getContactName());
                applicationDetails.put("contactPhone", application.getContactPhone());
                applicationDetails.put("contactEmail", application.getContactEmail());
                applicationDetails.put("businessLicense", application.getBusinessLicense());
                applicationDetails.put("certificateUrls", application.getCertificateUrls());
                applicationDetails.put("status", application.getStatus());
                applicationDetails.put("createTime", application.getCreateTime());
                
                if ("rejected".equals(application.getStatus())) {
                    applicationDetails.put("rejectReason", application.getRejectReason());
                }
                
                details.put("application", applicationDetails);
            }
            
            return details;
        }
        
        return null;
    }

    @Override
    @Transactional
    public boolean updateSellerStatus(Long sellerId, String status, String reason) {
        User user = userMapper.selectById(sellerId);
        
        if (user != null) {
            // 更新状态 (0:禁用 1:正常)
            user.setStatus("active".equals(status) ? 1 : 0);
            
            // 如果禁用，可以记录禁用原因
            // 这里需要根据实际需求实现
            
            userMapper.updateById(user);
            
            return true;
        }
        
        return false;
    }

    @Override
    public IPage<User> getSellers(int page, int size, String status) {
        log.info("获取销售者列表 - 页码: {}, 每页大小: {}, 状态筛选: {}", page, size, status);

        Page<User> pageParam = new Page<>(page + 1, size); // MyBatis-Plus页码从1开始
        QueryWrapper<User> wrapper = new QueryWrapper<>();

        // 查询销售者用户 - 检查role和user_type两个字段
        wrapper.and(w -> w.eq("role", "seller").or().eq("user_type", "seller"));

        // 排除已删除的用户
        wrapper.eq("deleted", 0);

        if (status != null && !status.isEmpty()) {
            if ("active".equals(status)) {
                wrapper.eq("status", 1);
            } else if ("inactive".equals(status)) {
                wrapper.eq("status", 0);
            }
        }

        wrapper.orderByDesc("created_at");

        IPage<User> result = userMapper.selectPage(pageParam, wrapper);
        log.info("销售者列表查询结果 - 总记录数: {}, 当前页记录数: {}",
                result.getTotal(), result.getRecords().size());

        // 清除敏感信息
        result.getRecords().forEach(user -> {
            user.setPassword(null); // 不返回密码
        });

        return result;
    }

    @Override
    public Map<String, Object> getSellerSalesData(Long sellerId) {
        Map<String, Object> salesData = new HashMap<>();
        
        // 获取销售者的销售数据
        // 这里需要根据实际的订单和产品表结构来实现
        salesData.put("totalOrders", 0); // 总订单数
        salesData.put("totalSales", 0.0); // 总销售额
        salesData.put("monthlyOrders", 0); // 本月订单数
        salesData.put("monthlySales", 0.0); // 本月销售额
        salesData.put("productCount", 0); // 商品数量
        salesData.put("customerCount", 0); // 客户数量
        
        // TODO: 实际实现需要查询订单表和产品表
        // 例如：
        // QueryWrapper<Order> orderWrapper = new QueryWrapper<>();
        // orderWrapper.eq("seller_id", sellerId);
        // List<Order> orders = orderMapper.selectList(orderWrapper);
        // salesData.put("totalOrders", orders.size());
        // salesData.put("totalSales", orders.stream().mapToDouble(Order::getTotalAmount).sum());
        
        return salesData;
    }

    @Override
    @Transactional
    public boolean deleteApplication(Long id) {
        SellerApplication application = sellerApplicationMapper.selectById(id);
        
        if (application != null) {
            // 只能删除被拒绝的申请或待审核的申请
            if ("rejected".equals(application.getStatus()) || "pending".equals(application.getStatus())) {
                int result = sellerApplicationMapper.deleteById(id);
                return result > 0;
            }
        }
        
        return false;
    }

    @Override
    public long getUnreadApplicationCount() {
        QueryWrapper<SellerApplication> wrapper = new QueryWrapper<>();
        wrapper.eq("status", 0); // 0表示待审核状态
        return sellerApplicationMapper.selectCount(wrapper);
    }

    @Override
    @Transactional
    public boolean markApplicationsAsRead() {
        // 这里可以实现标记为已读的逻辑
        // 由于申请本身没有已读/未读状态，这个方法可以用于其他用途
        // 比如清除缓存或者记录管理员查看时间等
        return true;
    }
}