package com.agriculture.service.impl;

import com.agriculture.service.AnalyticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 数据分析服务实现类
 * 基于数据库视图提供统计分析功能
 */
@Slf4j
@Service
public class AnalyticsServiceImpl implements AnalyticsService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public Map<String, Object> getSellerBasicStats(Long sellerId) {
        try {
            log.info("获取销售者基础统计数据 - sellerId: {}", sellerId);
            
            String sql = "SELECT * FROM v_seller_basic_stats WHERE seller_id = ?";
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, sellerId);
            
            if (!results.isEmpty()) {
                return results.get(0);
            } else {
                // 返回默认数据
                Map<String, Object> defaultStats = new HashMap<>();
                defaultStats.put("seller_id", sellerId);
                defaultStats.put("total_products", 0);
                defaultStats.put("active_products", 0);
                defaultStats.put("avg_product_rating", 0.0);
                defaultStats.put("pending_orders", 0);
                defaultStats.put("completed_orders", 0);
                defaultStats.put("total_revenue", 0.0);
                defaultStats.put("avg_order_value", 0.0);
                return defaultStats;
            }
        } catch (Exception e) {
            log.error("获取销售者基础统计数据失败 - sellerId: {}", sellerId, e);
            throw new RuntimeException("获取统计数据失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getAdminDashboardStats() {
        try {
            log.info("获取管理员仪表板统计数据");
            
            String sql = "SELECT * FROM v_admin_dashboard_stats LIMIT 1";
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);
            
            if (!results.isEmpty()) {
                return results.get(0);
            } else {
                // 返回默认数据
                Map<String, Object> defaultStats = new HashMap<>();
                defaultStats.put("total_users", 0);
                defaultStats.put("total_sellers", 0);
                defaultStats.put("total_admins", 0);
                defaultStats.put("total_products", 0);
                defaultStats.put("active_products", 0);
                defaultStats.put("total_orders", 0);
                defaultStats.put("completed_orders", 0);
                defaultStats.put("total_revenue", 0.0);
                defaultStats.put("pending_applications", 0);
                defaultStats.put("approved_applications", 0);
                defaultStats.put("rejected_applications", 0);
                defaultStats.put("today_new_users", 0);
                defaultStats.put("today_new_orders", 0);
                defaultStats.put("today_revenue", 0.0);
                return defaultStats;
            }
        } catch (Exception e) {
            log.error("获取管理员仪表板统计数据失败", e);
            throw new RuntimeException("获取仪表板数据失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getProductPerformance(Long sellerId, Integer page, Integer size) {
        try {
            log.info("获取商品性能数据 - sellerId: {}, page: {}, size: {}", sellerId, page, size);
            
            int offset = page * size;
            String sql = "SELECT * FROM v_product_performance WHERE seller_id = ? ORDER BY total_revenue DESC LIMIT ? OFFSET ?";
            List<Map<String, Object>> products = jdbcTemplate.queryForList(sql, sellerId, size, offset);
            
            String countSql = "SELECT COUNT(*) FROM v_product_performance WHERE seller_id = ?";
            Long total = jdbcTemplate.queryForObject(countSql, Long.class, sellerId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("items", products);
            result.put("total", total != null ? total : 0);
            result.put("page", page);
            result.put("size", size);
            
            return result;
        } catch (Exception e) {
            log.error("获取商品性能数据失败 - sellerId: {}", sellerId, e);
            throw new RuntimeException("获取商品性能数据失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getOrderAnalysis(Long sellerId, Integer page, Integer size) {
        try {
            log.info("获取订单分析数据 - sellerId: {}, page: {}, size: {}", sellerId, page, size);
            
            int offset = page * size;
            String sql = "SELECT * FROM v_order_analysis WHERE seller_id = ? ORDER BY order_time DESC LIMIT ? OFFSET ?";
            List<Map<String, Object>> orders = jdbcTemplate.queryForList(sql, sellerId, size, offset);
            
            String countSql = "SELECT COUNT(*) FROM v_order_analysis WHERE seller_id = ?";
            Long total = jdbcTemplate.queryForObject(countSql, Long.class, sellerId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("items", orders);
            result.put("total", total != null ? total : 0);
            result.put("page", page);
            result.put("size", size);
            
            return result;
        } catch (Exception e) {
            log.error("获取订单分析数据失败 - sellerId: {}", sellerId, e);
            throw new RuntimeException("获取订单分析数据失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getSystemTrends(Integer days) {
        try {
            log.info("获取系统趋势数据 - days: {}", days);
            
            String sql = "SELECT * FROM v_monthly_trends ORDER BY stat_month DESC LIMIT ?";
            List<Map<String, Object>> trends = jdbcTemplate.queryForList(sql, Math.min(days / 30, 12));
            
            Map<String, Object> result = new HashMap<>();
            result.put("trends", trends);
            
            return result;
        } catch (Exception e) {
            log.error("获取系统趋势数据失败", e);
            throw new RuntimeException("获取趋势数据失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getSellerApplicationStats(Integer page, Integer size, Integer status) {
        try {
            log.info("获取销售者申请统计 - page: {}, size: {}, status: {}", page, size, status);
            
            int offset = page * size;
            String sql;
            List<Map<String, Object>> applications;
            Long total;
            
            if (status != null) {
                sql = "SELECT * FROM v_seller_application_stats WHERE application_status = ? ORDER BY application_time DESC LIMIT ? OFFSET ?";
                applications = jdbcTemplate.queryForList(sql, status, size, offset);
                
                String countSql = "SELECT COUNT(*) FROM v_seller_application_stats WHERE application_status = ?";
                total = jdbcTemplate.queryForObject(countSql, Long.class, status);
            } else {
                sql = "SELECT * FROM v_seller_application_stats ORDER BY application_time DESC LIMIT ? OFFSET ?";
                applications = jdbcTemplate.queryForList(sql, size, offset);
                
                String countSql = "SELECT COUNT(*) FROM v_seller_application_stats";
                total = jdbcTemplate.queryForObject(countSql, Long.class);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("items", applications);
            result.put("total", total != null ? total : 0);
            result.put("page", page);
            result.put("size", size);
            
            return result;
        } catch (Exception e) {
            log.error("获取销售者申请统计失败", e);
            throw new RuntimeException("获取申请统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getUserPermissionStats(Integer page, Integer size) {
        try {
            log.info("获取用户权限统计 - page: {}, size: {}", page, size);
            
            int offset = page * size;
            String sql = "SELECT * FROM v_user_permission_stats ORDER BY register_time DESC LIMIT ? OFFSET ?";
            List<Map<String, Object>> users = jdbcTemplate.queryForList(sql, size, offset);
            
            String countSql = "SELECT COUNT(*) FROM v_user_permission_stats";
            Long total = jdbcTemplate.queryForObject(countSql, Long.class);
            
            Map<String, Object> result = new HashMap<>();
            result.put("items", users);
            result.put("total", total != null ? total : 0);
            result.put("page", page);
            result.put("size", size);
            
            return result;
        } catch (Exception e) {
            log.error("获取用户权限统计失败", e);
            throw new RuntimeException("获取用户权限统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getSystemRealtimeStatus() {
        try {
            log.info("获取系统实时状态");
            
            String sql = "SELECT * FROM v_system_realtime_status";
            List<Map<String, Object>> statusList = jdbcTemplate.queryForList(sql);
            
            Map<String, Object> result = new HashMap<>();
            for (Map<String, Object> status : statusList) {
                String metricName = (String) status.get("metric_name");
                Object metricValue = status.get("metric_value");
                result.put(metricName, metricValue);
            }
            
            return result;
        } catch (Exception e) {
            log.error("获取系统实时状态失败", e);
            throw new RuntimeException("获取实时状态失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getSystemAlerts() {
        try {
            log.info("获取系统预警信息");
            
            String sql = "SELECT * FROM v_system_alerts ORDER BY alert_time DESC LIMIT 50";
            return jdbcTemplate.queryForList(sql);
        } catch (Exception e) {
            log.error("获取系统预警信息失败", e);
            throw new RuntimeException("获取预警信息失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getSellerPerformanceScore(Long sellerId) {
        try {
            log.info("获取销售者绩效评分 - sellerId: {}", sellerId);
            
            String sql = "SELECT * FROM v_seller_performance_score WHERE seller_id = ?";
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, sellerId);
            
            if (!results.isEmpty()) {
                return results.get(0);
            } else {
                Map<String, Object> defaultScore = new HashMap<>();
                defaultScore.put("seller_id", sellerId);
                defaultScore.put("total_score", 0.0);
                defaultScore.put("performance_grade", "D");
                return defaultScore;
            }
        } catch (Exception e) {
            log.error("获取销售者绩效评分失败 - sellerId: {}", sellerId, e);
            throw new RuntimeException("获取绩效评分失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getProductCompetitiveness(Long productId) {
        try {
            log.info("获取商品竞争力分析 - productId: {}", productId);
            
            String sql = "SELECT * FROM v_product_competitiveness WHERE product_id = ?";
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, productId);
            
            if (!results.isEmpty()) {
                return results.get(0);
            } else {
                Map<String, Object> defaultData = new HashMap<>();
                defaultData.put("product_id", productId);
                defaultData.put("competitiveness_score", 0.0);
                return defaultData;
            }
        } catch (Exception e) {
            log.error("获取商品竞争力分析失败 - productId: {}", productId, e);
            throw new RuntimeException("获取竞争力分析失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getUserBehaviorAnalysis(Long userId) {
        try {
            log.info("获取用户行为分析 - userId: {}", userId);
            
            String sql = "SELECT * FROM v_user_behavior_analysis WHERE user_id = ?";
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, userId);
            
            if (!results.isEmpty()) {
                return results.get(0);
            } else {
                Map<String, Object> defaultData = new HashMap<>();
                defaultData.put("user_id", userId);
                defaultData.put("user_activity_level", "新用户");
                defaultData.put("customer_value_level", "潜在客户");
                return defaultData;
            }
        } catch (Exception e) {
            log.error("获取用户行为分析失败 - userId: {}", userId, e);
            throw new RuntimeException("获取用户行为分析失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getSalesTrends(Long sellerId, String period) {
        try {
            log.info("获取销售趋势数据 - sellerId: {}, period: {}", sellerId, period);
            
            // 根据period确定查询的天数
            int days = "week".equals(period) ? 7 : ("month".equals(period) ? 30 : 90);
            
            String sql = """
                SELECT 
                    DATE(o.created_at) as date,
                    COUNT(*) as orders,
                    COALESCE(SUM(o.actual_amount), 0) as revenue
                FROM `order` o 
                WHERE o.seller_id = ? 
                  AND o.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                  AND o.deleted = 0
                GROUP BY DATE(o.created_at)
                ORDER BY date ASC
                """;
            
            List<Map<String, Object>> trends = jdbcTemplate.queryForList(sql, sellerId, days);
            
            Map<String, Object> result = new HashMap<>();
            result.put("salesTrend", trends);
            result.put("period", period);
            result.put("days", days);
            
            return result;
        } catch (Exception e) {
            log.error("获取销售趋势数据失败 - sellerId: {}", sellerId, e);
            throw new RuntimeException("获取销售趋势失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getTopProducts(Long sellerId, Integer limit) {
        try {
            log.info("获取商品销售排行 - sellerId: {}, limit: {}", sellerId, limit);
            
            String sql = """
                SELECT 
                    p.id,
                    p.name,
                    p.image,
                    p.sales_count as sales,
                    p.sales_count * p.price as revenue,
                    COALESCE(p.rating, 0) as rating
                FROM product p
                WHERE p.seller_id = ? 
                  AND p.deleted = 0
                ORDER BY p.sales_count DESC
                LIMIT ?
                """;
            
            return jdbcTemplate.queryForList(sql, sellerId, limit);
        } catch (Exception e) {
            log.error("获取商品销售排行失败 - sellerId: {}", sellerId, e);
            throw new RuntimeException("获取商品排行失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getCustomerAnalysis(Long sellerId) {
        try {
            log.info("获取客户分析数据 - sellerId: {}", sellerId);
            
            // 获取客户地域分布
            String regionSql = """
                SELECT 
                    COALESCE(u.province, '未知') as region,
                    COUNT(DISTINCT o.user_id) as count
                FROM `order` o
                LEFT JOIN user u ON o.user_id = u.id
                WHERE o.seller_id = ? AND o.deleted = 0
                GROUP BY u.province
                ORDER BY count DESC
                LIMIT 10
                """;
            
            List<Map<String, Object>> regionDistribution = jdbcTemplate.queryForList(regionSql, sellerId);
            
            // 计算百分比
            int totalCustomers = regionDistribution.stream()
                    .mapToInt(r -> ((Number) r.get("count")).intValue())
                    .sum();
            
            regionDistribution.forEach(region -> {
                int count = ((Number) region.get("count")).intValue();
                double percentage = totalCustomers > 0 ? (count * 100.0 / totalCustomers) : 0;
                region.put("percentage", Math.round(percentage * 10) / 10.0);
            });
            
            Map<String, Object> result = new HashMap<>();
            result.put("regionDistribution", regionDistribution);
            result.put("totalCustomers", totalCustomers);
            
            return result;
        } catch (Exception e) {
            log.error("获取客户分析数据失败 - sellerId: {}", sellerId, e);
            throw new RuntimeException("获取客户分析失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getFinancialReport(Long sellerId, String startDate, String endDate) {
        try {
            log.info("获取财务报表数据 - sellerId: {}, startDate: {}, endDate: {}", sellerId, startDate, endDate);
            
            String sql = """
                SELECT 
                    COALESCE(SUM(actual_amount), 0) as total_revenue,
                    COUNT(*) as total_orders,
                    AVG(actual_amount) as avg_order_value
                FROM `order`
                WHERE seller_id = ? 
                  AND order_status = 3
                  AND deleted = 0
                  AND (? IS NULL OR created_at >= ?)
                  AND (? IS NULL OR created_at <= ?)
                """;
            
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, 
                sellerId, startDate, startDate, endDate, endDate);
            
            Map<String, Object> financial = new HashMap<>();
            if (!results.isEmpty()) {
                Map<String, Object> data = results.get(0);
                financial.put("totalRevenue", data.get("total_revenue"));
                financial.put("totalOrders", data.get("total_orders"));
                financial.put("avgOrderValue", data.get("avg_order_value"));
            } else {
                financial.put("totalRevenue", 0.0);
                financial.put("totalOrders", 0);
                financial.put("avgOrderValue", 0.0);
            }
            
            // 估算成本和利润（这里使用简单的估算，实际应该有更详细的成本计算）
            double revenue = ((Number) financial.get("totalRevenue")).doubleValue();
            double estimatedCost = revenue * 0.7; // 假设成本占收入的70%
            double grossProfit = revenue - estimatedCost;
            
            financial.put("totalCost", estimatedCost);
            financial.put("grossProfit", grossProfit);
            financial.put("grossProfitMargin", revenue > 0 ? (grossProfit / revenue * 100) : 0);
            
            return financial;
        } catch (Exception e) {
            log.error("获取财务报表数据失败 - sellerId: {}", sellerId, e);
            throw new RuntimeException("获取财务报表失败: " + e.getMessage());
        }
    }
}
