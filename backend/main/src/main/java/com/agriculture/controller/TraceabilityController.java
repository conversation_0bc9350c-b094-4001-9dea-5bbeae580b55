package com.agriculture.controller;

import com.agriculture.annotation.RequireRole;
import com.agriculture.annotation.RequirePermission;
import com.agriculture.dto.TraceabilityDetailVO;
import com.agriculture.dto.TraceabilityQueryDTO;
import com.agriculture.entity.*;
import com.agriculture.service.TraceabilityService;
import com.agriculture.service.TraceabilityQueryService;
import com.agriculture.utils.AuthUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 溯源管理控制器
 *
 * <AUTHOR> Team
 * @since 2024-12-01
 */
@Slf4j
@RestController
@RequestMapping("/api/traceability")
@Api(tags = "溯源管理")
@Validated
public class TraceabilityController {

    @Autowired
    private TraceabilityService traceabilityService;

    @Autowired
    private TraceabilityQueryService traceabilityQueryService;

    @Autowired
    private com.agriculture.utils.TraceabilityDataFixer traceabilityDataFixer;

    // ==================== 溯源记录相关 ====================

    // ==================== 销售者管理功能 ====================

    @GetMapping("/seller/records")
    @ApiOperation("销售者获取自己的溯源记录列表")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> getSellerRecords(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long productId,
            HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 从请求中获取当前用户ID（假设通过JWT或Session获取）
            Long sellerId = getCurrentUserId(request);
            if (sellerId == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                return ResponseEntity.status(401).body(response);
            }

            Page<TraceabilityRecord> pageParam = new Page<>(page, size);
            IPage<TraceabilityRecord> records = traceabilityService.getSellerRecords(
                pageParam, sellerId, keyword, status, productId);

            response.put("success", true);
            response.put("data", records);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取销售者溯源记录失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PostMapping("/seller/records")
    @ApiOperation("销售者创建溯源记录")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> createSellerRecord(
            @Valid @RequestBody TraceabilityRecord record,
            HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            Long sellerId = getCurrentUserId(request);
            if (sellerId == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                return ResponseEntity.status(401).body(response);
            }

            // 设置创建者为当前销售者
            record.setProducerId(sellerId);
            record.setStatus(0); // 默认为草稿状态

            TraceabilityRecord createdRecord = traceabilityService.createSellerRecord(record);
            if (createdRecord != null) {
                response.put("success", true);
                response.put("message", "创建成功");
                response.put("data", createdRecord);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "创建失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("销售者创建溯源记录失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PutMapping("/seller/records/{id}")
    @ApiOperation("销售者更新自己的溯源记录")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> updateSellerRecord(
            @PathVariable Long id,
            @Valid @RequestBody TraceabilityRecord record,
            HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            Long sellerId = getCurrentUserId(request);
            if (sellerId == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                return ResponseEntity.status(401).body(response);
            }

            // 验证记录所有权
            TraceabilityRecord existingRecord = traceabilityService.getTraceabilityRecordById(id);
            if (existingRecord == null) {
                response.put("success", false);
                response.put("message", "记录不存在");
                return ResponseEntity.notFound().build();
            }

            if (!sellerId.equals(existingRecord.getProducerId())) {
                response.put("success", false);
                response.put("message", "无权限操作此记录");
                return ResponseEntity.status(403).body(response);
            }

            record.setId(id);
            record.setProducerId(sellerId);
            boolean success = traceabilityService.updateSellerRecord(record);
            if (success) {
                response.put("success", true);
                response.put("message", "更新成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "更新失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("销售者更新溯源记录失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @DeleteMapping("/seller/records/{id}")
    @ApiOperation("销售者删除自己的溯源记录")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> deleteSellerRecord(
            @PathVariable Long id,
            HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            Long sellerId = getCurrentUserId(request);
            if (sellerId == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                return ResponseEntity.status(401).body(response);
            }

            // 验证记录所有权
            TraceabilityRecord existingRecord = traceabilityService.getTraceabilityRecordById(id);
            if (existingRecord == null) {
                response.put("success", false);
                response.put("message", "记录不存在");
                return ResponseEntity.notFound().build();
            }

            if (!sellerId.equals(existingRecord.getProducerId())) {
                response.put("success", false);
                response.put("message", "无权限操作此记录");
                return ResponseEntity.status(403).body(response);
            }

            boolean success = traceabilityService.deleteSellerRecord(id);
            if (success) {
                response.put("success", true);
                response.put("message", "删除成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "删除失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("销售者删除溯源记录失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PostMapping("/seller/records/batch-delete")
    @ApiOperation("销售者批量删除溯源记录")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> batchDeleteSellerRecords(
            @RequestBody List<Long> ids,
            HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            Long sellerId = getCurrentUserId(request);
            if (sellerId == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                return ResponseEntity.status(401).body(response);
            }

            int successCount = traceabilityService.batchDeleteSellerRecords(ids, sellerId);
            response.put("success", true);
            response.put("message", String.format("成功删除 %d 条记录", successCount));
            response.put("data", Map.of("successCount", successCount, "totalCount", ids.size()));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("销售者批量删除溯源记录失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PostMapping("/seller/records/batch-status")
    @ApiOperation("销售者批量更新记录状态")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> batchUpdateSellerRecordStatus(
            @RequestBody Map<String, Object> params,
            HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            Long sellerId = getCurrentUserId(request);
            if (sellerId == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                return ResponseEntity.status(401).body(response);
            }

            @SuppressWarnings("unchecked")
            List<Long> ids = (List<Long>) params.get("ids");
            String status = (String) params.get("status");

            int successCount = traceabilityService.batchUpdateSellerRecordStatus(ids, status, sellerId);
            response.put("success", true);
            response.put("message", String.format("成功更新 %d 条记录状态", successCount));
            response.put("data", Map.of("successCount", successCount, "totalCount", ids.size()));
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("销售者批量更新记录状态失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/seller/products")
    @ApiOperation("获取销售者产品列表")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> getSellerProducts(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            Long sellerId = getCurrentUserId(request);
            if (sellerId == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                return ResponseEntity.status(401).body(response);
            }

            List<Map<String, Object>> products = traceabilityService.getSellerProducts(sellerId);
            response.put("success", true);
            response.put("data", products);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取销售者产品列表失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 从请求中获取当前用户ID
     * 这里需要根据实际的认证机制实现
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        // TODO: 实现从JWT token或session中获取用户ID的逻辑
        // 这里暂时返回一个测试用户ID
        String userIdHeader = request.getHeader("X-User-Id");
        if (userIdHeader != null) {
            try {
                return Long.parseLong(userIdHeader);
            } catch (NumberFormatException e) {
                log.warn("Invalid user ID in header: {}", userIdHeader);
            }
        }
        return null;
    }

    @PostMapping("/records")
    @ApiOperation("创建溯源记录")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> createRecord(@RequestBody TraceabilityRecord record, HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 数据级权限控制：销售者只能创建自己的记录
            if (AuthUtils.isSeller(request)) {
                Long currentUserId = AuthUtils.getCurrentUserId(request);
                record.setProducerId(currentUserId);
                record.setCreatedBy(currentUserId);
            }

            boolean success = traceabilityService.createTraceabilityRecord(record);
            if (success) {
                response.put("success", true);
                response.put("message", "创建成功");
                response.put("data", record);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "创建失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("创建溯源记录失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PutMapping("/records/{id}")
    @ApiOperation("更新溯源记录")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> updateRecord(
            @PathVariable Long id,
            @RequestBody TraceabilityRecord record,
            HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 数据级权限控制：销售者只能更新自己的记录
            if (AuthUtils.isSeller(request)) {
                TraceabilityRecord existingRecord = traceabilityService.getTraceabilityRecordById(id);
                if (existingRecord == null) {
                    response.put("success", false);
                    response.put("message", "记录不存在");
                    return ResponseEntity.badRequest().body(response);
                }

                Long currentUserId = AuthUtils.getCurrentUserId(request);
                if (!currentUserId.equals(existingRecord.getProducerId())) {
                    response.put("success", false);
                    response.put("message", "无权限操作此记录");
                    return ResponseEntity.status(403).body(response);
                }

                // 已发布的记录不能修改关键信息
                if (existingRecord.getStatus() == 2) {
                    response.put("success", false);
                    response.put("message", "已发布的记录不能修改");
                    return ResponseEntity.badRequest().body(response);
                }
            }

            record.setId(id);
            boolean success = traceabilityService.updateTraceabilityRecord(record);
            if (success) {
                response.put("success", true);
                response.put("message", "更新成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "更新失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("更新溯源记录失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @DeleteMapping("/records/{id}")
    @ApiOperation("删除溯源记录")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> deleteRecord(@PathVariable Long id, HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 数据级权限控制：销售者只能删除自己的记录
            if (AuthUtils.isSeller(request)) {
                TraceabilityRecord existingRecord = traceabilityService.getTraceabilityRecordById(id);
                if (existingRecord == null) {
                    response.put("success", false);
                    response.put("message", "记录不存在");
                    return ResponseEntity.badRequest().body(response);
                }

                Long currentUserId = AuthUtils.getCurrentUserId(request);
                if (!currentUserId.equals(existingRecord.getProducerId())) {
                    response.put("success", false);
                    response.put("message", "无权限操作此记录");
                    return ResponseEntity.status(403).body(response);
                }

                // 已发布的记录不能删除
                if (existingRecord.getStatus() == 2) {
                    response.put("success", false);
                    response.put("message", "已发布的记录不能删除");
                    return ResponseEntity.badRequest().body(response);
                }
            }

            boolean success = traceabilityService.deleteTraceabilityRecord(id);
            if (success) {
                response.put("success", true);
                response.put("message", "删除成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "删除失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("删除溯源记录失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/records/{id}")
    @ApiOperation("获取溯源记录详情")
    public ResponseEntity<Map<String, Object>> getRecord(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            TraceabilityRecord record = traceabilityService.getTraceabilityRecordById(id);
            if (record != null) {
                response.put("success", true);
                response.put("data", record);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "记录不存在");
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("获取溯源记录失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/records")
    @ApiOperation("分页查询溯源记录")
    public ResponseEntity<Map<String, Object>> getRecordsPage(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String productName,
            @RequestParam(required = false) String producerName,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate) {
        Map<String, Object> response = new HashMap<>();
        try {
            Page<TraceabilityRecord> pageParam = new Page<>(page, size);
            IPage<TraceabilityRecord> result = traceabilityService.getTraceabilityRecordsPage(
                    pageParam, productName, producerName, status, startDate, endDate);
            
            response.put("success", true);
            response.put("data", result.getRecords());
            response.put("total", result.getTotal());
            response.put("pages", result.getPages());
            response.put("current", result.getCurrent());
            response.put("size", result.getSize());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("分页查询溯源记录失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PostMapping("/records/{id}/publish")
    @ApiOperation("发布溯源记录")
    @RequireRole({"admin"})
    public ResponseEntity<Map<String, Object>> publishRecord(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean success = traceabilityService.publishTraceabilityRecord(id);
            if (success) {
                response.put("success", true);
                response.put("message", "发布成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "发布失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("发布溯源记录失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PostMapping("/records/{id}/withdraw")
    @ApiOperation("撤回溯源记录")
    @RequireRole({"admin"})
    public ResponseEntity<Map<String, Object>> withdrawRecord(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean success = traceabilityService.withdrawTraceabilityRecord(id);
            if (success) {
                response.put("success", true);
                response.put("message", "撤回成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "撤回失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("撤回溯源记录失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    // ==================== 溯源事件相关 ====================

    @PostMapping("/events/batch")
    @ApiOperation("批量添加溯源事件")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> batchAddEvents(
            @RequestBody Map<String, Object> requestData,
            HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            Long traceRecordId = Long.valueOf(requestData.get("traceRecordId").toString());
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> eventsData = (List<Map<String, Object>>) requestData.get("events");

            // 数据级权限控制：销售者只能为自己的溯源记录添加事件
            if (AuthUtils.isSeller(request)) {
                TraceabilityRecord record = traceabilityService.getTraceabilityRecordById(traceRecordId);
                if (record == null) {
                    response.put("success", false);
                    response.put("message", "溯源记录不存在");
                    return ResponseEntity.badRequest().body(response);
                }

                Long currentUserId = AuthUtils.getCurrentUserId(request);
                if (!currentUserId.equals(record.getProducerId())) {
                    response.put("success", false);
                    response.put("message", "无权限操作此记录");
                    return ResponseEntity.status(403).body(response);
                }
            }

            // 转换并批量添加事件
            List<TraceabilityEvent> events = new ArrayList<>();
            for (Map<String, Object> eventData : eventsData) {
                TraceabilityEvent event = new TraceabilityEvent();
                event.setTraceRecordId(traceRecordId);
                event.setEventType((String) eventData.get("eventType"));
                event.setEventDate(LocalDateTime.parse((String) eventData.get("eventDate") + "T00:00:00"));
                event.setDescription((String) eventData.get("description"));
                event.setLocation((String) eventData.get("location"));
                event.setResponsiblePerson((String) eventData.get("responsiblePerson"));
                event.setEventSequence((Integer) eventData.get("eventSequence"));
                events.add(event);
            }

            boolean success = traceabilityService.batchAddTraceabilityEvents(events);
            if (success) {
                response.put("success", true);
                response.put("message", "批量添加成功");
                response.put("data", events.size());
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "批量添加失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("批量添加溯源事件失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PostMapping("/events")
    @ApiOperation("添加溯源事件")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> addEvent(@RequestBody TraceabilityEvent event, HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 数据级权限控制：销售者只能为自己的溯源记录添加事件
            if (AuthUtils.isSeller(request)) {
                TraceabilityRecord record = traceabilityService.getTraceabilityRecordById(event.getTraceRecordId());
                if (record == null) {
                    response.put("success", false);
                    response.put("message", "溯源记录不存在");
                    return ResponseEntity.badRequest().body(response);
                }

                Long currentUserId = AuthUtils.getCurrentUserId(request);
                if (!currentUserId.equals(record.getProducerId())) {
                    response.put("success", false);
                    response.put("message", "无权限操作此记录");
                    return ResponseEntity.status(403).body(response);
                }
            }

            boolean success = traceabilityService.addTraceabilityEvent(event);
            if (success) {
                response.put("success", true);
                response.put("message", "添加成功");
                response.put("data", event);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "添加失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("添加溯源事件失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PutMapping("/events/{id}")
    @ApiOperation("更新溯源事件")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> updateEvent(
            @PathVariable Long id,
            @RequestBody TraceabilityEvent event) {
        Map<String, Object> response = new HashMap<>();
        try {
            event.setId(id);
            boolean success = traceabilityService.updateTraceabilityEvent(event);
            if (success) {
                response.put("success", true);
                response.put("message", "更新成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "更新失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("更新溯源事件失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @DeleteMapping("/events/{id}")
    @ApiOperation("删除溯源事件")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> deleteEvent(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean success = traceabilityService.deleteTraceabilityEvent(id);
            if (success) {
                response.put("success", true);
                response.put("message", "删除成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "删除失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("删除溯源事件失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/records/{recordId}/events")
    @ApiOperation("获取溯源记录的事件列表")
    public ResponseEntity<Map<String, Object>> getEventsByRecordId(@PathVariable Long recordId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<TraceabilityEvent> events = traceabilityService.getTraceabilityEventsByRecordId(recordId);
            response.put("success", true);
            response.put("data", events);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取溯源事件列表失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/records/{recordId}/timeline")
    @ApiOperation("获取事件时间线")
    public ResponseEntity<Map<String, Object>> getEventTimeline(@PathVariable Long recordId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<Map<String, Object>> timeline = traceabilityService.getEventTimeline(recordId);
            response.put("success", true);
            response.put("data", timeline);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取事件时间线失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    // ==================== 溯源认证相关 ====================

    @PostMapping("/certificates")
    @ApiOperation("添加溯源认证")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> addCertificate(@RequestBody TraceabilityCertificate certificate) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean success = traceabilityService.addTraceabilityCertificate(certificate);
            if (success) {
                response.put("success", true);
                response.put("message", "添加成功");
                response.put("data", certificate);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "添加失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("添加溯源认证失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PutMapping("/certificates/{id}")
    @ApiOperation("更新溯源认证")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> updateCertificate(
            @PathVariable Long id,
            @RequestBody TraceabilityCertificate certificate) {
        Map<String, Object> response = new HashMap<>();
        try {
            certificate.setId(id);
            boolean success = traceabilityService.updateTraceabilityCertificate(certificate);
            if (success) {
                response.put("success", true);
                response.put("message", "更新成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "更新失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("更新溯源认证失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @DeleteMapping("/certificates/{id}")
    @ApiOperation("删除溯源认证")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> deleteCertificate(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean success = traceabilityService.deleteTraceabilityCertificate(id);
            if (success) {
                response.put("success", true);
                response.put("message", "删除成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "删除失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("删除溯源认证失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/records/{recordId}/certificates")
    @ApiOperation("获取溯源记录的认证列表")
    public ResponseEntity<Map<String, Object>> getCertificatesByRecordId(@PathVariable Long recordId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<TraceabilityCertificate> certificates = traceabilityService.getTraceabilityCertificatesByRecordId(recordId);
            response.put("success", true);
            response.put("data", certificates);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取溯源认证列表失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    // ==================== 溯源物流相关 ====================

    @PostMapping("/logistics")
    @ApiOperation("添加溯源物流信息")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> addLogistics(@RequestBody TraceabilityLogistics logistics) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean success = traceabilityService.addTraceabilityLogistics(logistics);
            if (success) {
                response.put("success", true);
                response.put("message", "添加成功");
                response.put("data", logistics);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "添加失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("添加溯源物流信息失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PutMapping("/logistics/{id}")
    @ApiOperation("更新溯源物流信息")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> updateLogistics(
            @PathVariable Long id,
            @RequestBody TraceabilityLogistics logistics) {
        Map<String, Object> response = new HashMap<>();
        try {
            logistics.setId(id);
            boolean success = traceabilityService.updateTraceabilityLogistics(logistics);
            if (success) {
                response.put("success", true);
                response.put("message", "更新成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "更新失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("更新溯源物流信息失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @DeleteMapping("/logistics/{id}")
    @ApiOperation("删除溯源物流信息")
    @RequireRole({"seller", "admin"})
    public ResponseEntity<Map<String, Object>> deleteLogistics(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        try {
            boolean success = traceabilityService.deleteTraceabilityLogistics(id);
            if (success) {
                response.put("success", true);
                response.put("message", "删除成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "删除失败");
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            log.error("删除溯源物流信息失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/records/{recordId}/logistics")
    @ApiOperation("获取溯源记录的物流信息列表")
    public ResponseEntity<Map<String, Object>> getLogisticsByRecordId(@PathVariable Long recordId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<TraceabilityLogistics> logistics = traceabilityService.getTraceabilityLogisticsByRecordId(recordId);
            response.put("success", true);
            response.put("data", logistics);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取溯源物流信息列表失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/records/{recordId}/logistics/track")
    @ApiOperation("获取物流轨迹")
    public ResponseEntity<Map<String, Object>> getLogisticsTrack(@PathVariable Long recordId) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<Map<String, Object>> track = traceabilityService.getLogisticsTrack(recordId);
            response.put("success", true);
            response.put("data", track);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取物流轨迹失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    // ==================== 公共查询接口 ====================

    @GetMapping("/public/stats")
    @ApiOperation("获取公开统计数据（普通用户）")
    public ResponseEntity<Map<String, Object>> getPublicStats() {
        Map<String, Object> response = new HashMap<>();
        try {
            // 获取公开的统计数据
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalProducts", 10000);
            stats.put("successRate", 99.9);
            stats.put("productsTrend", 12.5);
            stats.put("successRateTrend", 2.3);
            stats.put("totalQueries", 50000);
            stats.put("todayQueries", 1200);

            response.put("success", true);
            response.put("data", stats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取公开统计数据失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/seller/{sellerId}/stats")
    @ApiOperation("获取销售者统计数据")
    public ResponseEntity<Map<String, Object>> getSellerStats(@PathVariable Long sellerId) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 获取销售者的统计数据
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalRecords", 25);
            stats.put("publishedRecords", 20);
            stats.put("pendingRecords", 3);
            stats.put("totalQueries", 1500);
            stats.put("recordsTrend", 15.2);
            stats.put("publishRate", 80.0);
            stats.put("monthlyQueries", 450);

            response.put("success", true);
            response.put("data", stats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取销售者统计数据失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/admin/stats")
    @ApiOperation("获取管理员统计数据")
    @RequireRole({"admin"})
    public ResponseEntity<Map<String, Object>> getAdminStats() {
        Map<String, Object> response = new HashMap<>();
        try {
            // 获取管理员的统计数据
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalRecords", 500);
            stats.put("publishedRecords", 450);
            stats.put("draftRecords", 30);
            stats.put("pendingRecords", 20);
            stats.put("todayQueries", 2500);
            stats.put("activeSellers", 85);
            stats.put("newSellers", 12);
            stats.put("systemAlerts", 3);
            stats.put("dataIntegrity", 98.5);
            stats.put("queryTrend", 8.7);

            response.put("success", true);
            response.put("data", stats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取管理员统计数据失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/verify/{traceCode}")
    @ApiOperation("验证溯源码")
    public ResponseEntity<Map<String, Object>> verifyTraceCode(
            @ApiParam("溯源码") @PathVariable String traceCode) {
        try {
            Map<String, Object> result = traceabilityService.verifyTraceCode(traceCode);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("验证溯源码失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("valid", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/query/{traceCode}")
    @ApiOperation("查询完整溯源信息（基础版本）")
    public ResponseEntity<Map<String, Object>> queryTraceabilityInfo(
            @ApiParam("溯源码") @PathVariable String traceCode) {
        try {
            Map<String, Object> result = traceabilityService.getCompleteTraceabilityInfo(traceCode);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("查询溯源信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/stats")
    @ApiOperation("获取溯源统计信息")
    public ResponseEntity<Map<String, Object>> getTraceabilityStats() {
        try {
            Map<String, Object> stats = traceabilityService.getTraceabilityStats();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取溯源统计信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/producer/{producerId}/stats")
    @ApiOperation("获取生产者溯源统计")
    public ResponseEntity<Map<String, Object>> getProducerStats(@PathVariable Long producerId) {
        try {
            Map<String, Object> stats = traceabilityService.getProducerTraceabilityStats(producerId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取生产者溯源统计失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    // ==================== 溯源查询记录相关 ====================

    @PostMapping("/query/record")
    @ApiOperation("记录溯源查询")
    public ResponseEntity<Map<String, Object>> recordQuery(
            @RequestParam String traceCode,
            @RequestParam(required = false) Long traceRecordId,
            @RequestParam(required = false) String deviceInfo,
            @RequestParam(required = false) Long userId,
            HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 获取真实IP地址
            String realIp = getClientIpAddress(request);
            
            traceabilityService.recordQuery(
                traceCode, 
                traceRecordId,
                realIp, 
                deviceInfo, 
                null, // location参数，暂时设为null
                userId
            );
            
            response.put("success", true);
            response.put("message", "查询记录已保存");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("记录溯源查询失败", e);
            response.put("success", false);
            response.put("message", "记录查询失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/query/records")
    @ApiOperation("分页查询溯源查询记录")
    public ResponseEntity<Map<String, Object>> getQueryRecords(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String traceCode,
            @RequestParam(required = false) Long userId) {
        Map<String, Object> response = new HashMap<>();
        try {
            IPage<TraceabilityQuery> result = traceabilityService.getQueryRecords(page, size, traceCode, userId, null, null);
            
            response.put("success", true);
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("分页查询溯源查询记录失败", e);
            response.put("success", false);
            response.put("message", "获取查询记录失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/query/records/{traceCode}")
    @ApiOperation("根据溯源码获取查询记录")
    public ResponseEntity<Map<String, Object>> getQueryRecordsByTraceCode(@PathVariable String traceCode) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<TraceabilityQuery> records = traceabilityService.getQueryRecordsByTraceCode(traceCode);
            response.put("success", true);
            response.put("data", records);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("根据溯源码获取查询记录失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/query/statistics")
    @ApiOperation("获取查询统计信息")
    public ResponseEntity<Map<String, Object>> getQueryStatistics() {
        try {
            Map<String, Object> stats = traceabilityService.getQueryStatistics();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取查询统计信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/query/hot-codes")
    @ApiOperation("获取热门查询溯源码")
    public ResponseEntity<Map<String, Object>> getHotTraceCodes(
            @RequestParam(defaultValue = "10") Integer limit) {
        Map<String, Object> response = new HashMap<>();
        try {
            List<Map<String, Object>> hotCodes = traceabilityService.getHotQueryTraceCodes(limit);
            response.put("success", true);
            response.put("data", hotCodes);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取热门查询溯源码失败", e);
            response.put("success", false);
            response.put("message", "系统错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }



    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 获取客户端真实IP地址（别名方法）
     */
    private String getRealIpAddress(HttpServletRequest request) {
        return getClientIpAddress(request);
    }

    // ==================== 溯源查询相关 ====================

    @PostMapping("/query")
    @ApiOperation("查询溯源详情")
    public ResponseEntity<Map<String, Object>> queryTraceability(
            @Valid @RequestBody TraceabilityQueryDTO queryDTO,
            HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            // 补充请求信息
            enrichQueryDTO(queryDTO, request);

            TraceabilityDetailVO detail = traceabilityQueryService.queryTraceabilityDetail(queryDTO);

            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", detail);

            log.info("溯源查询成功返回 - traceCode: {}, dataSize: {}",
                queryDTO.getTraceCode(), detail != null ? "有数据" : "无数据");

            return ResponseEntity.ok(response);

        } catch (IllegalArgumentException e) {
            log.warn("溯源查询参数错误: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);

        } catch (Exception e) {
            log.error("溯源查询失败", e);
            response.put("success", false);
            response.put("message", "查询失败，请稍后重试");
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/detail/{traceCode}")
    @ApiOperation("查询溯源详情（增强版本，包含查询统计和用户行为记录）")
    public ResponseEntity<Map<String, Object>> queryTraceabilityByPath(
            @ApiParam("溯源码") @PathVariable @NotBlank String traceCode,
            HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            TraceabilityQueryDTO queryDTO = new TraceabilityQueryDTO();
            queryDTO.setTraceCode(traceCode);
            queryDTO.setSource("web");

            // 补充请求信息
            enrichQueryDTO(queryDTO, request);

            TraceabilityDetailVO detail = traceabilityQueryService.queryTraceabilityDetail(queryDTO);

            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", detail);
            return ResponseEntity.ok(response);

        } catch (IllegalArgumentException e) {
            log.warn("溯源查询参数错误: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);

        } catch (Exception e) {
            log.error("溯源查询失败", e);
            response.put("success", false);
            response.put("message", "查询失败，请稍后重试");
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/simple/{traceCode}")
    @ApiOperation("简单查询（仅基本信息）")
    public ResponseEntity<Map<String, Object>> simpleQuery(
            @ApiParam("溯源码") @PathVariable @NotBlank String traceCode) {
        Map<String, Object> response = new HashMap<>();
        try {
            TraceabilityQueryDTO queryDTO = new TraceabilityQueryDTO();
            queryDTO.setTraceCode(traceCode);
            queryDTO.setSource("simple");

            TraceabilityDetailVO detail = traceabilityQueryService.queryTraceabilityDetail(queryDTO);

            // 只返回基本信息
            Map<String, Object> basicInfo = new HashMap<>();
            basicInfo.put("traceCode", detail.getTraceCode());
            basicInfo.put("productName", detail.getProductName());
            basicInfo.put("farmName", detail.getFarmName());
            basicInfo.put("producerName", detail.getProducerName());
            basicInfo.put("qualityGrade", detail.getQualityGrade());
            basicInfo.put("harvestDate", detail.getHarvestDate());

            response.put("success", true);
            response.put("message", "查询成功");
            response.put("data", basicInfo);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("简单查询失败", e);
            response.put("success", false);
            response.put("message", "查询失败");
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PostMapping("/validate")
    @ApiOperation("验证溯源码是否有效")
    public ResponseEntity<Map<String, Object>> validateTraceCode(
            @RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        try {
            String traceCode = request.get("traceCode");
            if (traceCode == null || traceCode.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "溯源码不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            TraceabilityQueryDTO queryDTO = new TraceabilityQueryDTO();
            queryDTO.setTraceCode(traceCode.trim());
            queryDTO.setSource("validate");

            try {
                TraceabilityDetailVO detail = traceabilityQueryService.queryTraceabilityDetail(queryDTO);
                response.put("success", true);
                response.put("message", "溯源码有效");
                response.put("valid", true);
                response.put("productName", detail.getProductName());
            } catch (IllegalArgumentException e) {
                response.put("success", true);
                response.put("message", e.getMessage());
                response.put("valid", false);
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("验证溯源码失败", e);
            response.put("success", false);
            response.put("message", "验证失败");
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/test-query")
    @ApiOperation("测试溯源查询功能")
    public ResponseEntity<Map<String, Object>> testQuery() {
        Map<String, Object> response = new HashMap<>();
        try {
            // 使用第一个测试溯源码
            String testTraceCode = "SFAP25071410001001A1B2";

            TraceabilityQueryDTO queryDTO = new TraceabilityQueryDTO();
            queryDTO.setTraceCode(testTraceCode);
            queryDTO.setSource("test");
            queryDTO.setIpAddress("127.0.0.1");
            queryDTO.setDeviceInfo("Test Device");

            TraceabilityDetailVO detail = traceabilityQueryService.queryTraceabilityDetail(queryDTO);

            response.put("success", true);
            response.put("message", "测试查询成功");
            response.put("testTraceCode", testTraceCode);
            response.put("data", detail);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("测试查询失败", e);
            response.put("success", false);
            response.put("message", "测试失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PostMapping("/admin/fix-data-consistency")
    @ApiOperation("修复溯源数据一致性问题（管理员专用）")
    @RequireRole("admin")
    public ResponseEntity<Map<String, Object>> fixDataConsistency() {
        Map<String, Object> response = new HashMap<>();
        try {
            // 先检查问题
            com.agriculture.utils.TraceabilityDataFixer.ConsistencyReport report =
                traceabilityDataFixer.checkDataConsistency();

            // 执行修复
            com.agriculture.utils.TraceabilityDataFixer.FixResult result =
                traceabilityDataFixer.fixAllDataConsistency();

            response.put("success", true);
            response.put("message", "数据一致性修复完成");
            response.put("beforeFix", Map.of(
                "statusInconsistent", report.statusInconsistentCount,
                "qrUrlInconsistent", report.qrUrlInconsistentCount
            ));
            response.put("fixResult", Map.of(
                "statusFixed", result.statusFixed,
                "qrUrlFixed", result.qrUrlFixed,
                "traceabilityFlagFixed", result.traceabilityFlagFixed,
                "totalFixed", result.getTotalFixed()
            ));

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("修复数据一致性失败", e);
            response.put("success", false);
            response.put("message", "修复失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 补充查询DTO的请求信息
     */
    private void enrichQueryDTO(TraceabilityQueryDTO queryDTO, HttpServletRequest request) {
        if (queryDTO.getIpAddress() == null) {
            queryDTO.setIpAddress(getClientIpAddress(request));
        }

        if (queryDTO.getDeviceInfo() == null) {
            queryDTO.setDeviceInfo(request.getHeader("User-Agent"));
        }

        if (queryDTO.getSource() == null) {
            queryDTO.setSource("web");
        }
    }
}