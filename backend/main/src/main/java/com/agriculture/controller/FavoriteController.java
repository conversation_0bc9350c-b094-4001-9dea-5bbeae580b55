package com.agriculture.controller;

import com.agriculture.entity.Favorite;
import com.agriculture.service.FavoriteService;
import com.agriculture.service.UserBehaviorService;
import com.agriculture.common.Result;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import java.util.List;
import java.util.Map;

/**
 * 收藏控制器
 *
 * <AUTHOR> Team
 * @since 2024-12-01
 */
@Slf4j
@RestController
@RequestMapping("/api/mall/favorites")
@Api(tags = "收藏管理")
public class FavoriteController {

    @Autowired
    private FavoriteService favoriteService;
    
    @Autowired
    private UserBehaviorService userBehaviorService;

    @GetMapping
    @ApiOperation("分页查询用户收藏列表")
    public Result<IPage<Map<String, Object>>> getUserFavorites(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("用户ID") @RequestParam Long userId) {

        try {
            log.info("查询用户收藏列表，用户ID: {}, 页码: {}, 大小: {}", userId, page, size);
            IPage<Map<String, Object>> result = favoriteService.getUserFavorites(userId, page, size);
            log.info("查询用户收藏列表成功，共{}条数据", result.getRecords().size());
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询用户收藏列表失败，用户ID: {}", userId, e);
            return Result.error("查询用户收藏列表失败: " + e.getMessage());
        }
    }

    @PostMapping
    @ApiOperation("添加收藏")
    public Result<Boolean> addFavorite(
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("商品ID") @RequestParam Long productId,
            @ApiParam("收藏夹名称") @RequestParam(defaultValue = "默认收藏夹") String folderName,
            HttpServletRequest request) {

        try {
            log.info("添加收藏，用户ID: {}, 商品ID: {}, 收藏夹: {}", userId, productId, folderName);
            Boolean success = favoriteService.addFavoriteToFolder(userId, productId, folderName);

            // 记录用户收藏行为
            if (success) {
                String sessionId = request.getSession().getId();
                String ipAddress = getClientIpAddress(request);
                userBehaviorService.recordFavoriteBehavior(userId, productId, sessionId, ipAddress);
                log.info("添加收藏成功，用户ID: {}, 商品ID: {}", userId, productId);
            }

            return success ? Result.success("收藏成功", true) : Result.error("收藏失败");
        } catch (IllegalArgumentException e) {
            log.warn("添加收藏参数错误: {}", e.getMessage());
            return Result.error("参数错误: " + e.getMessage());
        } catch (IllegalStateException e) {
            log.warn("添加收藏状态错误: {}", e.getMessage());
            return Result.error("状态错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("添加收藏失败，用户ID: {}, 商品ID: {}", userId, productId, e);
            return Result.error("添加收藏失败: " + e.getMessage());
        }
    }

    @DeleteMapping
    @ApiOperation("取消收藏")
    public Result<Boolean> removeFavorite(
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("商品ID") @RequestParam Long productId) {

        try {
            log.info("取消收藏，用户ID: {}, 商品ID: {}", userId, productId);
            Boolean success = favoriteService.removeFavorite(userId, productId);
            if (success) {
                log.info("取消收藏成功，用户ID: {}, 商品ID: {}", userId, productId);
                return Result.success("取消收藏成功", true);
            } else {
                log.warn("取消收藏失败，未找到收藏记录，用户ID: {}, 商品ID: {}", userId, productId);
                return Result.error("未找到收藏记录");
            }
        } catch (Exception e) {
            log.error("取消收藏失败，用户ID: {}, 商品ID: {}", userId, productId, e);
            return Result.error("取消收藏失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/batch")
    @ApiOperation("批量取消收藏")
    public ResponseEntity<Void> batchRemoveFavorites(
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("商品ID列表") @RequestParam List<Long> productIds) {
        
        try {
            Boolean success = favoriteService.batchRemoveFavorites(userId, productIds);
            if (success) {
                return ResponseEntity.ok().build();
            } else {
                return ResponseEntity.badRequest().build();
            }
        } catch (Exception e) {
            log.error("批量取消收藏失败，用户ID: {}", userId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/check")
    @ApiOperation("检查用户是否收藏了商品")
    public ResponseEntity<Map<String, Object>> checkFavorite(
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("商品ID") @RequestParam Long productId) {

        log.info("检查收藏状态，用户ID: {}, 商品ID: {}", userId, productId);

        if (userId == null || productId == null) {
            log.warn("检查收藏状态失败：参数不能为空，用户ID: {}, 商品ID: {}", userId, productId);
            Map<String, Object> errorResult = Map.of(
                "success", false,
                "message", "用户ID和商品ID不能为空"
            );
            return ResponseEntity.badRequest().body(errorResult);
        }

        try {
            Boolean isFavorited = favoriteService.isFavorited(userId, productId);
            Integer favoriteCount = favoriteService.getProductFavoriteCount(productId);

            Map<String, Object> result = Map.of(
                "success", true,
                "data", Map.of(
                    "favorited", isFavorited,
                    "favoriteCount", favoriteCount,
                    "folderName", isFavorited ? "默认收藏夹" : ""
                )
            );

            log.info("检查收藏状态成功，用户ID: {}, 商品ID: {}, 收藏状态: {}", userId, productId, isFavorited);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查收藏状态失败，用户ID: {}, 商品ID: {}", userId, productId, e);
            Map<String, Object> errorResult = Map.of(
                "success", false,
                "message", "检查收藏状态失败：" + e.getMessage()
            );
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    @PostMapping("/check-batch")
    @ApiOperation("批量检查用户是否收藏了商品列表")
    public ResponseEntity<Map<String, Object>> batchCheckFavorites(
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("商品ID列表") @RequestParam List<Long> productIds) {
        
        try {
            Map<Long, Boolean> favoriteStatus = favoriteService.batchCheckFavorites(userId, productIds);
            Map<String, Object> result = Map.of("favoriteStatus", favoriteStatus);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量检查收藏状态失败，用户ID: {}", userId, e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/count/user")
    @ApiOperation("获取用户收藏数量")
    public ResponseEntity<Map<String, Object>> getUserFavoriteCount(
            @ApiParam("用户ID") @RequestParam Long userId) {
        
        try {
            Integer count = favoriteService.getUserFavoriteCount(userId);
            Map<String, Object> result = Map.of("count", count);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取用户收藏数量失败，用户ID: {}", userId, e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/count/product")
    @ApiOperation("获取商品收藏数量")
    public ResponseEntity<Map<String, Object>> getProductFavoriteCount(
            @ApiParam("商品ID") @RequestParam Long productId) {
        
        try {
            Integer count = favoriteService.getProductFavoriteCount(productId);
            Map<String, Object> result = Map.of("count", count);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取商品收藏数量失败，商品ID: {}", productId, e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/hot")
    @ApiOperation("获取热门收藏商品")
    public ResponseEntity<List<Map<String, Object>>> getHotFavoriteProducts(
            @ApiParam("数量限制") @RequestParam(defaultValue = "10") Integer limit) {
        
        try {
            List<Map<String, Object>> products = favoriteService.getHotFavoriteProducts(limit);
            return ResponseEntity.ok(products);
        } catch (Exception e) {
            log.error("获取热门收藏商品失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @DeleteMapping("/clear")
    @ApiOperation("清空用户收藏")
    public ResponseEntity<Void> clearUserFavorites(
            @ApiParam("用户ID") @RequestParam Long userId) {
        
        try {
            Boolean success = favoriteService.clearUserFavorites(userId);
            if (success) {
                return ResponseEntity.ok().build();
            } else {
                return ResponseEntity.badRequest().build();
            }
        } catch (Exception e) {
            log.error("清空用户收藏失败，用户ID: {}", userId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/product-ids")
    @ApiOperation("获取用户收藏的商品ID列表")
    public ResponseEntity<List<Long>> getUserFavoriteProductIds(
            @ApiParam("用户ID") @RequestParam Long userId) {
        
        try {
            List<Long> productIds = favoriteService.getUserFavoriteProductIds(userId);
            return ResponseEntity.ok(productIds);
        } catch (Exception e) {
            log.error("获取用户收藏商品ID列表失败，用户ID: {}", userId, e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/sync")
    @ApiOperation("同步收藏商品信息")
    public ResponseEntity<Void> syncFavoriteProducts(
            @ApiParam("用户ID") @RequestParam Long userId) {
        
        try {
            favoriteService.syncFavoriteProducts(userId);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("同步收藏商品信息失败，用户ID: {}", userId, e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/stats/trend")
    @ApiOperation("获取收藏趋势统计")
    public ResponseEntity<List<Map<String, Object>>> getFavoriteTrendStats(
            @ApiParam("统计天数") @RequestParam(defaultValue = "30") Integer days) {
        
        try {
            List<Map<String, Object>> stats = favoriteService.getFavoriteTrends(days);
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取收藏趋势统计失败，统计天数: {}", days, e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/stats/category")
    @ApiOperation("获取用户收藏分类统计")
    public ResponseEntity<List<Map<String, Object>>> getUserFavoriteCategoryStats(
            @ApiParam("用户ID") @RequestParam Long userId) {
        
        try {
            List<Map<String, Object>> stats = favoriteService.getUserFavoriteCategoryStats(userId);
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取用户收藏分类统计失败，用户ID: {}", userId, e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    @GetMapping("/by-folder")
    @ApiOperation("按收藏夹查询收藏列表")
    public ResponseEntity<IPage<Map<String, Object>>> getFavoritesByFolder(
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("收藏夹名称") @RequestParam String folderName,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size) {

        try {
            // TODO: 实现按收藏夹查询的逻辑
            IPage<Map<String, Object>> result = favoriteService.getUserFavorites(userId, page, size);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("按收藏夹查询收藏列表失败，用户ID: {}, 收藏夹: {}", userId, folderName, e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/move-to-folder")
    @ApiOperation("移动收藏到指定收藏夹")
    public ResponseEntity<Map<String, Object>> moveToFolder(
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("商品ID列表") @RequestParam List<Long> productIds,
            @ApiParam("目标收藏夹名称") @RequestParam String targetFolderName) {

        try {
            // TODO: 实现移动收藏到指定收藏夹的逻辑
            Map<String, Object> result = Map.of("success", true);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("移动收藏到收藏夹失败，用户ID: {}, 目标收藏夹: {}", userId, targetFolderName, e);
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/folders")
    @ApiOperation("获取用户收藏夹列表")
    public Result<List<Map<String, Object>>> getUserFolders(
            @ApiParam("用户ID") @RequestParam Long userId) {

        try {
            log.info("获取用户收藏夹列表，用户ID: {}", userId);
            List<Map<String, Object>> folders = favoriteService.getUserFolders(userId);
            log.info("获取用户收藏夹列表成功，共{}个收藏夹", folders.size());
            return Result.success(folders);
        } catch (Exception e) {
            log.error("获取用户收藏夹列表失败，用户ID: {}", userId, e);
            return Result.error("获取收藏夹列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/folders")
    @ApiOperation("创建收藏夹")
    public Result<Boolean> createFolder(
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("收藏夹名称") @RequestParam String folderName) {

        try {
            log.info("创建收藏夹，用户ID: {}, 收藏夹名称: {}", userId, folderName);
            Boolean success = favoriteService.createFolder(userId, folderName);
            if (success) {
                log.info("创建收藏夹成功，用户ID: {}, 收藏夹名称: {}", userId, folderName);
                return Result.success("创建收藏夹成功", true);
            } else {
                return Result.error("收藏夹已存在");
            }
        } catch (Exception e) {
            log.error("创建收藏夹失败，用户ID: {}, 收藏夹名称: {}", userId, folderName, e);
            return Result.error("创建收藏夹失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}