package com.agriculture.controller;

import com.agriculture.common.Result;
import com.agriculture.entity.UserFavoriteFolder;
import com.agriculture.service.UserFavoriteFolderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户收藏夹控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("/api/mall/favorite-folders")
@Api(tags = "用户收藏夹管理")
public class UserFavoriteFolderController {

    @Autowired
    private UserFavoriteFolderService userFavoriteFolderService;

    @GetMapping
    @ApiOperation("获取用户收藏夹列表")
    public Result<List<UserFavoriteFolder>> getUserFolders(
            @ApiParam("用户ID") @RequestParam Long userId) {
        
        try {
            List<UserFavoriteFolder> folders = userFavoriteFolderService.getUserFolders(userId);
            return Result.ok("获取收藏夹列表成功", folders);
        } catch (Exception e) {
            log.error("获取用户收藏夹列表失败，用户ID: {}", userId, e);
            return Result.fail("获取收藏夹列表失败: " + e.getMessage());
        }
    }

    @PostMapping
    @ApiOperation("创建收藏夹")
    public Result<UserFavoriteFolder> createFolder(
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("收藏夹名称") @RequestParam String folderName,
            @ApiParam("描述") @RequestParam(required = false) String description) {
        
        try {
            UserFavoriteFolder folder = userFavoriteFolderService.createFolder(userId, folderName, description);
            return Result.ok("创建收藏夹成功", folder);
        } catch (Exception e) {
            log.error("创建收藏夹失败，用户ID: {}, 收藏夹名称: {}", userId, folderName, e);
            return Result.fail("创建收藏夹失败: " + e.getMessage());
        }
    }

    @PutMapping("/{folderId}")
    @ApiOperation("更新收藏夹")
    public Result<UserFavoriteFolder> updateFolder(
            @ApiParam("收藏夹ID") @PathVariable Long folderId,
            @ApiParam("收藏夹名称") @RequestParam(required = false) String folderName,
            @ApiParam("描述") @RequestParam(required = false) String description) {
        
        try {
            UserFavoriteFolder folder = userFavoriteFolderService.updateFolder(folderId, folderName, description);
            return Result.ok("更新收藏夹成功", folder);
        } catch (Exception e) {
            log.error("更新收藏夹失败，收藏夹ID: {}", folderId, e);
            return Result.fail("更新收藏夹失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{folderId}")
    @ApiOperation("删除收藏夹")
    public Result<Void> deleteFolder(
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("收藏夹ID") @PathVariable Long folderId) {
        
        try {
            Boolean success = userFavoriteFolderService.deleteFolder(userId, folderId);
            if (success) {
                return Result.ok("删除收藏夹成功");
            } else {
                return Result.fail("删除收藏夹失败");
            }
        } catch (Exception e) {
            log.error("删除收藏夹失败，用户ID: {}, 收藏夹ID: {}", userId, folderId, e);
            return Result.fail("删除收藏夹失败: " + e.getMessage());
        }
    }

    @PutMapping("/{folderId}/rename")
    @ApiOperation("重命名收藏夹")
    public Result<Void> renameFolder(
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("收藏夹ID") @PathVariable Long folderId,
            @ApiParam("新名称") @RequestParam String newName) {
        
        try {
            Boolean success = userFavoriteFolderService.renameFolder(userId, folderId, newName);
            if (success) {
                return Result.ok("重命名收藏夹成功");
            } else {
                return Result.fail("重命名收藏夹失败");
            }
        } catch (Exception e) {
            log.error("重命名收藏夹失败，用户ID: {}, 收藏夹ID: {}, 新名称: {}", userId, folderId, newName, e);
            return Result.fail("重命名收藏夹失败: " + e.getMessage());
        }
    }

    @PutMapping("/reorder")
    @ApiOperation("调整收藏夹排序")
    public Result<Void> reorderFolders(
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("收藏夹ID列表") @RequestBody List<Long> folderIds) {
        
        try {
            Boolean success = userFavoriteFolderService.reorderFolders(userId, folderIds);
            if (success) {
                return Result.ok("调整收藏夹排序成功");
            } else {
                return Result.fail("调整收藏夹排序失败");
            }
        } catch (Exception e) {
            log.error("调整收藏夹排序失败，用户ID: {}", userId, e);
            return Result.fail("调整收藏夹排序失败: " + e.getMessage());
        }
    }

    @GetMapping("/default")
    @ApiOperation("获取或创建默认收藏夹")
    public Result<UserFavoriteFolder> getOrCreateDefaultFolder(
            @ApiParam("用户ID") @RequestParam Long userId) {
        
        try {
            UserFavoriteFolder folder = userFavoriteFolderService.getOrCreateDefaultFolder(userId);
            return Result.ok("获取默认收藏夹成功", folder);
        } catch (Exception e) {
            log.error("获取默认收藏夹失败，用户ID: {}", userId, e);
            return Result.fail("获取默认收藏夹失败: " + e.getMessage());
        }
    }

    @GetMapping("/check-name")
    @ApiOperation("检查收藏夹名称是否存在")
    public Result<Boolean> checkFolderNameExists(
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("收藏夹名称") @RequestParam String folderName,
            @ApiParam("排除的收藏夹ID") @RequestParam(required = false) Long excludeId) {
        
        try {
            Boolean exists = userFavoriteFolderService.checkFolderNameExists(userId, folderName, excludeId);
            return Result.ok("检查收藏夹名称成功", exists);
        } catch (Exception e) {
            log.error("检查收藏夹名称失败，用户ID: {}, 收藏夹名称: {}", userId, folderName, e);
            return Result.fail("检查收藏夹名称失败: " + e.getMessage());
        }
    }

    @GetMapping("/stats")
    @ApiOperation("获取收藏夹统计信息")
    public Result<Map<String, Object>> getFolderStats(
            @ApiParam("用户ID") @RequestParam Long userId) {
        
        try {
            Map<String, Object> stats = userFavoriteFolderService.getFolderStats(userId);
            return Result.ok("获取收藏夹统计信息成功", stats);
        } catch (Exception e) {
            log.error("获取收藏夹统计信息失败，用户ID: {}", userId, e);
            return Result.fail("获取收藏夹统计信息失败: " + e.getMessage());
        }
    }

    @PostMapping("/{folderId}/clear")
    @ApiOperation("清空收藏夹")
    public Result<Void> clearFolder(
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("收藏夹ID") @PathVariable Long folderId) {
        
        try {
            Boolean success = userFavoriteFolderService.clearFolder(userId, folderId);
            if (success) {
                return Result.ok("清空收藏夹成功");
            } else {
                return Result.fail("清空收藏夹失败");
            }
        } catch (Exception e) {
            log.error("清空收藏夹失败，用户ID: {}, 收藏夹ID: {}", userId, folderId, e);
            return Result.fail("清空收藏夹失败: " + e.getMessage());
        }
    }

    @PostMapping("/move-products")
    @ApiOperation("移动商品到其他收藏夹")
    public Result<Void> moveProductsToFolder(
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("商品ID列表") @RequestParam List<Long> productIds,
            @ApiParam("源收藏夹名称") @RequestParam String fromFolderName,
            @ApiParam("目标收藏夹名称") @RequestParam String toFolderName) {
        
        try {
            Boolean success = userFavoriteFolderService.moveProductsToFolder(userId, productIds, fromFolderName, toFolderName);
            if (success) {
                return Result.ok("移动商品成功");
            } else {
                return Result.fail("移动商品失败");
            }
        } catch (Exception e) {
            log.error("移动商品失败，用户ID: {}, 从 {} 到 {}", userId, fromFolderName, toFolderName, e);
            return Result.fail("移动商品失败: " + e.getMessage());
        }
    }
}
