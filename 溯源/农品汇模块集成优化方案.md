# SFAP平台农品汇模块与溯源模块集成优化方案

## 概述

本文档详细设计了农品汇模块与溯源模块的深度集成方案，包括产品详情页溯源信息展示、全链路用户体验优化、跨模块数据整合等关键功能。

## 🛍️ 产品详情页溯源集成优化

### 1. 当前ProductDetail.vue分析

首先分析现有的产品详情页实现：

```vue
<!-- 当前产品详情页结构分析 -->
<template>
  <div class="product-detail">
    <!-- 产品基本信息 -->
    <div class="product-info-section">
      <!-- 产品图片、名称、价格等 -->
    </div>
    
    <!-- 产品描述 -->
    <div class="product-description">
      <!-- 详细描述、规格参数等 -->
    </div>
    
    <!-- 需要集成溯源信息的位置 -->
    <div class="product-traceability" v-if="hasTraceabilityInfo">
      <!-- 溯源信息展示区域 -->
    </div>
  </div>
</template>
```

### 2. 优化后的产品详情页设计

```vue
<template>
  <div class="product-detail enhanced">
    <!-- 产品基本信息区域 -->
    <div class="product-main-info">
      <div class="product-images">
        <!-- 产品图片轮播 -->
        <el-carousel :interval="4000" type="card" height="400px">
          <el-carousel-item v-for="image in product.images" :key="image.id">
            <img :src="image.url" :alt="product.name" class="product-image">
          </el-carousel-item>
        </el-carousel>
        
        <!-- 溯源认证标识 -->
        <div v-if="traceabilityInfo" class="traceability-badge">
          <el-tooltip content="该产品支持全程溯源查询" placement="top">
            <div class="trace-badge">
              <i class="el-icon-medal"></i>
              <span>溯源认证</span>
            </div>
          </el-tooltip>
        </div>
      </div>
      
      <div class="product-details">
        <h1 class="product-title">{{ product.name }}</h1>
        
        <!-- 价格和溯源码展示 -->
        <div class="price-trace-section">
          <div class="price-info">
            <span class="current-price">¥{{ product.price }}</span>
            <span v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</span>
          </div>
          
          <!-- 溯源码展示卡片 -->
          <div v-if="traceabilityInfo" class="trace-code-card">
            <div class="trace-code-header">
              <i class="el-icon-view"></i>
              <span>溯源码</span>
            </div>
            <div class="trace-code-content">
              <span class="trace-code">{{ traceabilityInfo.traceCode }}</span>
              <el-button-group class="trace-actions">
                <el-button 
                  type="primary" 
                  size="mini" 
                  @click="viewFullTraceability"
                  icon="el-icon-search"
                >
                  查看溯源
                </el-button>
                <el-button 
                  size="mini" 
                  @click="copyTraceCode"
                  icon="el-icon-document-copy"
                >
                  复制
                </el-button>
                <el-button 
                  size="mini" 
                  @click="shareTraceability"
                  icon="el-icon-share"
                >
                  分享
                </el-button>
              </el-button-group>
            </div>
          </div>
        </div>
        
        <!-- 产品基本信息 -->
        <div class="product-basic-info">
          <div class="info-item">
            <span class="info-label">产地：</span>
            <span class="info-value">{{ product.origin }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">规格：</span>
            <span class="info-value">{{ product.specification }}</span>
          </div>
          <div class="info-item" v-if="traceabilityInfo">
            <span class="info-label">生产者：</span>
            <span class="info-value">{{ traceabilityInfo.producerName }}</span>
          </div>
          <div class="info-item" v-if="traceabilityInfo">
            <span class="info-label">生产日期：</span>
            <span class="info-value">{{ formatDate(traceabilityInfo.harvestDate) }}</span>
          </div>
        </div>
        
        <!-- 购买操作区域 -->
        <div class="purchase-actions">
          <el-input-number 
            v-model="quantity" 
            :min="1" 
            :max="product.stock"
            class="quantity-input"
          ></el-input-number>
          <el-button 
            type="primary" 
            size="large" 
            @click="addToCart"
            :disabled="product.stock === 0"
            class="add-cart-btn"
          >
            <i class="el-icon-shopping-cart-2"></i>
            加入购物车
          </el-button>
          <el-button 
            type="danger" 
            size="large" 
            @click="buyNow"
            :disabled="product.stock === 0"
            class="buy-now-btn"
          >
            立即购买
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 溯源信息详细展示区域 -->
    <div v-if="traceabilityInfo" class="traceability-detail-section">
      <el-card shadow="hover" class="traceability-card">
        <div slot="header" class="card-header">
          <div class="header-left">
            <i class="el-icon-connection"></i>
            <span>产品溯源信息</span>
          </div>
          <div class="header-right">
            <el-button 
              type="text" 
              @click="viewFullTraceability"
              class="view-full-btn"
            >
              查看完整溯源链 <i class="el-icon-arrow-right"></i>
            </el-button>
          </div>
        </div>
        
        <!-- 溯源环节预览 -->
        <div class="trace-steps-preview">
          <div class="steps-timeline">
            <div 
              v-for="(step, index) in traceabilityPreview.steps.slice(0, 4)" 
              :key="step.id"
              class="step-item"
              :class="{ 'active': index === 0 }"
            >
              <div class="step-icon">
                <i :class="getStepIcon(step.eventType)"></i>
              </div>
              <div class="step-content">
                <h4 class="step-title">{{ step.eventName }}</h4>
                <p class="step-desc">{{ step.description }}</p>
                <span class="step-time">{{ formatDateTime(step.eventTime) }}</span>
              </div>
            </div>
            
            <!-- 更多步骤提示 -->
            <div v-if="traceabilityPreview.steps.length > 4" class="more-steps">
              <div class="more-indicator">
                <i class="el-icon-more"></i>
                <span>还有 {{ traceabilityPreview.steps.length - 4 }} 个环节</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 认证信息预览 -->
        <div v-if="traceabilityPreview.certificates.length > 0" class="certificates-preview">
          <h4 class="preview-title">
            <i class="el-icon-medal"></i>
            认证信息
          </h4>
          <div class="certificates-grid">
            <div 
              v-for="cert in traceabilityPreview.certificates.slice(0, 3)" 
              :key="cert.id"
              class="certificate-item"
            >
              <div class="cert-icon">
                <i :class="getCertificateIcon(cert.certificateType)"></i>
              </div>
              <div class="cert-info">
                <span class="cert-name">{{ cert.certificateName }}</span>
                <span class="cert-org">{{ cert.issuingOrganization }}</span>
              </div>
              <div class="cert-status">
                <el-tag 
                  :type="cert.isValid ? 'success' : 'danger'" 
                  size="mini"
                >
                  {{ cert.isValid ? '有效' : '已过期' }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 快速操作按钮 -->
        <div class="trace-quick-actions">
          <el-button 
            type="primary" 
            @click="viewFullTraceability"
            icon="el-icon-view"
          >
            查看完整溯源链
          </el-button>
          <el-button 
            @click="downloadTraceReport"
            icon="el-icon-download"
          >
            下载溯源报告
          </el-button>
          <el-button 
            @click="shareTraceability"
            icon="el-icon-share"
          >
            分享溯源信息
          </el-button>
        </div>
      </el-card>
    </div>
    
    <!-- 无溯源信息时的提示 -->
    <div v-else class="no-traceability-section">
      <el-card shadow="hover" class="no-trace-card">
        <div class="no-trace-content">
          <div class="no-trace-icon">
            <i class="el-icon-warning-outline"></i>
          </div>
          <h4 class="no-trace-title">该产品暂无溯源信息</h4>
          <p class="no-trace-desc">生产者尚未为此产品创建溯源记录</p>
          
          <!-- 如果是销售者查看自己的产品 -->
          <div v-if="canCreateTrace" class="create-trace-prompt">
            <el-button 
              type="primary" 
              @click="createTraceabilityRecord"
              icon="el-icon-plus"
            >
              为此产品创建溯源记录
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 产品详细描述 -->
    <div class="product-description-section">
      <el-card shadow="hover">
        <div slot="header">
          <span>产品详情</span>
        </div>
        <div class="description-content" v-html="product.description"></div>
      </el-card>
    </div>
    
    <!-- 相关推荐 -->
    <div class="related-products-section">
      <el-card shadow="hover">
        <div slot="header">
          <span>相关推荐</span>
        </div>
        <div class="related-products-grid">
          <ProductCard 
            v-for="relatedProduct in relatedProducts" 
            :key="relatedProduct.id"
            :product="relatedProduct"
            :show-traceability="true"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getProductDetail, getRelatedProducts } from '@/api/product'
import { getTraceabilityByProductId, shareTraceability, downloadTraceReport } from '@/api/traceability'
import ProductCard from '@/components/ProductCard.vue'

export default {
  name: 'ProductDetail',
  components: {
    ProductCard
  },
  data() {
    return {
      product: {},
      traceabilityInfo: null,
      traceabilityPreview: {
        steps: [],
        certificates: []
      },
      relatedProducts: [],
      quantity: 1,
      loading: false
    }
  },
  computed: {
    canCreateTrace() {
      // 判断当前用户是否为该产品的销售者
      const currentUser = this.$store.state.user.userInfo
      return currentUser && 
             currentUser.role === 'seller' && 
             this.product.sellerId === currentUser.id
    }
  },
  async created() {
    await this.loadProductDetail()
    await this.loadTraceabilityInfo()
    await this.loadRelatedProducts()
  },
  methods: {
    async loadProductDetail() {
      try {
        const productId = this.$route.params.id
        const response = await getProductDetail(productId)
        
        if (response.data.success) {
          this.product = response.data.data
        }
      } catch (error) {
        this.$message.error('加载产品信息失败')
      }
    },
    
    async loadTraceabilityInfo() {
      try {
        const response = await getTraceabilityByProductId(this.product.id)
        
        if (response.data.success && response.data.data) {
          this.traceabilityInfo = response.data.data
          
          // 加载溯源预览信息
          this.traceabilityPreview = {
            steps: response.data.data.timeline || [],
            certificates: response.data.data.certificates || []
          }
        }
      } catch (error) {
        // 没有溯源信息不算错误，只是不显示
        console.log('该产品无溯源信息')
      }
    },
    
    async loadRelatedProducts() {
      try {
        const response = await getRelatedProducts(this.product.id, this.product.categoryId)
        
        if (response.data.success) {
          this.relatedProducts = response.data.data
        }
      } catch (error) {
        console.error('加载相关产品失败:', error)
      }
    },
    
    viewFullTraceability() {
      // 跳转到溯源中心，直接显示该产品的溯源信息
      this.$router.push({
        path: '/traceability',
        query: {
          code: this.traceabilityInfo.traceCode,
          from: 'product'
        }
      })
    },
    
    async copyTraceCode() {
      try {
        await navigator.clipboard.writeText(this.traceabilityInfo.traceCode)
        this.$message.success('溯源码已复制到剪贴板')
      } catch (error) {
        this.$message.error('复制失败，请手动复制')
      }
    },
    
    async shareTraceability() {
      try {
        const response = await shareTraceability({
          productId: this.product.id,
          traceCode: this.traceabilityInfo.traceCode
        })
        
        if (response.data.success) {
          const shareUrl = response.data.data.shareUrl
          
          // 显示分享对话框
          this.$alert(
            `分享链接：${shareUrl}`,
            '分享溯源信息',
            {
              confirmButtonText: '复制链接',
              callback: async () => {
                await navigator.clipboard.writeText(shareUrl)
                this.$message.success('分享链接已复制')
              }
            }
          )
        }
      } catch (error) {
        this.$message.error('生成分享链接失败')
      }
    },
    
    async downloadTraceReport() {
      try {
        const response = await downloadTraceReport(this.product.id)
        
        // 创建下载链接
        const blob = new Blob([response.data], { type: 'application/pdf' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${this.product.name}_溯源报告.pdf`
        link.click()
        
        window.URL.revokeObjectURL(url)
        this.$message.success('溯源报告下载成功')
      } catch (error) {
        this.$message.error('下载溯源报告失败')
      }
    },
    
    createTraceabilityRecord() {
      // 跳转到创建溯源记录页面
      this.$router.push({
        path: '/seller/traceability/create',
        query: {
          productId: this.product.id
        }
      })
    },
    
    getStepIcon(eventType) {
      const iconMap = {
        'PLANTING': 'el-icon-seedling',
        'GROWING': 'el-icon-sunny',
        'HARVESTING': 'el-icon-scissors',
        'PROCESSING': 'el-icon-setting',
        'PACKAGING': 'el-icon-box',
        'SHIPPING': 'el-icon-truck'
      }
      return iconMap[eventType] || 'el-icon-circle-check'
    },
    
    getCertificateIcon(certType) {
      const iconMap = {
        'ORGANIC': 'el-icon-medal-1',
        'QUALITY': 'el-icon-medal',
        'SAFETY': 'el-icon-shield'
      }
      return iconMap[certType] || 'el-icon-document'
    },
    
    formatDate(date) {
      return new Date(date).toLocaleDateString('zh-CN')
    },
    
    formatDateTime(datetime) {
      return new Date(datetime).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.product-detail.enhanced {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;

  .product-main-info {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;

    .product-images {
      flex: 1;
      position: relative;

      .traceability-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 10;

        .trace-badge {
          background: linear-gradient(135deg, #67C23A, #85CE61);
          color: white;
          padding: 8px 12px;
          border-radius: 20px;
          font-size: 12px;
          display: flex;
          align-items: center;
          gap: 4px;
          box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
        }
      }
    }

    .product-details {
      flex: 1;

      .product-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
        color: #303133;
      }

      .price-trace-section {
        margin-bottom: 20px;

        .price-info {
          margin-bottom: 15px;

          .current-price {
            font-size: 28px;
            font-weight: bold;
            color: #E6A23C;
            margin-right: 10px;
          }

          .original-price {
            font-size: 16px;
            color: #909399;
            text-decoration: line-through;
          }
        }

        .trace-code-card {
          background: linear-gradient(135deg, #f8f9fa, #e9ecef);
          border: 1px solid #dee2e6;
          border-radius: 8px;
          padding: 15px;

          .trace-code-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
            font-weight: bold;
            color: #495057;
          }

          .trace-code-content {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .trace-code {
              font-family: 'Courier New', monospace;
              font-size: 16px;
              font-weight: bold;
              color: #007bff;
              background: white;
              padding: 8px 12px;
              border-radius: 4px;
              border: 1px solid #ced4da;
            }
          }
        }
      }
    }
  }

  .traceability-detail-section {
    margin-bottom: 30px;

    .trace-steps-preview {
      margin-bottom: 20px;

      .steps-timeline {
        display: flex;
        flex-direction: column;
        gap: 15px;

        .step-item {
          display: flex;
          align-items: flex-start;
          gap: 15px;
          padding: 15px;
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover {
            background-color: #f8f9fa;
          }

          &.active {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            border-left: 4px solid #2196f3;
          }

          .step-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            flex-shrink: 0;
          }

          .step-content {
            flex: 1;

            .step-title {
              font-size: 16px;
              font-weight: bold;
              margin-bottom: 5px;
              color: #303133;
            }

            .step-desc {
              color: #606266;
              margin-bottom: 5px;
              line-height: 1.4;
            }

            .step-time {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }
    }

    .certificates-preview {
      margin-bottom: 20px;

      .preview-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 15px;
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }

      .certificates-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;

        .certificate-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px;
          border: 1px solid #ebeef5;
          border-radius: 6px;
          background: white;

          .cert-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #f0f9ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #0ea5e9;
          }

          .cert-info {
            flex: 1;

            .cert-name {
              display: block;
              font-weight: bold;
              color: #303133;
              margin-bottom: 2px;
            }

            .cert-org {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }
    }
  }

  .no-traceability-section {
    margin-bottom: 30px;

    .no-trace-content {
      text-align: center;
      padding: 40px 20px;

      .no-trace-icon {
        font-size: 48px;
        color: #e6a23c;
        margin-bottom: 15px;
      }

      .no-trace-title {
        font-size: 18px;
        color: #303133;
        margin-bottom: 10px;
      }

      .no-trace-desc {
        color: #909399;
        margin-bottom: 20px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .product-detail.enhanced {
    padding: 10px;

    .product-main-info {
      flex-direction: column;
      gap: 20px;
    }

    .certificates-grid {
      grid-template-columns: 1fr !important;
    }

    .trace-code-content {
      flex-direction: column;
      gap: 10px;
      align-items: stretch;
    }
  }
}
</style>
```

## 🔗 全链路溯源信息展示优化

### 1. 产品列表页溯源标识

```vue
<!-- 优化后的ProductCard组件 -->
<template>
  <div class="product-card enhanced" :class="{ 'has-traceability': hasTraceability }">
    <div class="product-image-container">
      <img :src="product.imageUrl" :alt="product.name" class="product-image">

      <!-- 溯源认证标识 -->
      <div v-if="hasTraceability" class="traceability-indicator">
        <el-tooltip content="支持全程溯源" placement="top">
          <div class="trace-indicator">
            <i class="el-icon-medal"></i>
          </div>
        </el-tooltip>
      </div>

      <!-- 认证等级标识 -->
      <div v-if="traceabilityLevel" class="trace-level-badge">
        <span class="level-text">{{ traceabilityLevel }}</span>
      </div>
    </div>

    <div class="product-info">
      <h3 class="product-name">{{ product.name }}</h3>
      <p class="product-origin">{{ product.origin }}</p>

      <!-- 溯源信息预览 -->
      <div v-if="hasTraceability && showTraceabilityPreview" class="trace-preview">
        <div class="trace-preview-item">
          <i class="el-icon-user"></i>
          <span>{{ traceabilityPreview.producerName }}</span>
        </div>
        <div class="trace-preview-item">
          <i class="el-icon-date"></i>
          <span>{{ formatDate(traceabilityPreview.harvestDate) }}</span>
        </div>
        <div class="trace-preview-item">
          <i class="el-icon-medal"></i>
          <span>{{ traceabilityPreview.certificateCount }}项认证</span>
        </div>
      </div>

      <div class="product-price">
        <span class="current-price">¥{{ product.price }}</span>
        <span v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</span>
      </div>

      <!-- 操作按钮 -->
      <div class="product-actions">
        <el-button
          type="primary"
          size="small"
          @click="viewProduct"
          class="view-btn"
        >
          查看详情
        </el-button>

        <el-button
          v-if="hasTraceability"
          type="success"
          size="small"
          @click="quickViewTrace"
          class="trace-btn"
        >
          <i class="el-icon-view"></i>
          溯源
        </el-button>
      </div>
    </div>

    <!-- 快速溯源查看弹窗 -->
    <el-dialog
      title="快速溯源查看"
      :visible.sync="showQuickTrace"
      width="600px"
      :before-close="closeQuickTrace"
    >
      <div v-if="quickTraceData" class="quick-trace-content">
        <!-- 溯源码 -->
        <div class="trace-code-section">
          <label>溯源码：</label>
          <span class="trace-code">{{ quickTraceData.traceCode }}</span>
          <el-button type="text" @click="copyTraceCode">复制</el-button>
        </div>

        <!-- 关键环节 -->
        <div class="key-steps-section">
          <h4>关键环节</h4>
          <div class="steps-list">
            <div
              v-for="step in quickTraceData.keySteps"
              :key="step.id"
              class="step-item"
            >
              <div class="step-icon">
                <i :class="getStepIcon(step.eventType)"></i>
              </div>
              <div class="step-info">
                <span class="step-name">{{ step.eventName }}</span>
                <span class="step-time">{{ formatDateTime(step.eventTime) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 认证信息 -->
        <div v-if="quickTraceData.certificates.length > 0" class="certificates-section">
          <h4>认证信息</h4>
          <div class="certificates-list">
            <el-tag
              v-for="cert in quickTraceData.certificates"
              :key="cert.id"
              :type="cert.isValid ? 'success' : 'danger'"
              size="small"
              class="cert-tag"
            >
              {{ cert.certificateName }}
            </el-tag>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeQuickTrace">关闭</el-button>
        <el-button type="primary" @click="viewFullTrace">查看完整溯源</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getProductTraceabilityPreview, getQuickTraceData } from '@/api/traceability'

export default {
  name: 'ProductCard',
  props: {
    product: {
      type: Object,
      required: true
    },
    showTraceabilityPreview: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      hasTraceability: false,
      traceabilityLevel: null,
      traceabilityPreview: {},
      showQuickTrace: false,
      quickTraceData: null
    }
  },
  async created() {
    await this.checkTraceability()
  },
  methods: {
    async checkTraceability() {
      try {
        const response = await getProductTraceabilityPreview(this.product.id)

        if (response.data.success && response.data.data) {
          this.hasTraceability = true
          this.traceabilityPreview = response.data.data
          this.traceabilityLevel = this.calculateTraceabilityLevel(response.data.data)
        }
      } catch (error) {
        // 没有溯源信息不算错误
        this.hasTraceability = false
      }
    },

    calculateTraceabilityLevel(traceData) {
      // 根据溯源数据完整性计算等级
      let score = 0

      if (traceData.steps && traceData.steps.length >= 5) score += 30
      if (traceData.certificates && traceData.certificates.length >= 2) score += 30
      if (traceData.producerVerified) score += 20
      if (traceData.hasQualityReport) score += 20

      if (score >= 80) return '优质溯源'
      if (score >= 60) return '标准溯源'
      if (score >= 40) return '基础溯源'
      return '简单溯源'
    },

    async quickViewTrace() {
      try {
        this.showQuickTrace = true

        const response = await getQuickTraceData(this.product.id)
        if (response.data.success) {
          this.quickTraceData = response.data.data
        }
      } catch (error) {
        this.$message.error('加载溯源信息失败')
      }
    },

    closeQuickTrace() {
      this.showQuickTrace = false
      this.quickTraceData = null
    },

    viewFullTrace() {
      this.$router.push({
        path: '/traceability',
        query: {
          code: this.quickTraceData.traceCode,
          from: 'product-list'
        }
      })
      this.closeQuickTrace()
    },

    viewProduct() {
      this.$router.push(`/product/${this.product.id}`)
    }
  }
}
</script>
```

## 🔄 跨模块数据整合优化

### 1. 数据库关联优化

#### 1.1 Product表与TraceabilityRecord表关联优化

```sql
-- 优化Product表，添加溯源相关字段
ALTER TABLE product ADD COLUMN has_traceability TINYINT(1) DEFAULT 0 COMMENT '是否有溯源信息';
ALTER TABLE product ADD COLUMN traceability_level VARCHAR(20) DEFAULT NULL COMMENT '溯源等级';
ALTER TABLE product ADD COLUMN trace_code VARCHAR(50) DEFAULT NULL COMMENT '溯源码';
ALTER TABLE product ADD COLUMN last_trace_update DATETIME DEFAULT NULL COMMENT '溯源信息最后更新时间';

-- 创建索引优化查询性能
CREATE INDEX idx_product_has_traceability ON product(has_traceability);
CREATE INDEX idx_product_trace_level ON product(traceability_level);
CREATE INDEX idx_product_trace_code ON product(trace_code);

-- 创建视图简化查询
CREATE VIEW v_product_with_traceability AS
SELECT
    p.*,
    tr.trace_code,
    tr.status as trace_status,
    tr.producer_name,
    tr.farm_name,
    tr.harvest_date,
    tr.quality_grade,
    COUNT(te.id) as event_count,
    COUNT(tc.id) as certificate_count,
    COUNT(tq.id) as query_count
FROM product p
LEFT JOIN traceability_record tr ON p.id = tr.product_id AND tr.status = 4
LEFT JOIN traceability_event te ON tr.id = te.trace_record_id
LEFT JOIN traceability_certificate tc ON tr.id = tc.trace_record_id
LEFT JOIN traceability_query tq ON tr.trace_code = tq.trace_code
GROUP BY p.id;
```

#### 1.2 数据同步机制

```java
// 溯源数据同步服务
@Service
public class TraceabilityDataSyncService {

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private TraceabilityRecordMapper traceabilityRecordMapper;

    /**
     * 当溯源记录状态变更时，同步更新产品表
     */
    @EventListener
    @Async
    public void handleTraceabilityStatusChange(TraceabilityStatusChangeEvent event) {
        try {
            TraceabilityRecord record = traceabilityRecordMapper.selectById(event.getRecordId());
            if (record == null) return;

            Product product = productMapper.selectById(record.getProductId());
            if (product == null) return;

            // 更新产品的溯源相关字段
            if (record.getStatus() == 4) { // 已发布
                product.setHasTraceability(true);
                product.setTraceCode(record.getTraceCode());
                product.setTraceabilityLevel(calculateTraceabilityLevel(record.getId()));
                product.setLastTraceUpdate(LocalDateTime.now());
            } else {
                product.setHasTraceability(false);
                product.setTraceCode(null);
                product.setTraceabilityLevel(null);
                product.setLastTraceUpdate(null);
            }

            productMapper.updateById(product);

            // 更新搜索索引
            updateProductSearchIndex(product);

        } catch (Exception e) {
            log.error("同步溯源数据到产品表失败", e);
        }
    }

    /**
     * 计算溯源等级
     */
    private String calculateTraceabilityLevel(Long recordId) {
        // 获取溯源记录的完整性评分
        int score = 0;

        // 事件完整性 (40分)
        List<TraceabilityEvent> events = traceabilityEventMapper.findByTraceRecordId(recordId);
        if (events.size() >= 6) score += 40;
        else if (events.size() >= 4) score += 30;
        else if (events.size() >= 2) score += 20;
        else score += 10;

        // 认证完整性 (30分)
        List<TraceabilityCertificate> certificates = traceabilityCertificateMapper.findByTraceRecordId(recordId);
        long validCerts = certificates.stream().filter(cert ->
            cert.getExpiryDate() == null || cert.getExpiryDate().isAfter(LocalDateTime.now())
        ).count();

        if (validCerts >= 3) score += 30;
        else if (validCerts >= 2) score += 20;
        else if (validCerts >= 1) score += 10;

        // 物流信息完整性 (20分)
        List<TraceabilityLogistics> logistics = traceabilityLogisticsMapper.findByTraceRecordId(recordId);
        if (!logistics.isEmpty()) score += 20;

        // 查询活跃度 (10分)
        List<TraceabilityQuery> queries = traceabilityQueryMapper.findByTraceRecordId(recordId);
        if (queries.size() >= 100) score += 10;
        else if (queries.size() >= 50) score += 8;
        else if (queries.size() >= 10) score += 5;

        // 根据评分确定等级
        if (score >= 90) return "钻石溯源";
        if (score >= 80) return "黄金溯源";
        if (score >= 70) return "白银溯源";
        if (score >= 60) return "青铜溯源";
        return "基础溯源";
    }

    /**
     * 批量同步所有产品的溯源状态
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void batchSyncTraceabilityStatus() {
        log.info("开始批量同步产品溯源状态");

        try {
            // 分页处理，避免内存溢出
            int pageSize = 100;
            int currentPage = 1;

            while (true) {
                Page<Product> page = new Page<>(currentPage, pageSize);
                IPage<Product> products = productMapper.selectPage(page, null);

                if (products.getRecords().isEmpty()) {
                    break;
                }

                for (Product product : products.getRecords()) {
                    syncProductTraceabilityStatus(product.getId());
                }

                currentPage++;

                // 避免过于频繁的数据库操作
                Thread.sleep(1000);
            }

            log.info("批量同步产品溯源状态完成");
        } catch (Exception e) {
            log.error("批量同步产品溯源状态失败", e);
        }
    }

    private void syncProductTraceabilityStatus(Long productId) {
        try {
            // 查找该产品的已发布溯源记录
            QueryWrapper<TraceabilityRecord> wrapper = new QueryWrapper<>();
            wrapper.eq("product_id", productId)
                   .eq("status", 4) // 已发布状态
                   .orderByDesc("created_at")
                   .last("LIMIT 1");

            TraceabilityRecord record = traceabilityRecordMapper.selectOne(wrapper);
            Product product = productMapper.selectById(productId);

            if (record != null) {
                // 有溯源记录
                product.setHasTraceability(true);
                product.setTraceCode(record.getTraceCode());
                product.setTraceabilityLevel(calculateTraceabilityLevel(record.getId()));
                product.setLastTraceUpdate(record.getUpdatedAt());
            } else {
                // 无溯源记录
                product.setHasTraceability(false);
                product.setTraceCode(null);
                product.setTraceabilityLevel(null);
                product.setLastTraceUpdate(null);
            }

            productMapper.updateById(product);
        } catch (Exception e) {
            log.error("同步产品{}溯源状态失败", productId, e);
        }
    }
}
```

### 2. 产品搜索优化

#### 2.1 支持溯源筛选的搜索接口

```java
// 产品搜索服务优化
@Service
public class ProductSearchService {

    public IPage<Product> searchProducts(ProductSearchRequest request) {
        Page<Product> page = new Page<>(request.getPage(), request.getSize());
        QueryWrapper<Product> wrapper = new QueryWrapper<>();

        // 基础搜索条件
        if (StringUtils.hasText(request.getKeyword())) {
            wrapper.and(w -> w.like("name", request.getKeyword())
                           .or().like("description", request.getKeyword())
                           .or().like("origin", request.getKeyword()));
        }

        // 分类筛选
        if (request.getCategoryId() != null) {
            wrapper.eq("category_id", request.getCategoryId());
        }

        // 价格区间筛选
        if (request.getMinPrice() != null) {
            wrapper.ge("price", request.getMinPrice());
        }
        if (request.getMaxPrice() != null) {
            wrapper.le("price", request.getMaxPrice());
        }

        // 溯源相关筛选
        if (request.getHasTraceability() != null) {
            wrapper.eq("has_traceability", request.getHasTraceability());
        }

        if (StringUtils.hasText(request.getTraceabilityLevel())) {
            wrapper.eq("traceability_level", request.getTraceabilityLevel());
        }

        // 溯源认证筛选
        if (request.getCertificationTypes() != null && !request.getCertificationTypes().isEmpty()) {
            // 子查询：查找具有指定认证类型的产品
            wrapper.inSql("id",
                "SELECT DISTINCT tr.product_id FROM traceability_record tr " +
                "JOIN traceability_certificate tc ON tr.id = tc.trace_record_id " +
                "WHERE tr.status = 4 AND tc.certificate_type IN (" +
                request.getCertificationTypes().stream()
                    .map(type -> "'" + type + "'")
                    .collect(Collectors.joining(",")) + ")"
            );
        }

        // 生产者筛选
        if (StringUtils.hasText(request.getProducerName())) {
            wrapper.inSql("id",
                "SELECT product_id FROM traceability_record " +
                "WHERE status = 4 AND producer_name LIKE '%" + request.getProducerName() + "%'"
            );
        }

        // 排序
        switch (request.getSortBy()) {
            case "price_asc":
                wrapper.orderByAsc("price");
                break;
            case "price_desc":
                wrapper.orderByDesc("price");
                break;
            case "traceability_level":
                wrapper.orderByDesc("has_traceability", "traceability_level");
                break;
            case "query_count":
                // 按溯源查询次数排序
                wrapper.inSql("1=1",
                    "ORDER BY (SELECT COUNT(*) FROM traceability_query tq " +
                    "JOIN traceability_record tr ON tq.trace_code = tr.trace_code " +
                    "WHERE tr.product_id = product.id) DESC"
                );
                break;
            default:
                wrapper.orderByDesc("created_at");
        }

        return productMapper.selectPage(page, wrapper);
    }
}

// 搜索请求DTO
@Data
public class ProductSearchRequest {
    private String keyword;
    private Long categoryId;
    private BigDecimal minPrice;
    private BigDecimal maxPrice;
    private Boolean hasTraceability;
    private String traceabilityLevel;
    private List<String> certificationTypes;
    private String producerName;
    private String sortBy = "created_at";
    private Integer page = 1;
    private Integer size = 20;
}
```

#### 2.2 前端搜索界面优化

```vue
<!-- 产品搜索页面优化 -->
<template>
  <div class="product-search-page">
    <!-- 搜索条件区域 -->
    <div class="search-filters">
      <el-card shadow="never" class="filter-card">
        <div class="filter-row">
          <!-- 关键词搜索 -->
          <div class="filter-item">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索产品名称、产地、生产者..."
              size="large"
              class="search-input"
              @keyup.enter="handleSearch"
            >
              <el-button
                slot="append"
                icon="el-icon-search"
                @click="handleSearch"
              ></el-button>
            </el-input>
          </div>

          <!-- 溯源筛选 -->
          <div class="filter-item">
            <el-select
              v-model="searchForm.hasTraceability"
              placeholder="溯源状态"
              clearable
              @change="handleSearch"
            >
              <el-option label="全部产品" :value="null"></el-option>
              <el-option label="有溯源信息" :value="true"></el-option>
              <el-option label="无溯源信息" :value="false"></el-option>
            </el-select>
          </div>

          <!-- 溯源等级筛选 -->
          <div class="filter-item">
            <el-select
              v-model="searchForm.traceabilityLevel"
              placeholder="溯源等级"
              clearable
              @change="handleSearch"
            >
              <el-option label="钻石溯源" value="钻石溯源"></el-option>
              <el-option label="黄金溯源" value="黄金溯源"></el-option>
              <el-option label="白银溯源" value="白银溯源"></el-option>
              <el-option label="青铜溯源" value="青铜溯源"></el-option>
              <el-option label="基础溯源" value="基础溯源"></el-option>
            </el-select>
          </div>
        </div>

        <!-- 高级筛选 -->
        <div class="advanced-filters" v-show="showAdvancedFilters">
          <div class="filter-row">
            <!-- 认证类型筛选 -->
            <div class="filter-item">
              <el-select
                v-model="searchForm.certificationTypes"
                placeholder="认证类型"
                multiple
                clearable
                @change="handleSearch"
              >
                <el-option label="有机认证" value="ORGANIC"></el-option>
                <el-option label="质量认证" value="QUALITY"></el-option>
                <el-option label="安全认证" value="SAFETY"></el-option>
                <el-option label="绿色食品" value="GREEN"></el-option>
              </el-select>
            </div>

            <!-- 生产者筛选 -->
            <div class="filter-item">
              <el-input
                v-model="searchForm.producerName"
                placeholder="生产者名称"
                clearable
                @change="handleSearch"
              ></el-input>
            </div>

            <!-- 价格区间 -->
            <div class="filter-item">
              <el-input
                v-model="searchForm.minPrice"
                placeholder="最低价格"
                type="number"
                @change="handleSearch"
              ></el-input>
              <span class="price-separator">-</span>
              <el-input
                v-model="searchForm.maxPrice"
                placeholder="最高价格"
                type="number"
                @change="handleSearch"
              ></el-input>
            </div>
          </div>
        </div>

        <!-- 筛选操作 -->
        <div class="filter-actions">
          <el-button
            type="text"
            @click="showAdvancedFilters = !showAdvancedFilters"
          >
            {{ showAdvancedFilters ? '收起' : '高级筛选' }}
            <i :class="showAdvancedFilters ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </el-button>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </div>
      </el-card>
    </div>

    <!-- 排序和视图切换 -->
    <div class="search-toolbar">
      <div class="search-stats">
        找到 <strong>{{ total }}</strong> 个产品
        <span v-if="searchForm.hasTraceability === true" class="trace-stats">
          (其中 {{ traceableCount }} 个支持溯源)
        </span>
      </div>

      <div class="toolbar-actions">
        <!-- 排序选择 -->
        <el-select
          v-model="searchForm.sortBy"
          @change="handleSearch"
          size="small"
        >
          <el-option label="最新发布" value="created_at"></el-option>
          <el-option label="价格从低到高" value="price_asc"></el-option>
          <el-option label="价格从高到低" value="price_desc"></el-option>
          <el-option label="溯源等级" value="traceability_level"></el-option>
          <el-option label="查询热度" value="query_count"></el-option>
        </el-select>

        <!-- 视图切换 -->
        <el-button-group class="view-toggle">
          <el-button
            :type="viewMode === 'grid' ? 'primary' : ''"
            size="small"
            @click="viewMode = 'grid'"
          >
            <i class="el-icon-menu"></i>
          </el-button>
          <el-button
            :type="viewMode === 'list' ? 'primary' : ''"
            size="small"
            @click="viewMode = 'list'"
          >
            <i class="el-icon-s-unfold"></i>
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 产品列表 -->
    <div class="search-results">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="6" animated></el-skeleton>
      </div>

      <div v-else-if="products.length === 0" class="no-results">
        <el-empty description="没有找到符合条件的产品">
          <el-button type="primary" @click="resetFilters">重新搜索</el-button>
        </el-empty>
      </div>

      <div v-else class="products-container" :class="viewMode">
        <ProductCard
          v-for="product in products"
          :key="product.id"
          :product="product"
          :show-traceability-preview="true"
          :view-mode="viewMode"
        />
      </div>

      <!-- 分页 -->
      <div v-if="total > 0" class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchForm.page"
          :page-sizes="[20, 40, 60, 80]"
          :page-size="searchForm.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
```

### 3. 溯源状态对推荐算法的影响

```java
// 产品推荐算法优化
@Service
public class ProductRecommendationService {

    /**
     * 获取推荐产品，考虑溯源因素
     */
    public List<Product> getRecommendedProducts(Long userId, int limit) {
        // 获取用户偏好
        UserPreference preference = getUserPreference(userId);

        // 基础推荐算法
        List<Product> baseRecommendations = getBaseRecommendations(userId, preference, limit * 2);

        // 应用溯源权重
        List<ProductScore> scoredProducts = baseRecommendations.stream()
            .map(product -> calculateProductScore(product, preference))
            .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
            .limit(limit)
            .collect(Collectors.toList());

        return scoredProducts.stream()
            .map(ProductScore::getProduct)
            .collect(Collectors.toList());
    }

    private ProductScore calculateProductScore(Product product, UserPreference preference) {
        double score = 0.0;

        // 基础评分 (40%)
        score += product.getRating() * 0.4;

        // 销量评分 (20%)
        score += Math.log(product.getSalesCount() + 1) * 0.2;

        // 溯源评分 (30%)
        if (product.getHasTraceability()) {
            score += 0.15; // 有溯源信息基础分

            // 溯源等级加分
            switch (product.getTraceabilityLevel()) {
                case "钻石溯源": score += 0.15; break;
                case "黄金溯源": score += 0.12; break;
                case "白银溯源": score += 0.09; break;
                case "青铜溯源": score += 0.06; break;
                case "基础溯源": score += 0.03; break;
            }
        }

        // 用户偏好评分 (10%)
        if (preference.getPrefersTraceableProducts()) {
            score += product.getHasTraceability() ? 0.1 : -0.05;
        }

        return new ProductScore(product, score);
    }

    @Data
    @AllArgsConstructor
    private static class ProductScore {
        private Product product;
        private double score;
    }
}
```

这个农品汇模块集成优化方案完整涵盖了：

1. **产品详情页溯源集成**：完整的界面设计和功能实现
2. **产品列表页溯源标识**：溯源认证标识、等级显示、快速查看
3. **跨模块数据整合**：数据库优化、同步机制、搜索优化
4. **推荐算法优化**：将溯源因素纳入推荐权重计算

通过这些优化，用户可以在农品汇中无缝地查看和使用溯源信息，提升整体用户体验。
```
```
