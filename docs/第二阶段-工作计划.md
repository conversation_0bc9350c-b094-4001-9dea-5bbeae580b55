# SFAP销售者销售中心 - 第二阶段工作计划

## 📋 阶段概览

**阶段名称**: 后端API系统开发与字段映射验证  
**计划开始时间**: 第一阶段文档确认后立即开始  
**预计完成时间**: 4周  
**主要目标**: 开发完整的销售者后端API系统，确保数据一致性和安全性  

## 🎯 阶段目标

### 核心目标
1. **Java实体类与数据库字段一致性验证** - 确保ORM映射正确
2. **销售者专用API接口开发** - 实现完整的CRUD操作
3. **权限验证系统实现** - 确保数据安全和访问控制
4. **测试数据生成** - 提供完整的功能测试数据
5. **API标准化** - 统一返回格式和异常处理

### 成功标准
- [ ] 所有Java实体类字段映射正确
- [ ] 完成4个核心API模块开发
- [ ] 实现严格的销售者权限验证
- [ ] 生成完整的测试数据集
- [ ] 所有API通过功能测试

## 📅 详细工作计划

### 第1周：基础设施建设与问题修复

#### 第1-2天：Java实体类验证与修复
**负责模块**: 实体类映射  
**工作内容**:
1. **检查现有实体类**
   - 分析`src/main/java/com/agriculture/entity/`下所有实体类
   - 重点检查User、Product、Order、TraceabilityRecord实体
   - 验证字段名称、数据类型映射

2. **修复字段映射问题**
   - 解决User实体的时间字段重复问题
   - 确保LocalDateTime与DATETIME正确映射
   - 添加缺失的getter/setter方法

3. **验证ORM配置**
   - 检查@Entity、@Table、@Column注解
   - 验证主键和外键映射
   - 测试基本CRUD操作

**交付物**:
- [ ] 实体类映射验证报告
- [ ] 修复后的实体类代码
- [ ] ORM映射测试用例

#### 第3-4天：权限验证系统完善
**负责模块**: 安全认证
**工作内容**:
1. **利用现有认证架构**
   - 基于现有UserAuthInterceptor和AuthorizationInterceptor
   - 使用Session + Header (X-User-Id) 认证机制
   - 确保销售者只能访问自己的数据

2. **实现权限验证注解**
   ```java
   // 使用现有权限验证注解
   @RequireRole("seller")
   @RequirePermission("product:manage")
   @GetMapping("/api/seller/products")
   public Result<List<Product>> getSellerProducts(HttpServletRequest request)
   ```

3. **安全测试**
   - 测试X-User-Id头部验证
   - 验证销售者数据隔离
   - 测试权限边界条件

**交付物**:
- [ ] 销售者API权限注解配置
- [ ] 数据访问权限验证逻辑
- [ ] 权限验证测试用例

#### 第5-6天：溯源码功能验证
**负责模块**: 溯源系统验证
**工作内容**:
1. **现有溯源码功能测试**
   - 测试TraceCodeGenerator生成功能
   - 验证24位格式的生成和解析
   - 确保与数据库实际格式一致

2. **API兼容性验证**
   - 测试溯源查询API
   - 验证格式自动识别功能
   - 确保来源类型正确识别

3. **二维码生成验证**
   - 测试二维码生成功能
   - 验证不同格式的兼容性
   - 确保查询链接正确

**交付物**:
- [ ] 溯源码功能测试报告
- [ ] API兼容性验证结果
- [ ] 二维码生成测试结果

#### 第7天：测试数据生成
**负责模块**: 数据准备
**工作内容**:
1. **设计测试数据结构**
   - 为每个销售者生成50-100条订单
   - 创建不同状态的订单数据
   - 生成对应的订单项和评价数据
   - 生成标准格式的溯源码数据

2. **实现数据生成脚本**
   ```sql
   -- 示例数据生成
   INSERT INTO `order` (order_no, user_id, seller_id, total_amount, order_status, ...)
   VALUES ('ORD' + TIMESTAMP + RANDOM, ?, ?, ?, ?, ...);
   ```

3. **数据验证**
   - 验证数据完整性和一致性
   - 确保外键关系正确
   - 测试数据查询性能
   - 验证溯源码格式正确性

**交付物**:
- [ ] 测试数据生成脚本
- [ ] 完整的测试数据集
- [ ] 数据验证报告

### 第2周：产品管理API开发

#### 第8-10天：产品CRUD API
**API路径**: `/api/seller/products/`  
**工作内容**:
1. **产品列表查询**
   ```java
   GET /api/seller/products/list
   - 支持分页查询
   - 支持状态筛选
   - 支持关键词搜索
   ```

2. **产品详情管理**
   ```java
   GET /api/seller/products/{id}     // 获取产品详情
   POST /api/seller/products/add     // 添加新产品
   PUT /api/seller/products/{id}     // 更新产品信息
   DELETE /api/seller/products/{id}  // 删除产品
   ```

3. **产品图片管理**
   ```java
   POST /api/seller/products/{id}/images  // 上传产品图片
   DELETE /api/seller/products/images/{imageId}  // 删除图片
   ```

**交付物**:
- [ ] 产品管理Controller
- [ ] 产品Service业务逻辑
- [ ] 产品Repository数据访问
- [ ] API文档和测试用例

#### 第11-14天：产品高级功能
**工作内容**:
1. **产品状态管理**
   - 上架/下架功能
   - 批量操作支持
   - 状态变更日志

2. **产品统计功能**
   - 销量统计
   - 浏览量统计
   - 收藏量统计

3. **产品搜索优化**
   - 全文搜索支持
   - 分类筛选
   - 价格区间筛选

**交付物**:
- [ ] 产品状态管理API
- [ ] 产品统计API
- [ ] 搜索优化功能

### 第3周：订单管理API开发

#### 第15-17天：订单查询与管理
**API路径**: `/api/seller/orders/`  
**工作内容**:
1. **订单列表查询**
   ```java
   GET /api/seller/orders/list
   - 支持状态筛选
   - 支持时间范围查询
   - 支持订单号搜索
   ```

2. **订单详情管理**
   ```java
   GET /api/seller/orders/{id}           // 获取订单详情
   PUT /api/seller/orders/{id}/status    // 更新订单状态
   POST /api/seller/orders/{id}/ship     // 订单发货处理
   ```

3. **订单状态流转**
   - 待支付 → 已支付 → 已发货 → 已完成
   - 支持订单取消和退款
   - 状态变更通知

**交付物**:
- [ ] 订单管理Controller
- [ ] 订单Service业务逻辑
- [ ] 订单状态流转逻辑
- [ ] API文档和测试用例

#### 第18-21天：订单高级功能
**工作内容**:
1. **发货管理**
   - 物流公司选择
   - 快递单号录入
   - 发货状态跟踪

2. **订单统计**
   - 销售额统计
   - 订单量统计
   - 客户分析

3. **订单导出**
   - Excel导出功能
   - 自定义导出字段
   - 批量操作支持

**交付物**:
- [ ] 发货管理功能
- [ ] 订单统计API
- [ ] 订单导出功能

### 第4周：统计分析与溯源管理API

#### 第22-24天：统计数据API
**API路径**: `/api/seller/analytics/`  
**工作内容**:
1. **仪表板统计**
   ```java
   GET /api/seller/analytics/dashboard
   - 今日销售额、订单数、访客数
   - 转化率、客单价等关键指标
   - 同比环比增长率
   ```

2. **销售趋势分析**
   ```java
   GET /api/seller/analytics/sales-trend
   - 7天/30天/90天销售趋势
   - 产品销售排行
   - 地区销售分布
   ```

3. **产品性能分析**
   ```java
   GET /api/seller/analytics/product-performance
   - 产品销售表现
   - 库存预警
   - 评价分析
   ```

**交付物**:
- [ ] 统计数据Controller
- [ ] 数据分析Service
- [ ] 图表数据格式化
- [ ] 统计API文档

#### 第25-28天：溯源管理API
**API路径**: `/api/seller/traceability/`
**工作内容**:
1. **溯源码生成功能**
   ```java
   POST /api/seller/traceability/generate-code
   - 支持现有格式溯源码生成：
     * 销售者格式(24位): SFAPS2507162209601459927
     * 管理员格式(24位): SFAPA24011109151006D3693
     * 旧格式(22位): 向后兼容支持
   - 自动生成二维码图片
   - 溯源信息关联
   ```

2. **溯源记录管理**
   ```java
   GET /api/seller/traceability/records     // 查询溯源记录
   POST /api/seller/traceability/events     // 添加溯源事件
   PUT /api/seller/traceability/{id}        // 更新溯源信息
   GET /api/seller/traceability/validate/{code}  // 验证溯源码
   ```

3. **溯源格式兼容处理**
   - 自动识别现有溯源码格式
   - 提取产品来源信息（销售者/管理员）
   - 保持与现有数据格式一致

4. **溯源查询统计**
   - 扫码次数统计
   - 查询来源分析
   - 溯源效果评估
   - 按来源类型统计

**交付物**:
- [ ] 溯源管理Controller
- [ ] 现有格式溯源码生成逻辑验证
- [ ] 格式兼容处理功能
- [ ] 二维码生成功能
- [ ] 溯源统计分析

## 🔧 技术规范

### API设计标准
```java
// 统一返回格式
public class Result<T> {
    private Integer code;
    private String message;
    private T data;
    private Long timestamp;
}

// 分页查询格式
public class PageResult<T> {
    private List<T> records;
    private Long total;
    private Integer current;
    private Integer size;
}
```

### 异常处理规范
```java
// 全局异常处理
@ControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(SellerAuthException.class)
    public Result<Void> handleSellerAuthException(SellerAuthException e) {
        return Result.error(401, "销售者权限验证失败");
    }
}
```

### 权限验证规范
```java
// 销售者权限注解
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface SellerAuth {
    boolean required() default true;
}
```

## 📊 进度跟踪

### 里程碑检查点
- **第1周结束**: 基础设施建设完成
- **第2周结束**: 产品管理API完成
- **第3周结束**: 订单管理API完成
- **第4周结束**: 统计分析和溯源API完成

### 质量标准
- [ ] 代码覆盖率 > 80%
- [ ] API响应时间 < 500ms
- [ ] 所有API通过安全测试
- [ ] 完整的API文档
- [ ] 通过功能验收测试

## 🚨 风险管控

### 技术风险
1. **实体类映射复杂** - 预留额外时间处理映射问题
2. **权限验证复杂** - 采用成熟的安全框架
3. **数据生成耗时** - 并行处理数据生成任务

### 进度风险
1. **需求变更** - 及时沟通确认需求
2. **技术难点** - 提前识别并寻求支持
3. **测试时间不足** - 开发过程中持续测试

## 📝 交付清单

### 代码交付
- [ ] 完整的后端API代码
- [ ] 单元测试和集成测试
- [ ] API文档和使用说明
- [ ] 数据库脚本和测试数据

### 文档交付
- [ ] 第二阶段开发报告
- [ ] API接口文档
- [ ] 数据库变更记录
- [ ] 问题解决报告

---
**计划制定**: 2025年7月22日  
**计划审核**: 待审核  
**执行状态**: 等待第一阶段确认完成  
**下一阶段**: 第三阶段前端路由系统完善与页面验证
