# SFAP智慧农业辅助平台文档中心

## 📋 文档目录

### 🏗️ 系统架构
- [系统架构设计](./architecture/system-architecture.md)
- [数据库设计](./architecture/database-design.md)
- [API接口设计](./architecture/api-design.md)

### 🔧 开发文档
- [开发环境搭建](./development/environment-setup.md)
- [代码规范](./development/coding-standards.md)
- [Git工作流程](./development/git-workflow.md)

### 📦 功能模块
- [用户管理模块](./modules/user-management.md)
- [产品管理模块](./modules/product-management.md)
- [溯源管理模块](./modules/traceability-management.md)
- [订单管理模块](./modules/order-management.md)

### 🧪 测试文档
- [测试计划](./testing/test-plan.md)
- [API测试](./testing/api-testing.md)
- [前端测试](./testing/frontend-testing.md)

### 🚀 部署文档
- [部署指南](./deployment/deployment-guide.md)
- [环境配置](./deployment/environment-config.md)
- [监控运维](./deployment/monitoring.md)

### 📊 项目管理
- [项目进度](./project/progress.md)
- [版本发布](./project/releases.md)
- [问题跟踪](./project/issues.md)

---

## 🔍 快速导航

### 新手入门
1. [环境搭建指南](./development/environment-setup.md)
2. [项目结构说明](./development/project-structure.md)
3. [开发流程](./development/development-workflow.md)

### 开发者指南
1. [API接口文档](./api/README.md)
2. [前端组件库](./frontend/components.md)
3. [数据库操作](./backend/database.md)

### 运维指南
1. [服务器部署](./deployment/server-deployment.md)
2. [数据库维护](./deployment/database-maintenance.md)
3. [性能监控](./deployment/performance-monitoring.md)

---

## 📝 文档更新记录

| 日期 | 版本 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 2025-07-15 | v1.0 | 创建文档中心，整理项目文档 | 开发团队 |

---

## 📞 联系方式

- **项目负责人**: SFAP开发团队
- **技术支持**: 开发团队
- **文档维护**: 技术文档组

---

**最后更新**: 2025-07-15  
**文档版本**: v1.0
