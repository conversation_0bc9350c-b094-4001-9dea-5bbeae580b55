# 生产环境图片配置指南

## 概述

本指南详细说明了如何在生产环境中正确配置图片资源，确保所有图片都能正常加载。

## 服务器配置

### 服务器信息
- **服务器地址**: 120.26.140.157
- **前端端口**: 8200
- **后端端口**: 8081

### 图片存储路径
- **主路径**: `/www/wwwroot/test.com/backend/uploads/images`
- **商品图片**: `/www/wwwroot/test.com/backend/uploads/images/products`
- **新闻图片**: `/www/wwwroot/test.com/backend/uploads/images/news`
- **农业百科图片**: `/www/wwwroot/test.com/backend/uploads/images/encyclopedia`
- **用户头像**: `/www/wwwroot/test.com/backend/uploads/images/avatars`
- **二维码图片**: `/www/wwwroot/test.com/backend/uploads/images/qrcodes`

## 前端配置

### 环境变量配置 (.env.production)
```bash
# 生产环境API地址 - 必须指向后端8081端口
VUE_APP_BASE_API=http://120.26.140.157:8081
VUE_APP_API_URL=http://120.26.140.157:8081

# 应用配置
NODE_ENV=production
VUE_APP_TITLE=智慧农业辅助平台
```

### 图片URL规范
所有图片URL都应该通过后端服务器(8081端口)访问：

**正确示例**:
- `http://120.26.140.157:8081/uploads/images/products/apple.jpg`
- `http://120.26.140.157:8081/uploads/images/avatars/user123.jpg`
- `http://120.26.140.157:8081/static/images/default-product.jpg`

**错误示例**:
- `http://120.26.140.157:8200/uploads/images/products/apple.jpg` ❌
- `http://120.26.140.157:8200/static/images/default-product.jpg` ❌

## 后端配置

### Spring Boot配置 (application-prod.yml)
```yaml
# 文件上传配置（生产环境）
file:
  upload:
    path: /www/wwwroot/test.com/backend/uploads
    avatar-dir: /www/wwwroot/test.com/backend/uploads/images/avatars
    product-dir: /www/wwwroot/test.com/backend/uploads/images/products
    news-dir: /www/wwwroot/test.com/backend/uploads/images/news
    encyclopedia-dir: /www/wwwroot/test.com/backend/uploads/images/encyclopedia

# 生产环境应用配置
app:
  domain: http://120.26.140.157:8200
  cors:
    allowed-origins: http://120.26.140.157,http://120.26.140.157:8200,http://120.26.140.157:8081
```

### 静态资源映射
确保WebConfig.java中的静态资源映射正确：
```java
// 配置上传文件访问路径
registry.addResourceHandler("/uploads/**")
        .addResourceLocations("file:/www/wwwroot/test.com/backend/uploads/")
        .setCachePeriod(3600);

// 配置静态资源访问路径
registry.addResourceHandler("/static/**")
        .addResourceLocations("classpath:/static/")
        .setCachePeriod(3600);
```

## 部署步骤

### 1. 前端构建
```bash
# 安装依赖
npm install

# 构建生产版本
npm run build
```

### 2. 后端部署
```bash
# 构建后端项目
mvn clean package -Pprod

# 部署到服务器
java -jar -Dspring.profiles.active=prod agriculture-platform.jar
```

### 3. 创建必要目录
```bash
# 在服务器上创建图片存储目录
mkdir -p /www/wwwroot/test.com/backend/uploads/images/products
mkdir -p /www/wwwroot/test.com/backend/uploads/images/news
mkdir -p /www/wwwroot/test.com/backend/uploads/images/encyclopedia
mkdir -p /www/wwwroot/test.com/backend/uploads/images/avatars
mkdir -p /www/wwwroot/test.com/backend/uploads/images/qrcodes

# 设置权限
chmod -R 755 /www/wwwroot/test.com/backend/uploads/
```

### 4. 复制默认图片
```bash
# 复制默认图片到静态资源目录
cp src/assets/images/products/default.jpg backend/main/src/main/resources/static/images/default-product.jpg
```

## 验证配置

### 1. 检查API配置
访问以下URL验证API配置：
- `http://120.26.140.157:8081/api/health` - 后端健康检查
- `http://120.26.140.157:8200` - 前端应用

### 2. 检查图片访问
验证以下图片路径是否可访问：
- `http://120.26.140.157:8081/static/images/default-product.jpg`
- `http://120.26.140.157:8081/uploads/images/products/` (目录)

### 3. 浏览器控制台检查
打开浏览器开发者工具，检查：
- 是否有404图片加载错误
- 是否有指向8200端口的图片请求
- 网络请求是否都指向正确的后端地址

## 常见问题

### Q: 图片显示404错误
**A**: 检查以下几点：
1. 图片文件是否存在于服务器指定路径
2. 后端静态资源映射是否正确配置
3. 前端是否使用了正确的API地址

### Q: 图片请求指向8200端口
**A**: 这是配置错误，需要：
1. 检查.env.production文件中的API地址
2. 确保所有图片处理函数都使用了正确的baseURL
3. 清除浏览器缓存重新测试

### Q: 上传的图片无法显示
**A**: 检查：
1. 文件上传目录权限是否正确
2. 后端文件上传配置是否正确
3. 数据库中存储的图片路径是否正确

## 技术支持

如果遇到问题，请检查：
1. 浏览器控制台错误信息
2. 后端日志文件
3. 服务器文件权限设置
4. 网络连接状态
