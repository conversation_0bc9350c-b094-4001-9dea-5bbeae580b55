# SFAP农品汇平台用户角色识别字段规范

## 1. 角色字段概述

### 1.1 现有字段分析
SFAP平台目前使用两个字段来标识用户角色：
- `role` (varchar(20)) - 用户角色字段
- `user_type` (varchar(20)) - 用户类型字段，默认值为'normal'

### 1.2 字段使用原则
- **主字段**: `role` 作为主要角色识别字段
- **辅助字段**: `user_type` 作为辅助字段，与role保持一致
- **兼容性**: 两个字段同时维护，确保向后兼容

## 2. 标准角色定义

### 2.1 角色类型及标准值

| 角色类型 | role字段值 | user_type字段值 | 描述 | 权限级别 |
|---------|-----------|----------------|------|---------|
| 普通用户 | `user` | `normal` | 平台普通用户 | 1 |
| 销售者 | `seller` | `seller` | 已认证的销售者 | 2 |
| 管理员 | `admin` | `admin` | 平台管理员 | 3 |

### 2.2 角色值规范
- **格式**: 全小写英文字母
- **长度**: 3-10个字符
- **特殊值**: 不允许使用ROLE_前缀（由系统自动添加）

## 3. 数据库字段映射

### 3.1 User实体类字段映射
```java
@TableField("role")
private String role;           // 主角色字段

@TableField("user_type") 
private String userType;       // 辅助类型字段
```

### 3.2 角色判断方法
```java
// 管理员判断
public boolean isAdmin() {
    return (userType != null && userType.equals("admin")) ||
           (role != null && (role.equals("admin") || role.equals("ADMIN")));
}

// 销售者判断  
public boolean isSeller() {
    return (userType != null && userType.equals("seller")) ||
           (role != null && (role.equals("seller") || role.equals("SELLER")));
}
```

## 4. 前端角色识别规范

### 4.1 前端字段映射
```javascript
// localStorage中的用户信息字段
{
  "id": 18,
  "username": "admin_new",
  "role": "admin",           // 主角色字段
  "userType": "admin",       // 辅助类型字段  
  "user_type": "admin"       // 数据库原始字段名
}
```

### 4.2 前端角色判断逻辑
```javascript
function isAdmin(userInfo) {
  return userInfo.role === 'admin' ||
         userInfo.userType === 'admin' ||
         userInfo.user_type === 'admin' ||
         userInfo.isAdmin === true;
}
```

## 5. API接口规范

### 5.1 登录接口返回格式
```json
{
  "success": true,
  "data": {
    "id": 18,
    "username": "admin_new", 
    "role": "admin",
    "userType": "admin",
    "isAdmin": true,
    "isSeller": false
  }
}
```

### 5.2 角色更新接口
- **接口**: `PUT /api/users/{id}/role`
- **参数**: `{"role": "admin", "userType": "admin"}`
- **说明**: 同时更新两个字段保持一致性

## 6. 数据迁移规范

### 6.1 角色数据标准化
```sql
-- 管理员角色标准化
UPDATE user SET role = 'admin', user_type = 'admin' 
WHERE role IN ('ADMIN', 'administrator') OR user_type = 'admin';

-- 销售者角色标准化  
UPDATE user SET role = 'seller', user_type = 'seller'
WHERE role IN ('SELLER', 'FARMER') OR user_type = 'seller';

-- 普通用户角色标准化
UPDATE user SET role = 'user', user_type = 'normal'
WHERE role IN ('USER', 'BUYER', 'ROLE_USER') OR user_type = 'normal';
```

## 7. 权限验证规范

### 7.1 Spring Security角色格式
- **标准格式**: `ROLE_ADMIN`, `ROLE_SELLER`, `ROLE_USER`
- **转换方法**: `getStandardRole()` 方法自动添加ROLE_前缀

### 7.2 权限级别
1. **ROLE_USER** (Level 1): 基础用户权限
2. **ROLE_SELLER** (Level 2): 销售者权限 + 基础权限
3. **ROLE_ADMIN** (Level 3): 管理员权限 + 所有权限

## 8. 测试验证清单

### 8.1 数据一致性检查
- [ ] role和user_type字段值一致
- [ ] 角色判断方法返回正确结果
- [ ] 前端角色识别正确

### 8.2 功能验证
- [ ] 管理员用户能访问管理后台
- [ ] 销售者用户能访问销售中心  
- [ ] 普通用户只能访问基础功能

## 9. 常见问题处理

### 9.1 角色不一致问题
**问题**: role='admin' 但 user_type=null
**解决**: 执行数据同步脚本统一字段值

### 9.2 前端角色识别失败
**问题**: 前端无法正确识别用户角色
**解决**: 检查localStorage中的用户信息字段完整性

### 9.3 权限验证失败
**问题**: 用户有角色但权限验证失败
**解决**: 检查Spring Security配置和角色格式转换
