# 数据库结构分析报告

## 概述

本报告对SFAP平台销售中心相关的数据库表结构进行全面分析，评估现有表结构的完整性和优化需求。

## 核心数据表分析

### 1. 用户相关表

#### user 表
```sql
-- 用户基础信息表
CREATE TABLE user (
  id bigint PRIMARY KEY AUTO_INCREMENT,
  username varchar(50) UNIQUE NOT NULL,
  password varchar(255) NOT NULL,
  email varchar(100) UNIQUE,
  phone varchar(20),
  name varchar(100),
  avatar varchar(255),
  role varchar(20) DEFAULT 'USER',
  user_type tinyint DEFAULT 0,
  status tinyint DEFAULT 1,
  last_login_time datetime,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**分析结果：**
- ✅ 表结构完整，包含基础用户信息
- ⚠️ 存在role和user_type双重角色字段，可能导致数据不一致
- ✅ 包含必要的时间戳字段
- ✅ 支持头像和联系方式

### 2. 销售者店铺表

#### seller_shop 表
```sql
-- 销售者店铺信息表
CREATE TABLE seller_shop (
  id bigint PRIMARY KEY AUTO_INCREMENT,
  seller_id bigint NOT NULL,
  shop_name varchar(100) NOT NULL,
  shop_description text,
  shop_logo varchar(255),
  shop_banner varchar(255),
  contact_phone varchar(20),
  contact_address text,
  business_hours varchar(100),
  status tinyint DEFAULT 1,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (seller_id) REFERENCES user(id)
);
```

**分析结果：**
- ✅ 表结构完整，支持店铺基础信息管理
- ✅ 包含店铺展示相关字段（logo、banner）
- ✅ 支持营业时间和联系方式
- ✅ 正确的外键关联

### 3. 产品管理表

#### product 表
```sql
-- 产品信息表
CREATE TABLE product (
  id bigint PRIMARY KEY AUTO_INCREMENT,
  seller_id bigint NOT NULL,
  name varchar(200) NOT NULL,
  description text,
  price decimal(10,2) NOT NULL,
  stock int DEFAULT 0,
  category_id bigint,
  image varchar(255),
  status tinyint DEFAULT 1,
  sales_count int DEFAULT 0,
  view_count int DEFAULT 0,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (seller_id) REFERENCES user(id),
  FOREIGN KEY (category_id) REFERENCES category(id)
);
```

**分析结果：**
- ✅ 表结构完整，支持产品基础信息
- ✅ 包含销售统计字段（sales_count, view_count）
- ✅ 支持库存管理
- ✅ 正确的外键关联
- ⚠️ 缺少产品详细规格字段
- ⚠️ 缺少产品多图支持

### 4. 订单管理表

#### order 表
```sql
-- 订单信息表
CREATE TABLE `order` (
  id bigint PRIMARY KEY AUTO_INCREMENT,
  order_no varchar(32) UNIQUE NOT NULL,
  user_id bigint NOT NULL,
  seller_id bigint NOT NULL,
  total_amount decimal(10,2) NOT NULL,
  discount_amount decimal(10,2) DEFAULT 0.00,
  shipping_fee decimal(10,2) DEFAULT 0.00,
  actual_amount decimal(10,2) NOT NULL,
  payment_method varchar(20),
  payment_status tinyint DEFAULT 0,
  order_status tinyint DEFAULT 0,
  shipping_address text,
  buyer_note text,
  seller_note text,
  delivery_company varchar(50),
  tracking_number varchar(100),
  payment_time datetime,
  shipping_time datetime,
  delivery_time datetime,
  completion_time datetime,
  receive_time datetime,
  cancel_time datetime,
  cancel_reason varchar(255),
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES user(id),
  FOREIGN KEY (seller_id) REFERENCES user(id)
);
```

**分析结果：**
- ✅ 表结构非常完整，支持完整的订单生命周期
- ✅ 包含详细的时间节点记录
- ✅ 支持物流信息管理
- ✅ 包含买家和卖家备注
- ✅ 支持订单状态管理

## 支持表分析

### 1. 分类表 (category)
- ✅ 支持产品分类管理
- ✅ 层级结构支持

### 2. 订单项表 (order_item)
- ✅ 支持订单详细商品信息
- ✅ 正确的关联关系

### 3. 产品图片表 (product_image)
- ✅ 支持产品多图管理
- ✅ 图片排序支持

### 4. 评价表 (review)
- ✅ 支持产品评价管理
- ✅ 包含评分和评价内容

### 5. 溯源相关表
- ✅ traceability_record - 溯源记录
- ✅ trace_code - 溯源码管理
- ✅ traceability_event - 溯源事件
- ✅ traceability_logistics - 物流溯源

## 统计分析需求表

### 缺失的统计表

1. **销售统计表 (sales_statistics)**
```sql
CREATE TABLE sales_statistics (
  id bigint PRIMARY KEY AUTO_INCREMENT,
  seller_id bigint NOT NULL,
  date date NOT NULL,
  total_orders int DEFAULT 0,
  total_amount decimal(12,2) DEFAULT 0.00,
  total_products int DEFAULT 0,
  new_customers int DEFAULT 0,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_seller_date (seller_id, date),
  FOREIGN KEY (seller_id) REFERENCES user(id)
);
```

2. **产品统计表 (product_statistics)**
```sql
CREATE TABLE product_statistics (
  id bigint PRIMARY KEY AUTO_INCREMENT,
  product_id bigint NOT NULL,
  date date NOT NULL,
  view_count int DEFAULT 0,
  order_count int DEFAULT 0,
  sales_amount decimal(10,2) DEFAULT 0.00,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_product_date (product_id, date),
  FOREIGN KEY (product_id) REFERENCES product(id)
);
```

3. **客户统计表 (customer_statistics)**
```sql
CREATE TABLE customer_statistics (
  id bigint PRIMARY KEY AUTO_INCREMENT,
  seller_id bigint NOT NULL,
  customer_id bigint NOT NULL,
  total_orders int DEFAULT 0,
  total_amount decimal(12,2) DEFAULT 0.00,
  last_order_time datetime,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_seller_customer (seller_id, customer_id),
  FOREIGN KEY (seller_id) REFERENCES user(id),
  FOREIGN KEY (customer_id) REFERENCES user(id)
);
```

## 数据完整性分析

### 现有数据查询

```sql
-- 检查用户角色数据
SELECT role, user_type, COUNT(*) as count 
FROM user 
GROUP BY role, user_type;

-- 检查销售者店铺数据
SELECT COUNT(*) as shop_count 
FROM seller_shop 
WHERE status = 1;

-- 检查产品数据
SELECT seller_id, COUNT(*) as product_count, 
       SUM(sales_count) as total_sales
FROM product 
WHERE status = 1 
GROUP BY seller_id;

-- 检查订单数据
SELECT seller_id, COUNT(*) as order_count,
       SUM(actual_amount) as total_amount
FROM `order` 
WHERE order_status != 5  -- 非取消订单
GROUP BY seller_id;
```

## 性能优化建议

### 1. 索引优化

```sql
-- 产品表索引
CREATE INDEX idx_product_seller_status ON product(seller_id, status);
CREATE INDEX idx_product_category_status ON product(category_id, status);
CREATE INDEX idx_product_created_at ON product(created_at);

-- 订单表索引
CREATE INDEX idx_order_seller_status ON `order`(seller_id, order_status);
CREATE INDEX idx_order_user_status ON `order`(user_id, order_status);
CREATE INDEX idx_order_created_at ON `order`(created_at);
CREATE INDEX idx_order_payment_status ON `order`(payment_status);

-- 销售统计表索引（如果创建）
CREATE INDEX idx_sales_stats_seller_date ON sales_statistics(seller_id, date);
CREATE INDEX idx_sales_stats_date ON sales_statistics(date);
```

### 2. 分区建议

对于大数据量的表，建议按时间分区：

```sql
-- 订单表按月分区
ALTER TABLE `order` 
PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
  PARTITION p202501 VALUES LESS THAN (202502),
  PARTITION p202502 VALUES LESS THAN (202503),
  -- ... 更多分区
);
```

## 数据一致性问题

### 1. 角色字段不一致
- **问题**: user表中role和user_type字段可能不同步
- **解决方案**: 创建触发器或定时任务保持同步

### 2. 统计数据不准确
- **问题**: product表中的sales_count可能与实际订单不符
- **解决方案**: 创建定时任务重新计算统计数据

## 建议的数据库优化

### 1. 立即需要的改进

1. **创建销售统计相关表**
2. **添加必要的索引**
3. **修复角色字段不一致问题**
4. **创建数据一致性检查机制**

### 2. 长期优化建议

1. **实施数据分区策略**
2. **建立数据归档机制**
3. **优化查询性能**
4. **建立数据监控体系**

## 结论

SFAP平台的数据库结构基本完整，能够支持销售中心的核心功能。主要需要补充统计相关的表结构，并解决一些数据一致性问题。建议优先实施统计表的创建和索引优化，以提升销售中心的数据查询性能。
