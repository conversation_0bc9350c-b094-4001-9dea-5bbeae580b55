# 销售中心API开发规范

## 概述

本文档为SFAP平台销售中心API开发提供详细的技术规范和最佳实践指导。

## 1. 项目结构规范

### 1.1 包结构
```
com.agriculture
├── controller/          # 控制器层
│   ├── SellerController.java
│   ├── SellerAnalyticsController.java
│   └── SellerCustomerController.java
├── service/            # 服务层
│   ├── SellerService.java
│   ├── SellerAnalyticsService.java
│   └── SellerCustomerService.java
├── mapper/             # 数据访问层
│   ├── SellerMapper.java
│   └── SellerAnalyticsMapper.java
├── entity/             # 实体类
│   ├── Seller.java
│   └── SalesStatistics.java
├── dto/                # 数据传输对象
│   ├── request/
│   └── response/
└── vo/                 # 视图对象
    ├── SellerVO.java
    └── SalesStatsVO.java
```

### 1.2 命名规范

#### 控制器命名
- 格式：`{模块名}Controller`
- 示例：`SellerAnalyticsController`、`SellerCustomerController`

#### 服务类命名
- 格式：`{模块名}Service`
- 示例：`SellerAnalyticsService`、`SellerCustomerService`

#### 接口路径命名
- 格式：`/api/seller/{模块}/{操作}`
- 示例：`/api/seller/analytics/sales-trend`

## 2. API设计规范

### 2.1 RESTful API设计

#### HTTP方法使用
```http
GET    /api/seller/products          # 获取产品列表
POST   /api/seller/products          # 创建产品
GET    /api/seller/products/{id}     # 获取产品详情
PUT    /api/seller/products/{id}     # 更新产品
DELETE /api/seller/products/{id}     # 删除产品
```

#### 资源命名规则
- 使用名词复数形式
- 使用小写字母和连字符
- 避免动词，通过HTTP方法表达操作

### 2.2 统一响应格式

#### 成功响应
```java
@Data
public class Result<T> {
    private Integer code;
    private String message;
    private T data;
    
    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setCode(200);
        result.setMessage("success");
        result.setData(data);
        return result;
    }
}
```

#### 分页响应
```java
@Data
public class PageResult<T> {
    private List<T> records;
    private Long total;
    private Long current;
    private Long size;
    private Long pages;
}
```

#### 错误响应
```java
public static <T> Result<T> error(String message) {
    Result<T> result = new Result<>();
    result.setCode(400);
    result.setMessage(message);
    result.setData(null);
    return result;
}
```

### 2.3 状态码规范

| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| 200 | 成功 | 请求成功处理 |
| 201 | 创建成功 | 资源创建成功 |
| 400 | 请求错误 | 参数验证失败 |
| 401 | 未授权 | 用户未登录 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 500 | 服务器错误 | 系统内部错误 |

## 3. 权限验证规范

### 3.1 权限注解使用

```java
@RestController
@RequestMapping("/api/seller")
@PreAuthorize("hasRole('SELLER')")  // 类级别权限
public class SellerController {
    
    @GetMapping("/products")
    @PreAuthorize("hasRole('SELLER')")  // 方法级别权限
    public Result<PageResult<Product>> getProducts() {
        // 实现逻辑
    }
}
```

### 3.2 数据权限验证

```java
@Service
public class SellerProductService {
    
    public Product getProduct(Long productId, Long sellerId) {
        Product product = productMapper.selectById(productId);
        
        // 验证数据权限
        if (product != null && !product.getSellerId().equals(sellerId)) {
            throw new BusinessException("无权访问该产品");
        }
        
        return product;
    }
}
```

### 3.3 用户身份获取

```java
@Component
public class AuthUtils {
    
    public static Long getCurrentUserId(HttpServletRequest request) {
        // 从JWT token或session中获取用户ID
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            return JwtUtils.getUserIdFromToken(token.substring(7));
        }
        return null;
    }
    
    public static boolean isSeller(HttpServletRequest request) {
        // 验证用户是否为销售者
        Long userId = getCurrentUserId(request);
        if (userId == null) return false;
        
        User user = userService.getById(userId);
        return user != null && "SELLER".equals(user.getRole());
    }
}
```

## 4. 数据验证规范

### 4.1 请求参数验证

```java
@Data
public class CreateProductRequest {
    
    @NotBlank(message = "产品名称不能为空")
    @Length(max = 200, message = "产品名称长度不能超过200字符")
    private String name;
    
    @NotNull(message = "产品价格不能为空")
    @DecimalMin(value = "0.01", message = "产品价格必须大于0")
    private BigDecimal price;
    
    @Min(value = 0, message = "库存数量不能为负数")
    private Integer stock;
    
    @NotNull(message = "产品分类不能为空")
    private Long categoryId;
}
```

### 4.2 控制器参数验证

```java
@PostMapping("/products")
public Result<Product> createProduct(
    @Valid @RequestBody CreateProductRequest request,
    HttpServletRequest httpRequest) {
    
    Long sellerId = AuthUtils.getCurrentUserId(httpRequest);
    Product product = sellerProductService.createProduct(request, sellerId);
    return Result.success(product);
}
```

### 4.3 全局异常处理

```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<Void> handleValidationException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        String message = bindingResult.getFieldErrors().stream()
            .map(FieldError::getDefaultMessage)
            .collect(Collectors.joining(", "));
        return Result.error(message);
    }
    
    @ExceptionHandler(BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        return Result.error(e.getMessage());
    }
}
```

## 5. 数据库操作规范

### 5.1 MyBatis Plus使用

```java
@Mapper
public interface SellerProductMapper extends BaseMapper<Product> {
    
    /**
     * 分页查询销售者产品
     */
    IPage<Product> selectSellerProducts(
        Page<Product> page, 
        @Param("sellerId") Long sellerId,
        @Param("keyword") String keyword,
        @Param("categoryId") Long categoryId,
        @Param("status") Integer status
    );
    
    /**
     * 获取销售者产品统计
     */
    SellerProductStats selectSellerProductStats(@Param("sellerId") Long sellerId);
}
```

### 5.2 SQL编写规范

```xml
<!-- SellerProductMapper.xml -->
<select id="selectSellerProducts" resultType="com.agriculture.entity.Product">
    SELECT 
        p.id,
        p.name,
        p.price,
        p.stock,
        p.sales_count,
        p.status,
        p.created_at,
        c.name as category_name
    FROM product p
    LEFT JOIN category c ON p.category_id = c.id
    WHERE p.seller_id = #{sellerId}
    <if test="keyword != null and keyword != ''">
        AND (p.name LIKE CONCAT('%', #{keyword}, '%') 
             OR p.description LIKE CONCAT('%', #{keyword}, '%'))
    </if>
    <if test="categoryId != null">
        AND p.category_id = #{categoryId}
    </if>
    <if test="status != null">
        AND p.status = #{status}
    </if>
    ORDER BY p.created_at DESC
</select>
```

### 5.3 事务管理

```java
@Service
@Transactional
public class SellerProductService {
    
    @Transactional(rollbackFor = Exception.class)
    public Product createProduct(CreateProductRequest request, Long sellerId) {
        // 创建产品
        Product product = new Product();
        BeanUtils.copyProperties(request, product);
        product.setSellerId(sellerId);
        product.setCreatedAt(LocalDateTime.now());
        
        productMapper.insert(product);
        
        // 创建产品图片记录
        if (request.getImages() != null && !request.getImages().isEmpty()) {
            productImageService.batchInsert(product.getId(), request.getImages());
        }
        
        return product;
    }
}
```

## 6. 缓存使用规范

### 6.1 Redis缓存配置

```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory factory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(10))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        return RedisCacheManager.builder(factory)
            .cacheDefaults(config)
            .build();
    }
}
```

### 6.2 缓存注解使用

```java
@Service
public class SellerAnalyticsService {
    
    @Cacheable(value = "seller:stats", key = "#sellerId + ':' + #period")
    public SalesStatsVO getSalesStats(Long sellerId, String period) {
        // 查询销售统计数据
        return salesStatsMapper.selectSalesStats(sellerId, period);
    }
    
    @CacheEvict(value = "seller:stats", key = "#sellerId + ':*'")
    public void clearSellerStatsCache(Long sellerId) {
        // 清除销售者统计缓存
    }
}
```

## 7. 日志记录规范

### 7.1 日志级别使用

```java
@Slf4j
@Service
public class SellerProductService {
    
    public Product createProduct(CreateProductRequest request, Long sellerId) {
        log.info("创建产品开始 - sellerId: {}, productName: {}", sellerId, request.getName());
        
        try {
            Product product = doCreateProduct(request, sellerId);
            log.info("创建产品成功 - productId: {}", product.getId());
            return product;
        } catch (Exception e) {
            log.error("创建产品失败 - sellerId: {}, error: {}", sellerId, e.getMessage(), e);
            throw new BusinessException("创建产品失败");
        }
    }
}
```

### 7.2 操作日志记录

```java
@Aspect
@Component
@Slf4j
public class OperationLogAspect {
    
    @Around("@annotation(operationLog)")
    public Object around(ProceedingJoinPoint point, OperationLog operationLog) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        try {
            Object result = point.proceed();
            long endTime = System.currentTimeMillis();
            
            log.info("操作日志 - 操作: {}, 耗时: {}ms", 
                operationLog.value(), endTime - startTime);
            
            return result;
        } catch (Exception e) {
            log.error("操作失败 - 操作: {}, 错误: {}", 
                operationLog.value(), e.getMessage());
            throw e;
        }
    }
}
```

## 8. 性能优化规范

### 8.1 分页查询优化

```java
// 使用MyBatis Plus分页插件
@GetMapping("/products")
public Result<PageResult<Product>> getProducts(
    @RequestParam(defaultValue = "1") Integer current,
    @RequestParam(defaultValue = "20") Integer size,
    HttpServletRequest request) {
    
    Long sellerId = AuthUtils.getCurrentUserId(request);
    Page<Product> page = new Page<>(current, size);
    
    IPage<Product> result = sellerProductService.getSellerProducts(page, sellerId);
    
    return Result.success(PageResult.of(result));
}
```

### 8.2 批量操作优化

```java
@Service
public class SellerProductService {
    
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStock(List<StockUpdateRequest> requests, Long sellerId) {
        // 验证权限
        List<Long> productIds = requests.stream()
            .map(StockUpdateRequest::getProductId)
            .collect(Collectors.toList());
        
        List<Product> products = productMapper.selectBatchIds(productIds);
        products.forEach(product -> {
            if (!product.getSellerId().equals(sellerId)) {
                throw new BusinessException("无权操作产品: " + product.getName());
            }
        });
        
        // 批量更新
        productMapper.batchUpdateStock(requests);
    }
}
```

## 9. 测试规范

### 9.1 单元测试

```java
@SpringBootTest
@Transactional
@Rollback
class SellerProductServiceTest {
    
    @Autowired
    private SellerProductService sellerProductService;
    
    @Test
    void testCreateProduct() {
        // Given
        CreateProductRequest request = new CreateProductRequest();
        request.setName("测试产品");
        request.setPrice(new BigDecimal("99.99"));
        request.setStock(100);
        request.setCategoryId(1L);
        
        Long sellerId = 1L;
        
        // When
        Product product = sellerProductService.createProduct(request, sellerId);
        
        // Then
        assertThat(product).isNotNull();
        assertThat(product.getName()).isEqualTo("测试产品");
        assertThat(product.getSellerId()).isEqualTo(sellerId);
    }
}
```

### 9.2 集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class SellerControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testGetProducts() {
        // 模拟登录获取token
        String token = loginAndGetToken("<EMAIL>", "password");
        
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        
        ResponseEntity<String> response = restTemplate.exchange(
            "/api/seller/products", HttpMethod.GET, entity, String.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
    }
}
```

## 10. 部署和监控规范

### 10.1 健康检查

```java
@RestController
public class HealthController {
    
    @Autowired
    private DataSource dataSource;
    
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", LocalDateTime.now());
        
        // 检查数据库连接
        try (Connection connection = dataSource.getConnection()) {
            health.put("database", "UP");
        } catch (Exception e) {
            health.put("database", "DOWN");
            health.put("status", "DOWN");
        }
        
        return health;
    }
}
```

### 10.2 性能监控

```java
@Component
public class PerformanceMonitor {
    
    private final MeterRegistry meterRegistry;
    
    public PerformanceMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }
    
    public void recordApiCall(String apiName, long duration) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("api.call.duration")
            .tag("api", apiName)
            .register(meterRegistry));
    }
}
```

## 总结

本规范涵盖了SFAP销售中心API开发的各个方面，包括项目结构、API设计、权限验证、数据操作、缓存使用、日志记录、性能优化、测试和部署监控等。遵循这些规范可以确保代码质量、系统性能和维护性。
