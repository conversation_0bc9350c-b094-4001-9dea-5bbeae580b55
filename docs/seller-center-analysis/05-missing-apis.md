# 缺失API接口清单

## 概述

本文档详细列出SFAP平台销售中心缺失的API接口，按优先级分类，并提供每个接口的详细规范。

## 🔴 高优先级缺失接口

### 1. 销售统计分析接口

#### 1.1 销售趋势分析
```http
GET /api/seller/analytics/sales-trend
```

**功能描述：** 获取销售趋势数据，支持不同时间周期的销售分析

**请求参数：**
```json
{
  "period": "7days|30days|90days",  // 时间周期
  "startDate": "2025-01-01",        // 开始日期（可选）
  "endDate": "2025-01-31"           // 结束日期（可选）
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "period": "7days",
    "totalSales": 12580.50,
    "totalOrders": 156,
    "avgOrderValue": 80.64,
    "growthRate": 15.6,
    "chartData": [
      {
        "date": "2025-01-15",
        "sales": 1580.50,
        "orders": 18,
        "customers": 15
      }
    ]
  }
}
```

#### 1.2 产品销售表现
```http
GET /api/seller/analytics/product-performance
```

**功能描述：** 获取产品销售表现排行和分析

**请求参数：**
```json
{
  "limit": 10,                      // 返回数量
  "sortBy": "sales|revenue|views",  // 排序字段
  "period": "30days",               // 统计周期
  "categoryId": 1                   // 分类筛选（可选）
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "products": [
      {
        "productId": 1,
        "productName": "有机苹果",
        "sales": 156,
        "revenue": 4032.80,
        "views": 1250,
        "conversionRate": 12.48,
        "growthRate": 8.5
      }
    ],
    "summary": {
      "totalProducts": 45,
      "activeProducts": 38,
      "topPerformerGrowth": 25.6
    }
  }
}
```

### 2. 订单管理真实数据接口

#### 2.1 订单统计数据修复
```http
GET /api/seller-center/orders/stats
```

**当前问题：** 使用模拟数据，需要实现真实的数据库查询

**需要实现的查询逻辑：**
```sql
-- 待处理订单数
SELECT COUNT(*) FROM `order` 
WHERE seller_id = ? AND order_status IN (0, 1);

-- 今日销售额
SELECT COALESCE(SUM(actual_amount), 0) FROM `order` 
WHERE seller_id = ? AND DATE(created_at) = CURDATE() 
AND order_status NOT IN (5, 6);

-- 月度收入
SELECT COALESCE(SUM(actual_amount), 0) FROM `order` 
WHERE seller_id = ? AND YEAR(created_at) = YEAR(CURDATE()) 
AND MONTH(created_at) = MONTH(CURDATE()) 
AND order_status NOT IN (5, 6);
```

#### 2.2 订单列表查询修复
```http
GET /api/seller-center/orders
```

**当前问题：** 返回模拟数据，需要实现真实查询

**需要实现的功能：**
- 分页查询销售者订单
- 按状态筛选
- 按时间范围筛选
- 关键词搜索（订单号、客户名）

### 3. 客户管理基础接口

#### 3.1 客户列表查询
```http
GET /api/seller/customers
```

**功能描述：** 获取销售者的客户列表

**请求参数：**
```json
{
  "page": 1,
  "size": 20,
  "keyword": "张三",               // 客户姓名搜索
  "orderBy": "lastOrder|totalAmount|orderCount",
  "sortOrder": "desc"
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "customerId": 1,
        "customerName": "张三",
        "phone": "13800138000",
        "email": "<EMAIL>",
        "totalOrders": 8,
        "totalAmount": 1580.50,
        "lastOrderTime": "2025-01-20T10:30:00",
        "customerLevel": "VIP",
        "tags": ["老客户", "高价值"]
      }
    ],
    "total": 156,
    "current": 1,
    "size": 20,
    "pages": 8
  }
}
```

#### 3.2 客户详情查询
```http
GET /api/seller/customers/{customerId}
```

**功能描述：** 获取客户详细信息和购买历史

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "customerInfo": {
      "customerId": 1,
      "customerName": "张三",
      "phone": "13800138000",
      "email": "<EMAIL>",
      "registrationDate": "2024-06-15",
      "lastLoginTime": "2025-01-20T15:30:00"
    },
    "purchaseStats": {
      "totalOrders": 8,
      "totalAmount": 1580.50,
      "avgOrderValue": 197.56,
      "favoriteCategory": "水果类",
      "repurchaseRate": 75.0
    },
    "recentOrders": [
      {
        "orderId": 1001,
        "orderNo": "SF202501200001",
        "amount": 158.50,
        "status": "completed",
        "createTime": "2025-01-20T10:30:00"
      }
    ]
  }
}
```

## 🟡 中优先级缺失接口

### 1. 评价管理接口

#### 1.1 评价列表查询
```http
GET /api/seller/reviews
```

**功能描述：** 获取产品评价列表

**请求参数：**
```json
{
  "page": 1,
  "size": 20,
  "productId": 1,                   // 产品筛选（可选）
  "rating": 5,                      // 评分筛选（可选）
  "status": "pending|replied",      // 回复状态筛选
  "keyword": "好吃"                 // 评价内容搜索
}
```

#### 1.2 评价回复
```http
POST /api/seller/reviews/{reviewId}/reply
```

**功能描述：** 回复客户评价

**请求参数：**
```json
{
  "replyContent": "感谢您的评价，我们会继续努力！"
}
```

### 2. 库存管理接口

#### 2.1 库存预警列表
```http
GET /api/seller/inventory/alerts
```

**功能描述：** 获取库存预警商品列表

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "alerts": [
      {
        "productId": 1,
        "productName": "有机苹果",
        "currentStock": 5,
        "minStock": 10,
        "alertLevel": "warning|critical",
        "lastRestockTime": "2025-01-15T10:00:00"
      }
    ],
    "summary": {
      "totalAlerts": 8,
      "criticalAlerts": 3,
      "warningAlerts": 5
    }
  }
}
```

#### 2.2 批量库存更新
```http
POST /api/seller/inventory/batch-update
```

**功能描述：** 批量更新产品库存

**请求参数：**
```json
{
  "updates": [
    {
      "productId": 1,
      "stock": 100,
      "operation": "set|add|subtract"
    }
  ]
}
```

### 3. 财务管理接口

#### 3.1 交易记录查询
```http
GET /api/seller/finance/transactions
```

**功能描述：** 获取销售者交易记录

**请求参数：**
```json
{
  "page": 1,
  "size": 20,
  "type": "income|withdraw|refund",  // 交易类型
  "startDate": "2025-01-01",
  "endDate": "2025-01-31"
}
```

#### 3.2 账户余额查询
```http
GET /api/seller/finance/balance
```

**功能描述：** 获取销售者账户余额信息

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "availableBalance": 5680.50,      // 可用余额
    "frozenBalance": 320.00,          // 冻结余额
    "totalIncome": 25680.50,          // 总收入
    "totalWithdraw": 20000.00,        // 总提现
    "pendingSettlement": 1200.00      // 待结算
  }
}
```

## 🟢 低优先级缺失接口

### 1. 营销管理接口

#### 1.1 促销活动管理
```http
GET /api/seller/marketing/promotions
POST /api/seller/marketing/promotions
PUT /api/seller/marketing/promotions/{id}
DELETE /api/seller/marketing/promotions/{id}
```

#### 1.2 优惠券管理
```http
GET /api/seller/marketing/coupons
POST /api/seller/marketing/coupons
PUT /api/seller/marketing/coupons/{id}
DELETE /api/seller/marketing/coupons/{id}
```

### 2. 高级分析接口

#### 2.1 客户行为分析
```http
GET /api/seller/analytics/customer-behavior
```

#### 2.2 市场趋势分析
```http
GET /api/seller/analytics/market-trends
```

## 开发时间估算

### 高优先级接口（2-3周）
- 销售统计分析：5天
- 订单管理修复：3天
- 客户管理基础：4天

### 中优先级接口（2-3周）
- 评价管理：3天
- 库存管理：4天
- 财务管理基础：5天

### 低优先级接口（3-4周）
- 营销管理：7天
- 高级分析：10天

## 技术依赖

### 数据库表需求
- 需要创建统计相关表
- 需要优化现有表索引
- 需要创建视图简化查询

### 缓存策略
- Redis缓存统计数据
- 缓存热点商品信息
- 缓存用户权限信息

### 第三方服务
- 可能需要集成数据分析服务
- 可能需要集成消息推送服务

## 总结

销售中心共缺失约20个核心API接口，建议按优先级分阶段实施。高优先级接口主要解决当前功能不完整的问题，中低优先级接口主要增强系统功能和用户体验。
