# SFAP溯源管理模块文档

## 📋 模块概述

SFAP溯源管理模块是智慧农业辅助平台的核心功能之一，为农产品提供完整的溯源追踪体系，支持从生产到销售的全链条信息记录和查询。

---

## 🏗️ 模块架构

### 角色权限体系
- **普通用户**: 查询溯源信息、扫码验证
- **销售者**: 创建和管理溯源记录、查看统计数据
- **管理员**: 审核溯源记录、系统管理、数据分析

### 功能模块划分
```
溯源管理模块
├── 用户端查询模块
│   ├── 溯源码查询
│   ├── 二维码扫描
│   └── 产品信息展示
├── 销售者管理模块
│   ├── 溯源记录管理
│   ├── 记录创建编辑
│   ├── 数据统计分析
│   └── 审核状态查看
└── 管理员审核模块
    ├── 记录审核管理
    ├── 系统配置
    └── 数据监控
```

---

## 📊 数据库设计

### 核心表结构

#### traceability_record (溯源记录表)
| 字段名 | 类型 | 说明 | 必填 |
|--------|------|------|------|
| id | bigint | 主键ID | ✓ |
| product_id | bigint | 产品ID | ✓ |
| trace_code | varchar(50) | 溯源码 | ✓ |
| product_name | varchar(255) | 产品名称 | ✓ |
| farm_name | varchar(255) | 农场名称 | - |
| producer_id | bigint | 生产者ID | ✓ |
| producer_name | varchar(255) | 生产者名称 | - |
| batch_number | varchar(100) | 生产批次 | - |
| specification | varchar(255) | 产品规格 | - |
| quality_grade | varchar(50) | 质量等级 | - |
| creation_date | date | 创建日期 | - |
| harvest_date | date | 收获日期 | - |
| packaging_date | date | 包装日期 | - |
| qr_code_url | varchar(500) | 二维码URL | - |
| status | tinyint | 状态(0:草稿 1:待审核 2:已发布) | - |
| created_at | datetime | 创建时间 | - |
| updated_at | datetime | 更新时间 | - |
| deleted | tinyint | 删除标记 | - |

### 状态定义
- **0 (草稿)**: 记录已创建但未提交审核
- **1 (待审核)**: 记录已提交等待管理员审核
- **2 (已发布)**: 记录审核通过并发布

---

## 🔧 API接口设计

### 销售者接口

#### 获取溯源记录列表
```http
GET /api/traceability/seller/records
```

**请求参数**:
- `page`: 页码 (默认: 1)
- `size`: 页面大小 (默认: 10)
- `keyword`: 搜索关键词
- `status`: 状态筛选
- `productId`: 产品ID筛选

**响应示例**:
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "id": 1,
        "traceCode": "SFAP25071510001001A1B2",
        "productName": "有机菠菜",
        "farmName": "绿色农场",
        "status": 2,
        "createdAt": "2025-07-15 10:30:00"
      }
    ],
    "total": 100,
    "current": 1,
    "size": 10
  }
}
```

#### 创建溯源记录
```http
POST /api/traceability/seller/records
```

**请求体**:
```json
{
  "productId": 1,
  "productName": "有机菠菜",
  "farmName": "绿色农场",
  "producerName": "张三",
  "batchNumber": "BATCH001",
  "specification": "500g/包",
  "qualityGrade": "premium",
  "creationDate": "2025-07-10",
  "harvestDate": "2025-07-12",
  "packagingDate": "2025-07-13"
}
```

### 用户查询接口

#### 溯源信息查询
```http
POST /api/traceability/query
```

**请求体**:
```json
{
  "traceCode": "SFAP25071510001001A1B2"
}
```

---

## 🎨 前端组件设计

### 销售者溯源中心

#### 路由结构
```
/seller/traceability-center
├── / (溯源中心首页)
├── /records (记录管理)
├── /records/:id (记录详情)
├── /stats (数据统计)
└── /audit (审核状态)
```

#### 核心组件
- **TraceabilityCenter.vue**: 溯源中心首页
- **TraceabilityRecords.vue**: 记录列表管理
- **TraceabilityRecordForm.vue**: 记录创建/编辑表单
- **TraceabilityRecordDetail.vue**: 记录详情展示
- **TraceabilityStats.vue**: 数据统计分析

### 组件功能特性

#### 记录管理列表
- ✅ 分页加载
- ✅ 搜索筛选
- ✅ 批量操作
- ✅ 状态管理
- ✅ 响应式设计

#### 记录创建表单
- ✅ 字段验证
- ✅ 分步骤填写
- ✅ 草稿保存
- ✅ 产品关联
- ✅ 附件上传

---

## 🧪 测试用例

### 功能测试

#### 销售者记录管理
1. **创建记录测试**
   - 输入完整信息创建记录
   - 验证必填字段校验
   - 测试草稿保存功能

2. **记录列表测试**
   - 验证分页加载
   - 测试搜索筛选功能
   - 检查状态显示

3. **权限控制测试**
   - 验证销售者只能操作自己的记录
   - 测试跨用户访问拦截

### API测试

#### 接口功能测试
```bash
# 创建溯源记录
curl -X POST "http://localhost:8081/api/traceability/seller/records" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: 18" \
  -d '{
    "productId": 1,
    "productName": "有机菠菜",
    "farmName": "绿色农场",
    "batchNumber": "BATCH001"
  }'

# 获取记录列表
curl -X GET "http://localhost:8081/api/traceability/seller/records?page=1&size=10" \
  -H "X-User-Id: 18"
```

---

## 🚀 部署配置

### 环境要求
- **前端**: Node.js 14+, Vue 2.6+, Element UI 2.15+
- **后端**: Java 17+, Spring Boot 2.7+, MySQL 8.0+
- **其他**: Redis 6.0+ (缓存), Nginx (反向代理)

### 配置文件

#### application.yml
```yaml
spring:
  datasource:
    url: ********************************************
    username: root
    password: ${DB_PASSWORD}
  
  redis:
    host: localhost
    port: 6379
    database: 0

# 溯源模块配置
traceability:
  qr-code:
    base-url: ${QR_CODE_BASE_URL:http://localhost:8080}
    storage-path: ${QR_CODE_STORAGE:/uploads/qrcodes}
```

---

## 📈 性能优化

### 数据库优化
- 溯源码字段添加唯一索引
- 产品ID和状态字段添加复合索引
- 创建时间字段添加索引用于排序

### 缓存策略
- 热门溯源记录缓存24小时
- 用户查询历史缓存1小时
- 统计数据缓存6小时

### 前端优化
- 组件懒加载
- 图片懒加载
- 分页数据虚拟滚动

---

## 🔍 故障排除

### 常见问题

#### 1. 溯源码查询失败
**症状**: 查询返回404或数据为空
**解决方案**: 
- 检查溯源码格式是否正确
- 验证记录状态是否为已发布
- 确认数据库连接正常

#### 2. 权限验证失败
**症状**: 销售者无法访问溯源中心
**解决方案**:
- 检查用户角色配置
- 验证路由守卫逻辑
- 确认token有效性

#### 3. 二维码生成失败
**症状**: 二维码图片无法显示
**解决方案**:
- 检查文件存储路径权限
- 验证二维码生成服务
- 确认URL配置正确

---

## 📞 技术支持

### 开发团队联系方式
- **模块负责人**: 溯源模块开发组
- **技术支持**: 后端开发团队
- **前端支持**: 前端开发团队

### 相关文档
- [API接口文档](../api/traceability-api.md)
- [数据库设计文档](../architecture/database-design.md)
- [部署指南](../deployment/deployment-guide.md)

---

**文档版本**: v2.0  
**最后更新**: 2025-07-15  
**维护团队**: SFAP开发团队
