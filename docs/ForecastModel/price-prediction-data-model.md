# SFAP农品汇平台价格预测数据模型设计文档

## 1. 数据库设计

### 1.1 历史价格数据表 (price_history)

```sql
CREATE TABLE price_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    category VARCHAR(50) NOT NULL COMMENT '农产品类别',
    product_name VARCHAR(100) NOT NULL COMMENT '具体产品名称',
    region VARCHAR(100) NOT NULL COMMENT '地区',
    market_name VARCHAR(200) COMMENT '市场名称',
    price DECIMAL(10,2) NOT NULL COMMENT '价格(元/kg)',
    volume INT COMMENT '交易量(kg)',
    quality_grade VARCHAR(10) COMMENT '质量等级',
    date DATE NOT NULL COMMENT '日期',
    data_source VARCHAR(50) COMMENT '数据来源',
    weather VARCHAR(50) COMMENT '天气情况',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category_region_date (category, region, date),
    INDEX idx_product_date (product_name, date),
    INDEX idx_date (date),
    UNIQUE KEY uk_category_region_date (category, region, date, market_name)
) COMMENT '历史价格数据表';
```

### 1.2 模型信息表 (prediction_models)

```sql
CREATE TABLE prediction_models (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    model_id VARCHAR(100) UNIQUE NOT NULL COMMENT '模型唯一标识',
    model_type ENUM('RNN', 'ARIMA') NOT NULL COMMENT '模型类型',
    category VARCHAR(50) NOT NULL COMMENT '农产品类别',
    region VARCHAR(100) NOT NULL COMMENT '地区',
    model_params JSON COMMENT '模型参数',
    training_metrics JSON COMMENT '训练指标',
    model_file_path VARCHAR(500) COMMENT '模型文件路径',
    model_size BIGINT COMMENT '模型文件大小(字节)',
    accuracy DECIMAL(5,2) COMMENT '模型准确率',
    status ENUM('training', 'active', 'deprecated') DEFAULT 'training',
    training_start_time TIMESTAMP COMMENT '训练开始时间',
    training_end_time TIMESTAMP COMMENT '训练结束时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category_region (category, region),
    INDEX idx_model_type (model_type),
    INDEX idx_status (status)
) COMMENT '预测模型信息表';
```

### 1.3 预测结果表 (prediction_results)

```sql
CREATE TABLE prediction_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    prediction_id VARCHAR(100) UNIQUE NOT NULL COMMENT '预测任务ID',
    model_id VARCHAR(100) NOT NULL COMMENT '使用的模型ID',
    category VARCHAR(50) NOT NULL COMMENT '农产品类别',
    region VARCHAR(100) NOT NULL COMMENT '地区',
    prediction_date DATE NOT NULL COMMENT '预测日期',
    predicted_price DECIMAL(10,2) NOT NULL COMMENT '预测价格',
    confidence DECIMAL(5,4) COMMENT '置信度',
    upper_bound DECIMAL(10,2) COMMENT '置信区间上界',
    lower_bound DECIMAL(10,2) COMMENT '置信区间下界',
    trend VARCHAR(20) COMMENT '趋势(up/down/stable)',
    factors JSON COMMENT '影响因素分析',
    actual_price DECIMAL(10,2) COMMENT '实际价格(用于后续验证)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_model_id (model_id),
    INDEX idx_category_region_date (category, region, prediction_date),
    INDEX idx_prediction_date (prediction_date),
    FOREIGN KEY (model_id) REFERENCES prediction_models(model_id)
) COMMENT '预测结果表';
```

### 1.4 模型评估表 (model_evaluations)

```sql
CREATE TABLE model_evaluations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    model_id VARCHAR(100) NOT NULL COMMENT '模型ID',
    evaluation_date DATE NOT NULL COMMENT '评估日期',
    mape DECIMAL(8,4) COMMENT '平均绝对百分比误差',
    rmse DECIMAL(10,4) COMMENT '均方根误差',
    mae DECIMAL(10,4) COMMENT '平均绝对误差',
    r2_score DECIMAL(8,4) COMMENT 'R²决定系数',
    trend_accuracy DECIMAL(5,2) COMMENT '趋势预测准确率',
    evaluation_details JSON COMMENT '详细评估信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_model_id (model_id),
    INDEX idx_evaluation_date (evaluation_date),
    FOREIGN KEY (model_id) REFERENCES prediction_models(model_id)
) COMMENT '模型评估表';
```

## 2. 特征工程设计

### 2.1 时间序列特征

#### 基础时间特征
```python
# 时间相关特征
features = {
    'year': date.year,
    'month': date.month,
    'day': date.day,
    'day_of_week': date.weekday(),
    'day_of_year': date.timetuple().tm_yday,
    'week_of_year': date.isocalendar()[1],
    'quarter': (date.month - 1) // 3 + 1,
    'is_weekend': date.weekday() >= 5,
    'is_holiday': check_holiday(date),  # 节假日标识
    'season': get_season(date.month)    # 季节标识
}
```

#### 滞后特征
```python
# 价格滞后特征
lag_features = {
    'price_lag_1': price[t-1],      # 前1天价格
    'price_lag_3': price[t-3],      # 前3天价格
    'price_lag_7': price[t-7],      # 前7天价格
    'price_lag_14': price[t-14],    # 前14天价格
    'price_lag_30': price[t-30],    # 前30天价格
}
```

#### 统计特征
```python
# 滑动窗口统计特征
window_features = {
    'price_mean_7d': price[t-7:t].mean(),      # 7天均价
    'price_std_7d': price[t-7:t].std(),        # 7天价格标准差
    'price_max_7d': price[t-7:t].max(),        # 7天最高价
    'price_min_7d': price[t-7:t].min(),        # 7天最低价
    'price_trend_7d': calculate_trend(price[t-7:t]),  # 7天趋势
    'price_volatility_7d': calculate_volatility(price[t-7:t])  # 7天波动率
}
```

### 2.2 外部因素特征

#### 天气特征
```python
weather_features = {
    'temperature': temperature,          # 温度
    'humidity': humidity,               # 湿度
    'precipitation': precipitation,     # 降水量
    'weather_type': weather_type,       # 天气类型(晴/雨/雪等)
    'extreme_weather': is_extreme       # 极端天气标识
}
```

#### 市场特征
```python
market_features = {
    'trading_volume': volume,           # 交易量
    'market_sentiment': sentiment,      # 市场情绪指数
    'supply_index': supply_index,       # 供应指数
    'demand_index': demand_index,       # 需求指数
    'inventory_level': inventory        # 库存水平
}
```

#### 政策特征
```python
policy_features = {
    'policy_impact': policy_score,      # 政策影响评分
    'subsidy_amount': subsidy,          # 补贴金额
    'trade_policy': trade_policy,       # 贸易政策影响
    'environmental_policy': env_policy  # 环保政策影响
}
```

## 3. 数据预处理流程

### 3.1 数据清洗

```python
def clean_price_data(df):
    """价格数据清洗"""
    # 1. 移除重复记录
    df = df.drop_duplicates(subset=['category', 'region', 'date'])
    
    # 2. 处理缺失值
    df['price'] = df['price'].fillna(method='ffill')  # 前向填充
    
    # 3. 异常值检测和处理
    Q1 = df['price'].quantile(0.25)
    Q3 = df['price'].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    
    # 标记异常值
    df['is_outlier'] = (df['price'] < lower_bound) | (df['price'] > upper_bound)
    
    # 处理异常值(可选择删除或修正)
    df.loc[df['is_outlier'], 'price'] = df['price'].median()
    
    return df
```

### 3.2 特征标准化

```python
def normalize_features(features):
    """特征标准化"""
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    
    # 价格相关特征使用MinMax标准化
    price_features = ['price', 'price_lag_1', 'price_mean_7d']
    price_scaler = MinMaxScaler()
    features[price_features] = price_scaler.fit_transform(features[price_features])
    
    # 其他数值特征使用Z-score标准化
    numeric_features = features.select_dtypes(include=[np.number]).columns
    other_features = [col for col in numeric_features if col not in price_features]
    
    if other_features:
        standard_scaler = StandardScaler()
        features[other_features] = standard_scaler.fit_transform(features[other_features])
    
    return features, price_scaler, standard_scaler
```

### 3.3 序列数据构建

```python
def create_sequences(data, sequence_length=30, forecast_horizon=1):
    """构建时间序列数据"""
    X, y = [], []
    
    for i in range(sequence_length, len(data) - forecast_horizon + 1):
        # 输入序列
        X.append(data[i-sequence_length:i])
        # 目标值
        y.append(data[i:i+forecast_horizon])
    
    return np.array(X), np.array(y)
```

## 4. 模型评估指标

### 4.1 回归评估指标

```python
def calculate_regression_metrics(y_true, y_pred):
    """计算回归评估指标"""
    from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
    
    metrics = {
        'mae': mean_absolute_error(y_true, y_pred),
        'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
        'mape': np.mean(np.abs((y_true - y_pred) / y_true)) * 100,
        'r2_score': r2_score(y_true, y_pred)
    }
    
    return metrics
```

### 4.2 趋势预测评估

```python
def calculate_trend_accuracy(y_true, y_pred):
    """计算趋势预测准确率"""
    # 计算实际趋势
    true_trend = np.diff(y_true) > 0
    # 计算预测趋势
    pred_trend = np.diff(y_pred) > 0
    
    # 趋势准确率
    trend_accuracy = np.mean(true_trend == pred_trend) * 100
    
    return trend_accuracy
```

### 4.3 置信区间评估

```python
def calculate_confidence_metrics(y_true, y_pred, confidence_intervals):
    """计算置信区间相关指标"""
    lower_bounds = confidence_intervals[:, 0]
    upper_bounds = confidence_intervals[:, 1]
    
    # 覆盖率
    coverage = np.mean((y_true >= lower_bounds) & (y_true <= upper_bounds))
    
    # 平均区间宽度
    avg_width = np.mean(upper_bounds - lower_bounds)
    
    # 区间得分(越小越好)
    interval_score = np.mean(
        (upper_bounds - lower_bounds) + 
        2 * (lower_bounds - y_true) * (y_true < lower_bounds) +
        2 * (y_true - upper_bounds) * (y_true > upper_bounds)
    )
    
    return {
        'coverage': coverage,
        'avg_width': avg_width,
        'interval_score': interval_score
    }
```

## 5. 数据存储策略

### 5.1 历史数据存储
- **分区策略**: 按年份和地区分区
- **索引优化**: 复合索引(category, region, date)
- **数据压缩**: 使用InnoDB压缩
- **归档策略**: 超过5年的数据归档到历史库

### 5.2 模型文件存储
- **文件系统**: 本地文件系统或对象存储(如MinIO)
- **版本管理**: 模型文件版本控制
- **备份策略**: 定期备份重要模型
- **清理策略**: 自动清理过期模型文件

### 5.3 预测结果缓存
- **缓存策略**: Redis缓存热点预测结果
- **过期时间**: 根据预测时效性设置TTL
- **缓存键**: category:region:model_type:params_hash
- **更新策略**: 模型更新时清理相关缓存
