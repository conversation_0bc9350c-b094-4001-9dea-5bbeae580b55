# SFAP农品汇平台价格预测算法设计文档

## 1. RN<PERSON>神经网络模型设计

### 1.1 网络架构设计

#### 基础LSTM架构
```python
class PricePredictionLSTM(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2):
        super(PricePredictionLSTM, self).__init__()
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,      # 特征维度
            hidden_size=hidden_size,    # 隐藏层大小
            num_layers=num_layers,      # LSTM层数
            dropout=dropout,            # Dropout率
            batch_first=True,           # 批次维度在前
            bidirectional=False         # 单向LSTM
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=8,
            dropout=dropout
        )
        
        # 全连接层
        self.fc_layers = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, hidden_size // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 4, output_size)
        )
        
    def forward(self, x):
        # LSTM前向传播
        lstm_out, (hidden, cell) = self.lstm(x)
        
        # 注意力机制
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
        
        # 取最后一个时间步的输出
        final_output = attn_out[:, -1, :]
        
        # 全连接层预测
        prediction = self.fc_layers(final_output)
        
        return prediction
```

#### 超参数配置
```python
MODEL_CONFIG = {
    'input_size': 20,           # 输入特征数量
    'hidden_size': 128,         # LSTM隐藏层大小
    'num_layers': 3,            # LSTM层数
    'output_size': 1,           # 输出维度(价格)
    'dropout': 0.2,             # Dropout率
    'sequence_length': 30,      # 输入序列长度
    'learning_rate': 0.001,     # 学习率
    'batch_size': 64,           # 批次大小
    'epochs': 200,              # 训练轮数
    'patience': 20,             # 早停耐心值
    'weight_decay': 1e-5        # L2正则化
}
```

### 1.2 损失函数设计

#### 多任务损失函数
```python
class MultiTaskLoss(nn.Module):
    def __init__(self, alpha=0.7, beta=0.2, gamma=0.1):
        super(MultiTaskLoss, self).__init__()
        self.alpha = alpha  # 价格预测权重
        self.beta = beta    # 趋势预测权重
        self.gamma = gamma  # 置信度权重
        
    def forward(self, price_pred, price_true, trend_pred, trend_true, confidence):
        # 价格预测损失(Huber Loss)
        price_loss = F.smooth_l1_loss(price_pred, price_true)
        
        # 趋势预测损失(交叉熵)
        trend_loss = F.cross_entropy(trend_pred, trend_true)
        
        # 置信度损失(鼓励合理的不确定性)
        confidence_loss = -torch.mean(confidence * torch.log(confidence + 1e-8))
        
        # 总损失
        total_loss = (self.alpha * price_loss + 
                     self.beta * trend_loss + 
                     self.gamma * confidence_loss)
        
        return total_loss, price_loss, trend_loss, confidence_loss
```

### 1.3 训练策略

#### 学习率调度
```python
def get_scheduler(optimizer, config):
    """获取学习率调度器"""
    return torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer,
        mode='min',
        factor=0.5,
        patience=10,
        min_lr=1e-6,
        verbose=True
    )
```

#### 早停机制
```python
class EarlyStopping:
    def __init__(self, patience=20, min_delta=0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_loss = float('inf')
        
    def __call__(self, val_loss):
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
            return False
        else:
            self.counter += 1
            return self.counter >= self.patience
```

## 2. ARIMA时间序列模型设计

### 2.1 模型参数选择

#### 自动参数选择
```python
def auto_arima_selection(data, seasonal=True, seasonal_periods=12):
    """自动选择ARIMA参数"""
    from pmdarima import auto_arima
    
    model = auto_arima(
        data,
        start_p=0, start_q=0,           # 起始参数
        max_p=5, max_q=5,               # 最大参数
        seasonal=seasonal,              # 是否考虑季节性
        m=seasonal_periods,             # 季节周期
        stepwise=True,                  # 逐步搜索
        suppress_warnings=True,         # 抑制警告
        D=1,                           # 季节差分阶数
        max_D=2,                       # 最大季节差分
        trace=True,                    # 显示搜索过程
        information_criterion='aic',    # 信息准则
        out_of_sample_size=int(len(data) * 0.2)  # 样本外验证
    )
    
    return model
```

#### 手动参数网格搜索
```python
def grid_search_arima(data, p_range=(0, 5), d_range=(0, 2), q_range=(0, 5)):
    """网格搜索ARIMA参数"""
    import itertools
    from statsmodels.tsa.arima.model import ARIMA
    
    best_aic = float('inf')
    best_params = None
    best_model = None
    
    # 生成参数组合
    param_combinations = list(itertools.product(
        range(p_range[0], p_range[1] + 1),
        range(d_range[0], d_range[1] + 1),
        range(q_range[0], q_range[1] + 1)
    ))
    
    for params in param_combinations:
        try:
            model = ARIMA(data, order=params)
            fitted_model = model.fit()
            
            if fitted_model.aic < best_aic:
                best_aic = fitted_model.aic
                best_params = params
                best_model = fitted_model
                
        except Exception as e:
            continue
    
    return best_model, best_params, best_aic
```

### 2.2 季节性处理

#### 季节性分解
```python
def seasonal_decomposition(data, period=12):
    """季节性分解"""
    from statsmodels.tsa.seasonal import seasonal_decompose
    
    decomposition = seasonal_decompose(
        data, 
        model='additive',  # 加法模型
        period=period,     # 季节周期
        extrapolate_trend='freq'  # 趋势外推
    )
    
    return {
        'trend': decomposition.trend,
        'seasonal': decomposition.seasonal,
        'residual': decomposition.resid,
        'observed': decomposition.observed
    }
```

#### SARIMA模型
```python
def fit_sarima_model(data, order, seasonal_order):
    """拟合SARIMA模型"""
    from statsmodels.tsa.statespace.sarimax import SARIMAX
    
    model = SARIMAX(
        data,
        order=order,                    # (p, d, q)
        seasonal_order=seasonal_order,  # (P, D, Q, s)
        enforce_stationarity=False,     # 不强制平稳性
        enforce_invertibility=False     # 不强制可逆性
    )
    
    fitted_model = model.fit(
        disp=False,                     # 不显示优化过程
        maxiter=200,                    # 最大迭代次数
        method='lbfgs'                  # 优化方法
    )
    
    return fitted_model
```

### 2.3 模型诊断

#### 残差分析
```python
def residual_analysis(model, data):
    """残差分析"""
    import scipy.stats as stats
    from statsmodels.stats.diagnostic import acorr_ljungbox
    
    residuals = model.resid
    
    # Ljung-Box检验(残差自相关)
    ljung_box = acorr_ljungbox(residuals, lags=10, return_df=True)
    
    # Jarque-Bera正态性检验
    jb_stat, jb_pvalue = stats.jarque_bera(residuals)
    
    # 异方差性检验
    from statsmodels.stats.diagnostic import het_breuschpagan
    bp_stat, bp_pvalue, _, _ = het_breuschpagan(residuals, model.model.exog)
    
    diagnostics = {
        'ljung_box_pvalue': ljung_box['lb_pvalue'].iloc[-1],
        'jarque_bera_pvalue': jb_pvalue,
        'breusch_pagan_pvalue': bp_pvalue,
        'residual_mean': residuals.mean(),
        'residual_std': residuals.std()
    }
    
    return diagnostics
```

## 3. 模型集成策略

### 3.1 加权平均集成
```python
class WeightedEnsemble:
    def __init__(self, models, weights=None):
        self.models = models
        self.weights = weights or [1/len(models)] * len(models)
        
    def predict(self, X):
        predictions = []
        
        for model in self.models:
            pred = model.predict(X)
            predictions.append(pred)
        
        # 加权平均
        ensemble_pred = np.average(predictions, weights=self.weights, axis=0)
        
        return ensemble_pred
    
    def update_weights(self, validation_errors):
        """根据验证误差更新权重"""
        inverse_errors = 1 / (validation_errors + 1e-8)
        self.weights = inverse_errors / inverse_errors.sum()
```

### 3.2 动态权重调整
```python
def dynamic_weight_adjustment(models, recent_performance, window_size=30):
    """动态调整模型权重"""
    # 计算最近性能的移动平均
    recent_scores = []
    for model_performance in recent_performance:
        if len(model_performance) >= window_size:
            recent_score = np.mean(model_performance[-window_size:])
        else:
            recent_score = np.mean(model_performance)
        recent_scores.append(recent_score)
    
    # 转换为权重(性能越好权重越高)
    inverse_scores = 1 / (np.array(recent_scores) + 1e-8)
    weights = inverse_scores / inverse_scores.sum()
    
    return weights
```

## 4. 不确定性量化

### 4.1 贝叶斯神经网络
```python
class BayesianLSTM(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers, output_size):
        super(BayesianLSTM, self).__init__()
        
        # 使用Dropout作为贝叶斯近似
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, 
                           dropout=0.2, batch_first=True)
        self.dropout = nn.Dropout(0.2)
        self.fc = nn.Linear(hidden_size, output_size)
        
    def forward(self, x, num_samples=100):
        # 多次前向传播获取不确定性
        predictions = []
        
        for _ in range(num_samples):
            lstm_out, _ = self.lstm(x)
            dropped = self.dropout(lstm_out[:, -1, :])
            pred = self.fc(dropped)
            predictions.append(pred)
        
        predictions = torch.stack(predictions, dim=0)
        
        # 计算均值和标准差
        mean_pred = predictions.mean(dim=0)
        std_pred = predictions.std(dim=0)
        
        return mean_pred, std_pred
```

### 4.2 置信区间计算
```python
def calculate_confidence_intervals(predictions, std_predictions, confidence_level=0.95):
    """计算置信区间"""
    import scipy.stats as stats
    
    # 计算置信水平对应的z值
    alpha = 1 - confidence_level
    z_score = stats.norm.ppf(1 - alpha/2)
    
    # 计算置信区间
    lower_bound = predictions - z_score * std_predictions
    upper_bound = predictions + z_score * std_predictions
    
    return lower_bound, upper_bound
```

## 5. 模型优化策略

### 5.1 超参数优化
```python
def hyperparameter_optimization(train_data, val_data):
    """使用Optuna进行超参数优化"""
    import optuna
    
    def objective(trial):
        # 建议超参数
        hidden_size = trial.suggest_int('hidden_size', 64, 256)
        num_layers = trial.suggest_int('num_layers', 2, 4)
        dropout = trial.suggest_float('dropout', 0.1, 0.5)
        learning_rate = trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True)
        
        # 训练模型
        model = PricePredictionLSTM(
            input_size=20,
            hidden_size=hidden_size,
            num_layers=num_layers,
            output_size=1,
            dropout=dropout
        )
        
        # 训练和验证
        val_loss = train_and_validate(model, train_data, val_data, learning_rate)
        
        return val_loss
    
    # 创建研究对象
    study = optuna.create_study(direction='minimize')
    study.optimize(objective, n_trials=100)
    
    return study.best_params
```

### 5.2 特征选择
```python
def feature_importance_analysis(model, X, y):
    """特征重要性分析"""
    from sklearn.inspection import permutation_importance
    
    # 排列重要性
    perm_importance = permutation_importance(
        model, X, y, 
        n_repeats=10, 
        random_state=42,
        scoring='neg_mean_squared_error'
    )
    
    # 特征重要性排序
    feature_importance = pd.DataFrame({
        'feature': range(X.shape[1]),
        'importance': perm_importance.importances_mean,
        'std': perm_importance.importances_std
    }).sort_values('importance', ascending=False)
    
    return feature_importance
```

## 6. 模型评估框架

### 6.1 时间序列交叉验证
```python
def time_series_cv(data, model_func, n_splits=5, test_size=30):
    """时间序列交叉验证"""
    from sklearn.model_selection import TimeSeriesSplit
    
    tscv = TimeSeriesSplit(n_splits=n_splits, test_size=test_size)
    scores = []
    
    for train_idx, test_idx in tscv.split(data):
        train_data = data[train_idx]
        test_data = data[test_idx]
        
        # 训练模型
        model = model_func(train_data)
        
        # 预测和评估
        predictions = model.predict(test_data)
        score = evaluate_model(test_data, predictions)
        scores.append(score)
    
    return np.array(scores)
```

### 6.2 在线学习评估
```python
def online_learning_evaluation(model, data_stream, update_frequency=7):
    """在线学习评估"""
    predictions = []
    actuals = []
    
    for i, (X, y) in enumerate(data_stream):
        # 预测
        pred = model.predict(X)
        predictions.append(pred)
        actuals.append(y)
        
        # 定期更新模型
        if i % update_frequency == 0 and i > 0:
            recent_data = data_stream[max(0, i-100):i]
            model.partial_fit(recent_data)
    
    # 计算累积性能
    cumulative_errors = []
    for i in range(1, len(predictions)):
        error = mean_absolute_error(actuals[:i], predictions[:i])
        cumulative_errors.append(error)
    
    return cumulative_errors
```
