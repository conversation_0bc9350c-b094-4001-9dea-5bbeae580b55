# SFAP农品汇平台价格预测API接口设计文档

## 1. API概述

### 1.1 基础信息
- **服务地址**: http://localhost:5000 (开发环境)
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: JWT Token (可选)

### 1.2 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2025-01-24T10:30:00Z"
}
```

### 1.3 错误码定义
- **200**: 成功
- **400**: 请求参数错误
- **401**: 未授权访问
- **404**: 资源不存在
- **500**: 服务器内部错误
- **503**: 服务暂时不可用

## 2. 模型训练接口

### 2.1 RNN模型训练

#### 接口信息
- **URL**: `/api/v1/train_rnn`
- **方法**: POST
- **描述**: 训练RNN神经网络模型

#### 请求参数
```json
{
  "category": "apple",           // 农产品类别
  "region": "山东省",            // 地区
  "history_data": [              // 历史价格数据
    {
      "date": "2024-01-01",
      "price": 5.2,
      "volume": 1000,            // 交易量(可选)
      "weather": "sunny"         // 天气信息(可选)
    }
  ],
  "model_params": {              // 模型参数(可选)
    "sequence_length": 30,       // 序列长度
    "hidden_units": 64,          // 隐藏层单元数
    "learning_rate": 0.001,      // 学习率
    "epochs": 100,               // 训练轮数
    "batch_size": 32             // 批次大小
  }
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "RNN模型训练成功",
  "data": {
    "model_id": "rnn_apple_shandong_20250124",
    "training_metrics": {
      "loss": 0.0234,
      "val_loss": 0.0267,
      "mape": 5.2,
      "rmse": 0.45
    },
    "training_time": 120.5,      // 训练时间(秒)
    "model_size": "2.3MB",       // 模型大小
    "feature_importance": [      // 特征重要性
      {"feature": "price_lag_1", "importance": 0.35},
      {"feature": "price_lag_7", "importance": 0.28},
      {"feature": "season", "importance": 0.15}
    ]
  }
}
```

### 2.2 ARIMA模型训练

#### 接口信息
- **URL**: `/api/v1/train_arima`
- **方法**: POST
- **描述**: 训练ARIMA时间序列模型

#### 请求参数
```json
{
  "category": "apple",
  "region": "山东省",
  "history_data": [
    {
      "date": "2024-01-01",
      "price": 5.2
    }
  ],
  "model_params": {              // 可选，不提供则自动选择
    "p": 2,                      // 自回归阶数
    "d": 1,                      // 差分阶数
    "q": 2,                      // 移动平均阶数
    "seasonal": true,            // 是否考虑季节性
    "seasonal_periods": 12       // 季节周期
  }
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "ARIMA模型训练成功",
  "data": {
    "model_id": "arima_apple_shandong_20250124",
    "model_params": {
      "order": [2, 1, 2],
      "seasonal_order": [1, 1, 1, 12]
    },
    "training_metrics": {
      "aic": 245.67,
      "bic": 258.34,
      "mape": 6.8,
      "rmse": 0.52
    },
    "model_diagnostics": {
      "ljung_box_p": 0.15,       // Ljung-Box检验p值
      "jarque_bera_p": 0.08,     // Jarque-Bera检验p值
      "heteroscedasticity_p": 0.12
    }
  }
}
```

## 3. 预测接口

### 3.1 RNN模型预测

#### 接口信息
- **URL**: `/api/v1/predict_rnn`
- **方法**: POST
- **描述**: 使用RNN模型进行价格预测

#### 请求参数
```json
{
  "model_id": "rnn_apple_shandong_20250124",  // 可选，不提供则使用最新模型
  "category": "apple",
  "region": "山东省",
  "history_data": [              // 最近的历史数据
    {
      "date": "2024-12-20",
      "price": 5.8
    }
  ],
  "forecast_days": 30,           // 预测天数
  "confidence_level": 0.95,      // 置信水平
  "external_factors": {          // 外部因素(可选)
    "weather_forecast": "rainy",
    "market_sentiment": "positive",
    "policy_impact": "neutral"
  }
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "RNN预测成功",
  "data": {
    "model_info": {
      "model_id": "rnn_apple_shandong_20250124",
      "model_type": "RNN-LSTM",
      "training_date": "2025-01-24",
      "accuracy": 94.2
    },
    "predictions": [
      {
        "date": "2025-01-25",
        "price": 5.95,
        "confidence": 0.92,
        "upper_bound": 6.25,
        "lower_bound": 5.65,
        "trend": "up"
      }
    ],
    "summary": {
      "avg_price": 6.12,
      "price_change": 0.32,       // 相对于最后历史价格的变化
      "volatility": 0.15,         // 价格波动率
      "trend_direction": "upward",
      "confidence_avg": 0.89
    },
    "factors": [                  // 影响因素分析
      {
        "name": "季节性因素",
        "impact": "positive",
        "weight": 0.25,
        "description": "春季需求增加"
      }
    ]
  }
}
```

### 3.2 ARIMA模型预测

#### 接口信息
- **URL**: `/api/v1/predict_arima`
- **方法**: POST
- **描述**: 使用ARIMA模型进行价格预测

#### 请求参数
```json
{
  "model_id": "arima_apple_shandong_20250124",
  "category": "apple",
  "region": "山东省",
  "history_data": [
    {
      "date": "2024-12-20",
      "price": 5.8
    }
  ],
  "forecast_days": 30,
  "confidence_level": 0.95
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "ARIMA预测成功",
  "data": {
    "model_info": {
      "model_id": "arima_apple_shandong_20250124",
      "model_type": "ARIMA(2,1,2)",
      "training_date": "2025-01-24",
      "accuracy": 91.5
    },
    "predictions": [
      {
        "date": "2025-01-25",
        "price": 5.87,
        "confidence": 0.88,
        "upper_bound": 6.15,
        "lower_bound": 5.59,
        "trend": "stable"
      }
    ],
    "summary": {
      "avg_price": 5.92,
      "price_change": 0.12,
      "volatility": 0.08,
      "trend_direction": "stable",
      "confidence_avg": 0.85
    }
  }
}
```

## 4. 模型管理接口

### 4.1 获取模型列表

#### 接口信息
- **URL**: `/api/v1/models`
- **方法**: GET
- **描述**: 获取可用的模型列表

#### 请求参数
```
?category=apple&region=山东省&model_type=rnn&page=1&size=10
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取模型列表成功",
  "data": {
    "models": [
      {
        "model_id": "rnn_apple_shandong_20250124",
        "model_type": "RNN-LSTM",
        "category": "apple",
        "region": "山东省",
        "created_date": "2025-01-24T10:30:00Z",
        "accuracy": 94.2,
        "status": "active"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 10,
      "total": 25,
      "pages": 3
    }
  }
}
```

### 4.2 模型性能评估

#### 接口信息
- **URL**: `/api/v1/models/{model_id}/evaluate`
- **方法**: POST
- **描述**: 评估模型性能

#### 请求参数
```json
{
  "test_data": [
    {
      "date": "2024-12-01",
      "actual_price": 5.5,
      "predicted_price": 5.3
    }
  ]
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "模型评估完成",
  "data": {
    "metrics": {
      "mape": 5.2,
      "rmse": 0.45,
      "mae": 0.32,
      "r2_score": 0.89,
      "accuracy": 94.2
    },
    "evaluation_details": {
      "total_predictions": 100,
      "correct_trend_predictions": 87,
      "trend_accuracy": 87.0
    }
  }
}
```

## 5. 数据管理接口

### 5.1 历史数据上传

#### 接口信息
- **URL**: `/api/v1/data/upload`
- **方法**: POST
- **描述**: 批量上传历史价格数据

#### 请求参数
```json
{
  "category": "apple",
  "region": "山东省",
  "data_source": "market_survey",
  "data": [
    {
      "date": "2024-01-01",
      "price": 5.2,
      "volume": 1000,
      "quality_grade": "A",
      "market_name": "寿光蔬菜批发市场"
    }
  ]
}
```

### 5.2 数据质量检查

#### 接口信息
- **URL**: `/api/v1/data/validate`
- **方法**: POST
- **描述**: 检查数据质量

#### 响应数据
```json
{
  "code": 200,
  "message": "数据质量检查完成",
  "data": {
    "total_records": 1000,
    "valid_records": 987,
    "issues": [
      {
        "type": "missing_value",
        "count": 8,
        "fields": ["price"]
      },
      {
        "type": "outlier",
        "count": 5,
        "description": "价格异常值"
      }
    ],
    "quality_score": 98.7
  }
}
```

## 6. 系统状态接口

### 6.1 健康检查

#### 接口信息
- **URL**: `/api/v1/health`
- **方法**: GET
- **描述**: 检查服务健康状态

#### 响应数据
```json
{
  "code": 200,
  "message": "服务正常",
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": 3600,
    "models_loaded": 15,
    "memory_usage": "2.1GB",
    "cpu_usage": "15%"
  }
}
```
