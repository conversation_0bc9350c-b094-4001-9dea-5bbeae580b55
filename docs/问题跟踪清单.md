# SFAP销售者销售中心 - 问题跟踪清单

## 📋 文档信息

**创建时间**: 2025年7月22日  
**最后更新**: 2025年7月22日  
**维护人**: AI助手  
**状态**: 活跃跟踪中  

## 🚨 问题分类与优先级

### 优先级定义
- **P0 (阻塞)**: 必须立即解决，否则无法继续开发
- **P1 (高)**: 影响核心功能，需要在当前阶段解决
- **P2 (中)**: 影响用户体验，可以在后续阶段解决
- **P3 (低)**: 优化项，不影响基本功能

## 🔴 P0级问题 (阻塞级)

### P0-001: 销售者权限验证缺失
**发现阶段**: 第一阶段
**问题描述**: 当前系统缺乏严格的销售者权限验证机制，可能导致数据安全问题
**影响范围**: 所有销售者API接口
**风险等级**: 高
**实际认证机制**: Session + Header认证 (X-User-Id)
**解决方案**:
- 在所有销售者API中添加权限验证注解
- 确保销售者只能操作自己的数据
- 利用现有的UserAuthInterceptor和AuthorizationInterceptor
- 实现基于用户ID的身份验证和权限控制

**状态**: 🔴 待解决
**负责人**: 待分配
**预计解决时间**: 第二阶段开始时
**验证标准**:
- [ ] 销售者无法访问其他销售者的数据
- [ ] 非销售者用户无法访问销售者API
- [ ] 所有API调用都经过现有认证拦截器验证
- [ ] 正确使用@RequireRole和@RequirePermission注解

---

## 🟡 P1级问题 (高优先级)

### P1-001: Java实体类字段映射不一致
**发现阶段**: 第一阶段  
**问题描述**: user表存在重复的时间字段，可能导致Java实体类映射错误  
**具体问题**:
```sql
-- 重复的时间字段
created_at (datetime) 
create_time (datetime(6))
updated_at (datetime)
update_time (datetime(6))
```
**影响范围**: 用户相关的所有API  
**解决方案**: 
1. 检查Java实体类的字段映射
2. 统一使用created_at/updated_at字段
3. 确保@Column注解正确映射

**状态**: 🟡 待解决  
**负责人**: 待分配  
**预计解决时间**: 第二阶段第1周  
**验证标准**: 
- [ ] Java实体类字段与数据库字段一一对应
- [ ] 时间字段映射正确
- [ ] 所有CRUD操作正常

### P1-002: 测试订单数据缺失
**发现阶段**: 第一阶段  
**问题描述**: 销售者没有订单记录，无法测试订单管理功能  
**查询结果**: 
```sql
SELECT COUNT(*) FROM `order` WHERE seller_id IN (7, 29, 30, 33);
-- 结果: 0条记录
```
**影响范围**: 订单管理、统计分析功能  
**解决方案**: 
1. 生成不同状态的测试订单数据
2. 创建对应的订单项数据
3. 确保数据覆盖完整的订单流程

**状态**: 🟡 待解决  
**负责人**: 待分配  
**预计解决时间**: 第二阶段第1周  
**验证标准**: 
- [ ] 每个销售者至少50条订单记录
- [ ] 覆盖所有订单状态 (0-4)
- [ ] 包含完整的时间节点数据

### P1-003: 溯源码格式兼容性处理
**发现阶段**: 第一阶段
**问题描述**: 确保系统API能正确处理现有的溯源码格式
**当前格式**:
```
新格式(24位): SFAP + 标识符(1位) + 时间戳(10位) + 产品ID(4位) + 随机码(5位)
- 销售者产品: SFAPS2507162209601459927
- 管理员产品: SFAPA24011109151006D3693
旧格式(22位): SFAP + 时间戳(10位) + 产品ID(4位) + 随机码(4位) (向后兼容)
```

**影响范围**: 溯源查询、验证功能
**解决方案**:
1. 确保API支持现有格式识别
2. 验证二维码生成功能兼容性
3. 测试前后端集成的溯源功能
4. 保持代码与实际数据格式一致

**状态**: 🟢 已回退到实际格式，待验证
**负责人**: 待分配
**预计解决时间**: 第二阶段第1周
**验证标准**:
- [ ] API能正确识别现有格式
- [ ] 查询功能对所有格式都有效
- [ ] 来源类型能正确识别
- [ ] 二维码生成功能正常

### P1-004: 溯源码格式文档更新
**发现阶段**: 第一阶段（格式确认后）
**问题描述**: 确保所有文档反映系统实际使用的溯源码格式
**实际格式**:
```sql
-- 数据库中的实际格式（已确认正确）
SFAPS2507162209601459927 (销售者产品，24位)
SFAPA24011109151006D3693 (管理员产品，24位)

-- 格式结构
SFAP + 标识符(1位) + 时间戳(10位) + 产品ID(4位) + 随机码(5位)
```
**影响范围**: 技术文档、API文档、用户手册
**解决方案**:
1. 更新所有相关技术文档
2. 确保API文档格式说明正确
3. 更新前端显示和验证逻辑
4. 保持文档与代码一致性

**状态**: 🟢 已更新主要文档
**负责人**: 待分配
**预计解决时间**: 第二阶段第1周
**验证标准**:
- [ ] 所有文档格式说明一致
- [ ] API文档反映实际格式
- [ ] 前端验证逻辑正确
- [ ] 用户手册格式说明准确

---

## 🟢 P2级问题 (中优先级)

### P2-001: 销售统计数据不完整
**发现阶段**: 第一阶段  
**问题描述**: seller_statistics表缺少历史统计数据，影响趋势分析  
**影响范围**: 销售仪表板、数据分析功能  
**解决方案**: 
1. 基于现有订单数据生成历史统计
2. 实现自动统计数据更新机制
3. 添加数据验证和修复功能

**状态**: 🟢 计划中  
**负责人**: 待分配  
**预计解决时间**: 第二阶段第3周  

### P2-002: 产品评价数据稀少
**发现阶段**: 第一阶段  
**问题描述**: 产品评价数据不足，影响评价管理功能测试  
**影响范围**: 评价管理、商品展示  
**解决方案**: 
1. 生成模拟的产品评价数据
2. 包含不同评分和评价内容
3. 添加销售者回复数据

**状态**: 🟢 计划中  
**负责人**: 待分配  
**预计解决时间**: 第二阶段第4周  

---

## 🔵 P3级问题 (低优先级)

### P3-001: 数据库字段冗余
**发现阶段**: 第一阶段  
**问题描述**: 部分表存在重复或冗余字段，影响维护效率  
**具体问题**:
- user表的时间字段重复
- seller_application表的联系信息字段重复
**解决方案**: 
1. 分析字段使用情况
2. 制定字段清理计划
3. 逐步迁移和清理冗余字段

**状态**: 🔵 待规划  
**预计解决时间**: 第三阶段或后续版本  

### P3-002: 数据库索引优化
**发现阶段**: 第一阶段  
**问题描述**: 部分查询可能需要额外的复合索引优化  
**解决方案**: 
1. 分析实际查询性能
2. 根据使用情况添加复合索引
3. 定期监控和优化查询性能

**状态**: 🔵 待规划  
**预计解决时间**: 性能优化阶段  

---

## 📊 问题统计

### 按优先级统计
- **P0级**: 1个问题 (阻塞级)
- **P1级**: 3个问题 (高优先级)
- **P2级**: 2个问题 (中优先级)
- **P3级**: 2个问题 (低优先级)
- **总计**: 8个问题

### 按状态统计
- **🔴 待解决**: 1个
- **🟡 待解决**: 3个
- **🟢 计划中**: 2个
- **🔵 待规划**: 2个

### 按阶段分布
- **第一阶段发现**: 8个问题
- **第二阶段预计解决**: 4个问题 (P0 + P1)
- **后续阶段解决**: 4个问题 (P2 + P3)

## 🎯 解决计划

### 第二阶段第1周
- [ ] P0-001: 实现销售者权限验证
- [ ] P1-001: 修复Java实体类字段映射
- [ ] P1-002: 生成测试订单数据

### 第二阶段第2周
- [ ] P1-003: 实现溯源码格式兼容

### 第二阶段第3-4周
- [ ] P2-001: 完善销售统计数据
- [ ] P2-002: 生成产品评价数据

### 后续阶段
- [ ] P3-001: 清理数据库冗余字段
- [ ] P3-002: 优化数据库索引

## 📝 问题报告模板

```markdown
### 问题ID: P{优先级}-{序号}
**发现阶段**: 
**问题描述**: 
**影响范围**: 
**风险等级**: 
**解决方案**: 
**状态**: 
**负责人**: 
**预计解决时间**: 
**验证标准**: 
```

---
**文档维护**: 每个阶段结束后更新  
**问题上报**: 发现新问题时立即记录  
**状态更新**: 问题解决后及时更新状态  
**定期回顾**: 每周回顾问题解决进度
